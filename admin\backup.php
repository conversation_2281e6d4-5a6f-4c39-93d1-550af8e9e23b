<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات المدير
if (!isLoggedIn() || !isAdmin()) {
    header('Location: ../login.php');
    exit;
}

$pageTitle = 'النسخ الاحتياطي';
$breadcrumbs = [
    ['title' => 'النسخ الاحتياطي']
];

$success = '';
$error = '';

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    try {
        switch ($action) {
            case 'create_backup':
                $backup_type = $_POST['backup_type'] ?? 'full';
                $tables = $_POST['tables'] ?? [];
                $compression = $_POST['compression'] ?? 'gzip';

                // إنشاء اسم الملف
                $timestamp = date('Y-m-d_H-i-s');
                $filename = "backup_{$backup_type}_{$timestamp}.sql";
                if ($compression === 'gzip') {
                    $filename .= '.gz';
                }

                // مسار حفظ النسخة الاحتياطية
                $backup_dir = '../backups/';
                if (!is_dir($backup_dir)) {
                    mkdir($backup_dir, 0755, true);
                }
                $file_path = $backup_dir . $filename;

                // تسجيل النسخة الاحتياطية في قاعدة البيانات
                $stmt = $conn->prepare("
                    INSERT INTO backups (filename, file_path, backup_type, status, tables_included, compression_type, created_by)
                    VALUES (?, ?, ?, 'in_progress', ?, ?, ?)
                ");
                $stmt->execute([
                    $filename,
                    $file_path,
                    $backup_type,
                    json_encode($tables),
                    $compression,
                    $_SESSION['user_id']
                ]);
                $backup_id = $conn->lastInsertId();

                // إنشاء النسخة الاحتياطية
                $result = createDatabaseBackup($conn, $file_path, $backup_type, $tables, $compression);

                if ($result['success']) {
                    // تحديث حالة النسخة الاحتياطية
                    $stmt = $conn->prepare("
                        UPDATE backups
                        SET status = 'completed', file_size = ?, progress_percentage = 100, completed_at = NOW()
                        WHERE id = ?
                    ");
                    $stmt->execute([filesize($file_path), $backup_id]);

                    $success = 'تم إنشاء النسخة الاحتياطية بنجاح';
                    logUserActivity($_SESSION['user_id'], 'إنشاء نسخة احتياطية', "تم إنشاء نسخة احتياطية: $filename");
                } else {
                    // تحديث حالة النسخة الاحتياطية كفاشلة
                    $stmt = $conn->prepare("
                        UPDATE backups
                        SET status = 'failed', error_message = ?
                        WHERE id = ?
                    ");
                    $stmt->execute([$result['error'], $backup_id]);

                    throw new Exception($result['error']);
                }
                break;

            case 'restore_backup':
                $backup_id = (int)$_POST['backup_id'];

                // جلب معلومات النسخة الاحتياطية
                $stmt = $conn->prepare("SELECT * FROM backups WHERE id = ? AND status = 'completed'");
                $stmt->execute([$backup_id]);
                $backup = $stmt->fetch();

                if (!$backup) {
                    throw new Exception('النسخة الاحتياطية غير موجودة أو غير مكتملة');
                }

                if (!file_exists($backup['file_path'])) {
                    throw new Exception('ملف النسخة الاحتياطية غير موجود');
                }

                // استعادة النسخة الاحتياطية
                $result = restoreDatabaseBackup($conn, $backup['file_path'], $backup['compression_type']);

                if ($result['success']) {
                    $success = 'تم استعادة النسخة الاحتياطية بنجاح';
                    logUserActivity($_SESSION['user_id'], 'استعادة نسخة احتياطية', "تم استعادة نسخة احتياطية: " . $backup['filename']);
                } else {
                    throw new Exception($result['error']);
                }
                break;

            case 'delete_backup':
                $backup_id = (int)$_POST['backup_id'];

                // جلب معلومات النسخة الاحتياطية
                $stmt = $conn->prepare("SELECT * FROM backups WHERE id = ?");
                $stmt->execute([$backup_id]);
                $backup = $stmt->fetch();

                if ($backup) {
                    // حذف الملف
                    if (file_exists($backup['file_path'])) {
                        unlink($backup['file_path']);
                    }

                    // حذف السجل من قاعدة البيانات
                    $stmt = $conn->prepare("DELETE FROM backups WHERE id = ?");
                    $stmt->execute([$backup_id]);

                    $success = 'تم حذف النسخة الاحتياطية بنجاح';
                    logUserActivity($_SESSION['user_id'], 'حذف نسخة احتياطية', "تم حذف نسخة احتياطية: " . $backup['filename']);
                }
                break;

            case 'cleanup_old_backups':
                $retention_days = (int)($_POST['retention_days'] ?? 30);

                // جلب النسخ القديمة
                $stmt = $conn->prepare("
                    SELECT * FROM backups
                    WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)
                ");
                $stmt->execute([$retention_days]);
                $old_backups = $stmt->fetchAll();

                $deleted_count = 0;
                foreach ($old_backups as $backup) {
                    // حذف الملف
                    if (file_exists($backup['file_path'])) {
                        unlink($backup['file_path']);
                    }

                    // حذف السجل
                    $stmt = $conn->prepare("DELETE FROM backups WHERE id = ?");
                    $stmt->execute([$backup['id']]);

                    $deleted_count++;
                }

                $success = "تم حذف $deleted_count نسخة احتياطية قديمة";
                logUserActivity($_SESSION['user_id'], 'تنظيف النسخ الاحتياطية', "تم تنظيف النسخ الاحتياطية القديمة: $deleted_count نسخة");
                break;
        }

    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// جلب النسخ الاحتياطية
try {
    $stmt = $conn->query("
        SELECT b.*, u.name as created_by_name
        FROM backups b
        LEFT JOIN users u ON b.created_by = u.id
        ORDER BY b.created_at DESC
    ");
    $backups = $stmt->fetchAll();

    // إحصائيات النسخ الاحتياطية
    $stats_stmt = $conn->query("
        SELECT
            COUNT(*) as total_backups,
            COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_backups,
            COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_backups,
            COUNT(CASE WHEN status = 'in_progress' THEN 1 END) as in_progress_backups,
            COALESCE(SUM(file_size), 0) as total_size,
            MAX(created_at) as last_backup_date
        FROM backups
    ");
    $stats = $stats_stmt->fetch();

    // جلب قائمة الجداول
    $tables_stmt = $conn->query("SHOW TABLES");
    $all_tables = $tables_stmt->fetchAll(PDO::FETCH_COLUMN);

} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء جلب البيانات';
}

// دالة إنشاء النسخة الاحتياطية
function createDatabaseBackup($conn, $file_path, $backup_type, $tables, $compression) {
    try {
        $sql_content = '';

        // إضافة معلومات النسخة الاحتياطية
        $sql_content .= "-- نسخة احتياطية تم إنشاؤها في: " . date('Y-m-d H:i:s') . "\n";
        $sql_content .= "-- نوع النسخة: $backup_type\n";
        $sql_content .= "-- قاعدة البيانات: " . $conn->query("SELECT DATABASE()")->fetchColumn() . "\n\n";

        $sql_content .= "SET FOREIGN_KEY_CHECKS = 0;\n";
        $sql_content .= "SET SQL_MODE = \"NO_AUTO_VALUE_ON_ZERO\";\n";
        $sql_content .= "SET AUTOCOMMIT = 0;\n";
        $sql_content .= "START TRANSACTION;\n\n";

        if ($backup_type === 'full' || $backup_type === 'structure') {
            // نسخ احتياطي كامل أو هيكل فقط
            $tables_to_backup = empty($tables) ? $conn->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN) : $tables;

            foreach ($tables_to_backup as $table) {
                // إنشاء الجدول
                $create_table = $conn->query("SHOW CREATE TABLE `$table`")->fetch();
                $sql_content .= "DROP TABLE IF EXISTS `$table`;\n";
                $sql_content .= $create_table['Create Table'] . ";\n\n";

                // إدراج البيانات (إذا لم يكن هيكل فقط)
                if ($backup_type !== 'structure') {
                    $rows = $conn->query("SELECT * FROM `$table`")->fetchAll(PDO::FETCH_ASSOC);

                    if (!empty($rows)) {
                        $columns = array_keys($rows[0]);
                        $sql_content .= "INSERT INTO `$table` (`" . implode('`, `', $columns) . "`) VALUES\n";

                        $values = [];
                        foreach ($rows as $row) {
                            $escaped_values = array_map(function($value) use ($conn) {
                                return $value === null ? 'NULL' : $conn->quote($value);
                            }, array_values($row));
                            $values[] = "(" . implode(', ', $escaped_values) . ")";
                        }

                        $sql_content .= implode(",\n", $values) . ";\n\n";
                    }
                }
            }
        }

        $sql_content .= "SET FOREIGN_KEY_CHECKS = 1;\n";
        $sql_content .= "COMMIT;\n";

        // حفظ الملف
        if ($compression === 'gzip') {
            $gz = gzopen($file_path, 'w9');
            gzwrite($gz, $sql_content);
            gzclose($gz);
        } else {
            file_put_contents($file_path, $sql_content);
        }

        return ['success' => true];

    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

// دالة استعادة النسخة الاحتياطية
function restoreDatabaseBackup($conn, $file_path, $compression) {
    try {
        // قراءة الملف
        if ($compression === 'gzip') {
            $sql_content = gzfile($file_path);
            $sql_content = implode('', $sql_content);
        } else {
            $sql_content = file_get_contents($file_path);
        }

        // تنفيذ الاستعلامات
        $statements = explode(';', $sql_content);

        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (!empty($statement) && !preg_match('/^--/', $statement)) {
                $conn->exec($statement);
            }
        }

        return ['success' => true];

    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

include 'includes/header.php';
?>

<!-- الإحصائيات السريعة -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3" data-aos="fade-up">
        <div class="stats-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3 class="text-primary mb-1"><?php echo number_format($stats['total_backups']); ?></h3>
                    <p class="text-muted mb-0">إجمالي النسخ</p>
                    <small class="text-success">
                        <i class="fas fa-check-circle me-1"></i>
                        <?php echo $stats['completed_backups']; ?> مكتملة
                    </small>
                </div>
                <div class="text-primary">
                    <i class="fas fa-database fa-2x"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3" data-aos="fade-up" data-aos-delay="100">
        <div class="stats-card success">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3 class="text-success mb-1"><?php echo formatFileSize($stats['total_size']); ?></h3>
                    <p class="text-muted mb-0">الحجم الإجمالي</p>
                    <small class="text-muted">
                        <i class="fas fa-hdd me-1"></i>
                        مساحة مستخدمة
                    </small>
                </div>
                <div class="text-success">
                    <i class="fas fa-hdd fa-2x"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3" data-aos="fade-up" data-aos-delay="200">
        <div class="stats-card warning">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3 class="text-warning mb-1"><?php echo $stats['failed_backups']; ?></h3>
                    <p class="text-muted mb-0">نسخ فاشلة</p>
                    <small class="text-muted">
                        <i class="fas fa-exclamation-triangle me-1"></i>
                        تحتاج مراجعة
                    </small>
                </div>
                <div class="text-warning">
                    <i class="fas fa-exclamation-triangle fa-2x"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3" data-aos="fade-up" data-aos-delay="300">
        <div class="stats-card info">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3 class="text-info mb-1">
                        <?php echo $stats['last_backup_date'] ? date('d/m', strtotime($stats['last_backup_date'])) : 'لا يوجد'; ?>
                    </h3>
                    <p class="text-muted mb-0">آخر نسخة</p>
                    <small class="text-muted">
                        <i class="fas fa-clock me-1"></i>
                        <?php echo $stats['last_backup_date'] ? date('H:i', strtotime($stats['last_backup_date'])) : ''; ?>
                    </small>
                </div>
                <div class="text-info">
                    <i class="fas fa-clock fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<?php if ($success): ?>
<div class="alert alert-success alert-dismissible fade show" data-aos="fade-down">
    <i class="fas fa-check-circle me-2"></i>
    <?php echo htmlspecialchars($success); ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<?php if ($error): ?>
<div class="alert alert-danger alert-dismissible fade show" data-aos="fade-down">
    <i class="fas fa-exclamation-circle me-2"></i>
    <?php echo htmlspecialchars($error); ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<!-- أدوات النسخ الاحتياطي -->
<div class="row mb-4">
    <div class="col-lg-8 mb-4" data-aos="fade-up">
        <div class="card-admin">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-plus me-2"></i>
                    إنشاء نسخة احتياطية جديدة
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" id="backupForm">
                    <input type="hidden" name="action" value="create_backup">

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">نوع النسخة الاحتياطية</label>
                            <select name="backup_type" class="form-select" id="backupType">
                                <option value="full">نسخة كاملة (البيانات + الهيكل)</option>
                                <option value="structure">الهيكل فقط</option>
                                <option value="data">البيانات فقط</option>
                                <option value="partial">نسخة جزئية (جداول محددة)</option>
                            </select>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label class="form-label">نوع الضغط</label>
                            <select name="compression" class="form-select">
                                <option value="gzip">ضغط Gzip (موصى به)</option>
                                <option value="none">بدون ضغط</option>
                            </select>
                        </div>
                    </div>

                    <div id="tablesSelection" style="display: none;">
                        <label class="form-label">اختر الجداول</label>
                        <div class="row">
                            <?php foreach ($all_tables as $table): ?>
                            <div class="col-md-4 mb-2">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="tables[]" value="<?php echo $table; ?>" id="table_<?php echo $table; ?>">
                                    <label class="form-check-label" for="table_<?php echo $table; ?>">
                                        <?php echo $table; ?>
                                    </label>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        <div class="mt-2">
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="selectAllTables()">تحديد الكل</button>
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="deselectAllTables()">إلغاء التحديد</button>
                        </div>
                    </div>

                    <div class="mt-3">
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-download me-1"></i>
                            إنشاء النسخة الاحتياطية
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4 mb-4" data-aos="fade-up" data-aos-delay="100">
        <div class="card-admin">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-broom me-2"></i>
                    تنظيف النسخ القديمة
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <input type="hidden" name="action" value="cleanup_old_backups">

                    <div class="mb-3">
                        <label class="form-label">حذف النسخ الأقدم من (أيام)</label>
                        <input type="number" name="retention_days" class="form-control" value="30" min="1" max="365">
                    </div>

                    <button type="submit" class="btn btn-warning w-100"
                            onclick="return confirm('هل أنت متأكد من حذف النسخ الاحتياطية القديمة؟')">
                        <i class="fas fa-broom me-1"></i>
                        تنظيف النسخ القديمة
                    </button>
                </form>

                <hr>

                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>نصيحة:</strong> احتفظ بنسخة احتياطية واحدة على الأقل من كل شهر
                </div>
            </div>
        </div>
    </div>
</div>

<!-- قائمة النسخ الاحتياطية -->
<div class="card-admin" data-aos="fade-up">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>
                النسخ الاحتياطية المحفوظة
            </h5>
            <div class="d-flex gap-2">
                <button class="btn btn-info btn-sm" onclick="refreshBackupList()">
                    <i class="fas fa-sync me-1"></i>
                    تحديث
                </button>
            </div>
        </div>
    </div>
    <div class="card-body p-0">
        <?php if (empty($backups)): ?>
        <div class="text-center py-5">
            <i class="fas fa-database fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد نسخ احتياطية</h5>
            <p class="text-muted">ابدأ بإنشاء نسخة احتياطية لحماية بياناتك</p>
        </div>
        <?php else: ?>
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>اسم الملف</th>
                        <th>النوع</th>
                        <th>الحجم</th>
                        <th>الحالة</th>
                        <th>تاريخ الإنشاء</th>
                        <th>المنشئ</th>
                        <th width="200">الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($backups as $backup): ?>
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <i class="fas fa-file-archive text-primary me-2"></i>
                                <div>
                                    <div class="fw-bold"><?php echo htmlspecialchars($backup['filename']); ?></div>
                                    <?php if ($backup['compression_type'] !== 'none'): ?>
                                    <small class="text-muted">
                                        <i class="fas fa-compress me-1"></i>
                                        <?php echo strtoupper($backup['compression_type']); ?>
                                    </small>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </td>
                        <td>
                            <?php
                            $type_badges = [
                                'full' => 'bg-success',
                                'structure' => 'bg-info',
                                'data' => 'bg-warning',
                                'partial' => 'bg-secondary'
                            ];
                            $type_names = [
                                'full' => 'كاملة',
                                'structure' => 'هيكل',
                                'data' => 'بيانات',
                                'partial' => 'جزئية'
                            ];
                            ?>
                            <span class="badge <?php echo $type_badges[$backup['backup_type']] ?? 'bg-secondary'; ?>">
                                <?php echo $type_names[$backup['backup_type']] ?? $backup['backup_type']; ?>
                            </span>
                        </td>
                        <td>
                            <?php echo $backup['file_size'] ? formatFileSize($backup['file_size']) : '-'; ?>
                        </td>
                        <td>
                            <?php
                            $status_badges = [
                                'completed' => 'bg-success',
                                'in_progress' => 'bg-warning',
                                'failed' => 'bg-danger',
                                'cancelled' => 'bg-secondary'
                            ];
                            $status_names = [
                                'completed' => 'مكتملة',
                                'in_progress' => 'جاري الإنشاء',
                                'failed' => 'فاشلة',
                                'cancelled' => 'ملغية'
                            ];
                            ?>
                            <span class="badge <?php echo $status_badges[$backup['status']] ?? 'bg-secondary'; ?>">
                                <?php echo $status_names[$backup['status']] ?? $backup['status']; ?>
                            </span>

                            <?php if ($backup['status'] === 'in_progress' && $backup['progress_percentage'] > 0): ?>
                            <div class="progress mt-1" style="height: 4px;">
                                <div class="progress-bar" style="width: <?php echo $backup['progress_percentage']; ?>%"></div>
                            </div>
                            <?php endif; ?>

                            <?php if ($backup['status'] === 'failed' && $backup['error_message']): ?>
                            <div class="mt-1">
                                <small class="text-danger" title="<?php echo htmlspecialchars($backup['error_message']); ?>">
                                    <i class="fas fa-exclamation-triangle me-1"></i>
                                    خطأ
                                </small>
                            </div>
                            <?php endif; ?>
                        </td>
                        <td>
                            <small class="text-muted">
                                <?php echo date('Y-m-d H:i', strtotime($backup['created_at'])); ?>
                            </small>
                        </td>
                        <td>
                            <small class="text-muted">
                                <?php echo htmlspecialchars($backup['created_by_name'] ?? 'غير معروف'); ?>
                            </small>
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <?php if ($backup['status'] === 'completed' && file_exists($backup['file_path'])): ?>
                                <a href="download-backup.php?id=<?php echo $backup['id']; ?>"
                                   class="btn btn-sm btn-outline-primary" title="تحميل">
                                    <i class="fas fa-download"></i>
                                </a>

                                <button class="btn btn-sm btn-outline-success"
                                        onclick="restoreBackup(<?php echo $backup['id']; ?>)" title="استعادة">
                                    <i class="fas fa-upload"></i>
                                </button>
                                <?php endif; ?>

                                <form method="POST" class="d-inline">
                                    <input type="hidden" name="action" value="delete_backup">
                                    <input type="hidden" name="backup_id" value="<?php echo $backup['id']; ?>">
                                    <button type="submit" class="btn btn-sm btn-outline-danger"
                                            onclick="return confirm('هل أنت متأكد من حذف هذه النسخة الاحتياطية؟')" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php endif; ?>
    </div>
</div>

<script>
// إظهار/إخفاء اختيار الجداول
document.getElementById('backupType').addEventListener('change', function() {
    const tablesSelection = document.getElementById('tablesSelection');
    if (this.value === 'partial') {
        tablesSelection.style.display = 'block';
    } else {
        tablesSelection.style.display = 'none';
    }
});

// تحديد جميع الجداول
function selectAllTables() {
    document.querySelectorAll('input[name="tables[]"]').forEach(checkbox => {
        checkbox.checked = true;
    });
}

// إلغاء تحديد جميع الجداول
function deselectAllTables() {
    document.querySelectorAll('input[name="tables[]"]').forEach(checkbox => {
        checkbox.checked = false;
    });
}

// استعادة نسخة احتياطية
function restoreBackup(backupId) {
    Swal.fire({
        title: 'تأكيد الاستعادة',
        text: 'هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟ سيتم استبدال البيانات الحالية.',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dc2626',
        cancelButtonColor: '#64748b',
        confirmButtonText: 'نعم، استعد النسخة',
        cancelButtonText: 'إلغاء',
        reverseButtons: true
    }).then((result) => {
        if (result.isConfirmed) {
            showLoading();

            const formData = new FormData();
            formData.append('action', 'restore_backup');
            formData.append('backup_id', backupId);

            fetch('backup.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.text())
            .then(data => {
                hideLoading();
                location.reload();
            })
            .catch(error => {
                hideLoading();
                showError('حدث خطأ أثناء استعادة النسخة الاحتياطية');
            });
        }
    });
}

// تحديث قائمة النسخ الاحتياطية
function refreshBackupList() {
    location.reload();
}

// تنسيق حجم الملف
<?php
function formatFileSize($bytes) {
    if ($bytes == 0) return '0 B';

    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    $factor = floor((strlen($bytes) - 1) / 3);

    return sprintf("%.2f %s", $bytes / pow(1024, $factor), $units[$factor]);
}
?>
</script>

<?php include 'includes/footer.php'; ?>