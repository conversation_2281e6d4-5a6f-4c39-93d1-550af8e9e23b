<?php
/**
 * نتائج الاختبار للطالب
 * Quiz Results for Student
 */

require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات الطالب
if (!isLoggedIn() || !isStudent()) {
    header('Location: ../login.php');
    exit;
}

$student_id = $_SESSION['user_id'];
$quiz_id = $_GET['quiz_id'] ?? 0;

// جلب معلومات الاختبار والنتائج
try {
    $stmt = $conn->prepare("
        SELECT q.*, c.title as course_title, u.name as instructor_name,
               qa.id as attempt_id, qa.attempt_number, qa.score, qa.total_marks, 
               qa.time_taken, qa.completed_at, qa.status,
               CASE 
                   WHEN qa.score >= q.passing_grade THEN 'نجح'
                   WHEN qa.status = 'completed' THEN 'راسب'
                   ELSE 'لم يكمل'
               END as result_status,
               CASE 
                   WHEN qa.score >= q.passing_grade THEN 'success'
                   WHEN qa.status = 'completed' THEN 'danger'
                   ELSE 'warning'
               END as result_class
        FROM quizzes q
        JOIN courses c ON q.course_id = c.id
        JOIN course_enrollments ce ON c.id = ce.course_id
        JOIN users u ON c.instructor_id = u.id
        LEFT JOIN quiz_attempts qa ON q.id = qa.quiz_id AND qa.student_id = ?
        WHERE q.id = ? AND ce.student_id = ?
        ORDER BY qa.completed_at DESC
        LIMIT 1
    ");
    $stmt->execute([$student_id, $quiz_id, $student_id]);
    $quiz_result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$quiz_result) {
        header('Location: quizzes.php');
        exit;
    }
    
} catch (PDOException $e) {
    header('Location: quizzes.php');
    exit;
}

// جلب جميع محاولات الطالب لهذا الاختبار
try {
    $stmt = $conn->prepare("
        SELECT qa.*, 
               CASE 
                   WHEN qa.score >= ? THEN 'نجح'
                   WHEN qa.status = 'completed' THEN 'راسب'
                   ELSE 'لم يكمل'
               END as result_status,
               CASE 
                   WHEN qa.score >= ? THEN 'success'
                   WHEN qa.status = 'completed' THEN 'danger'
                   ELSE 'warning'
               END as result_class
        FROM quiz_attempts qa
        WHERE qa.quiz_id = ? AND qa.student_id = ?
        ORDER BY qa.attempt_number DESC
    ");
    $stmt->execute([$quiz_result['passing_grade'], $quiz_result['passing_grade'], $quiz_id, $student_id]);
    $all_attempts = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (PDOException $e) {
    $all_attempts = [];
}

// جلب تفاصيل الإجابات للمحاولة الأخيرة
$detailed_answers = [];
if ($quiz_result['attempt_id']) {
    try {
        $stmt = $conn->prepare("
            SELECT qq.question_text, qq.question_type, qq.points,
                   qa.answer_text, qa.selected_option_id, qa.is_correct, qa.points_earned,
                   qqo.option_text as selected_option_text,
                   correct_opt.option_text as correct_option_text
            FROM quiz_questions qq
            LEFT JOIN quiz_answers qa ON qq.id = qa.question_id AND qa.attempt_id = ?
            LEFT JOIN quiz_question_options qqo ON qa.selected_option_id = qqo.id
            LEFT JOIN quiz_question_options correct_opt ON qq.id = correct_opt.question_id AND correct_opt.is_correct = 1
            WHERE qq.quiz_id = ?
            ORDER BY qq.question_order ASC
        ");
        $stmt->execute([$quiz_result['attempt_id'], $quiz_id]);
        $detailed_answers = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
    } catch (PDOException $e) {
        $detailed_answers = [];
    }
}

$pageTitle = 'نتائج الاختبار - ' . $quiz_result['title'];
$breadcrumbs = [
    ['title' => 'الاختبارات', 'url' => 'quizzes.php'],
    ['title' => 'النتائج']
];

include 'includes/header.php';
?>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="h3 mb-2">
                <i class="fas fa-chart-bar text-primary me-2"></i>
                نتائج الاختبار
            </h2>
            <p class="text-muted mb-0">
                <strong><?php echo htmlspecialchars($quiz_result['title']); ?></strong> - 
                <?php echo htmlspecialchars($quiz_result['course_title']); ?>
            </p>
        </div>
        <div class="d-flex gap-2">
            <a href="quizzes.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-1"></i>العودة للاختبارات
            </a>
            <?php if ($quiz_result['status'] === 'completed' && $quiz_result['score'] >= $quiz_result['passing_grade']): ?>
            <button class="btn btn-success" onclick="generateCertificate()">
                <i class="fas fa-certificate me-1"></i>تحميل الشهادة
            </button>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- بطاقة النتيجة الرئيسية -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center py-5">
                    <div class="row align-items-center">
                        <div class="col-md-4">
                            <div class="result-score">
                                <?php if ($quiz_result['status'] === 'completed'): ?>
                                <h1 class="display-3 mb-2 text-<?php echo $quiz_result['result_class']; ?>">
                                    <?php echo number_format($quiz_result['score'], 1); ?>%
                                </h1>
                                <p class="text-muted mb-0">
                                    <?php echo $quiz_result['score']; ?> من <?php echo $quiz_result['total_marks']; ?> نقطة
                                </p>
                                <?php else: ?>
                                <h2 class="text-warning">لم يكتمل</h2>
                                <p class="text-muted">الاختبار لم يكتمل بعد</p>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="result-status">
                                <span class="badge bg-<?php echo $quiz_result['result_class']; ?> fs-4 px-4 py-2">
                                    <i class="fas fa-<?php echo $quiz_result['result_class'] === 'success' ? 'trophy' : ($quiz_result['result_class'] === 'danger' ? 'times' : 'clock'); ?> me-2"></i>
                                    <?php echo $quiz_result['result_status']; ?>
                                </span>
                                <p class="text-muted mt-2 mb-0">
                                    درجة النجاح: <?php echo $quiz_result['passing_grade']; ?>%
                                </p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="result-details">
                                <?php if ($quiz_result['time_taken']): ?>
                                <h5 class="text-info mb-2">
                                    <i class="fas fa-clock me-2"></i>
                                    <?php echo gmdate('H:i:s', $quiz_result['time_taken']); ?>
                                </h5>
                                <p class="text-muted mb-2">الوقت المستغرق</p>
                                <?php endif; ?>
                                
                                <?php if ($quiz_result['completed_at']): ?>
                                <small class="text-muted">
                                    اكتمل في: <?php echo date('Y/m/d H:i', strtotime($quiz_result['completed_at'])); ?>
                                </small>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- معلومات الاختبار -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        معلومات الاختبار
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-6">
                            <div class="text-center">
                                <h6 class="text-primary mb-1"><?php echo $quiz_result['passing_grade']; ?>%</h6>
                                <small class="text-muted">درجة النجاح</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <h6 class="text-warning mb-1"><?php echo $quiz_result['time_limit'] ?: 'غير محدود'; ?></h6>
                                <small class="text-muted">المدة (دقيقة)</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <h6 class="text-info mb-1"><?php echo $quiz_result['max_attempts']; ?></h6>
                                <small class="text-muted">المحاولات المسموحة</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <h6 class="text-success mb-1"><?php echo $quiz_result['total_marks']; ?></h6>
                                <small class="text-muted">إجمالي النقاط</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-primary text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-line me-2"></i>
                        إحصائيات المحاولات
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (!empty($all_attempts)): ?>
                    <div class="row g-3">
                        <div class="col-6">
                            <div class="text-center">
                                <h6 class="text-primary mb-1"><?php echo count($all_attempts); ?></h6>
                                <small class="text-muted">إجمالي المحاولات</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <h6 class="text-success mb-1">
                                    <?php echo number_format(max(array_column(array_filter($all_attempts, fn($a) => $a['status'] === 'completed'), 'score')), 1); ?>%
                                </h6>
                                <small class="text-muted">أفضل نتيجة</small>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="text-center">
                                <h6 class="text-info mb-1">
                                    <?php echo count(array_filter($all_attempts, fn($a) => $a['score'] >= $quiz_result['passing_grade'])); ?>
                                </h6>
                                <small class="text-muted">محاولات ناجحة</small>
                            </div>
                        </div>
                    </div>
                    <?php else: ?>
                    <p class="text-muted text-center mb-0">لا توجد محاولات</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    
    <!-- جدول جميع المحاولات -->
    <?php if (!empty($all_attempts)): ?>
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-header bg-white border-bottom">
            <h5 class="mb-0">
                <i class="fas fa-history me-2"></i>
                سجل المحاولات
            </h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>المحاولة</th>
                            <th>النتيجة</th>
                            <th>الوقت المستغرق</th>
                            <th>تاريخ الإكمال</th>
                            <th>الحالة</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($all_attempts as $attempt): ?>
                        <tr>
                            <td>
                                <span class="badge bg-primary">#<?php echo $attempt['attempt_number']; ?></span>
                            </td>
                            <td>
                                <?php if ($attempt['status'] === 'completed'): ?>
                                <div>
                                    <strong><?php echo number_format($attempt['score'], 1); ?>%</strong>
                                    <small class="text-muted d-block"><?php echo $attempt['score']; ?> / <?php echo $attempt['total_marks']; ?></small>
                                </div>
                                <?php else: ?>
                                <span class="text-muted">لم يكمل</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($attempt['time_taken']): ?>
                                <span class="text-info">
                                    <i class="fas fa-clock me-1"></i>
                                    <?php echo gmdate('H:i:s', $attempt['time_taken']); ?>
                                </span>
                                <?php else: ?>
                                <span class="text-muted">-</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($attempt['completed_at']): ?>
                                <small class="text-muted">
                                    <?php echo date('Y/m/d H:i', strtotime($attempt['completed_at'])); ?>
                                </small>
                                <?php else: ?>
                                <small class="text-muted">
                                    بدأ: <?php echo date('Y/m/d H:i', strtotime($attempt['started_at'])); ?>
                                </small>
                                <?php endif; ?>
                            </td>
                            <td>
                                <span class="badge bg-<?php echo $attempt['result_class']; ?>">
                                    <?php echo $attempt['result_status']; ?>
                                </span>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <?php endif; ?>
    
    <!-- تفاصيل الإجابات -->
    <?php if ($quiz_result['show_results'] && !empty($detailed_answers)): ?>
    <div class="card border-0 shadow-sm">
        <div class="card-header bg-white border-bottom">
            <h5 class="mb-0">
                <i class="fas fa-list-check me-2"></i>
                تفاصيل الإجابات
            </h5>
        </div>
        <div class="card-body">
            <?php foreach ($detailed_answers as $index => $answer): ?>
            <div class="question-review mb-4 p-3 border rounded">
                <div class="d-flex justify-content-between align-items-start mb-3">
                    <h6 class="mb-0">السؤال <?php echo $index + 1; ?></h6>
                    <div class="text-end">
                        <span class="badge bg-<?php echo $answer['is_correct'] ? 'success' : 'danger'; ?>">
                            <?php echo $answer['points_earned'] ?? 0; ?> / <?php echo $answer['points']; ?> نقطة
                        </span>
                    </div>
                </div>
                
                <p class="mb-3"><?php echo nl2br(htmlspecialchars($answer['question_text'])); ?></p>
                
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary">إجابتك:</h6>
                        <?php if ($answer['question_type'] === 'multiple_choice'): ?>
                            <p class="text-<?php echo $answer['is_correct'] ? 'success' : 'danger'; ?>">
                                <?php echo htmlspecialchars($answer['selected_option_text'] ?? 'لم يتم الإجابة'); ?>
                            </p>
                        <?php else: ?>
                            <p class="text-muted">
                                <?php echo htmlspecialchars($answer['answer_text'] ?? 'لم يتم الإجابة'); ?>
                            </p>
                        <?php endif; ?>
                    </div>
                    
                    <?php if ($answer['question_type'] === 'multiple_choice' && $answer['correct_option_text']): ?>
                    <div class="col-md-6">
                        <h6 class="text-success">الإجابة الصحيحة:</h6>
                        <p class="text-success">
                            <?php echo htmlspecialchars($answer['correct_option_text']); ?>
                        </p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
    <?php endif; ?>
</div>

<script>
function generateCertificate() {
    window.open(`generate-certificate.php?attempt_id=<?php echo $quiz_result['attempt_id']; ?>`, '_blank');
}
</script>

<style>
.result-score h1 {
    font-weight: 700;
}

.question-review {
    transition: all 0.3s ease;
}

.question-review:hover {
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.badge {
    font-size: 0.875rem;
}

@media (max-width: 768px) {
    .result-score h1 {
        font-size: 2.5rem;
    }
    
    .card-body.py-5 {
        padding: 2rem 1rem !important;
    }
}
</style>

<?php include 'includes/footer.php'; ?>
