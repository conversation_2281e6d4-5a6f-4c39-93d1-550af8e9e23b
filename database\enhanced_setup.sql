-- ===== إعداد قاعدة البيانات المحسنة لنظام التعلم عن بعد =====
-- Enhanced Database Setup for Learning Management System

-- إنشاء قاعدة البيانات
CREATE DATABASE IF NOT EXISTS zoom_learning_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE zoom_learning_system;

-- ===== جدول المستخدمين المحسن =====
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL UNIQUE,
    username VARCHAR(50) UNIQUE,
    phone VARCHAR(20),
    password VARCHAR(255) NOT NULL,
    role ENUM('admin', 'instructor', 'student') NOT NULL DEFAULT 'student',
    status ENUM('active', 'inactive', 'pending', 'suspended') DEFAULT 'pending',
    profile_image VARCHAR(255) DEFAULT NULL,
    bio TEXT DEFAULT NULL,
    date_of_birth DATE DEFAULT NULL,
    gender ENUM('male', 'female') DEFAULT NULL,
    country VARCHAR(100) DEFAULT NULL,
    city VARCHAR(100) DEFAULT NULL,
    timezone VARCHAR(50) DEFAULT 'Asia/Riyadh',
    language ENUM('ar', 'en') DEFAULT 'ar',
    email_verified BOOLEAN DEFAULT FALSE,
    email_verification_token VARCHAR(255) DEFAULT NULL,
    password_reset_token VARCHAR(255) DEFAULT NULL,
    password_reset_expires DATETIME DEFAULT NULL,
    failed_attempts INT DEFAULT 0,
    lock_expires DATETIME DEFAULT NULL,
    last_login TIMESTAMP NULL,
    last_activity TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_email (email),
    INDEX idx_username (username),
    INDEX idx_role (role),
    INDEX idx_status (status),
    INDEX idx_last_activity (last_activity)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ===== جدول الكورسات المحسن =====
CREATE TABLE IF NOT EXISTS courses (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE,
    description TEXT,
    short_description VARCHAR(500),
    instructor_id INT,
    category_id INT DEFAULT NULL,
    level ENUM('beginner', 'intermediate', 'advanced') DEFAULT 'beginner',
    duration_hours INT DEFAULT 0,
    max_students INT DEFAULT 50,
    price DECIMAL(10,2) DEFAULT 0.00,
    currency VARCHAR(3) DEFAULT 'SAR',
    thumbnail VARCHAR(255) DEFAULT NULL,
    video_preview VARCHAR(255) DEFAULT NULL,
    requirements TEXT DEFAULT NULL,
    objectives TEXT DEFAULT NULL,
    target_audience TEXT DEFAULT NULL,
    certificate_template VARCHAR(255) DEFAULT NULL,
    status ENUM('draft', 'active', 'inactive', 'completed') DEFAULT 'draft',
    featured BOOLEAN DEFAULT FALSE,
    enrollment_start DATETIME DEFAULT NULL,
    enrollment_end DATETIME DEFAULT NULL,
    course_start DATETIME DEFAULT NULL,
    course_end DATETIME DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (instructor_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_instructor (instructor_id),
    INDEX idx_status (status),
    INDEX idx_featured (featured),
    INDEX idx_slug (slug)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ===== جدول فئات الكورسات =====
CREATE TABLE IF NOT EXISTS course_categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE,
    description TEXT,
    icon VARCHAR(100) DEFAULT NULL,
    color VARCHAR(7) DEFAULT '#2196F3',
    parent_id INT DEFAULT NULL,
    sort_order INT DEFAULT 0,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (parent_id) REFERENCES course_categories(id) ON DELETE SET NULL,
    INDEX idx_parent (parent_id),
    INDEX idx_status (status),
    INDEX idx_sort (sort_order)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ===== جدول الجلسات المحسن =====
CREATE TABLE IF NOT EXISTS sessions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    course_id INT,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    session_number INT DEFAULT 1,
    start_time DATETIME NOT NULL,
    end_time DATETIME NOT NULL,
    duration_minutes INT NOT NULL,
    timezone VARCHAR(50) DEFAULT 'Asia/Riyadh',
    zoom_meeting_id VARCHAR(255),
    zoom_meeting_password VARCHAR(255),
    zoom_join_url TEXT,
    zoom_start_url TEXT,
    meeting_type ENUM('zoom', 'teams', 'meet', 'other') DEFAULT 'zoom',
    recording_url VARCHAR(500) DEFAULT NULL,
    recording_password VARCHAR(100) DEFAULT NULL,
    materials TEXT DEFAULT NULL,
    homework TEXT DEFAULT NULL,
    max_attendees INT DEFAULT NULL,
    status ENUM('scheduled', 'live', 'completed', 'cancelled', 'postponed') DEFAULT 'scheduled',
    reminder_sent BOOLEAN DEFAULT FALSE,
    attendance_required BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    INDEX idx_course (course_id),
    INDEX idx_start_time (start_time),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ===== جدول تسجيل الطلاب في الكورسات =====
CREATE TABLE IF NOT EXISTS course_enrollments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    course_id INT,
    student_id INT,
    enrollment_type ENUM('free', 'paid', 'scholarship') DEFAULT 'free',
    payment_status ENUM('pending', 'completed', 'failed', 'refunded') DEFAULT 'pending',
    payment_amount DECIMAL(10,2) DEFAULT 0.00,
    payment_method VARCHAR(50) DEFAULT NULL,
    payment_reference VARCHAR(255) DEFAULT NULL,
    progress_percentage DECIMAL(5,2) DEFAULT 0.00,
    completion_date DATETIME DEFAULT NULL,
    certificate_issued BOOLEAN DEFAULT FALSE,
    certificate_url VARCHAR(500) DEFAULT NULL,
    status ENUM('active', 'completed', 'dropped', 'suspended') DEFAULT 'active',
    enrolled_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_enrollment (course_id, student_id),
    INDEX idx_student (student_id),
    INDEX idx_status (status),
    INDEX idx_payment_status (payment_status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ===== جدول حضور الجلسات المحسن =====
CREATE TABLE IF NOT EXISTS session_attendees (
    id INT AUTO_INCREMENT PRIMARY KEY,
    session_id INT,
    user_id INT,
    join_time TIMESTAMP NULL,
    leave_time TIMESTAMP NULL,
    duration_minutes INT DEFAULT 0,
    attendance_percentage DECIMAL(5,2) DEFAULT 0.00,
    status ENUM('present', 'absent', 'late', 'excused') DEFAULT 'absent',
    ip_address VARCHAR(45) DEFAULT NULL,
    user_agent TEXT DEFAULT NULL,
    device_info VARCHAR(255) DEFAULT NULL,
    notes TEXT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (session_id) REFERENCES sessions(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_attendance (session_id, user_id),
    INDEX idx_session (session_id),
    INDEX idx_user (user_id),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ===== جدول سجل الأنشطة المحسن =====
CREATE TABLE IF NOT EXISTS activity_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    action VARCHAR(255) NOT NULL,
    entity_type VARCHAR(100) DEFAULT NULL,
    entity_id INT DEFAULT NULL,
    details TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    session_id VARCHAR(255) DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user (user_id),
    INDEX idx_action (action),
    INDEX idx_entity (entity_type, entity_id),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ===== جدول الإشعارات =====
CREATE TABLE IF NOT EXISTS notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    type ENUM('info', 'success', 'warning', 'error') DEFAULT 'info',
    category ENUM('system', 'course', 'session', 'payment', 'general') DEFAULT 'general',
    action_url VARCHAR(500) DEFAULT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    read_at TIMESTAMP NULL,
    expires_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user (user_id),
    INDEX idx_read (is_read),
    INDEX idx_type (type),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ===== جدول محاولات تسجيل الدخول =====
CREATE TABLE IF NOT EXISTS login_attempts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(255) NOT NULL,
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT,
    success BOOLEAN DEFAULT FALSE,
    failure_reason VARCHAR(255) DEFAULT NULL,
    attempt_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_email (email),
    INDEX idx_ip (ip_address),
    INDEX idx_time (attempt_time),
    INDEX idx_success (success)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ===== جدول حظر عناوين IP =====
CREATE TABLE IF NOT EXISTS blocked_ips (
    id INT AUTO_INCREMENT PRIMARY KEY,
    ip_address VARCHAR(45) NOT NULL UNIQUE,
    reason VARCHAR(255) DEFAULT NULL,
    blocked_until DATETIME NOT NULL,
    blocked_by INT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (blocked_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_ip (ip_address),
    INDEX idx_blocked_until (blocked_until)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ===== جدول العلاقة بين المدربين والطلاب =====
CREATE TABLE IF NOT EXISTS instructor_students (
    id INT AUTO_INCREMENT PRIMARY KEY,
    instructor_id INT NOT NULL,
    student_id INT NOT NULL,
    status ENUM('pending', 'active', 'inactive', 'blocked') DEFAULT 'pending',
    approved_by INT DEFAULT NULL,
    approved_at TIMESTAMP NULL,
    notes TEXT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (instructor_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (approved_by) REFERENCES users(id) ON DELETE SET NULL,
    UNIQUE KEY unique_instructor_student (instructor_id, student_id),
    INDEX idx_instructor (instructor_id),
    INDEX idx_student (student_id),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ===== جدول إعدادات النظام =====
CREATE TABLE IF NOT EXISTS system_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(255) NOT NULL UNIQUE,
    setting_value TEXT,
    setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    description TEXT DEFAULT NULL,
    is_public BOOLEAN DEFAULT FALSE,
    updated_by INT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_key (setting_key),
    INDEX idx_public (is_public)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ===== إدراج البيانات الأولية =====

-- إنشاء المستخدم الافتراضي للمدير
INSERT INTO users (name, email, username, password, role, status, email_verified, created_at)
VALUES (
    'مدير النظام',
    '<EMAIL>',
    'admin',
    '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: password
    'admin',
    'active',
    TRUE,
    NOW()
) ON DUPLICATE KEY UPDATE id=id;

-- إنشاء مستخدم مدرب تجريبي
INSERT INTO users (name, email, username, password, role, status, email_verified, created_at)
VALUES (
    'أحمد محمد - مدرب',
    '<EMAIL>',
    'instructor_demo',
    '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: password
    'instructor',
    'active',
    TRUE,
    NOW()
) ON DUPLICATE KEY UPDATE id=id;

-- إنشاء مستخدم طالب تجريبي
INSERT INTO users (name, email, username, password, role, status, email_verified, created_at)
VALUES (
    'سارة أحمد - طالبة',
    '<EMAIL>',
    'student_demo',
    '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: password
    'student',
    'active',
    TRUE,
    NOW()
) ON DUPLICATE KEY UPDATE id=id;

-- إدراج فئات الكورسات الأساسية
INSERT INTO course_categories (name, slug, description, icon, color, sort_order) VALUES
('البرمجة وتطوير المواقع', 'programming-web-development', 'دورات في البرمجة وتطوير المواقع والتطبيقات', 'fas fa-code', '#2196F3', 1),
('التصميم والجرافيك', 'design-graphics', 'دورات في التصميم الجرافيكي وتصميم الواجهات', 'fas fa-paint-brush', '#E91E63', 2),
('التسويق الرقمي', 'digital-marketing', 'دورات في التسويق الإلكتروني ووسائل التواصل الاجتماعي', 'fas fa-bullhorn', '#FF9800', 3),
('إدارة الأعمال', 'business-management', 'دورات في إدارة الأعمال والقيادة', 'fas fa-briefcase', '#4CAF50', 4),
('اللغات', 'languages', 'دورات تعلم اللغات المختلفة', 'fas fa-language', '#9C27B0', 5);

-- إدراج كورس تجريبي
INSERT INTO courses (title, slug, description, short_description, instructor_id, category_id, level, duration_hours, max_students, status, featured)
VALUES (
    'مقدمة في تطوير المواقع',
    'intro-web-development',
    'دورة شاملة لتعلم أساسيات تطوير المواقع باستخدام HTML, CSS, و JavaScript',
    'تعلم أساسيات تطوير المواقع من الصفر',
    2, -- instructor_demo user id
    1, -- programming category
    'beginner',
    40,
    30,
    'active',
    TRUE
);

-- إدراج جلسة تجريبية
INSERT INTO sessions (course_id, title, description, session_number, start_time, end_time, duration_minutes, status)
VALUES (
    1,
    'الجلسة الأولى: مقدمة في HTML',
    'في هذه الجلسة سنتعلم أساسيات لغة HTML وكيفية إنشاء صفحات الويب',
    1,
    DATE_ADD(NOW(), INTERVAL 1 DAY),
    DATE_ADD(DATE_ADD(NOW(), INTERVAL 1 DAY), INTERVAL 2 HOUR),
    120,
    'scheduled'
);

-- إدراج إعدادات النظام الأساسية
INSERT INTO system_settings (setting_key, setting_value, setting_type, description, is_public) VALUES
('site_name', 'نظام التعلم عن بعد', 'string', 'اسم الموقع', TRUE),
('site_description', 'منصة تعليمية متطورة لإدارة الدورات والجلسات التعليمية عن بعد', 'string', 'وصف الموقع', TRUE),
('default_timezone', 'Asia/Riyadh', 'string', 'المنطقة الزمنية الافتراضية', FALSE),
('max_login_attempts', '5', 'number', 'الحد الأقصى لمحاولات تسجيل الدخول', FALSE),
('session_timeout', '30', 'number', 'مهلة انتهاء الجلسة بالدقائق', FALSE),
('email_notifications', 'true', 'boolean', 'تفعيل الإشعارات عبر البريد الإلكتروني', FALSE),
('auto_approve_students', 'false', 'boolean', 'الموافقة التلقائية على الطلاب الجدد', FALSE),
('maintenance_mode', 'false', 'boolean', 'وضع الصيانة', FALSE);

-- إنشاء إشعار ترحيبي للمدير
INSERT INTO notifications (user_id, title, message, type, category)
VALUES (
    1,
    'مرحباً بك في نظام التعلم عن بعد',
    'تم إعداد النظام بنجاح. يمكنك الآن البدء في إضافة المدربين والكورسات وإدارة النظام.',
    'success',
    'system'
);

-- ===== إنشاء المؤشرات الإضافية لتحسين الأداء =====
CREATE INDEX idx_users_role_status ON users(role, status);
CREATE INDEX idx_courses_instructor_status ON courses(instructor_id, status);
CREATE INDEX idx_sessions_course_start ON sessions(course_id, start_time);
CREATE INDEX idx_enrollments_student_status ON course_enrollments(student_id, status);
CREATE INDEX idx_attendance_session_status ON session_attendees(session_id, status);

-- ===== جدول الواجبات =====
CREATE TABLE IF NOT EXISTS assignments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    course_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    instructions TEXT,
    due_date DATETIME NOT NULL,
    max_score DECIMAL(5,2) DEFAULT 100.00,
    file_path VARCHAR(500) DEFAULT NULL,
    submission_type ENUM('file', 'text', 'both') DEFAULT 'both',
    status ENUM('draft', 'published', 'closed') DEFAULT 'draft',
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_course (course_id),
    INDEX idx_due_date (due_date),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ===== جدول تسليم الواجبات =====
CREATE TABLE IF NOT EXISTS assignment_submissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    assignment_id INT NOT NULL,
    student_id INT NOT NULL,
    submission_text TEXT DEFAULT NULL,
    file_path VARCHAR(500) DEFAULT NULL,
    submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    score DECIMAL(5,2) DEFAULT NULL,
    feedback TEXT DEFAULT NULL,
    graded_at TIMESTAMP NULL,
    graded_by INT DEFAULT NULL,
    status ENUM('submitted', 'graded', 'late') DEFAULT 'submitted',

    FOREIGN KEY (assignment_id) REFERENCES assignments(id) ON DELETE CASCADE,
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (graded_by) REFERENCES users(id) ON DELETE SET NULL,
    UNIQUE KEY unique_submission (assignment_id, student_id),
    INDEX idx_assignment (assignment_id),
    INDEX idx_student (student_id),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ===== جدول الاختبارات =====
CREATE TABLE IF NOT EXISTS quizzes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    course_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    instructions TEXT,
    time_limit INT DEFAULT NULL, -- بالدقائق
    max_attempts INT DEFAULT 1,
    passing_score DECIMAL(5,2) DEFAULT 60.00,
    total_marks DECIMAL(5,2) DEFAULT 100.00,
    start_date DATETIME DEFAULT NULL,
    end_date DATETIME DEFAULT NULL,
    shuffle_questions BOOLEAN DEFAULT TRUE,
    show_results BOOLEAN DEFAULT TRUE,
    status ENUM('draft', 'published', 'closed') DEFAULT 'draft',
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_course (course_id),
    INDEX idx_status (status),
    INDEX idx_dates (start_date, end_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ===== جدول أسئلة الاختبارات =====
CREATE TABLE IF NOT EXISTS quiz_questions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    quiz_id INT NOT NULL,
    question_text TEXT NOT NULL,
    question_type ENUM('multiple_choice', 'true_false', 'short_answer', 'essay') DEFAULT 'multiple_choice',
    points DECIMAL(5,2) DEFAULT 1.00,
    correct_answer TEXT DEFAULT NULL,
    explanation TEXT DEFAULT NULL,
    question_order INT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (quiz_id) REFERENCES quizzes(id) ON DELETE CASCADE,
    INDEX idx_quiz (quiz_id),
    INDEX idx_order (question_order)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ===== جدول خيارات الأسئلة =====
CREATE TABLE IF NOT EXISTS quiz_question_options (
    id INT AUTO_INCREMENT PRIMARY KEY,
    question_id INT NOT NULL,
    option_text TEXT NOT NULL,
    is_correct BOOLEAN DEFAULT FALSE,
    option_order INT DEFAULT 1,

    FOREIGN KEY (question_id) REFERENCES quiz_questions(id) ON DELETE CASCADE,
    INDEX idx_question (question_id),
    INDEX idx_order (option_order)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ===== جدول محاولات الاختبارات =====
CREATE TABLE IF NOT EXISTS quiz_attempts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    quiz_id INT NOT NULL,
    student_id INT NOT NULL,
    attempt_number INT DEFAULT 1,
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL,
    score DECIMAL(5,2) DEFAULT NULL,
    total_marks DECIMAL(5,2) DEFAULT NULL,
    time_taken INT DEFAULT NULL, -- بالثواني
    status ENUM('in_progress', 'completed', 'abandoned') DEFAULT 'in_progress',

    FOREIGN KEY (quiz_id) REFERENCES quizzes(id) ON DELETE CASCADE,
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_quiz (quiz_id),
    INDEX idx_student (student_id),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ===== جدول إجابات الاختبارات =====
CREATE TABLE IF NOT EXISTS quiz_answers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    attempt_id INT NOT NULL,
    question_id INT NOT NULL,
    answer_text TEXT DEFAULT NULL,
    selected_option_id INT DEFAULT NULL,
    is_correct BOOLEAN DEFAULT NULL,
    points_earned DECIMAL(5,2) DEFAULT 0.00,
    answered_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (attempt_id) REFERENCES quiz_attempts(id) ON DELETE CASCADE,
    FOREIGN KEY (question_id) REFERENCES quiz_questions(id) ON DELETE CASCADE,
    FOREIGN KEY (selected_option_id) REFERENCES quiz_question_options(id) ON DELETE SET NULL,
    UNIQUE KEY unique_answer (attempt_id, question_id),
    INDEX idx_attempt (attempt_id),
    INDEX idx_question (question_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ===== جدول الشهادات =====
CREATE TABLE IF NOT EXISTS certificates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    student_id INT NOT NULL,
    course_id INT NOT NULL,
    certificate_number VARCHAR(100) UNIQUE NOT NULL,
    issue_date DATE NOT NULL,
    completion_date DATE NOT NULL,
    grade DECIMAL(5,2) DEFAULT NULL,
    certificate_url VARCHAR(500) DEFAULT NULL,
    template_used VARCHAR(255) DEFAULT 'default',
    verification_code VARCHAR(50) UNIQUE NOT NULL,
    status ENUM('active', 'revoked', 'expired') DEFAULT 'active',
    issued_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    FOREIGN KEY (issued_by) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_student (student_id),
    INDEX idx_course (course_id),
    INDEX idx_verification (verification_code),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ===== جدول المكتبة =====
CREATE TABLE IF NOT EXISTS library_resources (
    id INT AUTO_INCREMENT PRIMARY KEY,
    course_id INT DEFAULT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    resource_type ENUM('pdf', 'video', 'audio', 'document', 'link', 'image') NOT NULL,
    file_path VARCHAR(500) DEFAULT NULL,
    file_size BIGINT DEFAULT NULL, -- بالبايت
    external_url VARCHAR(500) DEFAULT NULL,
    category VARCHAR(100) DEFAULT NULL,
    tags TEXT DEFAULT NULL,
    access_level ENUM('public', 'enrolled', 'premium') DEFAULT 'enrolled',
    download_count INT DEFAULT 0,
    view_count INT DEFAULT 0,
    uploaded_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE SET NULL,
    FOREIGN KEY (uploaded_by) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_course (course_id),
    INDEX idx_type (resource_type),
    INDEX idx_category (category),
    INDEX idx_access (access_level)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ===== جدول المنتديات =====
CREATE TABLE IF NOT EXISTS forums (
    id INT AUTO_INCREMENT PRIMARY KEY,
    course_id INT DEFAULT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100) DEFAULT 'general',
    is_public BOOLEAN DEFAULT FALSE,
    post_count INT DEFAULT 0,
    last_post_id INT DEFAULT NULL,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_course (course_id),
    INDEX idx_category (category),
    INDEX idx_public (is_public),
    INDEX idx_activity (last_activity)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ===== جدول مشاركات المنتدى =====
CREATE TABLE IF NOT EXISTS forum_posts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    forum_id INT NOT NULL,
    parent_id INT DEFAULT NULL, -- للردود
    title VARCHAR(255) DEFAULT NULL,
    content TEXT NOT NULL,
    author_id INT NOT NULL,
    reply_count INT DEFAULT 0,
    view_count INT DEFAULT 0,
    is_pinned BOOLEAN DEFAULT FALSE,
    is_locked BOOLEAN DEFAULT FALSE,
    last_reply_id INT DEFAULT NULL,
    last_reply_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (forum_id) REFERENCES forums(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES forum_posts(id) ON DELETE CASCADE,
    FOREIGN KEY (author_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_forum (forum_id),
    INDEX idx_parent (parent_id),
    INDEX idx_author (author_id),
    INDEX idx_pinned (is_pinned),
    INDEX idx_created (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ===== جدول الرسائل =====
CREATE TABLE IF NOT EXISTS messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    sender_id INT NOT NULL,
    receiver_id INT NOT NULL,
    subject VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    read_at TIMESTAMP NULL,
    parent_id INT DEFAULT NULL, -- للردود
    attachment_path VARCHAR(500) DEFAULT NULL,
    priority ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal',
    message_type ENUM('personal', 'system', 'notification') DEFAULT 'personal',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (receiver_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES messages(id) ON DELETE SET NULL,
    INDEX idx_sender (sender_id),
    INDEX idx_receiver (receiver_id),
    INDEX idx_read (is_read),
    INDEX idx_type (message_type),
    INDEX idx_created (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ===== جدول إعدادات الإشعارات =====
CREATE TABLE IF NOT EXISTS user_notification_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    email_notifications BOOLEAN DEFAULT TRUE,
    sms_notifications BOOLEAN DEFAULT FALSE,
    push_notifications BOOLEAN DEFAULT TRUE,
    course_updates BOOLEAN DEFAULT TRUE,
    assignment_reminders BOOLEAN DEFAULT TRUE,
    quiz_reminders BOOLEAN DEFAULT TRUE,
    session_reminders BOOLEAN DEFAULT TRUE,
    marketing_emails BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_notifications (user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ===== جدول إعدادات الخصوصية =====
CREATE TABLE IF NOT EXISTS user_privacy_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    profile_visibility ENUM('public', 'students', 'private') DEFAULT 'public',
    show_progress BOOLEAN DEFAULT TRUE,
    show_certificates BOOLEAN DEFAULT TRUE,
    allow_messages BOOLEAN DEFAULT TRUE,
    show_online_status BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_privacy (user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ===== إنشاء Views مفيدة =====

-- عرض إحصائيات المستخدمين
CREATE OR REPLACE VIEW user_stats AS
SELECT
    role,
    status,
    COUNT(*) as count,
    COUNT(CASE WHEN last_login >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as active_last_30_days
FROM users
GROUP BY role, status;

-- عرض إحصائيات الكورسات
CREATE OR REPLACE VIEW course_stats AS
SELECT
    c.id,
    c.title,
    c.status,
    u.name as instructor_name,
    COUNT(DISTINCT ce.student_id) as enrolled_students,
    COUNT(DISTINCT s.id) as total_sessions,
    AVG(ce.progress_percentage) as avg_progress
FROM courses c
LEFT JOIN users u ON c.instructor_id = u.id
LEFT JOIN course_enrollments ce ON c.id = ce.course_id
LEFT JOIN sessions s ON c.id = s.course_id
GROUP BY c.id;

-- عرض الجلسات القادمة
CREATE OR REPLACE VIEW upcoming_sessions AS
SELECT
    s.id,
    s.title,
    s.start_time,
    s.duration_minutes,
    c.title as course_title,
    u.name as instructor_name,
    COUNT(DISTINCT ce.student_id) as enrolled_students
FROM sessions s
JOIN courses c ON s.course_id = c.id
JOIN users u ON c.instructor_id = u.id
LEFT JOIN course_enrollments ce ON c.id = ce.course_id AND ce.status = 'active'
WHERE s.start_time > NOW() AND s.status = 'scheduled'
GROUP BY s.id
ORDER BY s.start_time ASC;
