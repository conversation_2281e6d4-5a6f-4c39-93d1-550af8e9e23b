-- إنشاء جدول المستخدمين
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    role ENUM('student', 'instructor', 'admin') DEFAULT 'student',
    phone VARCHAR(20),
    profile_image VARCHAR(500),
    bio TEXT,
    specialization VARCHAR(255),
    level ENUM('beginner', 'intermediate', 'advanced', 'expert') DEFAULT 'beginner',
    status ENUM('active', 'inactive', 'pending') DEFAULT 'active',
    email_verified TINYINT(1) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- <PERSON><PERSON><PERSON><PERSON><PERSON> جدول الكورسات
CREATE TABLE IF NOT EXISTS courses (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    instructor_id INT NOT NULL,
    category VARCHAR(100),
    level ENUM('beginner', 'intermediate', 'advanced') DEFAULT 'beginner',
    duration_hours INT DEFAULT 0,
    price DECIMAL(10,2) DEFAULT 0.00,
    is_free TINYINT(1) DEFAULT 1,
    max_students INT DEFAULT 0,
    status ENUM('active', 'inactive', 'draft') DEFAULT 'active',
    image VARCHAR(500),
    requirements TEXT,
    objectives TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- إنشاء جدول التسجيل في الكورسات
CREATE TABLE IF NOT EXISTS course_enrollments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    course_id INT NOT NULL,
    student_id INT NOT NULL,
    enrollment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status ENUM('active', 'completed', 'dropped', 'pending') DEFAULT 'active',
    progress DECIMAL(5,2) DEFAULT 0.00,
    completion_date TIMESTAMP NULL,
    UNIQUE KEY unique_enrollment (course_id, student_id)
);

-- إنشاء جدول الجلسات
CREATE TABLE IF NOT EXISTS sessions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    course_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    session_date DATETIME NOT NULL,
    duration_minutes INT DEFAULT 60,
    meeting_link VARCHAR(500),
    recording_link VARCHAR(500),
    status ENUM('scheduled', 'live', 'completed', 'cancelled') DEFAULT 'scheduled',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- إنشاء جدول الدرجات
CREATE TABLE IF NOT EXISTS student_grades (
    id INT AUTO_INCREMENT PRIMARY KEY,
    course_id INT NOT NULL,
    student_id INT NOT NULL,
    assignment_name VARCHAR(255) NOT NULL,
    grade DECIMAL(5,2) NOT NULL,
    max_grade DECIMAL(5,2) DEFAULT 100.00,
    notes TEXT,
    grade_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- إدراج مستخدم مدرب تجريبي
INSERT IGNORE INTO users (id, name, email, password, role, specialization, level, status, email_verified) VALUES
(1, 'أحمد محمد', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'instructor', 'البرمجة وتطوير الويب', 'expert', 'active', 1);

-- إدراج طلاب تجريبيين
INSERT IGNORE INTO users (id, name, email, password, role, status, email_verified) VALUES
(2, 'سارة أحمد', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'student', 'active', 1),
(3, 'محمد علي', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'student', 'active', 1),
(4, 'فاطمة حسن', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'student', 'active', 1);

-- إدراج كورس تجريبي
INSERT IGNORE INTO courses (id, title, description, instructor_id, category, level, duration_hours, price, is_free, max_students, status) VALUES
(39, 'مقدمة في البرمجة', 'كورس شامل لتعلم أساسيات البرمجة باستخدام Python', 1, 'البرمجة', 'beginner', 40, 0.00, 1, 50, 'active');

-- إدراج تسجيلات الطلاب في الكورس
INSERT IGNORE INTO course_enrollments (course_id, student_id, status, progress) VALUES
(39, 2, 'active', 25.50),
(39, 3, 'active', 45.75),
(39, 4, 'active', 15.25);

-- إدراج جلسات تجريبية
INSERT IGNORE INTO sessions (course_id, title, description, session_date, duration_minutes, status) VALUES
(39, 'الجلسة الأولى - مقدمة', 'مقدمة عن البرمجة وأساسياتها', DATE_ADD(NOW(), INTERVAL 1 DAY), 90, 'scheduled'),
(39, 'الجلسة الثانية - المتغيرات', 'تعلم استخدام المتغيرات في Python', DATE_ADD(NOW(), INTERVAL 3 DAY), 90, 'scheduled');

-- إدراج درجات تجريبية
INSERT IGNORE INTO student_grades (course_id, student_id, assignment_name, grade, max_grade, notes) VALUES
(39, 2, 'الواجب الأول', 85.50, 100.00, 'عمل جيد، يحتاج تحسين في التعليقات'),
(39, 3, 'الواجب الأول', 92.00, 100.00, 'عمل ممتاز ومنظم'),
(39, 2, 'اختبار منتصف الفصل', 78.25, 100.00, 'أداء جيد'),
(39, 3, 'اختبار منتصف الفصل', 88.75, 100.00, 'أداء ممتاز');
