<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات المدرب
if (!isLoggedIn() || !isInstructor()) {
    header('Location: ../login.php');
    exit;
}

$student_id = $_GET['student_id'] ?? '';
$course_id = $_GET['course_id'] ?? '';

if (!$student_id || !$course_id) {
    header('Location: student-progress.php');
    exit;
}

// جلب بيانات الطالب والكورس
try {
    $stmt = $conn->prepare("
        SELECT 
            u.*,
            c.title as course_title,
            c.description as course_description,
            ce.enrolled_at,
            ce.progress_percentage,
            ce.status as enrollment_status,
            instructor.name as instructor_name
        FROM users u
        INNER JOIN course_enrollments ce ON u.id = ce.student_id
        INNER JOIN courses c ON ce.course_id = c.id
        INNER JOIN users instructor ON c.instructor_id = instructor.id
        WHERE u.id = ? AND c.id = ? AND c.instructor_id = ?
    ");
    $stmt->execute([$student_id, $course_id, $_SESSION['user_id']]);
    $student_data = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$student_data) {
        header('Location: student-progress.php');
        exit;
    }
    
    // جلب بيانات الحضور
    $stmt = $conn->prepare("
        SELECT 
            s.title as session_title,
            s.session_date,
            s.start_time,
            s.duration_minutes,
            CASE WHEN sa.user_id IS NOT NULL THEN 1 ELSE 0 END as attended
        FROM sessions s
        LEFT JOIN session_attendees sa ON s.id = sa.session_id AND sa.user_id = ?
        WHERE s.course_id = ?
        ORDER BY s.session_date ASC
    ");
    $stmt->execute([$student_id, $course_id]);
    $attendance_data = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // جلب بيانات الدرجات
    $stmt = $conn->prepare("
        SELECT 
            assignment_name,
            grade,
            max_grade,
            grade_date,
            notes
        FROM student_grades
        WHERE student_id = ? AND course_id = ?
        ORDER BY grade_date ASC
    ");
    $stmt->execute([$student_id, $course_id]);
    $grades_data = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // حساب الإحصائيات
    $total_sessions = count($attendance_data);
    $total_attended = array_sum(array_column($attendance_data, 'attended'));
    $attendance_rate = $total_sessions > 0 ? ($total_attended / $total_sessions) * 100 : 0;
    
    $total_grades = count($grades_data);
    $avg_grade = 0;
    if (!empty($grades_data)) {
        $total_grade_points = 0;
        $total_max_points = 0;
        foreach ($grades_data as $grade) {
            $total_grade_points += $grade['grade'];
            $total_max_points += $grade['max_grade'];
        }
        $avg_grade = $total_max_points > 0 ? ($total_grade_points / $total_max_points) * 100 : 0;
    }
    
} catch (PDOException $e) {
    header('Location: student-progress.php');
    exit;
}
?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير الطالب - <?php echo htmlspecialchars($student_data['name']); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: white;
            color: #333;
        }
        
        .report-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            margin-bottom: 2rem;
            border-radius: 10px;
        }
        
        .report-section {
            margin-bottom: 2rem;
            page-break-inside: avoid;
        }
        
        .stats-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1rem;
            text-align: center;
            margin-bottom: 1rem;
        }
        
        .grade-excellent { background-color: #d4edda; color: #155724; }
        .grade-good { background-color: #d1ecf1; color: #0c5460; }
        .grade-average { background-color: #fff3cd; color: #856404; }
        .grade-poor { background-color: #f8d7da; color: #721c24; }
        
        .attendance-present { background-color: #d4edda; color: #155724; }
        .attendance-absent { background-color: #f8d7da; color: #721c24; }
        
        @media print {
            body {
                background: white !important;
                font-size: 12px !important;
                line-height: 1.4 !important;
            }
            .no-print { display: none !important; }
            .report-header {
                background: #667eea !important;
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
            }
            .page-break { page-break-before: always; }
            .container { max-width: 100% !important; margin: 0 !important; }
            .table { font-size: 11px !important; }
            .stats-card {
                border: 1px solid #000 !important;
                background: #f8f9fa !important;
                -webkit-print-color-adjust: exact !important;
            }
            .badge {
                border: 1px solid #000 !important;
                -webkit-print-color-adjust: exact !important;
            }
            h1, h2, h3, h4, h5, h6 { color: #000 !important; }
        }
        
        .signature-section {
            margin-top: 3rem;
            border-top: 2px solid #dee2e6;
            padding-top: 2rem;
        }
        
        .signature-box {
            border: 1px solid #dee2e6;
            height: 80px;
            margin-top: 1rem;
        }
    </style>
</head>
<body>
    <div class="container my-4">
        <!-- أزرار التحكم -->
        <div class="no-print mb-3 d-flex gap-2 justify-content-center">
            <button onclick="window.print()" class="btn btn-primary btn-lg">
                <i class="fas fa-print me-2"></i>طباعة التقرير
            </button>
            <button onclick="savePDF()" class="btn btn-success btn-lg">
                <i class="fas fa-file-pdf me-2"></i>حفظ PDF
            </button>
            <button onclick="shareReport()" class="btn btn-info btn-lg">
                <i class="fas fa-share me-2"></i>مشاركة
            </button>
            <a href="student-progress.php?id=<?php echo $student_id; ?>&course_id=<?php echo $course_id; ?>" class="btn btn-outline-primary btn-lg">
                <i class="fas fa-arrow-right me-2"></i>العودة
            </a>
            <button onclick="window.close()" class="btn btn-outline-secondary btn-lg">
                <i class="fas fa-times me-2"></i>إغلاق
            </button>
        </div>
        
        <!-- رأس التقرير -->
        <div class="report-header text-center">
            <h1 class="mb-3">
                <i class="fas fa-graduation-cap me-3"></i>
                تقرير أداء الطالب
            </h1>
            <h3><?php echo htmlspecialchars($student_data['name']); ?></h3>
            <p class="mb-0">كورس: <?php echo htmlspecialchars($student_data['course_title']); ?></p>
            <small>تاريخ التقرير: <?php echo date('Y-m-d H:i'); ?></small>
        </div>
        
        <!-- معلومات أساسية -->
        <div class="report-section">
            <h4 class="border-bottom pb-2 mb-3">
                <i class="fas fa-user text-primary me-2"></i>
                المعلومات الأساسية
            </h4>
            <div class="row">
                <div class="col-md-6">
                    <p><strong>اسم الطالب:</strong> <?php echo htmlspecialchars($student_data['name']); ?></p>
                    <p><strong>البريد الإلكتروني:</strong> <?php echo htmlspecialchars($student_data['email']); ?></p>
                    <?php if ($student_data['phone']): ?>
                    <p><strong>رقم الهاتف:</strong> <?php echo htmlspecialchars($student_data['phone']); ?></p>
                    <?php endif; ?>
                </div>
                <div class="col-md-6">
                    <p><strong>اسم الكورس:</strong> <?php echo htmlspecialchars($student_data['course_title']); ?></p>
                    <p><strong>المدرب:</strong> <?php echo htmlspecialchars($student_data['instructor_name']); ?></p>
                    <p><strong>تاريخ التسجيل:</strong> <?php echo date('Y-m-d', strtotime($student_data['enrolled_at'])); ?></p>
                </div>
            </div>
        </div>
        
        <!-- الإحصائيات العامة -->
        <div class="report-section">
            <h4 class="border-bottom pb-2 mb-3">
                <i class="fas fa-chart-bar text-success me-2"></i>
                الإحصائيات العامة
            </h4>
            <div class="row">
                <div class="col-md-3">
                    <div class="stats-card">
                        <h5 class="text-primary"><?php echo number_format($student_data['progress_percentage'], 1); ?>%</h5>
                        <small>التقدم العام</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card">
                        <h5 class="text-success"><?php echo $total_attended; ?>/<?php echo $total_sessions; ?></h5>
                        <small>الحضور</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card">
                        <h5 class="text-info"><?php echo number_format($attendance_rate, 1); ?>%</h5>
                        <small>معدل الحضور</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card">
                        <h5 class="text-warning"><?php echo number_format($avg_grade, 1); ?>%</h5>
                        <small>متوسط الدرجات</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- سجل الحضور -->
        <div class="report-section">
            <h4 class="border-bottom pb-2 mb-3">
                <i class="fas fa-calendar-check text-success me-2"></i>
                سجل الحضور
            </h4>
            <?php if (empty($attendance_data)): ?>
            <p class="text-muted">لا توجد جلسات مجدولة</p>
            <?php else: ?>
            <div class="table-responsive">
                <table class="table table-bordered table-sm">
                    <thead class="table-light">
                        <tr>
                            <th width="5%">#</th>
                            <th width="40%">عنوان الجلسة</th>
                            <th width="20%">التاريخ</th>
                            <th width="15%">الوقت</th>
                            <th width="10%">المدة</th>
                            <th width="10%">الحالة</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($attendance_data as $index => $session): ?>
                        <tr>
                            <td><?php echo $index + 1; ?></td>
                            <td><?php echo htmlspecialchars($session['session_title']); ?></td>
                            <td><?php echo date('Y-m-d', strtotime($session['session_date'])); ?></td>
                            <td><?php echo $session['start_time'] ? date('H:i', strtotime($session['start_time'])) : '-'; ?></td>
                            <td><?php echo $session['duration_minutes'] ? $session['duration_minutes'] . ' دقيقة' : '-'; ?></td>
                            <td>
                                <?php if ($session['attended']): ?>
                                <span class="badge attendance-present">حضر</span>
                                <?php else: ?>
                                <span class="badge attendance-absent">غاب</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            <?php endif; ?>
        </div>

        <!-- سجل الدرجات -->
        <div class="report-section page-break">
            <h4 class="border-bottom pb-2 mb-3">
                <i class="fas fa-star text-warning me-2"></i>
                سجل الدرجات والتقييمات
            </h4>
            <?php if (empty($grades_data)): ?>
            <p class="text-muted">لا توجد درجات مسجلة</p>
            <?php else: ?>
            <div class="table-responsive">
                <table class="table table-bordered table-sm">
                    <thead class="table-light">
                        <tr>
                            <th width="5%">#</th>
                            <th width="35%">اسم التقييم</th>
                            <th width="15%">الدرجة</th>
                            <th width="15%">النسبة المئوية</th>
                            <th width="15%">التاريخ</th>
                            <th width="15%">التقدير</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($grades_data as $index => $grade): ?>
                        <?php
                        $percentage = ($grade['grade'] / $grade['max_grade']) * 100;
                        $grade_class = '';
                        $grade_text = '';

                        if ($percentage >= 90) {
                            $grade_class = 'grade-excellent';
                            $grade_text = 'ممتاز';
                        } elseif ($percentage >= 80) {
                            $grade_class = 'grade-good';
                            $grade_text = 'جيد جداً';
                        } elseif ($percentage >= 70) {
                            $grade_class = 'grade-good';
                            $grade_text = 'جيد';
                        } elseif ($percentage >= 60) {
                            $grade_class = 'grade-average';
                            $grade_text = 'مقبول';
                        } else {
                            $grade_class = 'grade-poor';
                            $grade_text = 'ضعيف';
                        }
                        ?>
                        <tr>
                            <td><?php echo $index + 1; ?></td>
                            <td><?php echo htmlspecialchars($grade['assignment_name']); ?></td>
                            <td><?php echo $grade['grade']; ?>/<?php echo $grade['max_grade']; ?></td>
                            <td><?php echo number_format($percentage, 1); ?>%</td>
                            <td><?php echo date('Y-m-d', strtotime($grade['grade_date'])); ?></td>
                            <td><span class="badge <?php echo $grade_class; ?>"><?php echo $grade_text; ?></span></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                    <tfoot class="table-light">
                        <tr>
                            <td colspan="5"><strong>المتوسط العام</strong></td>
                            <td><strong><?php echo number_format($avg_grade, 1); ?>%</strong></td>
                        </tr>
                    </tfoot>
                </table>
            </div>
            <?php endif; ?>
        </div>

        <!-- تقييم الأداء -->
        <div class="report-section">
            <h4 class="border-bottom pb-2 mb-3">
                <i class="fas fa-chart-line text-info me-2"></i>
                تقييم الأداء العام
            </h4>
            <div class="row">
                <div class="col-md-6">
                    <h6>الحضور والمواظبة:</h6>
                    <?php if ($attendance_rate >= 90): ?>
                    <p class="text-success"><i class="fas fa-check-circle me-1"></i> ممتاز - حضور منتظم</p>
                    <?php elseif ($attendance_rate >= 75): ?>
                    <p class="text-info"><i class="fas fa-info-circle me-1"></i> جيد - حضور مقبول</p>
                    <?php else: ?>
                    <p class="text-warning"><i class="fas fa-exclamation-triangle me-1"></i> يحتاج تحسين - حضور ضعيف</p>
                    <?php endif; ?>
                </div>
                <div class="col-md-6">
                    <h6>الأداء الأكاديمي:</h6>
                    <?php if ($avg_grade >= 90): ?>
                    <p class="text-success"><i class="fas fa-star me-1"></i> ممتاز - أداء متميز</p>
                    <?php elseif ($avg_grade >= 80): ?>
                    <p class="text-info"><i class="fas fa-thumbs-up me-1"></i> جيد جداً - أداء مرضي</p>
                    <?php elseif ($avg_grade >= 70): ?>
                    <p class="text-warning"><i class="fas fa-hand-paper me-1"></i> جيد - أداء مقبول</p>
                    <?php else: ?>
                    <p class="text-danger"><i class="fas fa-exclamation-circle me-1"></i> يحتاج تحسين</p>
                    <?php endif; ?>
                </div>
            </div>

            <div class="mt-3">
                <h6>التوصيات:</h6>
                <ul>
                    <?php if ($attendance_rate < 75): ?>
                    <li>ضرورة تحسين معدل الحضور والمواظبة على الجلسات</li>
                    <?php endif; ?>

                    <?php if ($avg_grade < 70): ?>
                    <li>يُنصح بمراجعة المواد الدراسية والتركيز على نقاط الضعف</li>
                    <li>طلب المساعدة من المدرب في المواضيع الصعبة</li>
                    <?php endif; ?>

                    <?php if ($avg_grade >= 80 && $attendance_rate >= 90): ?>
                    <li>أداء ممتاز، يُنصح بالاستمرار على نفس المستوى</li>
                    <li>يمكن المشاركة في أنشطة إضافية لتطوير المهارات</li>
                    <?php endif; ?>

                    <li>المتابعة المستمرة مع المدرب لضمان التقدم المطلوب</li>
                </ul>
            </div>
        </div>

        <!-- التوقيعات -->
        <div class="signature-section">
            <div class="row">
                <div class="col-md-6">
                    <h6>توقيع المدرب:</h6>
                    <p><?php echo htmlspecialchars($student_data['instructor_name']); ?></p>
                    <div class="signature-box"></div>
                    <small class="text-muted">التاريخ: ________________</small>
                </div>
                <div class="col-md-6">
                    <h6>توقيع الطالب:</h6>
                    <p><?php echo htmlspecialchars($student_data['name']); ?></p>
                    <div class="signature-box"></div>
                    <small class="text-muted">التاريخ: ________________</small>
                </div>
            </div>
        </div>

        <!-- تذييل التقرير -->
        <div class="text-center mt-4 pt-3 border-top">
            <small class="text-muted">
                تم إنشاء هذا التقرير بواسطة نظام إدارة التعلم - <?php echo date('Y-m-d H:i:s'); ?>
            </small>
        </div>
    </div>

    <script>
        // تحسين الطباعة
        window.addEventListener('beforeprint', function() {
            document.title = 'تقرير_<?php echo htmlspecialchars($student_data['name']); ?>_<?php echo date('Y-m-d'); ?>';
        });

        // إضافة تأثيرات للطباعة
        window.addEventListener('afterprint', function() {
            console.log('تم طباعة التقرير');
        });

        // حفظ كـ PDF
        function savePDF() {
            // تغيير العنوان للحفظ
            const originalTitle = document.title;
            document.title = 'تقرير_<?php echo htmlspecialchars($student_data['name']); ?>_<?php echo date('Y-m-d'); ?>';

            // طباعة مع إعدادات PDF
            window.print();

            // إعادة العنوان الأصلي
            setTimeout(() => {
                document.title = originalTitle;
            }, 1000);
        }

        // مشاركة التقرير
        function shareReport() {
            const url = window.location.href;
            const title = 'تقرير أداء الطالب - <?php echo htmlspecialchars($student_data['name']); ?>';

            if (navigator.share) {
                navigator.share({
                    title: title,
                    url: url
                }).then(() => {
                    console.log('تم مشاركة التقرير بنجاح');
                }).catch((error) => {
                    console.log('خطأ في المشاركة:', error);
                    fallbackShare(url, title);
                });
            } else {
                fallbackShare(url, title);
            }
        }

        // مشاركة بديلة
        function fallbackShare(url, title) {
            // نسخ الرابط للحافظة
            navigator.clipboard.writeText(url).then(() => {
                alert('تم نسخ رابط التقرير إلى الحافظة');
            }).catch(() => {
                // إظهار نافذة مع الرابط
                prompt('انسخ هذا الرابط للمشاركة:', url);
            });
        }

        // تحسينات إضافية
        document.addEventListener('DOMContentLoaded', function() {
            // إضافة تأثيرات تفاعلية
            const cards = document.querySelectorAll('.stats-card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.05)';
                    this.style.transition = 'transform 0.3s ease';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1)';
                });
            });

            // إضافة معلومات إضافية عند التمرير
            const badges = document.querySelectorAll('.badge');
            badges.forEach(badge => {
                badge.style.cursor = 'help';
            });
        });

        // اختصارات لوحة المفاتيح
        document.addEventListener('keydown', function(e) {
            // Ctrl+P للطباعة
            if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                window.print();
            }

            // Ctrl+S للحفظ كـ PDF
            if (e.ctrlKey && e.key === 's') {
                e.preventDefault();
                savePDF();
            }

            // ESC للإغلاق
            if (e.key === 'Escape') {
                window.close();
            }
        });
    </script>
</body>
</html>
