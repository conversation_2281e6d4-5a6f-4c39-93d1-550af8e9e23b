<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات المدير
if (!isLoggedIn() || !isAdmin()) {
    header('Location: ../login.php');
    exit;
}

// التحقق من وجود معرف الجلسة
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: manage-sessions.php');
    exit;
}

$session_id = (int)$_GET['id'];
$success_message = '';
$error_message = '';

// جلب تفاصيل الجلسة
try {
    $stmt = $conn->prepare("
        SELECT s.*, c.title as course_title, c.description as course_description,
               u.name as instructor_name, u.email as instructor_email,
               (SELECT COUNT(*) FROM course_enrollments WHERE course_id = c.id) as enrolled_students
        FROM sessions s
        JOIN courses c ON s.course_id = c.id
        JOIN users u ON c.instructor_id = u.id
        WHERE s.id = ?
    ");
    $stmt->execute([$session_id]);
    $session = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$session) {
        header('Location: manage-sessions.php');
        exit;
    }
    
    // جلب الطلاب المسجلين في الكورس
    $stmt = $conn->prepare("
        SELECT u.id, u.name, u.email, u.phone, ce.enrolled_at, ce.status
        FROM users u
        JOIN course_enrollments ce ON u.id = ce.student_id
        WHERE ce.course_id = ? AND u.role = 'student'
        ORDER BY ce.enrolled_at DESC
    ");
    $stmt->execute([$session['course_id']]);
    $students = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // جلب ملفات الجلسة إذا كانت موجودة (إذا كان الجدول موجود)
    $session_files = [];
    try {
        $stmt = $conn->prepare("
            SELECT * FROM session_files
            WHERE session_id = ?
            ORDER BY uploaded_at DESC
        ");
        $stmt->execute([$session_id]);
        $session_files = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        // الجدول غير موجود، سنتركه فارغاً
        $session_files = [];
    }
    
} catch (Exception $e) {
    $error_message = 'حدث خطأ أثناء جلب تفاصيل الجلسة: ' . $e->getMessage();
}

// معالجة تحديث حالة الجلسة
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    if ($_POST['action'] === 'update_status') {
        $new_status = $_POST['status'];
        try {
            $stmt = $conn->prepare("UPDATE sessions SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?");
            $stmt->execute([$new_status, $session_id]);
            
            logUserActivity($_SESSION['user_id'], 'تحديث حالة جلسة', "تم تحديث حالة الجلسة رقم $session_id إلى $new_status");
            
            $success_message = 'تم تحديث حالة الجلسة بنجاح';
            $session['status'] = $new_status;
        } catch (Exception $e) {
            $error_message = 'حدث خطأ أثناء تحديث حالة الجلسة';
        }
    }
}

$pageTitle = 'تفاصيل الجلسة: ' . $session['title'];
$pageSubtitle = 'عرض تفاصيل شاملة للجلسة والمشاركين';
require_once 'includes/admin-header.php';
?>

<div class="container-fluid py-4">
    <!-- رسائل النجاح والخطأ -->
    <?php if ($success_message): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>
    
    <?php if ($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <!-- تفاصيل الجلسة -->
        <div class="col-lg-8">
            <div class="admin-card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-video me-2"></i>تفاصيل الجلسة</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5 class="text-primary"><?php echo htmlspecialchars($session['title']); ?></h5>
                            <p class="text-muted"><?php echo htmlspecialchars($session['description'] ?? 'لا يوجد وصف'); ?></p>
                            
                            <div class="mb-3">
                                <strong>الكورس:</strong> 
                                <a href="course-details.php?id=<?php echo $session['course_id']; ?>" class="text-decoration-none">
                                    <?php echo htmlspecialchars($session['course_title']); ?>
                                </a>
                            </div>
                            
                            <div class="mb-3">
                                <strong>المدرب:</strong> 
                                <a href="instructor-details.php?id=<?php echo $session['instructor_id'] ?? ''; ?>" class="text-decoration-none">
                                    <?php echo htmlspecialchars($session['instructor_name']); ?>
                                </a>
                            </div>
                            
                            <div class="mb-3">
                                <strong>تاريخ الجلسة:</strong> 
                                <?php echo date('Y-m-d', strtotime($session['session_date'])); ?>
                            </div>
                            
                            <div class="mb-3">
                                <strong>وقت الجلسة:</strong> 
                                <?php echo date('H:i', strtotime($session['session_date'])); ?>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <strong>المدة:</strong> 
                                <?php echo $session['duration'] ?? 60; ?> دقيقة
                            </div>
                            
                            <div class="mb-3">
                                <strong>الحالة:</strong>
                                <span class="badge bg-<?php 
                                    echo $session['status'] === 'scheduled' ? 'info' : 
                                        ($session['status'] === 'completed' ? 'success' : 
                                        ($session['status'] === 'cancelled' ? 'danger' : 'warning')); 
                                ?>">
                                    <?php
                                    switch($session['status']) {
                                        case 'scheduled': echo 'مجدولة'; break;
                                        case 'completed': echo 'مكتملة'; break;
                                        case 'cancelled': echo 'ملغية'; break;
                                        case 'ongoing': echo 'جارية'; break;
                                        default: echo $session['status'];
                                    }
                                    ?>
                                </span>
                                <button class="btn btn-sm btn-outline-primary ms-2" data-bs-toggle="modal" data-bs-target="#changeStatusModal">
                                    تغيير الحالة
                                </button>
                            </div>
                            
                            <?php if (isset($session['zoom_meeting_id']) && $session['zoom_meeting_id']): ?>
                            <div class="mb-3">
                                <strong>معرف اجتماع Zoom:</strong>
                                <code><?php echo htmlspecialchars($session['zoom_meeting_id']); ?></code>
                            </div>
                            <?php endif; ?>

                            <?php if (isset($session['zoom_join_url']) && $session['zoom_join_url']): ?>
                            <div class="mb-3">
                                <strong>رابط الانضمام:</strong>
                                <a href="<?php echo htmlspecialchars($session['zoom_join_url']); ?>" target="_blank" class="btn btn-success btn-sm">
                                    <i class="fas fa-video me-1"></i>انضمام للجلسة
                                </a>
                            </div>
                            <?php endif; ?>
                            
                            <div class="mb-3">
                                <strong>تاريخ الإنشاء:</strong> 
                                <?php echo date('Y-m-d H:i', strtotime($session['created_at'])); ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- ملفات الجلسة -->
            <div class="admin-card mt-4">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-file me-2"></i>ملفات الجلسة</h6>
                </div>
                <div class="card-body">
                    <?php if (empty($session_files)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-file fa-3x text-muted mb-3"></i>
                            <p class="text-muted">لا توجد ملفات مرفقة بهذه الجلسة</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>اسم الملف</th>
                                        <th>النوع</th>
                                        <th>الحجم</th>
                                        <th>تاريخ الرفع</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($session_files as $file): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($file['original_name']); ?></td>
                                            <td><?php echo htmlspecialchars($file['file_type']); ?></td>
                                            <td><?php echo formatFileSize($file['file_size']); ?></td>
                                            <td><?php echo date('Y-m-d H:i', strtotime($file['uploaded_at'])); ?></td>
                                            <td>
                                                <a href="../<?php echo htmlspecialchars($file['file_path']); ?>" 
                                                   class="btn btn-primary btn-sm" target="_blank">
                                                    <i class="fas fa-download"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- الشريط الجانبي -->
        <div class="col-lg-4">
            <!-- إحصائيات سريعة -->
            <div class="admin-card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-chart-bar me-2"></i>إحصائيات سريعة</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <h4 class="text-primary"><?php echo count($students); ?></h4>
                            <small class="text-muted">الطلاب المسجلين</small>
                        </div>
                        <div class="col-6 mb-3">
                            <h4 class="text-success"><?php echo is_array($session_files) ? count($session_files) : 0; ?></h4>
                            <small class="text-muted">الملفات المرفقة</small>
                        </div>
                        <div class="col-12">
                            <small class="text-muted">
                                <i class="fas fa-calendar me-1"></i>
                                أنشئت في <?php echo date('Y-m-d', strtotime($session['created_at'])); ?>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- إجراءات سريعة -->
            <div class="admin-card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-bolt me-2"></i>إجراءات سريعة</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="course-details.php?id=<?php echo $session['course_id']; ?>" class="btn btn-outline-info">
                            <i class="fas fa-book me-2"></i>عرض الكورس
                        </a>
                        <a href="instructor-details.php?id=<?php echo $session['instructor_id'] ?? ''; ?>" class="btn btn-outline-success">
                            <i class="fas fa-user-tie me-2"></i>عرض المدرب
                        </a>
                        <?php if ($session['zoom_join_url']): ?>
                        <a href="<?php echo htmlspecialchars($session['zoom_join_url']); ?>" target="_blank" class="btn btn-outline-primary">
                            <i class="fas fa-video me-2"></i>انضمام للجلسة
                        </a>
                        <?php endif; ?>
                        <button class="btn btn-outline-danger" onclick="deleteSession(<?php echo $session_id; ?>, '<?php echo htmlspecialchars($session['title']); ?>')">
                            <i class="fas fa-trash me-2"></i>حذف الجلسة
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- الطلاب المسجلين -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="admin-card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-users me-2"></i>الطلاب المسجلين (<?php echo count($students); ?>)</h6>
                </div>
                <div class="card-body">
                    <?php if (empty($students)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <p class="text-muted">لا يوجد طلاب مسجلين في هذا الكورس</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover" id="studentsTable">
                                <thead>
                                    <tr>
                                        <th>اسم الطالب</th>
                                        <th>البريد الإلكتروني</th>
                                        <th>رقم الهاتف</th>
                                        <th>تاريخ التسجيل</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($students as $student): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($student['name']); ?></td>
                                            <td><?php echo htmlspecialchars($student['email']); ?></td>
                                            <td><?php echo htmlspecialchars($student['phone'] ?? 'غير محدد'); ?></td>
                                            <td><?php echo date('Y-m-d', strtotime($student['enrolled_at'])); ?></td>
                                            <td>
                                                <span class="badge bg-<?php echo $student['status'] === 'active' ? 'success' : 'warning'; ?>">
                                                    <?php echo $student['status'] === 'active' ? 'نشط' : 'قيد المراجعة'; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <a href="student-details.php?id=<?php echo $student['id']; ?>" class="btn btn-primary btn-sm">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal تغيير حالة الجلسة -->
<div class="modal fade" id="changeStatusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تغيير حالة الجلسة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="update_status">
                    <div class="mb-3">
                        <label class="form-label">الحالة</label>
                        <select class="form-select" name="status" required>
                            <option value="scheduled" <?php echo $session['status'] === 'scheduled' ? 'selected' : ''; ?>>مجدولة</option>
                            <option value="ongoing" <?php echo $session['status'] === 'ongoing' ? 'selected' : ''; ?>>جارية</option>
                            <option value="completed" <?php echo $session['status'] === 'completed' ? 'selected' : ''; ?>>مكتملة</option>
                            <option value="cancelled" <?php echo $session['status'] === 'cancelled' ? 'selected' : ''; ?>>ملغية</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// تهيئة DataTable
$(document).ready(function() {
    $('#studentsTable').DataTable({
        order: [[3, 'desc']],
        pageLength: 10,
        responsive: true,
        language: {
            url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
        }
    });
});

// حذف الجلسة
function deleteSession(sessionId, sessionTitle) {
    Swal.fire({
        title: 'حذف الجلسة',
        html: `
            <p>هل أنت متأكد من حذف الجلسة <strong>${sessionTitle}</strong>؟</p>
            <div class="alert alert-warning text-start">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>تحذير:</strong> لا يمكن التراجع عن هذا الإجراء
            </div>
        `,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'نعم، احذف الجلسة',
        cancelButtonText: 'إلغاء',
        confirmButtonColor: '#dc3545'
    }).then((result) => {
        if (result.isConfirmed) {
            window.location.href = 'manage-sessions.php?action=delete&id=' + sessionId;
        }
    });
}

// دالة تنسيق حجم الملف
<?php
function formatFileSize($bytes) {
    if ($bytes >= 1073741824) {
        return number_format($bytes / 1073741824, 2) . ' GB';
    } elseif ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' bytes';
    }
}
?>
</script>

<?php require_once 'includes/admin-footer.php'; ?>
