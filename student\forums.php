<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات الطالب
if (!isLoggedIn() || !isStudent()) {
    header('Location: ../login.php');
    exit;
}

$pageTitle = 'المنتديات';
$breadcrumbs = [
    ['title' => 'المنتديات']
];

$student_id = $_SESSION['user_id'];
$success = '';
$error = '';

// معالجة إنشاء موضوع جديد
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_topic'])) {
    $forum_id = (int)$_POST['forum_id'];
    $title = trim($_POST['title']);
    $content = trim($_POST['content']);
    
    try {
        if (empty($title) || empty($content)) {
            throw new Exception('يرجى ملء جميع الحقول المطلوبة');
        }
        
        // التحقق من صلاحية الوصول للمنتدى
        $stmt = $conn->prepare("
            SELECT f.* FROM forums f
            LEFT JOIN courses c ON f.course_id = c.id
            LEFT JOIN course_enrollments ce ON c.id = ce.course_id AND ce.student_id = ?
            WHERE f.id = ? AND (f.is_public = 1 OR ce.id IS NOT NULL OR f.course_id IS NULL)
        ");
        $stmt->execute([$student_id, $forum_id]);
        $forum = $stmt->fetch();
        
        if (!$forum) {
            throw new Exception('المنتدى غير متاح');
        }
        
        // إنشاء الموضوع
        $stmt = $conn->prepare("
            INSERT INTO forum_posts (forum_id, title, content, author_id)
            VALUES (?, ?, ?, ?)
        ");
        $stmt->execute([$forum_id, $title, $content, $student_id]);
        
        // تحديث إحصائيات المنتدى
        $post_id = $conn->lastInsertId();
        $stmt = $conn->prepare("
            UPDATE forums SET 
                post_count = post_count + 1,
                last_post_id = ?,
                last_activity = NOW()
            WHERE id = ?
        ");
        $stmt->execute([$post_id, $forum_id]);
        
        $success = 'تم إنشاء الموضوع بنجاح';
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// معالجة إضافة رد
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_reply'])) {
    $parent_id = (int)$_POST['parent_id'];
    $content = trim($_POST['content']);
    
    try {
        if (empty($content)) {
            throw new Exception('يرجى كتابة محتوى الرد');
        }
        
        // التحقق من صلاحية الوصول للموضوع
        $stmt = $conn->prepare("
            SELECT fp.*, f.id as forum_id FROM forum_posts fp
            JOIN forums f ON fp.forum_id = f.id
            LEFT JOIN courses c ON f.course_id = c.id
            LEFT JOIN course_enrollments ce ON c.id = ce.course_id AND ce.student_id = ?
            WHERE fp.id = ? AND (f.is_public = 1 OR ce.id IS NOT NULL OR f.course_id IS NULL)
        ");
        $stmt->execute([$student_id, $parent_id]);
        $parent_post = $stmt->fetch();
        
        if (!$parent_post) {
            throw new Exception('الموضوع غير متاح');
        }
        
        // إضافة الرد
        $stmt = $conn->prepare("
            INSERT INTO forum_posts (forum_id, parent_id, content, author_id)
            VALUES (?, ?, ?, ?)
        ");
        $stmt->execute([$parent_post['forum_id'], $parent_id, $content, $student_id]);
        
        // تحديث عداد الردود
        $reply_id = $conn->lastInsertId();
        $stmt = $conn->prepare("
            UPDATE forum_posts SET 
                reply_count = reply_count + 1,
                last_reply_id = ?,
                last_reply_at = NOW()
            WHERE id = ?
        ");
        $stmt->execute([$reply_id, $parent_id]);
        
        // تحديث إحصائيات المنتدى
        $stmt = $conn->prepare("
            UPDATE forums SET 
                post_count = post_count + 1,
                last_post_id = ?,
                last_activity = NOW()
            WHERE id = ?
        ");
        $stmt->execute([$reply_id, $parent_post['forum_id']]);
        
        $success = 'تم إضافة الرد بنجاح';
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// معاملات البحث والفلترة
$search = $_GET['search'] ?? '';
$category = $_GET['category'] ?? '';
$forum_filter = $_GET['forum'] ?? '';

try {
    // جلب المنتديات المتاحة
    $stmt = $conn->prepare("
        SELECT f.*, c.title as course_title,
               (SELECT COUNT(*) FROM forum_posts WHERE forum_id = f.id) as total_posts,
               (SELECT COUNT(DISTINCT author_id) FROM forum_posts WHERE forum_id = f.id) as participants
        FROM forums f
        LEFT JOIN courses c ON f.course_id = c.id
        LEFT JOIN course_enrollments ce ON c.id = ce.course_id AND ce.student_id = ?
        WHERE f.is_public = 1 OR ce.id IS NOT NULL OR f.course_id IS NULL
        ORDER BY f.last_activity DESC
    ");
    $stmt->execute([$student_id]);
    $forums = $stmt->fetchAll();
    
    // بناء استعلام المواضيع
    $where_conditions = [
        "(f.is_public = 1 OR ce.id IS NOT NULL OR f.course_id IS NULL)",
        "fp.parent_id IS NULL" // المواضيع الرئيسية فقط
    ];
    $params = [$student_id];
    
    if ($search) {
        $where_conditions[] = "(fp.title LIKE ? OR fp.content LIKE ?)";
        $search_term = "%$search%";
        $params = array_merge($params, [$search_term, $search_term]);
    }
    
    if ($category) {
        $where_conditions[] = "f.category = ?";
        $params[] = $category;
    }
    
    if ($forum_filter) {
        $where_conditions[] = "f.id = ?";
        $params[] = $forum_filter;
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    // جلب المواضيع
    $stmt = $conn->prepare("
        SELECT fp.*, f.title as forum_title, f.category as forum_category,
               c.title as course_title,
               u.name as author_name,
               (SELECT COUNT(*) FROM forum_posts WHERE parent_id = fp.id) as reply_count,
               (SELECT content FROM forum_posts WHERE parent_id = fp.id ORDER BY created_at DESC LIMIT 1) as last_reply_content,
               (SELECT u2.name FROM forum_posts fp2 JOIN users u2 ON fp2.author_id = u2.id WHERE fp2.parent_id = fp.id ORDER BY fp2.created_at DESC LIMIT 1) as last_reply_author
        FROM forum_posts fp
        JOIN forums f ON fp.forum_id = f.id
        LEFT JOIN courses c ON f.course_id = c.id
        LEFT JOIN course_enrollments ce ON c.id = ce.course_id AND ce.student_id = ?
        JOIN users u ON fp.author_id = u.id
        WHERE $where_clause
        ORDER BY 
            CASE WHEN fp.is_pinned = 1 THEN 0 ELSE 1 END,
            COALESCE(fp.last_reply_at, fp.created_at) DESC
    ");
    $stmt->execute($params);
    $topics = $stmt->fetchAll();
    
    // جلب التصنيفات
    $stmt = $conn->prepare("
        SELECT DISTINCT f.category
        FROM forums f
        LEFT JOIN courses c ON f.course_id = c.id
        LEFT JOIN course_enrollments ce ON c.id = ce.course_id AND ce.student_id = ?
        WHERE f.category IS NOT NULL 
        AND (f.is_public = 1 OR ce.id IS NOT NULL OR f.course_id IS NULL)
        ORDER BY f.category
    ");
    $stmt->execute([$student_id]);
    $categories = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    // إحصائيات
    $stats = [
        'total_forums' => count($forums),
        'total_topics' => count($topics),
        'my_posts' => 0,
        'total_posts' => 0
    ];
    
    // حساب إجمالي المشاركات ومشاركاتي
    $stmt = $conn->prepare("
        SELECT 
            COUNT(*) as total_posts,
            SUM(CASE WHEN fp.author_id = ? THEN 1 ELSE 0 END) as my_posts
        FROM forum_posts fp
        JOIN forums f ON fp.forum_id = f.id
        LEFT JOIN courses c ON f.course_id = c.id
        LEFT JOIN course_enrollments ce ON c.id = ce.course_id AND ce.student_id = ?
        WHERE f.is_public = 1 OR ce.id IS NOT NULL OR f.course_id IS NULL
    ");
    $stmt->execute([$student_id, $student_id]);
    $post_stats = $stmt->fetch();
    $stats['total_posts'] = $post_stats['total_posts'];
    $stats['my_posts'] = $post_stats['my_posts'];
    
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء جلب المنتديات';
}

include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- العنوان -->
        <div class="col-12 mb-4">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="text-primary">
                        <i class="fas fa-comments me-2"></i>
                        المنتديات التعليمية
                    </h2>
                    <p class="text-muted">شارك الأفكار والأسئلة مع زملائك والمدربين</p>
                </div>
                <button class="btn btn-student-primary" data-bs-toggle="modal" data-bs-target="#createTopicModal">
                    <i class="fas fa-plus me-1"></i>
                    موضوع جديد
                </button>
            </div>
        </div>

        <?php if ($success): ?>
        <div class="col-12 mb-3">
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo htmlspecialchars($success); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </div>
        <?php endif; ?>

        <?php if ($error): ?>
        <div class="col-12 mb-3">
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-circle me-2"></i>
                <?php echo htmlspecialchars($error); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </div>
        <?php endif; ?>

        <!-- الإحصائيات -->
        <div class="col-12 mb-4">
            <div class="row">
                <div class="col-md-3 mb-3">
                    <div class="card-student text-center">
                        <div class="card-body">
                            <i class="fas fa-comments fa-2x text-primary mb-2"></i>
                            <h4 class="text-primary"><?php echo $stats['total_forums']; ?></h4>
                            <p class="text-muted mb-0">منتديات</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card-student text-center">
                        <div class="card-body">
                            <i class="fas fa-list fa-2x text-success mb-2"></i>
                            <h4 class="text-success"><?php echo $stats['total_topics']; ?></h4>
                            <p class="text-muted mb-0">مواضيع</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card-student text-center">
                        <div class="card-body">
                            <i class="fas fa-reply fa-2x text-info mb-2"></i>
                            <h4 class="text-info"><?php echo $stats['total_posts']; ?></h4>
                            <p class="text-muted mb-0">مشاركات</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card-student text-center">
                        <div class="card-body">
                            <i class="fas fa-user-edit fa-2x text-warning mb-2"></i>
                            <h4 class="text-warning"><?php echo $stats['my_posts']; ?></h4>
                            <p class="text-muted mb-0">مشاركاتي</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- فلاتر البحث -->
        <div class="col-12 mb-4">
            <div class="card-student">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label class="form-label">البحث</label>
                            <input type="text" name="search" class="form-control" 
                                   placeholder="ابحث في المواضيع..." value="<?php echo htmlspecialchars($search); ?>">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">التصنيف</label>
                            <select name="category" class="form-select">
                                <option value="">جميع التصنيفات</option>
                                <?php foreach ($categories as $cat): ?>
                                <option value="<?php echo htmlspecialchars($cat); ?>" 
                                        <?php echo $category === $cat ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($cat); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">المنتدى</label>
                            <select name="forum" class="form-select">
                                <option value="">جميع المنتديات</option>
                                <?php foreach ($forums as $forum): ?>
                                <option value="<?php echo $forum['id']; ?>" 
                                        <?php echo $forum_filter == $forum['id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($forum['title']); ?>
                                    <?php if ($forum['course_title']): ?>
                                        - <?php echo htmlspecialchars($forum['course_title']); ?>
                                    <?php endif; ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-1">
                            <label class="form-label">&nbsp;</label>
                            <button type="submit" class="btn btn-student-primary w-100">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- قائمة المواضيع -->
        <div class="col-12">
            <?php if (empty($topics)): ?>
            <div class="card-student text-center py-5">
                <div class="card-body">
                    <i class="fas fa-comments fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">لا توجد مواضيع</h4>
                    <p class="text-muted">لم يتم العثور على مواضيع تطابق معايير البحث</p>
                    <button class="btn btn-student-primary mt-3" data-bs-toggle="modal" data-bs-target="#createTopicModal">
                        <i class="fas fa-plus me-1"></i>
                        إنشاء أول موضوع
                    </button>
                </div>
            </div>
            <?php else: ?>
            <div class="card-student">
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th style="width: 50%;">الموضوع</th>
                                    <th style="width: 15%;">المنتدى</th>
                                    <th style="width: 10%;">الردود</th>
                                    <th style="width: 10%;">المشاهدات</th>
                                    <th style="width: 15%;">آخر رد</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($topics as $topic): ?>
                                <tr class="topic-row" data-topic-id="<?php echo $topic['id']; ?>">
                                    <td>
                                        <div class="d-flex align-items-start">
                                            <div class="topic-icon me-3">
                                                <?php if ($topic['is_pinned']): ?>
                                                <i class="fas fa-thumbtack text-warning"></i>
                                                <?php elseif ($topic['is_locked']): ?>
                                                <i class="fas fa-lock text-danger"></i>
                                                <?php else: ?>
                                                <i class="fas fa-comment text-primary"></i>
                                                <?php endif; ?>
                                            </div>
                                            <div class="topic-content">
                                                <h6 class="mb-1">
                                                    <a href="forum-topic.php?id=<?php echo $topic['id']; ?>" 
                                                       class="text-decoration-none">
                                                        <?php echo htmlspecialchars($topic['title']); ?>
                                                    </a>
                                                    <?php if ($topic['is_pinned']): ?>
                                                    <span class="badge bg-warning ms-1">مثبت</span>
                                                    <?php endif; ?>
                                                    <?php if ($topic['is_locked']): ?>
                                                    <span class="badge bg-danger ms-1">مغلق</span>
                                                    <?php endif; ?>
                                                </h6>
                                                <small class="text-muted">
                                                    بواسطة <strong><?php echo htmlspecialchars($topic['author_name']); ?></strong>
                                                    في <?php echo date('Y-m-d H:i', strtotime($topic['created_at'])); ?>
                                                </small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <div class="fw-bold"><?php echo htmlspecialchars($topic['forum_title']); ?></div>
                                            <?php if ($topic['course_title']): ?>
                                            <small class="text-muted"><?php echo htmlspecialchars($topic['course_title']); ?></small>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td class="text-center">
                                        <span class="badge bg-primary"><?php echo $topic['reply_count']; ?></span>
                                    </td>
                                    <td class="text-center">
                                        <span class="badge bg-info"><?php echo $topic['view_count']; ?></span>
                                    </td>
                                    <td>
                                        <?php if ($topic['last_reply_author']): ?>
                                        <div>
                                            <small class="text-muted">بواسطة</small>
                                            <div class="fw-bold"><?php echo htmlspecialchars($topic['last_reply_author']); ?></div>
                                            <small class="text-muted">
                                                <?php echo date('Y-m-d H:i', strtotime($topic['last_reply_at'])); ?>
                                            </small>
                                        </div>
                                        <?php else: ?>
                                        <small class="text-muted">لا توجد ردود</small>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Modal إنشاء موضوع جديد -->
<div class="modal fade" id="createTopicModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إنشاء موضوع جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">المنتدى</label>
                        <select name="forum_id" class="form-select" required>
                            <option value="">اختر المنتدى</option>
                            <?php foreach ($forums as $forum): ?>
                            <option value="<?php echo $forum['id']; ?>">
                                <?php echo htmlspecialchars($forum['title']); ?>
                                <?php if ($forum['course_title']): ?>
                                    - <?php echo htmlspecialchars($forum['course_title']); ?>
                                <?php endif; ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">عنوان الموضوع</label>
                        <input type="text" name="title" class="form-control" required 
                               placeholder="اكتب عنوان الموضوع...">
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">محتوى الموضوع</label>
                        <textarea name="content" class="form-control" rows="8" required 
                                  placeholder="اكتب محتوى الموضوع..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" name="create_topic" class="btn btn-student-primary">
                        <i class="fas fa-plus me-1"></i>
                        إنشاء الموضوع
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.topic-row {
    cursor: pointer;
    transition: all 0.3s ease;
}

.topic-row:hover {
    background-color: rgba(59, 130, 246, 0.05);
}

.topic-icon {
    width: 20px;
    text-align: center;
}

.topic-content h6 a {
    color: var(--student-dark);
    transition: color 0.3s ease;
}

.topic-content h6 a:hover {
    color: var(--student-primary);
}

.table td {
    vertical-align: middle;
    padding: 1rem 0.75rem;
}

.table thead th {
    border-bottom: 2px solid var(--student-primary);
    color: var(--student-primary);
    font-weight: 600;
}
</style>

<script>
// إضافة تفاعل للنقر على الصف
document.querySelectorAll('.topic-row').forEach(row => {
    row.addEventListener('click', function(e) {
        // تجاهل النقر إذا كان على رابط
        if (e.target.tagName === 'A') return;
        
        const topicId = this.dataset.topicId;
        window.location.href = `forum-topic.php?id=${topicId}`;
    });
});
</script>

<?php include 'includes/footer.php'; ?>
