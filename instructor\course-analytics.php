<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات المدرب
if (!isLoggedIn() || !isInstructor()) {
    header('Location: ../login.php');
    exit;
}

$pageTitle = 'إحصائيات الكورسات';
$breadcrumbs = [
    ['title' => 'إحصائيات الكورسات']
];

// الحصول على معرف الكورس المحدد
$selected_course_id = $_GET['id'] ?? '';

// جلب كورسات المدرب
try {
    $stmt = $conn->prepare("
        SELECT id, title 
        FROM courses 
        WHERE instructor_id = ? AND status = 'active'
        ORDER BY title
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $instructor_courses = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $instructor_courses = [];
}

// إحصائيات عامة
$general_stats = [];
$course_details = null;
$enrollment_data = [];
$session_data = [];
$performance_data = [];

if ($selected_course_id) {
    try {
        // تفاصيل الكورس
        $stmt = $conn->prepare("
            SELECT c.*, 
                   COUNT(DISTINCT ce.student_id) as total_students,
                   COUNT(DISTINCT s.id) as total_sessions,
                   COUNT(DISTINCT cv.id) as total_videos,
                   COUNT(DISTINCT cm.id) as total_materials,
                   AVG(cr.rating) as avg_rating,
                   COUNT(DISTINCT cr.id) as total_reviews
            FROM courses c
            LEFT JOIN course_enrollments ce ON c.id = ce.course_id AND ce.status = 'active'
            LEFT JOIN sessions s ON c.id = s.course_id
            LEFT JOIN course_videos cv ON c.id = cv.course_id AND cv.status = 'active'
            LEFT JOIN course_materials cm ON c.id = cm.course_id AND cm.status = 'active'
            LEFT JOIN course_reviews cr ON c.id = cr.course_id
            WHERE c.id = ? AND c.instructor_id = ?
            GROUP BY c.id
        ");
        $stmt->execute([$selected_course_id, $_SESSION['user_id']]);
        $course_details = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($course_details) {
            // بيانات التسجيل الشهرية
            $stmt = $conn->prepare("
                SELECT 
                    DATE_FORMAT(enrolled_at, '%Y-%m') as month,
                    COUNT(*) as enrollments
                FROM course_enrollments 
                WHERE course_id = ? AND status = 'active'
                GROUP BY DATE_FORMAT(enrolled_at, '%Y-%m')
                ORDER BY month DESC
                LIMIT 12
            ");
            $stmt->execute([$selected_course_id]);
            $enrollment_data = array_reverse($stmt->fetchAll(PDO::FETCH_ASSOC));
            
            // بيانات الجلسات
            $stmt = $conn->prepare("
                SELECT 
                    s.*,
                    COUNT(DISTINCT sa.user_id) as attendees
                FROM sessions s
                LEFT JOIN session_attendees sa ON s.id = sa.session_id
                WHERE s.course_id = ?
                GROUP BY s.id
                ORDER BY s.session_date DESC
                LIMIT 10
            ");
            $stmt->execute([$selected_course_id]);
            $session_data = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // بيانات الأداء
            $stmt = $conn->prepare("
                SELECT 
                    u.name as student_name,
                    AVG(sg.grade) as avg_grade,
                    COUNT(sg.id) as total_grades,
                    ce.enrolled_at
                FROM course_enrollments ce
                INNER JOIN users u ON ce.student_id = u.id
                LEFT JOIN student_grades sg ON ce.student_id = sg.student_id AND ce.course_id = sg.course_id
                WHERE ce.course_id = ? AND ce.status = 'active'
                GROUP BY ce.student_id
                ORDER BY avg_grade DESC
                LIMIT 10
            ");
            $stmt->execute([$selected_course_id]);
            $performance_data = $stmt->fetchAll(PDO::FETCH_ASSOC);
        }
        
    } catch (PDOException $e) {
        $error_message = 'حدث خطأ أثناء جلب البيانات';
    }
} else {
    // إحصائيات عامة لجميع الكورسات
    try {
        $stmt = $conn->prepare("
            SELECT 
                COUNT(DISTINCT c.id) as total_courses,
                COUNT(DISTINCT ce.student_id) as total_students,
                COUNT(DISTINCT s.id) as total_sessions,
                COUNT(DISTINCT cv.id) as total_videos,
                COUNT(DISTINCT cm.id) as total_materials,
                AVG(cr.rating) as avg_rating
            FROM courses c
            LEFT JOIN course_enrollments ce ON c.id = ce.course_id AND ce.status = 'active'
            LEFT JOIN sessions s ON c.id = s.course_id
            LEFT JOIN course_videos cv ON c.id = cv.course_id AND cv.status = 'active'
            LEFT JOIN course_materials cm ON c.id = cm.course_id AND cm.status = 'active'
            LEFT JOIN course_reviews cr ON c.id = cr.course_id
            WHERE c.instructor_id = ? AND c.status = 'active'
        ");
        $stmt->execute([$_SESSION['user_id']]);
        $general_stats = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // بيانات التسجيل العامة
        $stmt = $conn->prepare("
            SELECT 
                DATE_FORMAT(ce.enrolled_at, '%Y-%m') as month,
                COUNT(*) as enrollments
            FROM course_enrollments ce
            INNER JOIN courses c ON ce.course_id = c.id
            WHERE c.instructor_id = ? AND ce.status = 'active'
            GROUP BY DATE_FORMAT(ce.enrolled_at, '%Y-%m')
            ORDER BY month DESC
            LIMIT 12
        ");
        $stmt->execute([$_SESSION['user_id']]);
        $enrollment_data = array_reverse($stmt->fetchAll(PDO::FETCH_ASSOC));
        
    } catch (PDOException $e) {
        $error_message = 'حدث خطأ أثناء جلب البيانات';
        $general_stats = ['total_courses' => 0, 'total_students' => 0, 'total_sessions' => 0, 'total_videos' => 0, 'total_materials' => 0, 'avg_rating' => 0];
    }
}

include 'includes/header.php';
?>

<!-- رأس الصفحة -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="h3 mb-2">
            <i class="fas fa-chart-bar text-primary me-2"></i>
            إحصائيات الكورسات
        </h2>
        <p class="text-muted mb-0">تحليل شامل لأداء الكورسات والطلاب</p>
    </div>
    <div class="d-flex gap-2">
        <button class="btn btn-outline-primary" onclick="exportReport()">
            <i class="fas fa-download me-1"></i>تصدير التقرير
        </button>
        <button class="btn btn-outline-success" onclick="printReport()">
            <i class="fas fa-print me-1"></i>طباعة
        </button>
    </div>
</div>

<!-- اختيار الكورس -->
<div class="card border-0 shadow-sm mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3 align-items-end">
            <div class="col-md-6">
                <label for="course_id" class="form-label">اختر كورس للتحليل التفصيلي</label>
                <select name="id" id="course_id" class="form-select" onchange="this.form.submit()">
                    <option value="">-- جميع الكورسات --</option>
                    <?php foreach ($instructor_courses as $course): ?>
                    <option value="<?php echo $course['id']; ?>" 
                            <?php echo $selected_course_id == $course['id'] ? 'selected' : ''; ?>>
                        <?php echo htmlspecialchars($course['title']); ?>
                    </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-3">
                <label for="date_range" class="form-label">الفترة الزمنية</label>
                <select id="date_range" class="form-select">
                    <option value="all">جميع الفترات</option>
                    <option value="last_month">الشهر الماضي</option>
                    <option value="last_3_months">آخر 3 أشهر</option>
                    <option value="last_6_months">آخر 6 أشهر</option>
                    <option value="last_year">السنة الماضية</option>
                </select>
            </div>
            <div class="col-md-3">
                <button type="button" class="btn btn-primary w-100" onclick="refreshData()">
                    <i class="fas fa-sync-alt me-1"></i>تحديث البيانات
                </button>
            </div>
        </form>
    </div>
</div>

<?php if ($course_details): ?>
<!-- تفاصيل الكورس المحدد -->
<div class="row g-4 mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-graduation-cap me-2"></i>
                    <?php echo htmlspecialchars($course_details['title']); ?>
                </h5>
            </div>
            <div class="card-body">
                <div class="row g-4">
                    <div class="col-lg-3 col-md-6">
                        <div class="text-center">
                            <div class="bg-success bg-opacity-10 rounded-3 p-3 mb-3 d-inline-block">
                                <i class="fas fa-users text-success fs-2"></i>
                            </div>
                            <h4 class="mb-1"><?php echo $course_details['total_students']; ?></h4>
                            <p class="text-muted mb-0">طالب مسجل</p>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="text-center">
                            <div class="bg-primary bg-opacity-10 rounded-3 p-3 mb-3 d-inline-block">
                                <i class="fas fa-video text-primary fs-2"></i>
                            </div>
                            <h4 class="mb-1"><?php echo $course_details['total_sessions']; ?></h4>
                            <p class="text-muted mb-0">جلسة</p>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="text-center">
                            <div class="bg-info bg-opacity-10 rounded-3 p-3 mb-3 d-inline-block">
                                <i class="fas fa-play text-info fs-2"></i>
                            </div>
                            <h4 class="mb-1"><?php echo $course_details['total_videos']; ?></h4>
                            <p class="text-muted mb-0">فيديو</p>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="text-center">
                            <div class="bg-warning bg-opacity-10 rounded-3 p-3 mb-3 d-inline-block">
                                <i class="fas fa-star text-warning fs-2"></i>
                            </div>
                            <h4 class="mb-1"><?php echo $course_details['avg_rating'] ? number_format($course_details['avg_rating'], 1) : 'N/A'; ?></h4>
                            <p class="text-muted mb-0">متوسط التقييم</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php else: ?>
<!-- الإحصائيات العامة -->
<div class="row g-4 mb-4">
    <div class="col-lg-2 col-md-4 col-sm-6">
        <div class="card border-0 shadow-sm text-center">
            <div class="card-body">
                <div class="bg-primary bg-opacity-10 rounded-3 p-3 mb-3 d-inline-block">
                    <i class="fas fa-graduation-cap text-primary fs-3"></i>
                </div>
                <h4 class="mb-1"><?php echo $general_stats['total_courses']; ?></h4>
                <p class="text-muted mb-0">كورس</p>
            </div>
        </div>
    </div>
    <div class="col-lg-2 col-md-4 col-sm-6">
        <div class="card border-0 shadow-sm text-center">
            <div class="card-body">
                <div class="bg-success bg-opacity-10 rounded-3 p-3 mb-3 d-inline-block">
                    <i class="fas fa-users text-success fs-3"></i>
                </div>
                <h4 class="mb-1"><?php echo $general_stats['total_students']; ?></h4>
                <p class="text-muted mb-0">طالب</p>
            </div>
        </div>
    </div>
    <div class="col-lg-2 col-md-4 col-sm-6">
        <div class="card border-0 shadow-sm text-center">
            <div class="card-body">
                <div class="bg-info bg-opacity-10 rounded-3 p-3 mb-3 d-inline-block">
                    <i class="fas fa-video text-info fs-3"></i>
                </div>
                <h4 class="mb-1"><?php echo $general_stats['total_sessions']; ?></h4>
                <p class="text-muted mb-0">جلسة</p>
            </div>
        </div>
    </div>
    <div class="col-lg-2 col-md-4 col-sm-6">
        <div class="card border-0 shadow-sm text-center">
            <div class="card-body">
                <div class="bg-warning bg-opacity-10 rounded-3 p-3 mb-3 d-inline-block">
                    <i class="fas fa-play text-warning fs-3"></i>
                </div>
                <h4 class="mb-1"><?php echo $general_stats['total_videos']; ?></h4>
                <p class="text-muted mb-0">فيديو</p>
            </div>
        </div>
    </div>
    <div class="col-lg-2 col-md-4 col-sm-6">
        <div class="card border-0 shadow-sm text-center">
            <div class="card-body">
                <div class="bg-secondary bg-opacity-10 rounded-3 p-3 mb-3 d-inline-block">
                    <i class="fas fa-file-alt text-secondary fs-3"></i>
                </div>
                <h4 class="mb-1"><?php echo $general_stats['total_materials']; ?></h4>
                <p class="text-muted mb-0">مادة</p>
            </div>
        </div>
    </div>
    <div class="col-lg-2 col-md-4 col-sm-6">
        <div class="card border-0 shadow-sm text-center">
            <div class="card-body">
                <div class="bg-danger bg-opacity-10 rounded-3 p-3 mb-3 d-inline-block">
                    <i class="fas fa-star text-danger fs-3"></i>
                </div>
                <h4 class="mb-1"><?php echo $general_stats['avg_rating'] ? number_format($general_stats['avg_rating'], 1) : 'N/A'; ?></h4>
                <p class="text-muted mb-0">تقييم</p>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- الرسوم البيانية -->
<div class="row g-4 mb-4">
    <!-- رسم بياني للتسجيلات -->
    <div class="col-lg-8">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-bottom">
                <h6 class="mb-0">
                    <i class="fas fa-chart-line text-primary me-2"></i>
                    تطور التسجيلات الشهرية
                </h6>
            </div>
            <div class="card-body">
                <canvas id="enrollmentChart" height="300"></canvas>
            </div>
        </div>
    </div>
    
    <!-- إحصائيات سريعة -->
    <div class="col-lg-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-white border-bottom">
                <h6 class="mb-0">
                    <i class="fas fa-tachometer-alt text-success me-2"></i>
                    مؤشرات الأداء
                </h6>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span class="text-muted">معدل التسجيل الشهري</span>
                    <strong class="text-primary">
                        <?php 
                        $avg_enrollment = !empty($enrollment_data) ? array_sum(array_column($enrollment_data, 'enrollments')) / count($enrollment_data) : 0;
                        echo number_format($avg_enrollment, 1);
                        ?>
                    </strong>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span class="text-muted">أعلى شهر تسجيل</span>
                    <strong class="text-success">
                        <?php 
                        if (!empty($enrollment_data)) {
                            $max_month = array_reduce($enrollment_data, function($carry, $item) {
                                return ($carry === null || $item['enrollments'] > $carry['enrollments']) ? $item : $carry;
                            });
                            echo $max_month ? $max_month['enrollments'] : 0;
                        } else {
                            echo 0;
                        }
                        ?>
                    </strong>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span class="text-muted">نمو التسجيلات</span>
                    <strong class="text-info">
                        <?php 
                        if (count($enrollment_data) >= 2) {
                            $current = end($enrollment_data)['enrollments'];
                            $previous = prev($enrollment_data)['enrollments'];
                            $growth = $previous > 0 ? (($current - $previous) / $previous) * 100 : 0;
                            echo ($growth >= 0 ? '+' : '') . number_format($growth, 1) . '%';
                        } else {
                            echo 'N/A';
                        }
                        ?>
                    </strong>
                </div>
                
                <?php if ($course_details): ?>
                <hr>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span class="text-muted">معدل الحضور</span>
                    <strong class="text-warning">
                        <?php 
                        $total_sessions = count($session_data);
                        $total_attendees = array_sum(array_column($session_data, 'attendees'));
                        $avg_attendance = $total_sessions > 0 ? $total_attendees / $total_sessions : 0;
                        echo number_format($avg_attendance, 1);
                        ?>
                    </strong>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                    <span class="text-muted">معدل الدرجات</span>
                    <strong class="text-danger">
                        <?php 
                        $grades = array_filter(array_column($performance_data, 'avg_grade'));
                        $avg_grade = !empty($grades) ? array_sum($grades) / count($grades) : 0;
                        echo number_format($avg_grade, 1);
                        ?>
                    </strong>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php if ($course_details && !empty($session_data)): ?>
<!-- جدول الجلسات -->
<div class="row g-4 mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-bottom">
                <h6 class="mb-0">
                    <i class="fas fa-calendar text-info me-2"></i>
                    آخر الجلسات
                </h6>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>عنوان الجلسة</th>
                                <th>التاريخ</th>
                                <th>الحالة</th>
                                <th>عدد الحضور</th>
                                <th>معدل الحضور</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($session_data as $session): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($session['title']); ?></td>
                                <td><?php echo date('Y-m-d', strtotime($session['session_date'])); ?></td>
                                <td>
                                    <?php
                                    $status_class = [
                                        'scheduled' => 'warning',
                                        'live' => 'danger',
                                        'completed' => 'success',
                                        'cancelled' => 'secondary'
                                    ];
                                    $status_text = [
                                        'scheduled' => 'مجدولة',
                                        'live' => 'مباشرة',
                                        'completed' => 'مكتملة',
                                        'cancelled' => 'ملغية'
                                    ];
                                    ?>
                                    <span class="badge bg-<?php echo $status_class[$session['status']] ?? 'secondary'; ?>">
                                        <?php echo $status_text[$session['status']] ?? $session['status']; ?>
                                    </span>
                                </td>
                                <td><?php echo $session['attendees']; ?></td>
                                <td>
                                    <?php 
                                    $attendance_rate = $course_details['total_students'] > 0 ? 
                                        ($session['attendees'] / $course_details['total_students']) * 100 : 0;
                                    ?>
                                    <div class="d-flex align-items-center">
                                        <span class="me-2"><?php echo number_format($attendance_rate, 1); ?>%</span>
                                        <div class="progress flex-grow-1" style="height: 6px;">
                                            <div class="progress-bar bg-success" style="width: <?php echo $attendance_rate; ?>%"></div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// رسم بياني للتسجيلات
const enrollmentData = <?php echo json_encode($enrollment_data); ?>;
const ctx = document.getElementById('enrollmentChart').getContext('2d');

new Chart(ctx, {
    type: 'line',
    data: {
        labels: enrollmentData.map(item => item.month),
        datasets: [{
            label: 'التسجيلات',
            data: enrollmentData.map(item => item.enrollments),
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.1)',
            tension: 0.4,
            fill: true
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    stepSize: 1
                }
            }
        }
    }
});

// دوال الإجراءات
function refreshData() {
    location.reload();
}

function exportReport() {
    const courseId = document.getElementById('course_id').value;
    const url = courseId ? `export-analytics.php?course_id=${courseId}` : 'export-analytics.php';
    window.open(url, '_blank');
}

function printReport() {
    window.print();
}

// تحديث البيانات كل 5 دقائق
setInterval(function() {
    // يمكن إضافة AJAX لتحديث البيانات
    console.log('تحديث البيانات...');
}, 300000);
</script>

<style>
@media print {
    .no-print,
    .btn,
    .card-header {
        display: none !important;
    }
    
    .card {
        border: 1px solid #ddd !important;
        box-shadow: none !important;
    }
}
</style>

<?php include 'includes/footer.php'; ?>
