<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات المدرب
if (!isLoggedIn() || !isInstructor()) {
    header('Location: ../login.php');
    exit;
}

$pageTitle = 'تتبع تقدم الطلاب';
$breadcrumbs = [
    ['title' => 'الطلاب', 'url' => 'students.php'],
    ['title' => 'تتبع التقدم']
];

// الحصول على معرف الطالب والكورس
$student_id = $_GET['id'] ?? '';
$course_id = $_GET['course_id'] ?? '';

// جلب الكورسات الخاصة بالمدرب
try {
    $stmt = $conn->prepare("
        SELECT id, title 
        FROM courses 
        WHERE instructor_id = ? AND status = 'active'
        ORDER BY title
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $instructor_courses = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $instructor_courses = [];
}

// بيانات التقدم - تهيئة افتراضية
$progress_data = [
    'attendance' => [],
    'grades' => [],
    'attendance_rate' => 0,
    'avg_grade' => 0,
    'total_attended' => 0,
    'total_sessions' => 0,
    'total_grades' => 0
];
$student_details = null;
$course_details = [
    'total_videos' => 0,
    'total_materials' => 0
];

if ($student_id && $course_id) {
    try {
        // تفاصيل الطالب والكورس
        $stmt = $conn->prepare("
            SELECT 
                u.*,
                c.title as course_title,
                ce.enrolled_at,
                ce.progress_percentage,
                ce.status as enrollment_status
            FROM users u
            INNER JOIN course_enrollments ce ON u.id = ce.student_id
            INNER JOIN courses c ON ce.course_id = c.id
            WHERE u.id = ? AND c.id = ? AND c.instructor_id = ?
        ");
        $stmt->execute([$student_id, $course_id, $_SESSION['user_id']]);
        $student_details = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($student_details) {
            // تفاصيل الكورس مع إحصائيات شاملة
            $stmt = $conn->prepare("
                SELECT
                    c.*,
                    COALESCE(session_stats.total_sessions, 0) as total_sessions,
                    COALESCE(video_stats.total_videos, 0) as total_videos,
                    COALESCE(material_stats.total_materials, 0) as total_materials
                FROM courses c
                LEFT JOIN (
                    SELECT course_id, COUNT(*) as total_sessions
                    FROM sessions
                    WHERE course_id = ?
                    GROUP BY course_id
                ) session_stats ON c.id = session_stats.course_id
                LEFT JOIN (
                    SELECT course_id, COUNT(*) as total_videos
                    FROM course_videos
                    WHERE course_id = ? AND status = 'active'
                    GROUP BY course_id
                ) video_stats ON c.id = video_stats.course_id
                LEFT JOIN (
                    SELECT course_id, COUNT(*) as total_materials
                    FROM course_materials
                    WHERE course_id = ? AND status = 'active'
                    GROUP BY course_id
                ) material_stats ON c.id = material_stats.course_id
                WHERE c.id = ? AND c.instructor_id = ?
            ");
            $stmt->execute([$course_id, $course_id, $course_id, $course_id, $_SESSION['user_id']]);
            $course_details = $stmt->fetch(PDO::FETCH_ASSOC);

            // التأكد من وجود البيانات
            if (!$course_details) {
                $course_details = [
                    'total_sessions' => 0,
                    'total_videos' => 0,
                    'total_materials' => 0
                ];
            }
            
            // بيانات الحضور مع التحقق من وجود الجداول
            $attendance_data = [];
            try {
                $stmt = $conn->prepare("
                    SELECT
                        s.id as session_id,
                        s.title as session_title,
                        s.session_date,
                        s.start_time,
                        s.duration_minutes,
                        CASE WHEN sa.user_id IS NOT NULL THEN 1 ELSE 0 END as attended
                    FROM sessions s
                    LEFT JOIN session_attendees sa ON s.id = sa.session_id AND sa.user_id = ?
                    WHERE s.course_id = ?
                    ORDER BY s.session_date DESC
                ");
                $stmt->execute([$student_id, $course_id]);
                $attendance_data = $stmt->fetchAll(PDO::FETCH_ASSOC);
            } catch (PDOException $e) {
                // إذا لم يوجد جدول session_attendees، إنشاء بيانات افتراضية
                $stmt = $conn->prepare("
                    SELECT
                        id as session_id,
                        title as session_title,
                        session_date,
                        start_time,
                        duration_minutes,
                        0 as attended
                    FROM sessions
                    WHERE course_id = ?
                    ORDER BY session_date DESC
                ");
                $stmt->execute([$course_id]);
                $attendance_data = $stmt->fetchAll(PDO::FETCH_ASSOC);
            }

            // بيانات الدرجات مع التحقق من وجود الجدول
            $grades_data = [];
            try {
                $stmt = $conn->prepare("
                    SELECT
                        assignment_name,
                        grade,
                        max_grade,
                        grade_date,
                        notes
                    FROM student_grades
                    WHERE student_id = ? AND course_id = ?
                    ORDER BY grade_date DESC
                ");
                $stmt->execute([$student_id, $course_id]);
                $grades_data = $stmt->fetchAll(PDO::FETCH_ASSOC);
            } catch (PDOException $e) {
                // إذا لم يوجد جدول student_grades، إنشاء بيانات افتراضية
                $grades_data = [];
            }
            
            // حساب الإحصائيات بشكل آمن
            $total_attended = 0;
            $total_sessions = count($attendance_data);

            if (!empty($attendance_data)) {
                $total_attended = array_sum(array_column($attendance_data, 'attended'));
            }

            $attendance_rate = $total_sessions > 0 ? ($total_attended / $total_sessions) * 100 : 0;

            $total_grades = count($grades_data);
            $avg_grade = 0;

            if (!empty($grades_data)) {
                $total_grade_points = 0;
                $total_max_points = 0;

                foreach ($grades_data as $grade) {
                    $total_grade_points += $grade['grade'];
                    $total_max_points += $grade['max_grade'];
                }

                $avg_grade = $total_max_points > 0 ? ($total_grade_points / $total_max_points) * 100 : 0;
            }

            // تحديث بيانات التقدم
            $progress_data = [
                'attendance' => $attendance_data,
                'grades' => $grades_data,
                'attendance_rate' => round($attendance_rate, 1),
                'avg_grade' => round($avg_grade, 1),
                'total_attended' => $total_attended,
                'total_sessions' => $total_sessions,
                'total_grades' => $total_grades
            ];
        }

    } catch (PDOException $e) {
        $error_message = 'حدث خطأ أثناء جلب البيانات: ' . $e->getMessage();
        error_log("Student progress error: " . $e->getMessage());

        // إعادة تعيين البيانات الافتراضية في حالة الخطأ
        $progress_data = [
            'attendance' => [],
            'grades' => [],
            'attendance_rate' => 0,
            'avg_grade' => 0,
            'total_attended' => 0,
            'total_sessions' => 0,
            'total_grades' => 0
        ];

        $course_details = [
            'total_sessions' => 0,
            'total_videos' => 0,
            'total_materials' => 0
        ];
    }
} else {
    // جلب جميع الطلاب للاختيار
    try {
        $stmt = $conn->prepare("
            SELECT 
                u.id,
                u.name,
                u.email,
                c.id as course_id,
                c.title as course_title,
                ce.progress_percentage,
                AVG(sg.grade) as avg_grade
            FROM users u
            INNER JOIN course_enrollments ce ON u.id = ce.student_id
            INNER JOIN courses c ON ce.course_id = c.id
            LEFT JOIN student_grades sg ON u.id = sg.student_id AND c.id = sg.course_id
            WHERE c.instructor_id = ? AND ce.status = 'active'
            GROUP BY u.id, c.id
            ORDER BY u.name, c.title
        ");
        $stmt->execute([$_SESSION['user_id']]);
        $all_students = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        $all_students = [];
    }
}

include 'includes/header.php';
?>

<!-- رأس الصفحة -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="h3 mb-2">
            <i class="fas fa-chart-line text-success me-2"></i>
            تتبع تقدم الطلاب
        </h2>
        <p class="text-muted mb-0">متابعة تفصيلية لتقدم الطلاب في الكورسات</p>
    </div>
    <div class="d-flex gap-2">
        <a href="students.php" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>العودة للطلاب
        </a>
        <?php if ($student_details): ?>
        <button class="btn btn-primary" onclick="generateReport()">
            <i class="fas fa-file-pdf me-1"></i>تقرير مفصل
        </button>
        <?php endif; ?>
    </div>
</div>

<?php if (!$student_details): ?>
<!-- اختيار الطالب -->
<div class="card border-0 shadow-sm mb-4">
    <div class="card-header bg-white border-bottom">
        <h5 class="mb-0">
            <i class="fas fa-user-graduate me-2"></i>
            اختر طالباً لعرض تقدمه
        </h5>
    </div>
    <div class="card-body">
        <?php if (empty($all_students)): ?>
        <div class="text-center py-4">
            <i class="fas fa-user-graduate text-muted" style="font-size: 3rem;"></i>
            <h5 class="mt-3 text-muted">لا توجد طلاب</h5>
            <p class="text-muted">لا يوجد طلاب مسجلون في كورساتك حالياً</p>
        </div>
        <?php else: ?>
        <div class="row g-3">
            <?php foreach ($all_students as $student): ?>
            <div class="col-lg-4 col-md-6">
                <div class="card border h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-3">
                            <div class="avatar-sm bg-primary bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center me-3">
                                <i class="fas fa-user text-primary"></i>
                            </div>
                            <div>
                                <h6 class="mb-0"><?php echo htmlspecialchars($student['name']); ?></h6>
                                <small class="text-muted"><?php echo htmlspecialchars($student['email']); ?></small>
                            </div>
                        </div>
                        
                        <p class="text-muted mb-2">
                            <i class="fas fa-graduation-cap me-1"></i>
                            <?php echo htmlspecialchars($student['course_title']); ?>
                        </p>
                        
                        <div class="mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                <small class="text-muted">التقدم</small>
                                <small class="text-muted"><?php echo number_format($student['progress_percentage'], 1); ?>%</small>
                            </div>
                            <div class="progress" style="height: 6px;">
                                <div class="progress-bar bg-success" style="width: <?php echo $student['progress_percentage']; ?>%"></div>
                            </div>
                        </div>
                        
                        <?php if ($student['avg_grade']): ?>
                        <p class="text-muted mb-3">
                            <i class="fas fa-star me-1"></i>
                            متوسط الدرجات: <strong><?php echo number_format($student['avg_grade'], 1); ?></strong>
                        </p>
                        <?php endif; ?>
                        
                        <a href="?id=<?php echo $student['id']; ?>&course_id=<?php echo $student['course_id']; ?>" 
                           class="btn btn-primary btn-sm w-100">
                            <i class="fas fa-chart-line me-1"></i>عرض التقدم التفصيلي
                        </a>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>
    </div>
</div>

<?php else: ?>
<!-- تفاصيل تقدم الطالب -->
<div class="row g-4 mb-4">
    <!-- معلومات الطالب -->
    <div class="col-lg-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-primary text-white">
                <h6 class="mb-0">
                    <i class="fas fa-user me-2"></i>
                    معلومات الطالب
                </h6>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <div class="avatar-lg bg-primary bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3">
                        <i class="fas fa-user text-primary fs-2"></i>
                    </div>
                    <h5 class="mb-1"><?php echo htmlspecialchars($student_details['name']); ?></h5>
                    <p class="text-muted mb-0"><?php echo htmlspecialchars($student_details['email']); ?></p>
                    <?php if ($student_details['phone']): ?>
                    <p class="text-muted mb-0">
                        <i class="fas fa-phone me-1"></i><?php echo htmlspecialchars($student_details['phone']); ?>
                    </p>
                    <?php endif; ?>
                </div>
                
                <hr>
                
                <div class="row g-3 text-center">
                    <div class="col-6">
                        <h6 class="text-primary mb-1"><?php echo date('Y-m-d', strtotime($student_details['enrolled_at'])); ?></h6>
                        <small class="text-muted">تاريخ التسجيل</small>
                    </div>
                    <div class="col-6">
                        <h6 class="text-success mb-1"><?php echo number_format($student_details['progress_percentage'], 1); ?>%</h6>
                        <small class="text-muted">التقدم العام</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- إحصائيات سريعة -->
    <div class="col-lg-8">
        <div class="row g-3">
            <div class="col-md-3">
                <div class="card border-0 shadow-sm text-center">
                    <div class="card-body">
                        <div class="bg-success bg-opacity-10 rounded-3 p-3 mb-2 d-inline-block">
                            <i class="fas fa-calendar-check text-success fs-4"></i>
                        </div>
                        <h5 class="mb-1"><?php echo $progress_data['total_attended'] ?? 0; ?>/<?php echo $progress_data['total_sessions'] ?? 0; ?></h5>
                        <p class="text-muted mb-0">الحضور</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-0 shadow-sm text-center">
                    <div class="card-body">
                        <div class="bg-info bg-opacity-10 rounded-3 p-3 mb-2 d-inline-block">
                            <i class="fas fa-percentage text-info fs-4"></i>
                        </div>
                        <h5 class="mb-1"><?php echo number_format($progress_data['attendance_rate'] ?? 0, 1); ?>%</h5>
                        <p class="text-muted mb-0">معدل الحضور</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-0 shadow-sm text-center">
                    <div class="card-body">
                        <div class="bg-warning bg-opacity-10 rounded-3 p-3 mb-2 d-inline-block">
                            <i class="fas fa-star text-warning fs-4"></i>
                        </div>
                        <h5 class="mb-1"><?php echo number_format($progress_data['avg_grade'] ?? 0, 1); ?></h5>
                        <p class="text-muted mb-0">متوسط الدرجات</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-0 shadow-sm text-center">
                    <div class="card-body">
                        <div class="bg-primary bg-opacity-10 rounded-3 p-3 mb-2 d-inline-block">
                            <i class="fas fa-tasks text-primary fs-4"></i>
                        </div>
                        <h5 class="mb-1"><?php echo $progress_data['total_grades'] ?? 0; ?></h5>
                        <p class="text-muted mb-0">عدد التقييمات</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- شريط التقدم التفصيلي -->
        <div class="card border-0 shadow-sm mt-3">
            <div class="card-body">
                <h6 class="mb-3">التقدم في الكورس</h6>
                <div class="progress mb-2" style="height: 20px;">
                    <div class="progress-bar bg-success" style="width: <?php echo $student_details['progress_percentage']; ?>%">
                        <?php echo number_format($student_details['progress_percentage'], 1); ?>%
                    </div>
                </div>
                <div class="row g-2 text-center">
                    <div class="col-4">
                        <small class="text-muted">الجلسات: <?php echo $progress_data['total_attended'] ?? 0; ?>/<?php echo $progress_data['total_sessions'] ?? 0; ?></small>
                    </div>
                    <div class="col-4">
                        <small class="text-muted">الفيديوهات: <?php echo $course_details['total_videos'] ?? 0; ?></small>
                    </div>
                    <div class="col-4">
                        <small class="text-muted">المواد: <?php echo $course_details['total_materials'] ?? 0; ?></small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- تفاصيل الحضور والدرجات -->
<div class="row g-4">
    <!-- سجل الحضور -->
    <div class="col-lg-6">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-bottom">
                <h6 class="mb-0">
                    <i class="fas fa-calendar-alt text-success me-2"></i>
                    سجل الحضور
                </h6>
            </div>
            <div class="card-body p-0">
                <?php if (empty($progress_data['attendance'])): ?>
                <div class="text-center py-4">
                    <i class="fas fa-calendar text-muted" style="font-size: 2rem;"></i>
                    <p class="text-muted mt-2 mb-0">لا توجد جلسات مجدولة</p>
                </div>
                <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>الجلسة</th>
                                <th>التاريخ</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($progress_data['attendance'] as $session): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($session['session_title']); ?></td>
                                <td><?php echo date('Y-m-d', strtotime($session['session_date'])); ?></td>
                                <td>
                                    <?php if ($session['attended']): ?>
                                    <span class="badge bg-success">
                                        <i class="fas fa-check me-1"></i>حضر
                                    </span>
                                    <?php else: ?>
                                    <span class="badge bg-danger">
                                        <i class="fas fa-times me-1"></i>غاب
                                    </span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- سجل الدرجات -->
    <div class="col-lg-6">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-bottom">
                <h6 class="mb-0">
                    <i class="fas fa-star text-warning me-2"></i>
                    سجل الدرجات
                </h6>
            </div>
            <div class="card-body p-0">
                <?php if (empty($progress_data['grades'])): ?>
                <div class="text-center py-4">
                    <i class="fas fa-star text-muted" style="font-size: 2rem;"></i>
                    <p class="text-muted mt-2 mb-0">لا توجد درجات مسجلة</p>
                </div>
                <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>التقييم</th>
                                <th>الدرجة</th>
                                <th>التاريخ</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($progress_data['grades'] as $grade): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($grade['assignment_name']); ?></td>
                                <td>
                                    <span class="badge bg-primary fs-6">
                                        <?php echo $grade['grade']; ?>/<?php echo $grade['max_grade']; ?>
                                    </span>
                                    <small class="text-muted ms-1">
                                        (<?php echo number_format(($grade['grade'] / $grade['max_grade']) * 100, 1); ?>%)
                                    </small>
                                </td>
                                <td><?php echo date('Y-m-d', strtotime($grade['grade_date'])); ?></td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<script>
function generateReport() {
    const studentId = <?php echo $student_id; ?>;
    const courseId = <?php echo $course_id; ?>;
    window.open(`student-report.php?student_id=${studentId}&course_id=${courseId}`, '_blank');
}

// تحديث البيانات كل دقيقتين
setInterval(function() {
    // يمكن إضافة AJAX لتحديث البيانات
    console.log('تحديث بيانات التقدم...');
}, 120000);
</script>

<style>
.avatar-sm {
    width: 40px;
    height: 40px;
}

.avatar-lg {
    width: 80px;
    height: 80px;
}

.progress {
    border-radius: 10px;
}

.progress-bar {
    border-radius: 10px;
}

.card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15) !important;
}
</style>

<?php include 'includes/footer.php'; ?>
