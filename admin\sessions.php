<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من صلاحيات المديرif (!isLoggedIn() || !isAdmin()) {    header('Location: ../login.php');    exit;}
$error = '';
$success = '';

// إنشاء جدول الأنشطة إذا لم يكن موجوداً
try {
    $conn->exec("
        CREATE TABLE IF NOT EXISTS activity_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NULL,
            user_type ENUM('admin', 'instructor', 'student', 'visitor') DEFAULT 'visitor',
            activity_type VARCHAR(100) NOT NULL,
            description TEXT NOT NULL,
            ip_address VARCHAR(45),
            user_agent TEXT,
            page_url VARCHAR(500),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_activity_type (activity_type),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
} catch (PDOException $e) {
    // الجدول موجود بالفعل
}

// دالة لتسجيل النشاط
function logActivity($user_id, $user_type, $activity_type, $description, $page_url = '') {
    global $conn;
    try {
        $stmt = $conn->prepare("
            INSERT INTO activity_logs (user_id, user_type, activity_type, description, ip_address, user_agent, page_url)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute([
            $user_id,
            $user_type,
            $activity_type,
            $description,
            $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            $page_url ?: $_SERVER['REQUEST_URI'] ?? ''
        ]);
    } catch (PDOException $e) {
        error_log("Error logging activity: " . $e->getMessage());
    }
}

// تسجيل زيارة المدير لهذه الصفحة
logActivity($_SESSION['user_id'], 'admin', 'page_visit', 'زيارة صفحة إدارة الأنشطة', '/admin/sessions.php');

// معالجة الفلترة والبحث
$filter_type = $_GET['filter'] ?? 'all';
$search_term = $_GET['search'] ?? '';
$date_from = $_GET['date_from'] ?? '';
$date_to = $_GET['date_to'] ?? '';
$user_type_filter = $_GET['user_type'] ?? 'all';

// بناء استعلام الأنشطة
$where_conditions = [];
$params = [];

if ($filter_type !== 'all') {
    $where_conditions[] = "al.activity_type = ?";
    $params[] = $filter_type;
}

if (!empty($search_term)) {
    $where_conditions[] = "(al.description LIKE ? OR u.name LIKE ? OR u.email LIKE ?)";
    $params[] = "%$search_term%";
    $params[] = "%$search_term%";
    $params[] = "%$search_term%";
}

if (!empty($date_from)) {
    $where_conditions[] = "DATE(al.created_at) >= ?";
    $params[] = $date_from;
}

if (!empty($date_to)) {
    $where_conditions[] = "DATE(al.created_at) <= ?";
    $params[] = $date_to;
}

if ($user_type_filter !== 'all') {
    $where_conditions[] = "al.user_type = ?";
    $params[] = $user_type_filter;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// جلب جميع الأنشطة
try {
    $stmt = $conn->prepare("
        SELECT
            al.*,
            u.name as user_name,
            u.email as user_email,
            u.role as user_role
        FROM activity_logs al
        LEFT JOIN users u ON al.user_id = u.id
        $where_clause
        ORDER BY al.created_at DESC
        LIMIT 1000
    ");
    $stmt->execute($params);
    $activities = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // إحصائيات الأنشطة
    $stats_stmt = $conn->prepare("
        SELECT
            COUNT(*) as total_activities,
            COUNT(DISTINCT al.user_id) as unique_users,
            COUNT(CASE WHEN al.user_type = 'visitor' THEN 1 END) as visitor_activities,
            COUNT(CASE WHEN al.user_type = 'student' THEN 1 END) as student_activities,
            COUNT(CASE WHEN al.user_type = 'instructor' THEN 1 END) as instructor_activities,
            COUNT(CASE WHEN al.user_type = 'admin' THEN 1 END) as admin_activities,
            COUNT(CASE WHEN DATE(al.created_at) = CURDATE() THEN 1 END) as today_activities
        FROM activity_logs al
        LEFT JOIN users u ON al.user_id = u.id
        $where_clause
    ");
    $stats_stmt->execute($params);
    $stats = $stats_stmt->fetch(PDO::FETCH_ASSOC);

} catch (PDOException $e) {
    $activities = [];
    $stats = [
        'total_activities' => 0,
        'unique_users' => 0,
        'visitor_activities' => 0,
        'student_activities' => 0,
        'instructor_activities' => 0,
        'admin_activities' => 0,
        'today_activities' => 0
    ];
    $error = 'حدث خطأ في جلب الأنشطة: ' . $e->getMessage();
}

// أنواع الأنشطة المختلفة
$activity_types = [
    'page_visit' => 'زيارة صفحة',
    'login' => 'تسجيل دخول',
    'logout' => 'تسجيل خروج',
    'register' => 'إنشاء حساب',
    'course_view' => 'عرض كورس',
    'course_enroll' => 'التسجيل في كورس',
    'session_join' => 'الانضمام لجلسة',
    'file_download' => 'تحميل ملف',
    'profile_update' => 'تحديث الملف الشخصي',
    'password_change' => 'تغيير كلمة المرور',
    'course_create' => 'إنشاء كورس',
    'session_create' => 'إنشاء جلسة',
    'user_management' => 'إدارة المستخدمين',
    'search' => 'بحث',
    'error' => 'خطأ',
    'security_alert' => 'تنبيه أمني'
];

$pageTitle = 'إدارة الأنشطة والجلسات';
?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="إدارة الأنشطة والجلسات - لوحة تحكم المدير">
    <title>إدارة الأنشطة والجلسات - المدير</title>

    <!-- الخطوط المحسنة -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Bootstrap و Font Awesome -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- DataTables -->
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">

    <!-- AOS Animation -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

    <!-- ملفات CSS المخصصة -->
    <link href="../assets/css/main.css" rel="stylesheet">
    <link href="../assets/css/components.css" rel="stylesheet">

    <style>
        body {
            background: var(--gray-50);
            font-family: var(--font-family-arabic);
        }

        .admin-header {
            background: var(--gradient-primary);
            color: var(--white);
            padding: var(--spacing-8) 0;
            margin-bottom: var(--spacing-8);
            position: relative;
            overflow: hidden;
        }

        .admin-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(255,255,255,0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255,255,255,0.1) 0%, transparent 50%);
            animation: particleFloat 8s ease-in-out infinite;
        }

        .admin-header-content {
            position: relative;
            z-index: 2;
        }

        .stats-cards {
            margin-top: -var(--spacing-12);
            position: relative;
            z-index: 3;
        }

        .stat-card-admin {
            background: var(--white);
            border-radius: var(--border-radius-2xl);
            padding: var(--spacing-6);
            box-shadow: var(--shadow-lg);
            border: var(--border-width-1) solid var(--gray-200);
            transition: var(--transition-normal);
            height: 100%;
        }

        .stat-card-admin:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow-2xl);
        }

        .stat-icon-admin {
            width: 60px;
            height: 60px;
            border-radius: var(--border-radius-2xl);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: var(--spacing-4);
            font-size: var(--font-size-2xl);
            color: var(--white);
        }

        .stat-number-admin {
            font-size: var(--font-size-4xl);
            font-weight: var(--font-weight-extrabold);
            color: var(--gray-900);
            line-height: 1;
            margin-bottom: var(--spacing-2);
        }

        .stat-label-admin {
            font-size: var(--font-size-lg);
            font-weight: var(--font-weight-semibold);
            color: var(--gray-700);
        }

        .main-card {
            background: var(--white);
            border-radius: var(--border-radius-2xl);
            box-shadow: var(--shadow-md);
            border: var(--border-width-1) solid var(--gray-200);
            overflow: hidden;
        }

        .main-card-header {
            background: var(--gray-50);
            border-bottom: var(--border-width-1) solid var(--gray-200);
            padding: var(--spacing-6);
        }

        .main-card-body {
            padding: var(--spacing-6);
        }

        .filter-card {
            background: var(--white);
            border-radius: var(--border-radius-xl);
            padding: var(--spacing-6);
            box-shadow: var(--shadow-sm);
            border: var(--border-width-1) solid var(--gray-200);
            margin-bottom: var(--spacing-6);
        }

        .activity-item {
            background: var(--white);
            border: var(--border-width-1) solid var(--gray-200);
            border-radius: var(--border-radius-xl);
            padding: var(--spacing-4);
            margin-bottom: var(--spacing-3);
            transition: var(--transition-normal);
            position: relative;
        }

        .activity-item:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
            border-color: var(--primary-light);
        }

        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: var(--border-radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-lg);
            color: var(--white);
            margin-left: var(--spacing-3);
        }

        .activity-type-page_visit { background: var(--info-color); }
        .activity-type-login { background: var(--success-color); }
        .activity-type-logout { background: var(--warning-color); }
        .activity-type-register { background: var(--primary-color); }
        .activity-type-course_view { background: var(--purple-500); }
        .activity-type-course_enroll { background: var(--green-500); }
        .activity-type-session_join { background: var(--blue-500); }
        .activity-type-file_download { background: var(--indigo-500); }
        .activity-type-error { background: var(--danger-color); }
        .activity-type-security_alert { background: var(--red-600); }

        .user-type-badge {
            padding: var(--spacing-1) var(--spacing-3);
            border-radius: var(--border-radius-full);
            font-size: var(--font-size-xs);
            font-weight: var(--font-weight-semibold);
            text-transform: uppercase;
        }

        .user-type-admin { background: rgba(239, 68, 68, 0.1); color: var(--red-600); }
        .user-type-instructor { background: rgba(59, 130, 246, 0.1); color: var(--blue-600); }
        .user-type-student { background: rgba(34, 197, 94, 0.1); color: var(--green-600); }
        .user-type-visitor { background: rgba(107, 114, 128, 0.1); color: var(--gray-600); }

        .time-ago {
            font-size: var(--font-size-sm);
            color: var(--gray-500);
        }

        .ip-address {
            font-family: 'Courier New', monospace;
            font-size: var(--font-size-xs);
            color: var(--gray-400);
        }

        @media (max-width: 768px) {
            .admin-header {
                padding: var(--spacing-6) 0;
            }

            .stats-cards {
                margin-top: -var(--spacing-8);
            }

            .activity-item {
                padding: var(--spacing-3);
            }
        }
    </style>
</head>
<body>
    <!-- رأس الصفحة -->
    <div class="admin-header">
        <div class="container">
            <div class="admin-header-content">
                <div class="row align-items-center">
                    <div class="col-md-8" data-aos="fade-right">
                        <h1 class="display-5 fw-bold mb-3">إدارة الأنشطة والجلسات</h1>
                        <p class="lead mb-0 opacity-90">مراقبة جميع أنشطة المستخدمين والزوار في المنصة</p>
                    </div>
                    <div class="col-md-4 text-center" data-aos="fade-left">
                        <i class="fas fa-chart-line" style="font-size: 5rem; opacity: 0.3;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- بطاقات الإحصائيات -->
        <div class="stats-cards">
            <div class="row g-4 mb-5">
                <div class="col-lg-3 col-md-6" data-aos="fade-up" data-aos-delay="100">
                    <div class="stat-card-admin">
                        <div class="stat-icon-admin bg-primary">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <div class="stat-number-admin counter" data-target="<?php echo $stats['total_activities']; ?>">0</div>
                        <div class="stat-label-admin">إجمالي الأنشطة</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6" data-aos="fade-up" data-aos-delay="200">
                    <div class="stat-card-admin">
                        <div class="stat-icon-admin bg-success">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-number-admin counter" data-target="<?php echo $stats['unique_users']; ?>">0</div>
                        <div class="stat-label-admin">مستخدمون فريدون</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6" data-aos="fade-up" data-aos-delay="300">
                    <div class="stat-card-admin">
                        <div class="stat-icon-admin bg-warning">
                            <i class="fas fa-eye"></i>
                        </div>
                        <div class="stat-number-admin counter" data-target="<?php echo $stats['visitor_activities']; ?>">0</div>
                        <div class="stat-label-admin">أنشطة الزوار</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6" data-aos="fade-up" data-aos-delay="400">
                    <div class="stat-card-admin">
                        <div class="stat-icon-admin bg-info">
                            <i class="fas fa-calendar-day"></i>
                        </div>
                        <div class="stat-number-admin counter" data-target="<?php echo $stats['today_activities']; ?>">0</div>
                        <div class="stat-label-admin">أنشطة اليوم</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- الرسائل -->
        <?php if ($error): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert" data-aos="fade-down">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?php echo htmlspecialchars($error); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($success): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert" data-aos="fade-down">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo htmlspecialchars($success); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- أدوات الفلترة والبحث -->
        <div class="filter-card" data-aos="fade-up">
            <form method="GET" action="" class="row g-3">
                <div class="col-md-3">
                    <label for="search" class="form-label">البحث</label>
                    <input type="text" class="form-control" id="search" name="search"
                           value="<?php echo htmlspecialchars($search_term); ?>"
                           placeholder="البحث في الأنشطة...">
                </div>
                <div class="col-md-2">
                    <label for="filter" class="form-label">نوع النشاط</label>
                    <select class="form-select" id="filter" name="filter">
                        <option value="all" <?php echo $filter_type === 'all' ? 'selected' : ''; ?>>جميع الأنشطة</option>
                        <?php foreach ($activity_types as $type => $label): ?>
                            <option value="<?php echo $type; ?>" <?php echo $filter_type === $type ? 'selected' : ''; ?>>
                                <?php echo $label; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="user_type" class="form-label">نوع المستخدم</label>
                    <select class="form-select" id="user_type" name="user_type">
                        <option value="all" <?php echo $user_type_filter === 'all' ? 'selected' : ''; ?>>الكل</option>
                        <option value="visitor" <?php echo $user_type_filter === 'visitor' ? 'selected' : ''; ?>>زائر</option>
                        <option value="student" <?php echo $user_type_filter === 'student' ? 'selected' : ''; ?>>طالب</option>
                        <option value="instructor" <?php echo $user_type_filter === 'instructor' ? 'selected' : ''; ?>>مدرب</option>
                        <option value="admin" <?php echo $user_type_filter === 'admin' ? 'selected' : ''; ?>>مدير</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="date_from" class="form-label">من تاريخ</label>
                    <input type="date" class="form-control" id="date_from" name="date_from"
                           value="<?php echo htmlspecialchars($date_from); ?>">
                </div>
                <div class="col-md-2">
                    <label for="date_to" class="form-label">إلى تاريخ</label>
                    <input type="date" class="form-control" id="date_to" name="date_to"
                           value="<?php echo htmlspecialchars($date_to); ?>">
                </div>
                <div class="col-md-1">
                    <label class="form-label">&nbsp;</label>
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </form>
        </div>
