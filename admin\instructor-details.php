<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من تسجيل الدخول كمدير
if (!isLoggedIn() || !isAdmin()) {
    header('Location: ../login.php');
    exit;
}

$error = '';
$success = '';
$instructor_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

// التحقق من وجود المدرب
try {
    $stmt = $conn->prepare("
        SELECT u.*,
        (SELECT COUNT(*) FROM courses WHERE instructor_id = u.id) as course_count,
        (SELECT COUNT(DISTINCT ce.student_id) FROM course_enrollments ce JOIN courses c ON ce.course_id = c.id WHERE c.instructor_id = u.id) as student_count,
        (SELECT COUNT(*) FROM sessions s JOIN courses c ON s.course_id = c.id WHERE c.instructor_id = u.id) as session_count
        FROM users u
        WHERE u.id = ? AND u.role = 'instructor'
    ");
    $stmt->execute([$instructor_id]);
    $instructor = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$instructor) {
        die('المدرب غير موجود');
    }

    // جلب الكورسات الخاصة بالمدرب
    $stmt = $conn->prepare("
        SELECT c.*,
        (SELECT COUNT(*) FROM course_enrollments WHERE course_id = c.id) as enrolled_students,
        (SELECT COUNT(*) FROM sessions WHERE course_id = c.id) as total_sessions
        FROM courses c
        WHERE c.instructor_id = ?
        ORDER BY c.created_at DESC
    ");
    $stmt->execute([$instructor_id]);
    $courses = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // جلب الطلاب المسجلين مع المدرب
    $stmt = $conn->prepare("
        SELECT u.*, ce.status, ce.enrolled_at as enrollment_date
        FROM users u
        JOIN course_enrollments ce ON u.id = ce.student_id
        JOIN courses c ON ce.course_id = c.id
        WHERE c.instructor_id = ?
        ORDER BY ce.enrolled_at DESC
    ");
    $stmt->execute([$instructor_id]);
    $students = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // جلب الجلسات القادمة
    $stmt = $conn->prepare("
        SELECT s.*, c.title as course_title
        FROM sessions s
        JOIN courses c ON s.course_id = c.id
        WHERE c.instructor_id = ? AND s.session_date >= CURDATE()
        ORDER BY s.session_date ASC
        LIMIT 5
    ");
    $stmt->execute([$instructor_id]);
    $upcoming_sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);

} catch (PDOException $e) {
    error_log($e->getMessage());
    die('حدث خطأ في النظام');
}

// معالجة تحديث بيانات المدرب
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'update_status':
                $new_status = $_POST['status'];
                try {
                    $stmt = $conn->prepare("UPDATE users SET status = ? WHERE id = ?");
                    $stmt->execute([$new_status, $instructor_id]);
                    $success = 'تم تحديث حالة المدرب بنجاح';
                    $instructor['status'] = $new_status;
                } catch (PDOException $e) {
                    $error = 'حدث خطأ أثناء تحديث حالة المدرب';
                }
                break;

            case 'update_info':
                $name = trim($_POST['name']);
                $email = trim($_POST['email']);
                $phone = trim($_POST['phone']);
                
                if (empty($name) || empty($email)) {
                    $error = 'الاسم والبريد الإلكتروني مطلوبان';
                } else {
                    try {
                        $stmt = $conn->prepare("UPDATE users SET name = ?, email = ?, phone = ? WHERE id = ?");
                        $stmt->execute([$name, $email, $phone, $instructor_id]);
                        $success = 'تم تحديث بيانات المدرب بنجاح';
                        $instructor['name'] = $name;
                        $instructor['email'] = $email;
                        $instructor['phone'] = $phone;
                    } catch (PDOException $e) {
                        $error = 'حدث خطأ أثناء تحديث البيانات';
                    }
                }
                break;
        }
    }
}

$pageTitle = 'تفاصيل المدرب: ' . $instructor['name'];
$pageSubtitle = 'عرض تفاصيل شاملة للمدرب وإحصائياته';
require_once 'includes/admin-header.php';
?>

<div class="container-fluid py-4">
        <?php if ($error): ?>
            <div class="alert alert-danger"><?php echo $error; ?></div>
        <?php endif; ?>
        <?php if ($success): ?>
            <div class="alert alert-success"><?php echo $success; ?></div>
        <?php endif; ?>

        <!-- بيانات المدرب -->
        <div class="row mb-4">
            <div class="col-md-8">
                <div class="admin-card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0"><i class="fas fa-user-tie me-2"></i>بيانات المدرب</h6>
                        <div>
                            <a href="edit-instructor.php?id=<?php echo $instructor_id; ?>" class="btn btn-warning btn-sm me-2">
                                <i class="fas fa-edit me-1"></i>تعديل
                            </a>
                            <a href="manage-instructors.php" class="btn btn-secondary btn-sm">
                                <i class="fas fa-arrow-right me-1"></i>العودة
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>الاسم:</strong> <?php echo htmlspecialchars($instructor['name']); ?></p>
                                <p><strong>البريد الإلكتروني:</strong> <?php echo htmlspecialchars($instructor['email']); ?></p>
                                <p><strong>رقم الهاتف:</strong> <?php echo htmlspecialchars($instructor['phone']); ?></p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>تاريخ التسجيل:</strong> <?php echo date('Y-m-d', strtotime($instructor['created_at'])); ?></p>
                                <p><strong>آخر تسجيل دخول:</strong> <?php echo $instructor['last_login'] ? date('Y-m-d H:i', strtotime($instructor['last_login'])) : 'لم يسجل الدخول بعد'; ?></p>
                                <p>
                                    <strong>الحالة:</strong>
                                    <span class="badge bg-<?php echo $instructor['status'] === 'active' ? 'success' : ($instructor['status'] === 'pending' ? 'warning' : 'danger'); ?>">
                                        <?php
                                        switch($instructor['status']) {
                                            case 'active': echo 'نشط'; break;
                                            case 'pending': echo 'قيد المراجعة'; break;
                                            case 'inactive': echo 'غير نشط'; break;
                                        }
                                        ?>
                                    </span>
                                    <button class="btn btn-sm btn-outline-primary ms-2" data-bs-toggle="modal" data-bs-target="#changeStatusModal">
                                        تغيير الحالة
                                    </button>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="row">
                    <div class="col-md-12 mb-4">
                        <div class="admin-card">
                            <div class="card-body text-center">
                                <div class="stat-icon bg-primary text-white mb-3">
                                    <i class="fas fa-book"></i>
                                </div>
                                <h3 class="mb-1"><?php echo number_format($instructor['course_count']); ?></h3>
                                <p class="text-muted mb-0">الكورسات</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-12 mb-4">
                        <div class="admin-card">
                            <div class="card-body text-center">
                                <div class="stat-icon bg-success text-white mb-3">
                                    <i class="fas fa-user-graduate"></i>
                                </div>
                                <h3 class="mb-1"><?php echo number_format($instructor['student_count']); ?></h3>
                                <p class="text-muted mb-0">الطلاب</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="admin-card">
                            <div class="card-body text-center">
                                <div class="stat-icon bg-info text-white mb-3">
                                    <i class="fas fa-video"></i>
                                </div>
                                <h3 class="mb-1"><?php echo number_format($instructor['session_count']); ?></h3>
                                <p class="text-muted mb-0">الجلسات</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- الكورسات -->
        <div class="admin-card mb-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-book me-2"></i>الكورسات</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="coursesTable">
                        <thead>
                            <tr>
                                <th>عنوان الكورس</th>
                                <th>عدد الطلاب</th>
                                <th>عدد الجلسات</th>
                                <th>الحالة</th>
                                <th>تاريخ الإنشاء</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($courses as $course): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($course['title']); ?></td>
                                    <td><?php echo $course['enrolled_students']; ?></td>
                                    <td><?php echo $course['total_sessions']; ?></td>
                                    <td>
                                        <span class="badge bg-<?php echo $course['status'] === 'active' ? 'success' : 'secondary'; ?>">
                                            <?php echo $course['status'] === 'active' ? 'نشط' : 'غير نشط'; ?>
                                        </span>
                                    </td>
                                    <td><?php echo date('Y-m-d', strtotime($course['created_at'])); ?></td>
                                    <td>
                                        <a href="course-details.php?id=<?php echo $course['id']; ?>" class="btn btn-primary btn-sm">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- الطلاب -->
        <div class="admin-card mb-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-users me-2"></i>الطلاب</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="studentsTable">
                        <thead>
                            <tr>
                                <th>اسم الطالب</th>
                                <th>البريد الإلكتروني</th>
                                <th>رقم الهاتف</th>
                                <th>الحالة</th>
                                <th>تاريخ التسجيل</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($students as $student): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($student['name']); ?></td>
                                    <td><?php echo htmlspecialchars($student['email']); ?></td>
                                    <td><?php echo htmlspecialchars($student['phone']); ?></td>
                                    <td>
                                        <span class="badge bg-<?php echo $student['status'] === 'active' ? 'success' : ($student['status'] === 'pending' ? 'warning' : 'danger'); ?>">
                                            <?php
                                            switch($student['status']) {
                                                case 'active': echo 'نشط'; break;
                                                case 'pending': echo 'قيد المراجعة'; break;
                                                case 'inactive': echo 'غير نشط'; break;
                                            }
                                            ?>
                                        </span>
                                    </td>
                                    <td><?php echo date('Y-m-d', strtotime($student['enrollment_date'])); ?></td>
                                    <td>
                                        <a href="student-details.php?id=<?php echo $student['id']; ?>" class="btn btn-primary btn-sm">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- الجلسات القادمة -->
        <div class="admin-card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-calendar me-2"></i>الجلسات القادمة</h6>
            </div>
            <div class="card-body">
                <?php if ($upcoming_sessions): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>الكورس</th>
                                    <th>تاريخ الجلسة</th>
                                    <th>وقت البدء</th>
                                    <th>المدة (دقيقة)</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($upcoming_sessions as $session): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($session['course_title']); ?></td>
                                        <td><?php echo date('Y-m-d', strtotime($session['session_date'])); ?></td>
                                        <td><?php echo date('H:i', strtotime($session['session_date'])); ?></td>
                                        <td><?php echo $session['duration'] ?? 60; ?></td>
                                        <td>
                                            <span class="badge bg-<?php echo $session['status'] === 'scheduled' ? 'info' : 'success'; ?>">
                                                <?php echo $session['status'] === 'scheduled' ? 'مجدولة' : 'جارية'; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <a href="session-details.php?id=<?php echo $session['id']; ?>" class="btn btn-primary btn-sm">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <p class="text-muted mb-0">لا توجد جلسات قادمة</p>
                <?php endif; ?>
            </div>
        </div>

        <!-- Modal تعديل بيانات المدرب -->
        <div class="modal fade" id="editInstructorModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">تعديل بيانات المدرب</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <form method="POST">
                        <div class="modal-body">
                            <input type="hidden" name="action" value="update_info">
                            <div class="mb-3">
                                <label class="form-label">الاسم</label>
                                <input type="text" class="form-control" name="name" value="<?php echo htmlspecialchars($instructor['name']); ?>" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" name="email" value="<?php echo htmlspecialchars($instructor['email']); ?>" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" name="phone" value="<?php echo htmlspecialchars($instructor['phone']); ?>">
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Modal تغيير حالة المدرب -->
        <div class="modal fade" id="changeStatusModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">تغيير حالة المدرب</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <form method="POST">
                        <div class="modal-body">
                            <input type="hidden" name="action" value="update_status">
                            <div class="mb-3">
                                <label class="form-label">الحالة</label>
                                <select class="form-select" name="status" required>
                                    <option value="active" <?php echo $instructor['status'] === 'active' ? 'selected' : ''; ?>>نشط</option>
                                    <option value="inactive" <?php echo $instructor['status'] === 'inactive' ? 'selected' : ''; ?>>غير نشط</option>
                                    <option value="pending" <?php echo $instructor['status'] === 'pending' ? 'selected' : ''; ?>>قيد المراجعة</option>
                                </select>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    font-size: 24px;
}
</style>

<script>
$(document).ready(function() {
    // تهيئة DataTables
    $('#coursesTable').DataTable({
        order: [[4, 'desc']],
        pageLength: 10,
        responsive: true,
        language: {
            url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
        }
    });

    $('#studentsTable').DataTable({
        order: [[4, 'desc']],
        pageLength: 10,
        responsive: true,
        language: {
            url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
        }
    });
});
</script>

<?php require_once 'includes/admin-footer.php'; ?>
