<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات المدرب
if (!isLoggedIn() || !isInstructor()) {
    header('Location: ../login.php');
    exit;
}

$quiz_id = $_GET['quiz_id'] ?? 0;

// التحقق من وجود الاختبار وأنه ينتمي للمدرب
try {
    $stmt = $conn->prepare("
        SELECT q.*, c.title as course_title
        FROM quizzes q
        INNER JOIN courses c ON q.course_id = c.id
        WHERE q.id = ? AND c.instructor_id = ?
    ");
    $stmt->execute([$quiz_id, $_SESSION['user_id']]);
    $quiz = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$quiz) {
        header('Location: quizzes.php');
        exit;
    }
} catch (PDOException $e) {
    header('Location: quizzes.php');
    exit;
}

// معالجة إضافة الأسئلة
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $questions_data = $_POST['questions'] ?? [];
    $errors = [];
    $success_count = 0;
    
    if (empty($questions_data)) {
        $errors[] = 'يجب إضافة سؤال واحد على الأقل';
    }
    
    if (empty($errors)) {
        try {
            $conn->beginTransaction();
            
            foreach ($questions_data as $index => $question_data) {
                $question_text = trim($question_data['text'] ?? '');
                $question_type = $question_data['type'] ?? 'multiple_choice';
                $points = $question_data['points'] ?? 1;
                
                if (empty($question_text)) {
                    continue;
                }
                
                // إدراج السؤال
                $stmt = $conn->prepare("
                    INSERT INTO quiz_questions (quiz_id, question_text, question_type, points, question_order) 
                    VALUES (?, ?, ?, ?, ?)
                ");
                $stmt->execute([$quiz_id, $question_text, $question_type, $points, $index + 1]);
                $question_id = $conn->lastInsertId();
                
                // إضافة الخيارات للأسئلة متعددة الاختيارات
                if ($question_type === 'multiple_choice' && !empty($question_data['options'])) {
                    foreach ($question_data['options'] as $option_index => $option_data) {
                        $option_text = trim($option_data['text'] ?? '');
                        $is_correct = isset($option_data['correct']) ? 1 : 0;
                        
                        if (!empty($option_text)) {
                            $stmt = $conn->prepare("
                                INSERT INTO quiz_question_options (question_id, option_text, is_correct) 
                                VALUES (?, ?, ?)
                            ");
                            $stmt->execute([$question_id, $option_text, $is_correct]);
                        }
                    }
                } elseif ($question_type === 'true_false') {
                    // إضافة خيارات صح/خطأ
                    $correct_answer = $question_data['true_false_answer'] ?? 'true';
                    
                    $stmt = $conn->prepare("
                        INSERT INTO quiz_question_options (question_id, option_text, is_correct) 
                        VALUES (?, ?, ?)
                    ");
                    $stmt->execute([$question_id, 'صح', $correct_answer === 'true' ? 1 : 0]);
                    $stmt->execute([$question_id, 'خطأ', $correct_answer === 'false' ? 1 : 0]);
                }
                
                $success_count++;
            }
            
            $conn->commit();
            
            if ($success_count > 0) {
                header("Location: quiz-questions.php?quiz_id=$quiz_id&success=1");
                exit;
            }
            
        } catch (PDOException $e) {
            $conn->rollBack();
            $errors[] = 'حدث خطأ أثناء حفظ الأسئلة';
        }
    }
}

$pageTitle = 'إضافة أسئلة الاختبار - ' . $quiz['title'];
$breadcrumbs = [
    ['title' => 'الاختبارات', 'url' => 'quizzes.php'],
    ['title' => 'إضافة الأسئلة']
];

include 'includes/header.php';
?>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="h3 mb-2">
                <i class="fas fa-clipboard-list text-primary me-2"></i>
                إضافة أسئلة الاختبار
            </h2>
            <p class="text-muted mb-0">
                <strong><?php echo htmlspecialchars($quiz['title']); ?></strong> - 
                <?php echo htmlspecialchars($quiz['course_title']); ?>
            </p>
        </div>
        <div>
            <a href="quiz-questions.php?quiz_id=<?php echo $quiz_id; ?>" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-1"></i>العودة
            </a>
        </div>
    </div>

    <!-- رسالة ترحيب -->
    <div class="alert alert-success border-0 shadow-sm mb-4" id="welcomeAlert" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h5 class="text-white mb-2">
                    <i class="fas fa-check-circle me-2"></i>
                    تم إنشاء الاختبار بنجاح! 🎉
                </h5>
                <p class="text-white-50 mb-0">
                    الآن يمكنك إضافة الأسئلة لاختبارك. حدد عدد الأسئلة ونوعها وابدأ في الكتابة.
                </p>
            </div>
            <div class="col-md-4 text-end">
                <button class="btn btn-light btn-lg shadow" onclick="document.getElementById('questionCount').focus()">
                    <i class="fas fa-rocket me-2 text-success"></i>
                    <strong>ابدأ الآن</strong>
                </button>
            </div>
        </div>
    </div>

    <!-- رسائل الخطأ -->
    <?php if (!empty($errors)): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <ul class="mb-0">
            <?php foreach ($errors as $error): ?>
            <li><?php echo $error; ?></li>
            <?php endforeach; ?>
        </ul>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <!-- شريط التحكم -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-body">
            <div class="row align-items-center">
                <div class="col-md-4">
                    <label for="questionCount" class="form-label fw-bold">
                        <i class="fas fa-list-ol text-primary me-1"></i>عدد الأسئلة:
                    </label>
                    <select id="questionCount" class="form-select form-select-lg" onchange="generateQuestions()">
                        <option value="1">سؤال واحد</option>
                        <option value="5" selected>5 أسئلة</option>
                        <option value="10">10 أسئلة</option>
                        <option value="15">15 سؤال</option>
                        <option value="20">20 سؤال</option>
                        <option value="25">25 سؤال</option>
                        <option value="30">30 سؤال</option>
                        <option value="custom">عدد مخصص</option>
                    </select>
                </div>
                <div class="col-md-3" id="customCountDiv" style="display: none;">
                    <label for="customCount" class="form-label">العدد المخصص:</label>
                    <input type="number" id="customCount" class="form-control" min="1" max="100" value="5">
                </div>
                <div class="col-md-3">
                    <label for="defaultType" class="form-label fw-bold">
                        <i class="fas fa-cog text-success me-1"></i>النوع الافتراضي:
                    </label>
                    <select id="defaultType" class="form-select form-select-lg">
                        <option value="multiple_choice">🔘 اختيار متعدد</option>
                        <option value="true_false">✅ صح/خطأ</option>
                        <option value="short_answer">📝 إجابة قصيرة</option>
                        <option value="essay">📄 مقال</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-primary btn-lg" onclick="generateQuestions()" id="generateBtn">
                            <i class="fas fa-magic me-2"></i>إنشاء الأسئلة
                        </button>
                        <button type="button" class="btn btn-outline-info btn-sm" onclick="saveDraft()">
                            <i class="fas fa-save me-1"></i>حفظ مسودة
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نموذج الأسئلة -->
    <form method="POST" id="questionsForm">
        <div id="questionsContainer">
            <!-- سيتم إنشاء الأسئلة هنا بواسطة JavaScript -->
        </div>
        
        <!-- شريط الإجراءات الثابت -->
        <div class="fixed-bottom bg-white border-top shadow-lg p-3" id="actionBar" style="display: none;">
            <div class="container-fluid">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <span class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            <span id="questionCountDisplay">0</span> سؤال جاهز للحفظ
                        </span>
                    </div>
                    <div>
                        <button type="button" class="btn btn-outline-secondary me-2" onclick="history.back()">
                            <i class="fas fa-times me-1"></i>إلغاء
                        </button>
                        <button type="submit" class="btn btn-success btn-lg px-4">
                            <i class="fas fa-save me-2"></i>حفظ جميع الأسئلة
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="text-center mt-4 mb-5">
            <button type="button" class="btn btn-outline-primary btn-lg me-3" onclick="addQuestion()">
                <i class="fas fa-plus me-2"></i>إضافة سؤال آخر
            </button>
            <button type="button" class="btn btn-outline-secondary btn-lg me-3" onclick="history.back()">
                <i class="fas fa-times me-2"></i>إلغاء
            </button>
            <button type="submit" class="btn btn-success btn-lg px-5">
                <i class="fas fa-save me-2"></i>حفظ جميع الأسئلة
            </button>
        </div>
    </form>
</div>

<style>
.question-card {
    transition: all 0.3s ease;
    border: 2px solid #e9ecef;
}

.question-card:hover {
    border-color: #667eea;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.1);
}

.question-card.active {
    border-color: #667eea;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
}

.option-input {
    transition: all 0.3s ease;
}

.option-input:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.btn-add-option, .btn-remove-option {
    transition: all 0.2s ease;
}

.btn-add-option:hover {
    transform: scale(1.05);
}

.btn-remove-option:hover {
    transform: scale(1.1);
}

.form-control:focus, .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8, #6a4190);
    transform: translateY(-2px);
}

.btn-success {
    background: linear-gradient(135deg, #28a745, #20c997);
    border: none;
}

.btn-success:hover {
    background: linear-gradient(135deg, #218838, #1ea080);
    transform: translateY(-2px);
}

@keyframes slideInDown {
    from {
        transform: translateY(-100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes slideOutUp {
    from {
        transform: translateY(0);
        opacity: 1;
    }
    to {
        transform: translateY(-100%);
        opacity: 0;
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0,0,0);
    }
    40%, 43% {
        transform: translate3d(0, -10px, 0);
    }
    70% {
        transform: translate3d(0, -5px, 0);
    }
    90% {
        transform: translate3d(0, -2px, 0);
    }
}

.btn:hover {
    animation: bounce 1s ease;
}

@media (max-width: 768px) {
    .question-card {
        margin-bottom: 1rem;
    }

    .btn-lg {
        font-size: 1rem;
        padding: 0.75rem 1.5rem;
    }

    #welcomeAlert .col-md-4 {
        text-align: center !important;
        margin-top: 1rem;
    }
}
</style>

<script>
let questionCounter = 0;

// إنشاء الأسئلة
function generateQuestions() {
    const count = document.getElementById('questionCount').value === 'custom'
        ? parseInt(document.getElementById('customCount').value) || 5
        : parseInt(document.getElementById('questionCount').value);

    const defaultType = document.getElementById('defaultType').value;
    const container = document.getElementById('questionsContainer');
    const generateBtn = document.getElementById('generateBtn');

    // تأثير بصري للزر
    if (generateBtn) {
        const originalText = generateBtn.innerHTML;
        generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الإنشاء...';
        generateBtn.disabled = true;
        generateBtn.style.transform = 'scale(0.95)';

        setTimeout(() => {
            generateBtn.innerHTML = originalText;
            generateBtn.disabled = false;
            generateBtn.style.transform = '';
        }, 1500);
    }

    // إخفاء رسالة الترحيب
    const welcomeAlert = document.getElementById('welcomeAlert');
    if (welcomeAlert && welcomeAlert.style.display !== 'none') {
        welcomeAlert.style.animation = 'slideOutUp 0.5s ease-in';
        setTimeout(() => {
            welcomeAlert.style.display = 'none';
        }, 500);
    }

    // مسح الأسئلة الموجودة مع تأثير
    const existingQuestions = container.querySelectorAll('.question-card');
    if (existingQuestions.length > 0) {
        existingQuestions.forEach((question, index) => {
            setTimeout(() => {
                question.style.animation = 'slideOutUp 0.3s ease-in';
                setTimeout(() => {
                    question.remove();
                }, 300);
            }, index * 100);
        });

        setTimeout(() => {
            createNewQuestions();
        }, existingQuestions.length * 100 + 300);
    } else {
        createNewQuestions();
    }

    function createNewQuestions() {
        container.innerHTML = '';
        questionCounter = 0;

        for (let i = 0; i < count; i++) {
            setTimeout(() => {
                addQuestion(defaultType);
            }, i * 200);
        }

        // تحديث العداد
        setTimeout(() => {
            updateQuestionCount();
        }, count * 200 + 500);
    }
}

// إضافة سؤال جديد
function addQuestion(type = 'multiple_choice') {
    questionCounter++;
    const container = document.getElementById('questionsContainer');
    
    const questionDiv = document.createElement('div');
    questionDiv.className = 'question-card card border-0 shadow-sm mb-4';
    questionDiv.id = `question-${questionCounter}`;
    
    questionDiv.innerHTML = `
        <div class="card-header bg-light">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h6 class="mb-0">
                        <i class="fas fa-question-circle text-primary me-2"></i>
                        السؤال ${questionCounter}
                    </h6>
                </div>
                <div class="col-md-3">
                    <select name="questions[${questionCounter-1}][type]" class="form-select form-select-sm" onchange="changeQuestionType(${questionCounter}, this.value)">
                        <option value="multiple_choice" ${type === 'multiple_choice' ? 'selected' : ''}>اختيار متعدد</option>
                        <option value="true_false" ${type === 'true_false' ? 'selected' : ''}>صح/خطأ</option>
                        <option value="short_answer" ${type === 'short_answer' ? 'selected' : ''}>إجابة قصيرة</option>
                        <option value="essay" ${type === 'essay' ? 'selected' : ''}>مقال</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <input type="number" name="questions[${questionCounter-1}][points]" class="form-control form-control-sm" 
                           value="1" min="0.5" step="0.5" placeholder="النقاط">
                </div>
                <div class="col-md-1 text-end">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-info btn-sm" onclick="previewQuestion(${questionCounter})" title="معاينة">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeQuestion(${questionCounter})" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="mb-3">
                <label class="form-label fw-bold">نص السؤال:</label>
                <textarea name="questions[${questionCounter-1}][text]" class="form-control" rows="3" 
                          placeholder="اكتب السؤال هنا..." required></textarea>
            </div>
            <div id="options-${questionCounter}">
                ${generateOptionsHTML(questionCounter, type)}
            </div>
        </div>
    `;
    
    container.appendChild(questionDiv);
    
    // تأثير بصري
    setTimeout(() => {
        questionDiv.style.opacity = '0';
        questionDiv.style.transform = 'translateY(20px)';
        questionDiv.style.transition = 'all 0.3s ease';
        
        setTimeout(() => {
            questionDiv.style.opacity = '1';
            questionDiv.style.transform = 'translateY(0)';
        }, 50);
    }, 10);
}

// إنشاء HTML للخيارات
function generateOptionsHTML(questionNum, type) {
    if (type === 'multiple_choice') {
        return `
            <label class="form-label fw-bold">الخيارات:</label>
            <div id="optionsList-${questionNum}">
                ${generateMultipleChoiceOptions(questionNum, 4)}
            </div>
            <div class="mt-2">
                <button type="button" class="btn btn-outline-primary btn-sm btn-add-option" onclick="addOption(${questionNum})">
                    <i class="fas fa-plus me-1"></i>إضافة خيار
                </button>
                <small class="text-muted ms-3">حدد الإجابة الصحيحة بوضع علامة ✓</small>
            </div>
        `;
    } else if (type === 'true_false') {
        return `
            <label class="form-label fw-bold">الإجابة الصحيحة:</label>
            <div class="row">
                <div class="col-md-6">
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="questions[${questionNum-1}][true_false_answer]" 
                               value="true" id="true-${questionNum}" checked>
                        <label class="form-check-label" for="true-${questionNum}">
                            <i class="fas fa-check text-success me-1"></i>صح
                        </label>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="questions[${questionNum-1}][true_false_answer]" 
                               value="false" id="false-${questionNum}">
                        <label class="form-check-label" for="false-${questionNum}">
                            <i class="fas fa-times text-danger me-1"></i>خطأ
                        </label>
                    </div>
                </div>
            </div>
        `;
    } else {
        return `
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                ${type === 'short_answer' ? 'سيتم تصحيح هذا السؤال يدوياً' : 'سؤال مقال - يتطلب تصحيح يدوي'}
            </div>
        `;
    }
}

// إنشاء خيارات الاختيار المتعدد
function generateMultipleChoiceOptions(questionNum, count) {
    let html = '';
    for (let i = 0; i < count; i++) {
        html += `
            <div class="option-item mb-2" id="option-${questionNum}-${i}">
                <div class="input-group">
                    <div class="input-group-text">
                        <input class="form-check-input" type="checkbox" 
                               name="questions[${questionNum-1}][options][${i}][correct]" value="1">
                    </div>
                    <input type="text" name="questions[${questionNum-1}][options][${i}][text]" 
                           class="form-control option-input" placeholder="الخيار ${i + 1}">
                    <button type="button" class="btn btn-outline-danger btn-remove-option" 
                            onclick="removeOption(${questionNum}, ${i})" ${count <= 2 ? 'disabled' : ''}>
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `;
    }
    return html;
}

// إضافة خيار جديد
function addOption(questionNum) {
    const optionsList = document.getElementById(`optionsList-${questionNum}`);
    const currentOptions = optionsList.children.length;
    
    if (currentOptions >= 8) {
        alert('الحد الأقصى للخيارات هو 8');
        return;
    }
    
    const newOptionIndex = currentOptions;
    const optionDiv = document.createElement('div');
    optionDiv.className = 'option-item mb-2';
    optionDiv.id = `option-${questionNum}-${newOptionIndex}`;
    
    optionDiv.innerHTML = `
        <div class="input-group">
            <div class="input-group-text">
                <input class="form-check-input" type="checkbox" 
                       name="questions[${questionNum-1}][options][${newOptionIndex}][correct]" value="1">
            </div>
            <input type="text" name="questions[${questionNum-1}][options][${newOptionIndex}][text]" 
                   class="form-control option-input" placeholder="الخيار ${newOptionIndex + 1}">
            <button type="button" class="btn btn-outline-danger btn-remove-option" 
                    onclick="removeOption(${questionNum}, ${newOptionIndex})">
                <i class="fas fa-trash"></i>
            </button>
        </div>
    `;
    
    optionsList.appendChild(optionDiv);
    updateRemoveButtons(questionNum);
    
    // تأثير بصري
    optionDiv.style.opacity = '0';
    optionDiv.style.transform = 'translateX(-20px)';
    optionDiv.style.transition = 'all 0.3s ease';
    
    setTimeout(() => {
        optionDiv.style.opacity = '1';
        optionDiv.style.transform = 'translateX(0)';
    }, 50);
}

// حذف خيار
function removeOption(questionNum, optionIndex) {
    const optionsList = document.getElementById(`optionsList-${questionNum}`);
    const currentOptions = optionsList.children.length;
    
    if (currentOptions <= 2) {
        alert('الحد الأدنى للخيارات هو 2');
        return;
    }
    
    const optionElement = document.getElementById(`option-${questionNum}-${optionIndex}`);
    if (optionElement) {
        optionElement.style.transition = 'all 0.3s ease';
        optionElement.style.opacity = '0';
        optionElement.style.transform = 'translateX(20px)';
        
        setTimeout(() => {
            optionElement.remove();
            updateRemoveButtons(questionNum);
        }, 300);
    }
}

// تحديث أزرار الحذف
function updateRemoveButtons(questionNum) {
    const optionsList = document.getElementById(`optionsList-${questionNum}`);
    const removeButtons = optionsList.querySelectorAll('.btn-remove-option');
    const currentOptions = optionsList.children.length;
    
    removeButtons.forEach(button => {
        button.disabled = currentOptions <= 2;
    });
}

// تغيير نوع السؤال
function changeQuestionType(questionNum, newType) {
    const optionsContainer = document.getElementById(`options-${questionNum}`);
    optionsContainer.innerHTML = generateOptionsHTML(questionNum, newType);
}

// حذف سؤال
function removeQuestion(questionNum) {
    const questionElement = document.getElementById(`question-${questionNum}`);
    if (questionElement) {
        if (confirm('هل أنت متأكد من حذف هذا السؤال؟')) {
            questionElement.style.transition = 'all 0.3s ease';
            questionElement.style.opacity = '0';
            questionElement.style.transform = 'translateY(-20px)';
            
            setTimeout(() => {
                questionElement.remove();
            }, 300);
        }
    }
}

// إظهار/إخفاء حقل العدد المخصص
document.getElementById('questionCount').addEventListener('change', function() {
    const customDiv = document.getElementById('customCountDiv');
    if (this.value === 'custom') {
        customDiv.style.display = 'block';
    } else {
        customDiv.style.display = 'none';
    }
});

// تحديث عداد الأسئلة
function updateQuestionCount() {
    const questions = document.querySelectorAll('[name*="[text]"]');
    let validQuestions = 0;

    questions.forEach(question => {
        if (question.value.trim() !== '') {
            validQuestions++;
        }
    });

    const countDisplay = document.getElementById('questionCountDisplay');
    const actionBar = document.getElementById('actionBar');

    if (countDisplay) {
        countDisplay.textContent = validQuestions;
    }

    // إظهار/إخفاء شريط الإجراءات
    if (actionBar) {
        if (validQuestions > 0) {
            actionBar.style.display = 'block';
        } else {
            actionBar.style.display = 'none';
        }
    }
}

// معاينة سريعة للسؤال
function previewQuestion(questionNum) {
    const questionText = document.querySelector(`[name="questions[${questionNum-1}][text]"]`).value;
    const questionType = document.querySelector(`[name="questions[${questionNum-1}][type]"]`).value;

    if (!questionText.trim()) {
        alert('يرجى كتابة نص السؤال أولاً');
        return;
    }

    let previewHTML = `<h6>معاينة السؤال ${questionNum}:</h6><p><strong>${questionText}</strong></p>`;

    if (questionType === 'multiple_choice') {
        const options = document.querySelectorAll(`[name*="questions[${questionNum-1}][options]"][name*="[text]"]`);
        previewHTML += '<ul>';
        options.forEach((option, index) => {
            if (option.value.trim()) {
                const isCorrect = document.querySelector(`[name="questions[${questionNum-1}][options][${index}][correct]"]`).checked;
                previewHTML += `<li${isCorrect ? ' style="color: green; font-weight: bold;"' : ''}>${option.value} ${isCorrect ? '✓' : ''}</li>`;
            }
        });
        previewHTML += '</ul>';
    } else if (questionType === 'true_false') {
        const answer = document.querySelector(`[name="questions[${questionNum-1}][true_false_answer]"]:checked`).value;
        previewHTML += `<p><strong>الإجابة الصحيحة:</strong> ${answer === 'true' ? 'صح' : 'خطأ'}</p>`;
    }

    // إظهار المعاينة في نافذة منبثقة
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">معاينة السؤال</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">${previewHTML}</div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();

    modal.addEventListener('hidden.bs.modal', function() {
        document.body.removeChild(modal);
    });
}

// حفظ مسودة
function saveDraft() {
    const formData = new FormData(document.getElementById('questionsForm'));

    // حفظ في localStorage
    const draftData = {};
    for (let [key, value] of formData.entries()) {
        draftData[key] = value;
    }

    localStorage.setItem('quiz_draft_<?php echo $quiz_id; ?>', JSON.stringify(draftData));

    // إظهار رسالة نجاح
    const toast = document.createElement('div');
    toast.className = 'toast position-fixed top-0 end-0 m-3';
    toast.innerHTML = `
        <div class="toast-header">
            <i class="fas fa-save text-success me-2"></i>
            <strong class="me-auto">تم الحفظ</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
        </div>
        <div class="toast-body">تم حفظ المسودة بنجاح</div>
    `;

    document.body.appendChild(toast);
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();

    setTimeout(() => {
        document.body.removeChild(toast);
    }, 3000);
}

// استرداد المسودة
function loadDraft() {
    const draftData = localStorage.getItem('quiz_draft_<?php echo $quiz_id; ?>');
    if (draftData) {
        const data = JSON.parse(draftData);

        // ملء النموذج بالبيانات المحفوظة
        for (let [key, value] of Object.entries(data)) {
            const element = document.querySelector(`[name="${key}"]`);
            if (element) {
                if (element.type === 'checkbox') {
                    element.checked = value === '1';
                } else {
                    element.value = value;
                }
            }
        }

        updateQuestionCount();
    }
}

// تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تأثير ترحيب
    const welcomeAlert = document.getElementById('welcomeAlert');
    if (welcomeAlert) {
        welcomeAlert.style.animation = 'slideInDown 0.6s ease-out';

        // إخفاء رسالة الترحيب بعد 10 ثوان
        setTimeout(() => {
            welcomeAlert.style.animation = 'slideOutUp 0.5s ease-in';
            setTimeout(() => {
                welcomeAlert.style.display = 'none';
            }, 500);
        }, 10000);
    }

    generateQuestions(); // إنشاء 5 أسئلة افتراضياً

    // محاولة استرداد المسودة
    setTimeout(loadDraft, 500);

    // تحديث العداد عند تغيير النصوص
    document.addEventListener('input', function(e) {
        if (e.target.name && e.target.name.includes('[text]')) {
            updateQuestionCount();
        }
    });

    // حفظ مسودة تلقائي كل 30 ثانية
    setInterval(saveDraft, 30000);

    // التحقق من صحة النموذج قبل الإرسال
    document.getElementById('questionsForm').addEventListener('submit', function(e) {
        const questions = document.querySelectorAll('[name*="[text]"]');
        let hasValidQuestion = false;
        let validationErrors = [];

        questions.forEach((question, index) => {
            if (question.value.trim() !== '') {
                hasValidQuestion = true;

                // التحقق من الأسئلة متعددة الاختيارات
                const questionType = document.querySelector(`[name="questions[${index}][type]"]`).value;
                if (questionType === 'multiple_choice') {
                    const options = document.querySelectorAll(`[name*="questions[${index}][options]"][name*="[text]"]`);
                    const correctOptions = document.querySelectorAll(`[name*="questions[${index}][options]"][name*="[correct]"]:checked`);

                    let validOptions = 0;
                    options.forEach(option => {
                        if (option.value.trim()) validOptions++;
                    });

                    if (validOptions < 2) {
                        validationErrors.push(`السؤال ${index + 1}: يجب أن يحتوي على خيارين على الأقل`);
                    }

                    if (correctOptions.length === 0) {
                        validationErrors.push(`السؤال ${index + 1}: يجب تحديد إجابة صحيحة واحدة على الأقل`);
                    }
                }
            }
        });

        if (!hasValidQuestion) {
            e.preventDefault();
            alert('يجب إضافة سؤال واحد على الأقل');
            return false;
        }

        if (validationErrors.length > 0) {
            e.preventDefault();
            alert('يرجى تصحيح الأخطاء التالية:\n\n' + validationErrors.join('\n'));
            return false;
        }

        // إظهار مؤشر التحميل
        const submitBtns = this.querySelectorAll('button[type="submit"]');
        submitBtns.forEach(btn => {
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الحفظ...';
            btn.disabled = true;
        });

        // حذف المسودة بعد الحفظ الناجح
        localStorage.removeItem('quiz_draft_<?php echo $quiz_id; ?>');
    });

    // تحديث العداد في البداية
    setTimeout(updateQuestionCount, 1000);
});
</script>

<?php include 'includes/footer.php'; ?>
