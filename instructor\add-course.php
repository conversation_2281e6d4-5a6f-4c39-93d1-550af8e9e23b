<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';
require_once '../includes/activity_logger.php';

// التحقق من صلاحيات المدرب
if (!isLoggedIn() || !isInstructor()) {
    header('Location: ../login.php');
    exit;
}

// Process form submission before including header
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    ob_start(); // Start output buffering
}

$pageTitle = 'إضافة كورس جديد';
include 'includes/header.php';


$success = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // التحقق من صلاحية المدرب
        $instructor_id = $_SESSION['user_id'];
        $stmt = $conn->prepare("SELECT id FROM users WHERE id = ? AND role = 'instructor' AND status = 'active'");
        $stmt->execute([$instructor_id]);
        if (!$stmt->fetch()) {
            throw new Exception('غير مصرح لك بإضافة كورس');
        }

        // جمع وتنظيف البيانات
        $title = trim($_POST['title']);
        $description = trim($_POST['description']);
        $start_date = $_POST['start_date'];
        $end_date = $_POST['end_date'];
        $max_students = (int)$_POST['max_students'];
        $status = isset($_POST['status']) ? $_POST['status'] : 'active';
        $category_id = isset($_POST['category_id']) ? (int)$_POST['category_id'] : 0;

        // بيانات الدفع
        $course_type = isset($_POST['course_type']) ? $_POST['course_type'] : 'free';
        $price = 0.00;
        $currency = 'SAR';

        if ($course_type === 'paid') {
            $price = isset($_POST['price']) ? (float)$_POST['price'] : 0.00;
            if ($price <= 0) {
                throw new Exception('يجب أن يكون سعر الكورس المدفوع أكبر من صفر');
            }
        }

        // التحقق من البيانات
        if (empty($title) || empty($description) || empty($start_date) || empty($end_date) || empty($category_id)) {
            throw new Exception('يرجى ملء جميع الحقول المطلوبة واختيار تصنيف');
        }

        if (strtotime($end_date) <= strtotime($start_date)) {
            throw new Exception('تاريخ الانتهاء يجب أن يكون بعد تاريخ البدء');
        }

        if ($max_students < 1) {
            throw new Exception('يجب أن يكون الحد الأقصى للطلاب أكبر من صفر');
        }

        // التحقق من الصورة
        if (!isset($_FILES['course_image']) || $_FILES['course_image']['error'] !== UPLOAD_ERR_OK) {
            throw new Exception('يرجى اختيار صورة للكورس');
        }

        // التحقق من نوع الملف
        $allowed_types = ['image/jpeg', 'image/png', 'image/gif'];
        $file_info = finfo_open(FILEINFO_MIME_TYPE);
        $mime_type = finfo_file($file_info, $_FILES['course_image']['tmp_name']);
        finfo_close($file_info);

        if (!in_array($mime_type, $allowed_types)) {
            throw new Exception('نوع الملف غير مسموح. يجب أن تكون الصورة من نوع JPG, PNG, أو GIF');
        }

        // إنشاء مجلد الصور إذا لم يكن موجوداً
        $upload_dir = '../uploads/courses';
        if (!file_exists($upload_dir)) {
            mkdir($upload_dir, 0777, true);
        }

        // توليد اسم فريد للصورة
        $file_extension = pathinfo($_FILES['course_image']['name'], PATHINFO_EXTENSION);
        $file_name = uniqid('course_') . '.' . $file_extension;
        $file_path = $upload_dir . '/' . $file_name;

        // التحقق من وجود الأعمدة المطلوبة وإضافتها إذا لم تكن موجودة
        try {
            // التحقق من عمود category_id
            $stmt = $conn->query("SHOW COLUMNS FROM courses LIKE 'category_id'");
            if ($stmt->rowCount() == 0) {
                $conn->exec("ALTER TABLE courses ADD COLUMN category_id INT DEFAULT NULL");
                try {
                    $conn->exec("ALTER TABLE courses ADD FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL");
                } catch (Exception $e) {
                    // تجاهل خطأ المفتاح الخارجي إذا كان موجود
                }
            }

            // التحقق من عمود image_path
            $stmt = $conn->query("SHOW COLUMNS FROM courses LIKE 'image_path'");
            if ($stmt->rowCount() == 0) {
                $conn->exec("ALTER TABLE courses ADD COLUMN image_path VARCHAR(255) DEFAULT NULL");
            }

            // التحقق من أعمدة الدفع
            $stmt = $conn->query("SHOW COLUMNS FROM courses LIKE 'course_type'");
            if ($stmt->rowCount() == 0) {
                $conn->exec("ALTER TABLE courses ADD COLUMN course_type ENUM('free', 'paid') DEFAULT 'free' AFTER description");
                $conn->exec("ALTER TABLE courses ADD COLUMN price DECIMAL(10,2) DEFAULT 0.00 AFTER course_type");
                $conn->exec("ALTER TABLE courses ADD COLUMN currency VARCHAR(3) DEFAULT 'SAR' AFTER price");
            }
        } catch (Exception $e) {
            // تجاهل أخطاء تعديل الجدول
            error_log("Database modification error: " . $e->getMessage());
        }

        // نقل الصورة
        if (!move_uploaded_file($_FILES['course_image']['tmp_name'], $file_path)) {
            throw new Exception('حدث خطأ أثناء رفع الصورة');
        }

        // إضافة الكورس إلى قاعدة البيانات
        $stmt = $conn->prepare("INSERT INTO courses (title, description, course_type, price, currency, start_date, end_date, max_students, status, instructor_id, image_path, category_id, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())");

        $result = $stmt->execute([
            $title,
            $description,
            $course_type,
            $price,
            $currency,
            $start_date,
            $end_date,
            $max_students,
            $status,
            $instructor_id,
            'uploads/courses/' . $file_name,
            $category_id
        ]);

        if (!$result) {
            // حذف الصورة في حالة فشل إضافة الكورس
            if (file_exists($file_path)) {
                unlink($file_path);
            }
            throw new Exception('فشل في إضافة الكورس إلى قاعدة البيانات');
        }

        $course_id = $conn->lastInsertId();

        // تسجيل النشاط (اختياري - لا يؤثر على نجاح العملية)
        try {
            logUserActivity($instructor_id, 'إضافة كورس', "تم إضافة كورس جديد: $title");
        } catch (Exception $e) {
            // تجاهل خطأ تسجيل النشاط
            error_log("Activity logging error: " . $e->getMessage());
        }

        $success = 'تم إضافة الكورس بنجاح';

        // إعادة توجيه بعد النجاح
        echo "<script>
            alert('تم إضافة الكورس بنجاح!');
            window.location.href = 'dashboard.php';
        </script>";
        exit();
    } catch (Exception $e) {
        $error = $e->getMessage();
        error_log($e->getMessage());
    }
}
?>

<div class="container-fluid py-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">إضافة كورس جديد</h5>
                </div>
                <div class="card-body">
                    <?php if ($success): ?>
                        <div class="alert alert-success" role="alert">
                            <?php echo htmlspecialchars($success); ?>
                        </div>
                    <?php endif; ?>

                    <?php if ($error): ?>
                        <div class="alert alert-danger" role="alert">
                            <?php echo htmlspecialchars($error); ?>
                        </div>
                    <?php endif; ?>

                    <form method="POST" action="" enctype="multipart/form-data">
                        <div class="form-group mb-3">
                            <label for="title">عنوان الكورس</label>
                            <input type="text" class="form-control" id="title" name="title" required
                                value="<?php echo isset($_POST['title']) ? htmlspecialchars($_POST['title']) : ''; ?>">
                        </div>

                        <div class="form-group mb-3">
                            <label for="description">وصف الكورس</label>
                            <textarea class="form-control" id="description" name="description" rows="4" required><?php echo isset($_POST['description']) ? htmlspecialchars($_POST['description']) : ''; ?></textarea>
                        </div>

                        <div class="form-group mb-3">
                            <label for="course_image">صورة الكورس</label>
                            <input type="file" class="form-control" id="course_image" name="course_image" accept="image/*" required>
                        </div>

                        <!-- نوع الكورس والسعر -->
                        <div class="form-group mb-3">
                            <label>نوع الكورس</label>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="course_type" id="course_free" value="free"
                                               <?php echo (!isset($_POST['course_type']) || $_POST['course_type'] === 'free') ? 'checked' : ''; ?>
                                               onchange="togglePriceField()">
                                        <label class="form-check-label" for="course_free">
                                            <i class="fas fa-gift text-success"></i> مجاني
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="course_type" id="course_paid" value="paid"
                                               <?php echo (isset($_POST['course_type']) && $_POST['course_type'] === 'paid') ? 'checked' : ''; ?>
                                               onchange="togglePriceField()">
                                        <label class="form-check-label" for="course_paid">
                                            <i class="fas fa-money-bill-wave text-warning"></i> مدفوع
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- حقل السعر -->
                        <div class="form-group mb-3" id="price_field" style="display: none;">
                            <label for="price">سعر الكورس (ريال سعودي)</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="price" name="price" min="1" step="0.01"
                                       value="<?php echo isset($_POST['price']) ? htmlspecialchars($_POST['price']) : ''; ?>"
                                       placeholder="أدخل سعر الكورس">
                                <span class="input-group-text">ريال</span>
                            </div>
                            <small class="form-text text-muted">
                                <i class="fas fa-info-circle"></i>
                                ستحصل على 70% من السعر، والمنصة تحصل على 30% كعمولة
                            </small>
                        </div>

                        <div class="form-group mb-3">
                            <label>تصنيف الكورس</label>
                            <?php
                            // التحقق من وجود جدول التصنيفات وإنشاؤه إذا لم يكن موجوداً
                            try {
                                $stmt = $conn->query("SELECT COUNT(*) FROM categories");
                                $categoriesExist = true;
                            } catch (Exception $e) {
                                $categoriesExist = false;
                                // إنشاء جدول التصنيفات
                                try {
                                    $conn->exec("
                                        CREATE TABLE IF NOT EXISTS categories (
                                            id INT AUTO_INCREMENT PRIMARY KEY,
                                            name VARCHAR(255) NOT NULL,
                                            description TEXT,
                                            icon VARCHAR(100) DEFAULT NULL,
                                            color VARCHAR(7) DEFAULT '#007bff',
                                            status ENUM('active', 'inactive') DEFAULT 'active',
                                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

                                            INDEX idx_name (name),
                                            INDEX idx_status (status)
                                        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                                    ");

                                    // إضافة تصنيفات أساسية
                                    $basicCategories = [
                                        'البرمجة وتطوير المواقع',
                                        'التصميم الجرافيكي',
                                        'التسويق الرقمي',
                                        'إدارة الأعمال',
                                        'اللغات'
                                    ];

                                    $insertCat = $conn->prepare("INSERT INTO categories (name) VALUES (?)");
                                    foreach ($basicCategories as $catName) {
                                        $insertCat->execute([$catName]);
                                    }
                                    $categoriesExist = true;
                                } catch (Exception $e2) {
                                    // في حالة فشل إنشاء الجدول
                                }
                            }

                            if ($categoriesExist) {
                                // جلب التصنيفات
                                $stmt = $conn->query("SELECT * FROM categories ORDER BY name");
                                $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);

                                if (!empty($categories)) {
                                    echo '<select class="form-control" name="category_id" required>';
                                    echo '<option value="">اختر التصنيف...</option>';
                                    foreach ($categories as $category) {
                                        $selected = (isset($_POST['category_id']) && $_POST['category_id'] == $category['id']) ? 'selected' : '';
                                        echo '<option value="' . $category['id'] . '" ' . $selected . '>' . htmlspecialchars($category['name']) . '</option>';
                                    }
                                    echo '</select>';
                                } else {
                                    echo '<div class="alert alert-warning">لا توجد تصنيفات متاحة. <a href="../create_categories_table.php">إنشاء التصنيفات</a></div>';
                                }
                            } else {
                                echo '<div class="alert alert-danger">خطأ في جدول التصنيفات. <a href="../create_categories_table.php" class="btn btn-primary btn-sm">إنشاء جدول التصنيفات</a></div>';
                            }
                            ?>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="start_date">تاريخ البدء</label>
                                    <input type="date" class="form-control" id="start_date" name="start_date" required
                                        value="<?php echo isset($_POST['start_date']) ? htmlspecialchars($_POST['start_date']) : ''; ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="end_date">تاريخ الانتهاء</label>
                                    <input type="date" class="form-control" id="end_date" name="end_date" required
                                        value="<?php echo isset($_POST['end_date']) ? htmlspecialchars($_POST['end_date']) : ''; ?>">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="max_students">الحد الأقصى للطلاب</label>
                                    <input type="number" class="form-control" id="max_students" name="max_students" min="1" required
                                        value="<?php echo isset($_POST['max_students']) ? htmlspecialchars($_POST['max_students']) : '30'; ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="status">حالة الكورس</label>
                                    <select class="form-control" id="status" name="status" required>
                                        <option value="active" <?php echo (isset($_POST['status']) && $_POST['status'] === 'active') ? 'selected' : ''; ?>>نشط</option>
                                        <option value="draft" <?php echo (isset($_POST['status']) && $_POST['status'] === 'draft') ? 'selected' : ''; ?>>مسودة</option>
                                        <option value="completed" <?php echo (isset($_POST['status']) && $_POST['status'] === 'completed') ? 'selected' : ''; ?>>مكتمل</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="text-center">
                            <button type="submit" class="btn btn-primary">إضافة الكورس</button>
                            <a href="dashboard.php" class="btn btn-secondary">إلغاء</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// إظهار/إخفاء حقل السعر حسب نوع الكورس
function togglePriceField() {
    const courseType = document.querySelector('input[name="course_type"]:checked').value;
    const priceField = document.getElementById('price_field');
    const priceInput = document.getElementById('price');

    if (courseType === 'paid') {
        priceField.style.display = 'block';
        priceInput.required = true;
    } else {
        priceField.style.display = 'none';
        priceInput.required = false;
        priceInput.value = '';
    }
}

// تشغيل الدالة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    togglePriceField();

    // إضافة مستمع للأحداث
    document.querySelectorAll('input[name="course_type"]').forEach(function(radio) {
        radio.addEventListener('change', togglePriceField);
    });
});

// التحقق من صحة النموذج قبل الإرسال
document.querySelector('form').addEventListener('submit', function(e) {
    const courseType = document.querySelector('input[name="course_type"]:checked').value;
    const price = document.getElementById('price').value;

    if (courseType === 'paid') {
        if (!price || parseFloat(price) <= 0) {
            e.preventDefault();
            alert('يرجى إدخال سعر صحيح للكورس المدفوع');
            return false;
        }
    }
});
</script>

<?php require_once '../includes/footer.php'; ?>