<?php
/**
 * ملف الوظائف المساعدة العامة
 * General Helper Functions
 */

/**
 * تنظيف وتأمين النصوص
 */
function sanitize($input) {
    if (is_array($input)) {
        return array_map('sanitize', $input);
    }
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

/**
 * التحقق من صحة البريد الإلكتروني
 */
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * التحقق من قوة كلمة المرور
 */
function isStrongPassword($password) {
    return strlen($password) >= 8 && 
           preg_match('/[A-Z]/', $password) && 
           preg_match('/[a-z]/', $password) && 
           preg_match('/[0-9]/', $password) && 
           preg_match('/[^A-Za-z0-9]/', $password);
}

/**
 * تشفير كلمة المرور
 */
function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

/**
 * التحقق من كلمة المرور
 */
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

/**
 * إنشاء رمز عشوائي آمن
 */
function generateToken($length = 32) {
    return bin2hex(random_bytes($length / 2));
}

/**
 * تحويل التاريخ إلى تنسيق عربي
 */
function formatArabicDate($date, $format = 'Y-m-d H:i') {
    $arabicMonths = [
        1 => 'يناير', 2 => 'فبراير', 3 => 'مارس', 4 => 'أبريل',
        5 => 'مايو', 6 => 'يونيو', 7 => 'يوليو', 8 => 'أغسطس',
        9 => 'سبتمبر', 10 => 'أكتوبر', 11 => 'نوفمبر', 12 => 'ديسمبر'
    ];
    
    $timestamp = is_string($date) ? strtotime($date) : $date;
    $day = date('d', $timestamp);
    $month = $arabicMonths[(int)date('m', $timestamp)];
    $year = date('Y', $timestamp);
    $time = date('H:i', $timestamp);
    
    return "$day $month $year - $time";
}

/**
 * حساب الوقت المنقضي
 */
function timeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return 'الآن';
    if ($time < 3600) return floor($time/60) . ' دقيقة';
    if ($time < 86400) return floor($time/3600) . ' ساعة';
    if ($time < 2592000) return floor($time/86400) . ' يوم';
    if ($time < 31536000) return floor($time/2592000) . ' شهر';
    
    return floor($time/31536000) . ' سنة';
}

/**
 * تحويل حجم الملف إلى تنسيق قابل للقراءة
 */
function formatFileSize($bytes) {
    $units = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت', 'تيرابايت'];
    
    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, 2) . ' ' . $units[$i];
}

/**
 * اقتطاع النص مع الحفاظ على الكلمات
 */
function truncateText($text, $length = 100, $suffix = '...') {
    if (mb_strlen($text) <= $length) {
        return $text;
    }
    
    $truncated = mb_substr($text, 0, $length);
    $lastSpace = mb_strrpos($truncated, ' ');
    
    if ($lastSpace !== false) {
        $truncated = mb_substr($truncated, 0, $lastSpace);
    }
    
    return $truncated . $suffix;
}

/**
 * تحويل الأرقام إلى أرقام عربية
 */
function toArabicNumbers($string) {
    $western = ['0','1','2','3','4','5','6','7','8','9'];
    $arabic = ['٠','١','٢','٣','٤','٥','٦','٧','٨','٩'];
    
    return str_replace($western, $arabic, $string);
}

/**
 * تحويل الأرقام العربية إلى إنجليزية
 */
function toEnglishNumbers($string) {
    $arabic = ['٠','١','٢','٣','٤','٥','٦','٧','٨','٩'];
    $western = ['0','1','2','3','4','5','6','7','8','9'];
    
    return str_replace($arabic, $western, $string);
}

/**
 * التحقق من نوع الملف
 */
function isValidFileType($filename, $allowedTypes) {
    $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
    return in_array($extension, $allowedTypes);
}

/**
 * رفع الملفات بشكل آمن
 */
function uploadFile($file, $destination, $allowedTypes = [], $maxSize = 5242880) {
    if (!isset($file['tmp_name']) || !is_uploaded_file($file['tmp_name'])) {
        return ['success' => false, 'message' => 'لم يتم رفع الملف'];
    }
    
    if ($file['size'] > $maxSize) {
        return ['success' => false, 'message' => 'حجم الملف كبير جداً'];
    }
    
    if (!empty($allowedTypes) && !isValidFileType($file['name'], $allowedTypes)) {
        return ['success' => false, 'message' => 'نوع الملف غير مسموح'];
    }
    
    $filename = generateToken(16) . '_' . basename($file['name']);
    $filepath = $destination . '/' . $filename;
    
    if (!is_dir($destination)) {
        mkdir($destination, 0755, true);
    }
    
    if (move_uploaded_file($file['tmp_name'], $filepath)) {
        return ['success' => true, 'filename' => $filename, 'filepath' => $filepath];
    }
    
    return ['success' => false, 'message' => 'فشل في رفع الملف'];
}

/**
 * إرسال بريد إلكتروني
 */
function sendEmail($to, $subject, $message, $from = null) {
    $from = $from ?: '<EMAIL>';
    
    $headers = [
        'From' => $from,
        'Reply-To' => $from,
        'Content-Type' => 'text/html; charset=UTF-8',
        'MIME-Version' => '1.0'
    ];
    
    $headerString = '';
    foreach ($headers as $key => $value) {
        $headerString .= "$key: $value\r\n";
    }
    
    return mail($to, $subject, $message, $headerString);
}

/**
 * تسجيل الأخطاء
 */
function logError($message, $file = null, $line = null) {
    $logMessage = date('Y-m-d H:i:s') . ' - ' . $message;
    if ($file) $logMessage .= " في $file";
    if ($line) $logMessage .= " السطر $line";
    $logMessage .= "\n";
    
    $logFile = 'logs/error_' . date('Y-m-d') . '.log';
    file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
}

/**
 * تسجيل النشاطات
 */
function logActivity($userId, $action, $details = []) {
    $logData = [
        'timestamp' => date('Y-m-d H:i:s'),
        'user_id' => $userId,
        'action' => $action,
        'details' => $details,
        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
    ];
    
    $logFile = 'logs/activity_' . date('Y-m-d') . '.log';
    file_put_contents($logFile, json_encode($logData) . "\n", FILE_APPEND | LOCK_EX);
}

/**
 * إنشاء URL آمن
 */
function url($path = '') {
    $baseUrl = rtrim($_SERVER['HTTP_HOST'], '/');
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    return $protocol . '://' . $baseUrl . '/' . ltrim($path, '/');
}

/**
 * إعادة التوجيه
 */
function redirect($url, $statusCode = 302) {
    header("Location: $url", true, $statusCode);
    exit;
}

/**
 * إرجاع استجابة JSON
 */
function jsonResponse($data, $statusCode = 200) {
    http_response_code($statusCode);
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode($data, JSON_UNESCAPED_UNICODE);
    exit;
}

/**
 * التحقق من CSRF Token
 */
function verifyCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * إنشاء CSRF Token
 */
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = generateToken();
    }
    return $_SESSION['csrf_token'];
}

/**
 * تنظيف المدخلات من XSS
 */
function cleanXSS($input) {
    $input = trim($input);
    $input = stripslashes($input);
    $input = htmlspecialchars($input, ENT_QUOTES, 'UTF-8');
    return $input;
}

/**
 * التحقق من صحة رقم الهاتف السعودي
 */
function isValidSaudiPhone($phone) {
    $phone = preg_replace('/[^0-9]/', '', $phone);
    return preg_match('/^(05|5)[0-9]{8}$/', $phone);
}

/**
 * تحويل النص إلى slug
 */
function createSlug($text) {
    $text = trim($text);
    $text = mb_strtolower($text);
    $text = preg_replace('/[^a-z0-9\u0600-\u06FF\s-]/', '', $text);
    $text = preg_replace('/[\s-]+/', '-', $text);
    return trim($text, '-');
}

/**
 * التحقق من وجود الإنترنت
 */
function isOnline() {
    $connected = @fsockopen("www.google.com", 80);
    if ($connected) {
        fclose($connected);
        return true;
    }
    return false;
}

/**
 * ضغط الصور
 */
function compressImage($source, $destination, $quality = 85) {
    $info = getimagesize($source);
    
    if ($info['mime'] == 'image/jpeg') {
        $image = imagecreatefromjpeg($source);
        imagejpeg($image, $destination, $quality);
    } elseif ($info['mime'] == 'image/png') {
        $image = imagecreatefrompng($source);
        imagepng($image, $destination, 9 - round(($quality / 100) * 9));
    } elseif ($info['mime'] == 'image/gif') {
        $image = imagecreatefromgif($source);
        imagegif($image, $destination);
    }
    
    if (isset($image)) {
        imagedestroy($image);
        return true;
    }
    
    return false;
}

/**
 * إنشاء صورة مصغرة
 */
function createThumbnail($source, $destination, $width = 300, $height = 300) {
    list($originalWidth, $originalHeight) = getimagesize($source);
    
    $ratio = min($width / $originalWidth, $height / $originalHeight);
    $newWidth = $originalWidth * $ratio;
    $newHeight = $originalHeight * $ratio;
    
    $thumbnail = imagecreatetruecolor($newWidth, $newHeight);
    $source_image = imagecreatefromjpeg($source);
    
    imagecopyresampled($thumbnail, $source_image, 0, 0, 0, 0, $newWidth, $newHeight, $originalWidth, $originalHeight);
    
    imagejpeg($thumbnail, $destination, 85);
    imagedestroy($thumbnail);
    imagedestroy($source_image);
    
    return true;
}

/**
 * تحويل العملة
 */
function convertCurrency($amount, $from = 'USD', $to = 'SAR') {
    // هذه دالة مبسطة - في التطبيق الحقيقي يجب استخدام API للعملات
    $rates = [
        'USD' => 1,
        'SAR' => 3.75,
        'EUR' => 0.85,
    ];
    
    if (!isset($rates[$from]) || !isset($rates[$to])) {
        return $amount;
    }
    
    $usdAmount = $amount / $rates[$from];
    return $usdAmount * $rates[$to];
}

/**
 * التحقق من صحة البيانات
 */
function validate($data, $rules) {
    $errors = [];
    
    foreach ($rules as $field => $rule) {
        $value = $data[$field] ?? null;
        $ruleArray = explode('|', $rule);
        
        foreach ($ruleArray as $singleRule) {
            if ($singleRule === 'required' && empty($value)) {
                $errors[$field] = "حقل $field مطلوب";
                break;
            }
            
            if ($singleRule === 'email' && !empty($value) && !isValidEmail($value)) {
                $errors[$field] = "حقل $field يجب أن يكون بريد إلكتروني صحيح";
                break;
            }
            
            if (strpos($singleRule, 'min:') === 0) {
                $min = (int)substr($singleRule, 4);
                if (!empty($value) && strlen($value) < $min) {
                    $errors[$field] = "حقل $field يجب أن يكون على الأقل $min أحرف";
                    break;
                }
            }
            
            if (strpos($singleRule, 'max:') === 0) {
                $max = (int)substr($singleRule, 4);
                if (!empty($value) && strlen($value) > $max) {
                    $errors[$field] = "حقل $field يجب أن يكون أقل من $max حرف";
                    break;
                }
            }
        }
    }
    
    return $errors;
}
?>
