<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من صلاحيات المدرب
if (!isLoggedIn() || !isInstructor()) {
    header('Location: ../login.php');
    exit;
}

$course_id = (int)($_GET['course_id'] ?? 0);
$student_id = (int)($_GET['student_id'] ?? 0);
$error = '';
$success = '';

// التحقق من أن الكورس ينتمي للمدرب
try {
    $stmt = $conn->prepare("SELECT title FROM courses WHERE id = ? AND instructor_id = ?");
    $stmt->execute([$course_id, $_SESSION['user_id']]);
    $course = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$course) {
        header('Location: courses.php');
        exit;
    }
} catch (PDOException $e) {
    $error = 'خطأ في التحقق من الكورس';
}

// جلب قائمة الطلاب في الكورس
try {
    $stmt = $conn->prepare("
        SELECT u.id, u.name, u.email, e.status 
        FROM users u 
        JOIN enrollments e ON u.id = e.student_id 
        WHERE e.course_id = ? AND e.status = 'approved'
        ORDER BY u.name
    ");
    $stmt->execute([$course_id]);
    $students = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $students = [];
}

// إنشاء جدول الرسائل إذا لم يكن موجوداً
try {
    $conn->exec("
        CREATE TABLE IF NOT EXISTS messages (
            id INT AUTO_INCREMENT PRIMARY KEY,
            sender_id INT NOT NULL,
            recipient_id INT NULL,
            course_id INT NOT NULL,
            subject VARCHAR(255) NOT NULL,
            message TEXT NOT NULL,
            message_type ENUM('individual', 'group', 'announcement') DEFAULT 'individual',
            is_read BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            
            INDEX idx_sender (sender_id),
            INDEX idx_recipient (recipient_id),
            INDEX idx_course (course_id),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
} catch (PDOException $e) {
    // الجدول موجود بالفعل
}

// معالجة إرسال الرسالة
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $message_type = $_POST['message_type'] ?? 'individual';
    $subject = trim($_POST['subject'] ?? '');
    $message = trim($_POST['message'] ?? '');
    $recipients = $_POST['recipients'] ?? [];
    $send_email = isset($_POST['send_email']);
    
    if (empty($subject)) {
        $error = 'عنوان الرسالة مطلوب';
    } elseif (empty($message)) {
        $error = 'نص الرسالة مطلوب';
    } elseif ($message_type === 'individual' && empty($recipients)) {
        $error = 'يرجى اختيار المستقبلين';
    } else {
        try {
            $conn->beginTransaction();
            
            if ($message_type === 'group') {
                // رسالة جماعية لجميع الطلاب
                foreach ($students as $student) {
                    $stmt = $conn->prepare("
                        INSERT INTO messages (sender_id, recipient_id, course_id, subject, message, message_type) 
                        VALUES (?, ?, ?, ?, ?, 'group')
                    ");
                    $stmt->execute([$_SESSION['user_id'], $student['id'], $course_id, $subject, $message]);
                    
                    // إرسال بريد إلكتروني إذا تم تحديد الخيار
                    if ($send_email && function_exists('sendEmail')) {
                        $email_subject = "رسالة من مدرب الكورس: " . $course['title'];
                        $email_message = "
                            مرحباً {$student['name']},
                            
                            لديك رسالة جديدة من مدرب الكورس: {$course['title']}
                            
                            الموضوع: $subject
                            
                            الرسالة:
                            $message
                            
                            يمكنك تسجيل الدخول لعرض الرسالة والرد عليها.
                            
                            مع تحيات فريق المنصة
                        ";
                        
                        sendEmail($student['email'], $email_subject, $email_message);
                    }
                }
                
                $success = 'تم إرسال الرسالة لجميع الطلاب (' . count($students) . ' طالب)';
                
            } elseif ($message_type === 'announcement') {
                // إعلان عام
                $stmt = $conn->prepare("
                    INSERT INTO messages (sender_id, course_id, subject, message, message_type) 
                    VALUES (?, ?, ?, ?, 'announcement')
                ");
                $stmt->execute([$_SESSION['user_id'], $course_id, $subject, $message]);
                
                $success = 'تم نشر الإعلان بنجاح';
                
            } else {
                // رسائل فردية
                $sent_count = 0;
                foreach ($recipients as $recipient_id) {
                    if (in_array($recipient_id, array_column($students, 'id'))) {
                        $stmt = $conn->prepare("
                            INSERT INTO messages (sender_id, recipient_id, course_id, subject, message, message_type) 
                            VALUES (?, ?, ?, ?, ?, 'individual')
                        ");
                        $stmt->execute([$_SESSION['user_id'], $recipient_id, $course_id, $subject, $message]);
                        
                        // إرسال بريد إلكتروني
                        if ($send_email && function_exists('sendEmail')) {
                            $student = array_filter($students, function($s) use ($recipient_id) {
                                return $s['id'] == $recipient_id;
                            });
                            $student = reset($student);
                            
                            if ($student) {
                                $email_subject = "رسالة شخصية من مدرب الكورس: " . $course['title'];
                                $email_message = "
                                    مرحباً {$student['name']},
                                    
                                    لديك رسالة شخصية من مدرب الكورس: {$course['title']}
                                    
                                    الموضوع: $subject
                                    
                                    الرسالة:
                                    $message
                                    
                                    يمكنك تسجيل الدخول للرد على الرسالة.
                                    
                                    مع تحيات فريق المنصة
                                ";
                                
                                sendEmail($student['email'], $email_subject, $email_message);
                            }
                        }
                        
                        $sent_count++;
                    }
                }
                
                $success = "تم إرسال الرسالة لـ $sent_count طالب";
            }
            
            $conn->commit();
            
        } catch (PDOException $e) {
            $conn->rollBack();
            $error = 'خطأ في إرسال الرسالة: ' . $e->getMessage();
        }
    }
}

$pageTitle = 'إرسال رسالة - ' . $course['title'];
$breadcrumbs = [
    ['title' => 'الكورسات', 'url' => 'courses.php'],
    ['title' => $course['title'], 'url' => 'course-details.php?id=' . $course_id],
    ['title' => 'الطلاب', 'url' => 'students.php?course_id=' . $course_id],
    ['title' => 'إرسال رسالة']
];

include 'includes/header.php';
?>

<div class="row">
    <div class="col-md-8">
        <!-- نموذج إرسال الرسالة -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-envelope me-2"></i>
                    إرسال رسالة للطلاب
                </h5>
            </div>
            <div class="card-body">
                <?php if ($error): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endif; ?>

                <?php if ($success): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        <?php echo htmlspecialchars($success); ?>
                    </div>
                <?php endif; ?>

                <form method="POST" action="">
                    <!-- نوع الرسالة -->
                    <div class="mb-4">
                        <label class="form-label">نوع الرسالة</label>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="message_type" id="individual" value="individual" checked>
                                    <label class="form-check-label" for="individual">
                                        <i class="fas fa-user me-2"></i>
                                        رسالة فردية
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="message_type" id="group" value="group">
                                    <label class="form-check-label" for="group">
                                        <i class="fas fa-users me-2"></i>
                                        رسالة جماعية
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="message_type" id="announcement" value="announcement">
                                    <label class="form-check-label" for="announcement">
                                        <i class="fas fa-bullhorn me-2"></i>
                                        إعلان عام
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- اختيار المستقبلين -->
                    <div class="mb-3" id="recipients-section">
                        <label class="form-label">المستقبلون</label>
                        <div class="border rounded p-3" style="max-height: 200px; overflow-y: auto;">
                            <?php if (empty($students)): ?>
                                <p class="text-muted mb-0">لا يوجد طلاب مسجلون في هذا الكورس</p>
                            <?php else: ?>
                                <div class="mb-2">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="select-all">
                                        <label class="form-check-label fw-bold" for="select-all">
                                            تحديد الكل
                                        </label>
                                    </div>
                                    <hr>
                                </div>
                                
                                <?php foreach ($students as $student): ?>
                                    <div class="form-check">
                                        <input class="form-check-input student-checkbox" type="checkbox" 
                                               name="recipients[]" value="<?php echo $student['id']; ?>" 
                                               id="student-<?php echo $student['id']; ?>"
                                               <?php echo $student_id == $student['id'] ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="student-<?php echo $student['id']; ?>">
                                            <?php echo htmlspecialchars($student['name']); ?>
                                            <small class="text-muted">(<?php echo htmlspecialchars($student['email']); ?>)</small>
                                        </label>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- موضوع الرسالة -->
                    <div class="mb-3">
                        <label for="subject" class="form-label">موضوع الرسالة</label>
                        <input type="text" class="form-control" id="subject" name="subject" 
                               placeholder="أدخل موضوع الرسالة..." required>
                    </div>

                    <!-- نص الرسالة -->
                    <div class="mb-3">
                        <label for="message" class="form-label">نص الرسالة</label>
                        <textarea class="form-control" id="message" name="message" rows="6" 
                                  placeholder="اكتب رسالتك هنا..." required></textarea>
                    </div>

                    <!-- خيارات إضافية -->
                    <div class="mb-4">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="send_email" name="send_email" checked>
                            <label class="form-check-label" for="send_email">
                                إرسال نسخة بالبريد الإلكتروني
                            </label>
                            <div class="form-text">سيتم إرسال نسخة من الرسالة لبريد الطلاب الإلكتروني</div>
                        </div>
                    </div>

                    <!-- أزرار الإرسال -->
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-paper-plane me-2"></i>
                            إرسال الرسالة
                        </button>
                        <a href="students.php?course_id=<?php echo $course_id; ?>" class="btn btn-secondary">
                            <i class="fas fa-arrow-right me-2"></i>
                            العودة للطلاب
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <!-- معلومات الكورس -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-graduation-cap me-2"></i>
                    معلومات الكورس
                </h6>
            </div>
            <div class="card-body">
                <h6><?php echo htmlspecialchars($course['title']); ?></h6>
                <p class="text-muted mb-3">عدد الطلاب: <?php echo count($students); ?></p>
                
                <div class="d-grid gap-2">
                    <a href="students.php?course_id=<?php echo $course_id; ?>" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-users me-2"></i>
                        عرض جميع الطلاب
                    </a>
                    <a href="course-details.php?id=<?php echo $course_id; ?>" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للكورس
                    </a>
                </div>
            </div>
        </div>

        <!-- قوالب الرسائل -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-templates me-2"></i>
                    قوالب سريعة
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-outline-info btn-sm template-btn" 
                            data-subject="تذكير بموعد الجلسة القادمة"
                            data-message="مرحباً،&#10;&#10;أذكركم بموعد الجلسة القادمة للكورس يوم [التاريخ] في تمام الساعة [الوقت].&#10;&#10;يرجى الحضور في الموعد المحدد.&#10;&#10;مع تحياتي">
                        <i class="fas fa-calendar me-2"></i>
                        تذكير بالجلسة
                    </button>
                    
                    <button type="button" class="btn btn-outline-success btn-sm template-btn"
                            data-subject="تهنئة بالأداء المتميز"
                            data-message="مرحباً،&#10;&#10;أود أن أهنئك على أدائك المتميز في الكورس. استمر في هذا المستوى الرائع!&#10;&#10;مع تحياتي">
                        <i class="fas fa-trophy me-2"></i>
                        تهنئة
                    </button>
                    
                    <button type="button" class="btn btn-outline-warning btn-sm template-btn"
                            data-subject="تنبيه بخصوص الواجب"
                            data-message="مرحباً،&#10;&#10;أذكرك بأن موعد تسليم الواجب هو [التاريخ]. يرجى التأكد من إرسال الواجب في الموعد المحدد.&#10;&#10;مع تحياتي">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        تذكير بالواجب
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.card-header {
    background: var(--instructor-light);
    border-bottom: 1px solid var(--gray-200);
    border-radius: 12px 12px 0 0 !important;
}

.form-check-input:checked {
    background-color: var(--instructor-primary);
    border-color: var(--instructor-primary);
}

.form-control:focus,
.form-check-input:focus {
    border-color: var(--instructor-primary);
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}
</style>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const messageTypeRadios = document.querySelectorAll('input[name="message_type"]');
    const recipientsSection = document.getElementById('recipients-section');
    const selectAllCheckbox = document.getElementById('select-all');
    const studentCheckboxes = document.querySelectorAll('.student-checkbox');
    
    // إخفاء/إظهار قسم المستقبلين حسب نوع الرسالة
    messageTypeRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            if (this.value === 'individual') {
                recipientsSection.style.display = 'block';
            } else {
                recipientsSection.style.display = 'none';
            }
        });
    });
    
    // تحديد/إلغاء تحديد الكل
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            studentCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
        });
    }
    
    // تحديث حالة "تحديد الكل" عند تغيير الطلاب
    studentCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const checkedCount = document.querySelectorAll('.student-checkbox:checked').length;
            const totalCount = studentCheckboxes.length;
            
            if (selectAllCheckbox) {
                selectAllCheckbox.checked = checkedCount === totalCount;
                selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < totalCount;
            }
        });
    });
    
    // قوالب الرسائل
    document.querySelectorAll('.template-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const subject = this.getAttribute('data-subject');
            const message = this.getAttribute('data-message').replace(/&#10;/g, '\n');
            
            document.getElementById('subject').value = subject;
            document.getElementById('message').value = message;
        });
    });
});
</script>

</body>
</html>
