<?php
require_once '../../includes/session_config.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';
require_once '../../includes/security.php';

// التحقق من صلاحيات المدير
if (!isLoggedIn() || !isAdmin()) {
    echo json_encode(['success' => false, 'message' => 'غير مصرح لك بهذا الإجراء']);
    exit;
}

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'طريقة طلب غير صحيحة']);
    exit;
}

$course_id = $_POST['course_id'] ?? 0;

if (!$course_id) {
    echo json_encode(['success' => false, 'message' => 'معرف الكورس مطلوب']);
    exit;
}

try {
    // بدء المعاملة
    $conn->beginTransaction();
    
    // جلب معلومات الكورس قبل الحذف
    $stmt = $conn->prepare("SELECT title, image FROM courses WHERE id = ?");
    $stmt->execute([$course_id]);
    $course = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$course) {
        throw new Exception('الكورس غير موجود');
    }
    
    // حذف صورة الكورس إذا كانت موجودة
    if ($course['image'] && file_exists('../../' . $course['image'])) {
        unlink('../../' . $course['image']);
    }

    // 1. حذف سجلات الحضور للجلسات المرتبطة بالكورس
    $stmt = $conn->prepare("
        DELETE sa FROM session_attendance sa
        JOIN sessions s ON sa.session_id = s.id
        WHERE s.course_id = ?
    ");
    $stmt->execute([$course_id]);
    $deleted_attendance = $stmt->rowCount();

    // 2. حذف الجلسات
    $stmt = $conn->prepare("DELETE FROM sessions WHERE course_id = ?");
    $stmt->execute([$course_id]);
    $deleted_sessions = $stmt->rowCount();

    // 3. حذف تقييمات المدربين المرتبطة بالكورس
    $stmt = $conn->prepare("
        DELETE ir FROM instructor_reviews ir
        JOIN courses c ON ir.instructor_id = c.instructor_id
        WHERE c.id = ?
    ");
    $stmt->execute([$course_id]);

    // 4. حذف تقييمات الكورس
    $stmt = $conn->prepare("DELETE FROM course_reviews WHERE course_id = ?");
    $stmt->execute([$course_id]);
    $deleted_reviews = $stmt->rowCount();

    // 5. حذف تسجيلات الطلاب
    $stmt = $conn->prepare("DELETE FROM course_enrollments WHERE course_id = ?");
    $stmt->execute([$course_id]);
    $deleted_enrollments = $stmt->rowCount();

    // 6. حذف أي ملفات مرفقة بالجلسات (إذا كان العمود موجود)
    try {
        $stmt = $conn->prepare("SELECT recording_url FROM sessions WHERE course_id = ? AND recording_url IS NOT NULL");
        $stmt->execute([$course_id]);
        $recordings = $stmt->fetchAll(PDO::FETCH_COLUMN);

        foreach ($recordings as $recording) {
            if ($recording && file_exists('../../' . $recording)) {
                unlink('../../' . $recording);
            }
        }
    } catch (Exception $e) {
        // تجاهل الخطأ إذا كان العمود غير موجود
    }

    // 7. حذف الكورس نفسه
    $stmt = $conn->prepare("DELETE FROM courses WHERE id = ?");
    $stmt->execute([$course_id]);

    // التحقق من نجاح الحذف
    if ($stmt->rowCount() === 0) {
        throw new Exception('فشل في حذف الكورس');
    }
    
    // تسجيل النشاط مع تفاصيل الحذف
    $details = "تم حذف الكورس: " . $course['title'] . " مع البيانات التالية:";
    $details .= "\n- عدد الجلسات المحذوفة: $deleted_sessions";
    $details .= "\n- عدد التسجيلات المحذوفة: $deleted_enrollments";
    $details .= "\n- عدد التقييمات المحذوفة: $deleted_reviews";
    $details .= "\n- عدد سجلات الحضور المحذوفة: $deleted_attendance";

    logUserActivity($_SESSION['user_id'], 'حذف كورس', $details);

    // تأكيد المعاملة
    $conn->commit();

    echo json_encode([
        'success' => true,
        'message' => 'تم حذف الكورس وجميع بياناته بنجاح',
        'details' => [
            'sessions_deleted' => $deleted_sessions,
            'enrollments_deleted' => $deleted_enrollments,
            'reviews_deleted' => $deleted_reviews,
            'attendance_deleted' => $deleted_attendance
        ]
    ]);
    
} catch (Exception $e) {
    // التراجع عن المعاملة في حالة الخطأ
    $conn->rollBack();
    
    error_log("Error deleting course: " . $e->getMessage());
    echo json_encode([
        'success' => false, 
        'message' => 'حدث خطأ أثناء حذف الكورس: ' . $e->getMessage()
    ]);
}
?>
