<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

$course_id = isset($_GET['course_id']) ? (int)$_GET['course_id'] : 0;
$current_lesson_id = isset($_GET['lesson_id']) ? (int)$_GET['lesson_id'] : 0;
$error = '';

// جلب معلومات الكورس
try {
    $stmt = $conn->prepare("
        SELECT c.*, u.name as instructor_name
        FROM courses c
        JOIN users u ON c.instructor_id = u.id
        WHERE c.id = ? AND c.status = 'active'
    ");
    $stmt->execute([$course_id]);
    $course = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$course) {
        $error = 'الكورس غير موجود أو غير متاح';
    }
} catch (PDOException $e) {
    $error = 'حدث خطأ في جلب معلومات الكورس';
}

// التحقق من التسجيل في الكورس (للطلاب المسجلين)
$is_enrolled = false;
$can_access = false;

if (isLoggedIn() && isStudent()) {
    try {
        $stmt = $conn->prepare("
            SELECT * FROM course_enrollments 
            WHERE course_id = ? AND student_id = ? AND status = 'active'
        ");
        $stmt->execute([$course_id, $_SESSION['user_id']]);
        $enrollment = $stmt->fetch(PDO::FETCH_ASSOC);
        $is_enrolled = !empty($enrollment);
    } catch (PDOException $e) {
        // تجاهل الخطأ
    }
}

// يمكن الوصول إذا كان مسجل أو إذا كان الدرس مجاني
$can_access = $is_enrolled;

// جلب فصول ودروس الكورس
$chapters = [];
$current_lesson = null;

if ($course) {
    try {
        $stmt = $conn->prepare("
            SELECT ch.*, 
                   (SELECT COUNT(*) FROM course_lessons WHERE chapter_id = ch.id AND status = 'active') as lessons_count
            FROM course_chapters ch
            WHERE ch.course_id = ? AND ch.status = 'active'
            ORDER BY ch.chapter_order
        ");
        $stmt->execute([$course_id]);
        $chapters = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // جلب دروس كل فصل
        foreach ($chapters as &$chapter) {
            $stmt = $conn->prepare("
                SELECT * FROM course_lessons 
                WHERE chapter_id = ? AND status = 'active'
                ORDER BY lesson_order
            ");
            $stmt->execute([$chapter['id']]);
            $chapter['lessons'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
        }
        
        // إذا لم يتم تحديد درس، اختر أول درس متاح
        if (!$current_lesson_id && !empty($chapters)) {
            foreach ($chapters as $chapter) {
                if (!empty($chapter['lessons'])) {
                    $first_lesson = $chapter['lessons'][0];
                    if ($first_lesson['is_free'] || $can_access) {
                        $current_lesson_id = $first_lesson['id'];
                        break;
                    }
                }
            }
        }
        
        // جلب الدرس الحالي
        if ($current_lesson_id) {
            $stmt = $conn->prepare("
                SELECT cl.*, ch.title as chapter_title
                FROM course_lessons cl
                JOIN course_chapters ch ON cl.chapter_id = ch.id
                WHERE cl.id = ? AND cl.course_id = ? AND cl.status = 'active'
            ");
            $stmt->execute([$current_lesson_id, $course_id]);
            $current_lesson = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // التحقق من إمكانية الوصول للدرس
            if ($current_lesson && !$current_lesson['is_free'] && !$can_access) {
                $current_lesson = null;
            }
        }
        
    } catch (PDOException $e) {
        $error = 'حدث خطأ في جلب محتوى الكورس';
    }
}

// تسجيل تقدم الطالب
if ($current_lesson && isLoggedIn() && isStudent() && $is_enrolled) {
    try {
        $stmt = $conn->prepare("
            INSERT INTO student_lesson_progress (student_id, course_id, lesson_id, last_watched_at)
            VALUES (?, ?, ?, NOW())
            ON DUPLICATE KEY UPDATE last_watched_at = NOW()
        ");
        $stmt->execute([$_SESSION['user_id'], $course_id, $current_lesson_id]);
    } catch (PDOException $e) {
        // تجاهل الخطأ
    }
}

$pageTitle = $course ? $course['title'] : 'محتوى الكورس';
?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($pageTitle); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        
        .course-sidebar {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            max-height: calc(100vh - 100px);
            overflow-y: auto;
        }
        
        .video-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .chapter-header {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .chapter-header:hover {
            background: linear-gradient(45deg, #764ba2, #667eea);
        }
        
        .lesson-item {
            padding: 12px 20px;
            border-bottom: 1px solid #e9ecef;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .lesson-item:hover {
            background: #f8f9fa;
        }
        
        .lesson-item.active {
            background: linear-gradient(45deg, #e3f2fd, #f3e5f5);
            border-left: 4px solid #667eea;
        }
        
        .lesson-item.locked {
            opacity: 0.6;
            cursor: not-allowed;
        }
        
        .video-player {
            width: 100%;
            height: 400px;
            background: #000;
            border-radius: 10px;
        }
        
        .lesson-content {
            padding: 20px;
        }
        
        .progress-bar-custom {
            height: 8px;
            border-radius: 10px;
        }
        
        .btn-download {
            background: linear-gradient(45deg, #28a745, #20c997);
            border: none;
            border-radius: 25px;
            color: white;
            padding: 10px 20px;
            transition: all 0.3s ease;
        }
        
        .btn-download:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
            color: white;
        }
        
        .enrollment-notice {
            background: linear-gradient(45deg, #ffc107, #fd7e14);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 20px;
        }
        
        .free-badge {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 0.8rem;
        }
        
        .locked-badge {
            background: linear-gradient(45deg, #dc3545, #fd7e14);
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 0.8rem;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
        <div class="container">
            <a class="navbar-brand" href="../index.php">
                <i class="fas fa-graduation-cap text-primary me-2"></i>
                منصة التعلم
            </a>
            
            <div class="d-flex align-items-center">
                <a href="../course-details.php?id=<?php echo $course_id; ?>" class="btn btn-outline-primary me-2">
                    <i class="fas fa-info-circle me-1"></i>
                    تفاصيل الكورس
                </a>
                
                <?php if (isLoggedIn()): ?>
                    <a href="../<?php echo $_SESSION['role']; ?>/dashboard.php" class="btn btn-primary">
                        <i class="fas fa-tachometer-alt me-1"></i>
                        لوحة التحكم
                    </a>
                <?php else: ?>
                    <a href="../login.php" class="btn btn-primary">
                        <i class="fas fa-sign-in-alt me-1"></i>
                        تسجيل الدخول
                    </a>
                <?php endif; ?>
            </div>
        </div>
    </nav>

    <div class="container-fluid py-4">
        <?php if ($error): ?>
            <div class="row justify-content-center">
                <div class="col-md-6">
                    <div class="alert alert-danger text-center">
                        <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
                        <h4><?php echo htmlspecialchars($error); ?></h4>
                        <a href="../courses.php" class="btn btn-primary mt-3">تصفح الكورسات</a>
                    </div>
                </div>
            </div>
        <?php else: ?>
            <div class="row">
                <!-- الشريط الجانبي - قائمة الدروس -->
                <div class="col-lg-4">
                    <div class="course-sidebar">
                        <div class="p-3 border-bottom">
                            <h5 class="mb-1"><?php echo htmlspecialchars($course['title']); ?></h5>
                            <p class="text-muted mb-0">المدرب: <?php echo htmlspecialchars($course['instructor_name']); ?></p>
                        </div>
                        
                        <?php if (!$is_enrolled && isLoggedIn()): ?>
                            <div class="enrollment-notice">
                                <i class="fas fa-lock fa-2x mb-2"></i>
                                <h6>يجب التسجيل في الكورس</h6>
                                <p class="mb-2">للوصول لجميع الدروس</p>
                                <a href="../course-details.php?id=<?php echo $course_id; ?>" class="btn btn-light">
                                    سجل الآن
                                </a>
                            </div>
                        <?php elseif (!isLoggedIn()): ?>
                            <div class="enrollment-notice">
                                <i class="fas fa-user-plus fa-2x mb-2"></i>
                                <h6>سجل دخولك أولاً</h6>
                                <p class="mb-2">للوصول لمحتوى الكورس</p>
                                <a href="../login.php" class="btn btn-light">
                                    تسجيل الدخول
                                </a>
                            </div>
                        <?php endif; ?>
                        
                        <!-- قائمة الفصول والدروس -->
                        <?php foreach ($chapters as $chapter): ?>
                            <div class="chapter-section">
                                <div class="chapter-header" data-bs-toggle="collapse" data-bs-target="#chapter-<?php echo $chapter['id']; ?>">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span>
                                            <i class="fas fa-book me-2"></i>
                                            <?php echo htmlspecialchars($chapter['title']); ?>
                                        </span>
                                        <span class="badge bg-light text-dark"><?php echo $chapter['lessons_count']; ?> دروس</span>
                                    </div>
                                </div>
                                
                                <div class="collapse show" id="chapter-<?php echo $chapter['id']; ?>">
                                    <?php foreach ($chapter['lessons'] as $lesson): ?>
                                        <?php 
                                        $can_access_lesson = $lesson['is_free'] || $can_access;
                                        $is_current = ($lesson['id'] == $current_lesson_id);
                                        ?>
                                        <div class="lesson-item <?php echo $is_current ? 'active' : ''; ?> <?php echo !$can_access_lesson ? 'locked' : ''; ?>"
                                             <?php if ($can_access_lesson): ?>
                                                onclick="window.location.href='?course_id=<?php echo $course_id; ?>&lesson_id=<?php echo $lesson['id']; ?>'"
                                             <?php endif; ?>>
                                            <div>
                                                <i class="fas fa-play-circle me-2 text-primary"></i>
                                                <span><?php echo htmlspecialchars($lesson['title']); ?></span>
                                                <?php if ($lesson['is_free']): ?>
                                                    <span class="free-badge ms-2">مجاني</span>
                                                <?php elseif (!$can_access): ?>
                                                    <span class="locked-badge ms-2">مقفل</span>
                                                <?php endif; ?>
                                            </div>
                                            <div>
                                                <?php if (!$can_access_lesson): ?>
                                                    <i class="fas fa-lock text-muted"></i>
                                                <?php elseif ($is_current): ?>
                                                    <i class="fas fa-play text-primary"></i>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                
                <!-- المحتوى الرئيسي - مشغل الفيديو -->
                <div class="col-lg-8">
                    <?php if ($current_lesson): ?>
                        <div class="video-container">
                            <!-- مشغل الفيديو -->
                            <div class="p-3">
                                <?php if ($current_lesson['video_file_path'] && file_exists($current_lesson['video_file_path'])): ?>
                                    <video class="video-player" controls>
                                        <source src="<?php echo htmlspecialchars($current_lesson['video_file_path']); ?>" type="video/mp4">
                                        متصفحك لا يدعم تشغيل الفيديو
                                    </video>
                                <?php else: ?>
                                    <div class="video-player d-flex align-items-center justify-content-center">
                                        <div class="text-center text-white">
                                            <i class="fas fa-video fa-5x mb-3"></i>
                                            <h4>الفيديو غير متاح حالياً</h4>
                                            <p>سيتم رفع الفيديو قريباً</p>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                            
                            <!-- معلومات الدرس -->
                            <div class="lesson-content">
                                <div class="d-flex justify-content-between align-items-start mb-3">
                                    <div>
                                        <h4><?php echo htmlspecialchars($current_lesson['title']); ?></h4>
                                        <p class="text-muted mb-0">
                                            <i class="fas fa-book me-1"></i>
                                            <?php echo htmlspecialchars($current_lesson['chapter_title']); ?>
                                        </p>
                                    </div>
                                    
                                    <?php if ($current_lesson['video_file_path'] && file_exists($current_lesson['video_file_path'])): ?>
                                        <a href="<?php echo htmlspecialchars($current_lesson['video_file_path']); ?>" 
                                           class="btn btn-download" download>
                                            <i class="fas fa-download me-2"></i>
                                            تحميل الفيديو
                                        </a>
                                    <?php endif; ?>
                                </div>
                                
                                <?php if ($current_lesson['description']): ?>
                                    <div class="mb-4">
                                        <h6>وصف الدرس:</h6>
                                        <p><?php echo nl2br(htmlspecialchars($current_lesson['description'])); ?></p>
                                    </div>
                                <?php endif; ?>
                                
                                <!-- أزرار التنقل -->
                                <div class="d-flex justify-content-between">
                                    <button class="btn btn-outline-primary" onclick="previousLesson()">
                                        <i class="fas fa-chevron-right me-1"></i>
                                        الدرس السابق
                                    </button>
                                    <button class="btn btn-outline-primary" onclick="nextLesson()">
                                        الدرس التالي
                                        <i class="fas fa-chevron-left ms-1"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="video-container">
                            <div class="lesson-content text-center py-5">
                                <i class="fas fa-play-circle fa-5x text-muted mb-4"></i>
                                <h4>اختر درساً لبدء المشاهدة</h4>
                                <p class="text-muted">اختر درساً من القائمة الجانبية لبدء التعلم</p>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function nextLesson() {
            // منطق الانتقال للدرس التالي
            const currentLessonId = <?php echo $current_lesson_id ?: 0; ?>;
            const courseId = <?php echo $course_id; ?>;
            
            // يمكن تطوير هذا لاحقاً للانتقال التلقائي
            console.log('الانتقال للدرس التالي');
        }
        
        function previousLesson() {
            // منطق الانتقال للدرس السابق
            const currentLessonId = <?php echo $current_lesson_id ?: 0; ?>;
            const courseId = <?php echo $course_id; ?>;
            
            // يمكن تطوير هذا لاحقاً للانتقال التلقائي
            console.log('الانتقال للدرس السابق');
        }
        
        // تسجيل تقدم المشاهدة
        const video = document.querySelector('video');
        if (video) {
            video.addEventListener('timeupdate', function() {
                // يمكن إرسال تقدم المشاهدة للخادم
                const progress = (video.currentTime / video.duration) * 100;
                console.log('تقدم المشاهدة:', progress + '%');
            });
        }
    </script>
</body>
</html>
