<?php
require_once '../config/database.php';

echo "<h2>إعداد جداول المدفوعات والعمولات</h2>";

try {
    // إنشاء جدول المدفوعات
    $conn->exec("CREATE TABLE IF NOT EXISTS payments (
        id INT AUTO_INCREMENT PRIMARY KEY,
        student_id INT NOT NULL,
        course_id INT NOT NULL,
        amount DECIMAL(10,2) NOT NULL,
        payment_method VARCHAR(50) NOT NULL DEFAULT 'credit_card',
        payment_reference VARCHAR(255) NULL,
        transaction_id VARCHAR(255) NULL,
        status ENUM('pending', 'completed', 'failed', 'refunded') DEFAULT 'pending',
        admin_notes TEXT NULL,
        payment_date DATETIME NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_student_id (student_id),
        INDEX idx_course_id (course_id),
        INDEX idx_status (status),
        INDEX idx_payment_date (payment_date)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    
    echo "<p>✅ تم إنشاء جدول payments</p>";
    
    // إنشاء جدول العمولات
    $conn->exec("CREATE TABLE IF NOT EXISTS commissions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        instructor_id INT NOT NULL,
        course_id INT NOT NULL,
        payment_id INT NOT NULL,
        course_price DECIMAL(10,2) NOT NULL,
        commission_rate DECIMAL(5,2) NOT NULL,
        commission_amount DECIMAL(10,2) NOT NULL,
        instructor_amount DECIMAL(10,2) NOT NULL,
        status ENUM('pending', 'paid') DEFAULT 'pending',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        paid_at TIMESTAMP NULL,
        INDEX idx_instructor_id (instructor_id),
        INDEX idx_course_id (course_id),
        INDEX idx_payment_id (payment_id),
        INDEX idx_status (status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    
    echo "<p>✅ تم إنشاء جدول commissions</p>";
    
    // إنشاء جدول إعدادات النظام
    $conn->exec("CREATE TABLE IF NOT EXISTS system_settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        setting_key VARCHAR(100) NOT NULL UNIQUE,
        setting_value TEXT NOT NULL,
        description TEXT NULL,
        updated_by INT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_setting_key (setting_key)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    
    echo "<p>✅ تم إنشاء جدول system_settings</p>";
    
    // إضافة إعدادات افتراضية
    $default_settings = [
        ['commission_rate', '30.00', 'نسبة عمولة المنصة (%)'],
        ['currency', 'SAR', 'العملة المستخدمة'],
        ['payment_gateway', 'stripe', 'بوابة الدفع الافتراضية'],
        ['site_name', 'منصة زووم التعليمية', 'اسم الموقع'],
        ['site_email', '<EMAIL>', 'البريد الإلكتروني للموقع'],
        ['max_students_per_course', '100', 'الحد الأقصى للطلاب في الكورس الواحد']
    ];
    
    foreach ($default_settings as $setting) {
        try {
            $stmt = $conn->prepare("INSERT IGNORE INTO system_settings (setting_key, setting_value, description) VALUES (?, ?, ?)");
            $stmt->execute($setting);
            echo "<p>✅ تم إضافة إعداد: " . $setting[0] . "</p>";
        } catch (Exception $e) {
            echo "<p>⚠️ الإعداد موجود مسبقاً: " . $setting[0] . "</p>";
        }
    }
    
    // إضافة بعض المدفوعات التجريبية
    echo "<h3>إضافة مدفوعات تجريبية:</h3>";
    
    // جلب بعض الطلاب والكورسات
    $students = $conn->query("SELECT id, name FROM users WHERE role = 'student' LIMIT 3")->fetchAll();
    $courses = $conn->query("SELECT id, title, price FROM courses WHERE price > 0 LIMIT 3")->fetchAll();
    
    if (!empty($students) && !empty($courses)) {
        $payment_methods = ['credit_card', 'bank_transfer', 'paypal'];
        $statuses = ['completed', 'pending', 'completed', 'completed']; // معظمها مكتملة
        
        foreach ($students as $student) {
            foreach ($courses as $course) {
                // التحقق من عدم وجود دفعة مسبقة
                $check_stmt = $conn->prepare("SELECT id FROM payments WHERE student_id = ? AND course_id = ?");
                $check_stmt->execute([$student['id'], $course['id']]);
                
                if ($check_stmt->rowCount() == 0) {
                    $status = $statuses[array_rand($statuses)];
                    $payment_method = $payment_methods[array_rand($payment_methods)];
                    $amount = $course['price'] ?? 100.00;
                    
                    $stmt = $conn->prepare("
                        INSERT INTO payments (student_id, course_id, amount, payment_method, status, payment_date, transaction_id) 
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    ");
                    $stmt->execute([
                        $student['id'],
                        $course['id'],
                        $amount,
                        $payment_method,
                        $status,
                        date('Y-m-d H:i:s', strtotime('-' . rand(1, 30) . ' days')),
                        'TXN_' . strtoupper(uniqid())
                    ]);
                    
                    $payment_id = $conn->lastInsertId();
                    
                    echo "<p>✅ تم إضافة دفعة: " . htmlspecialchars($student['name']) . " - " . htmlspecialchars($course['title']) . " ($amount ر.س)</p>";
                    
                    // إنشاء عمولة إذا كانت الدفعة مكتملة
                    if ($status === 'completed') {
                        // جلب معرف المدرب
                        $instructor_stmt = $conn->prepare("SELECT instructor_id FROM courses WHERE id = ?");
                        $instructor_stmt->execute([$course['id']]);
                        $instructor = $instructor_stmt->fetch();
                        
                        if ($instructor && $instructor['instructor_id']) {
                            $commission_rate = 30.00; // 30%
                            $commission_amount = ($amount * $commission_rate) / 100;
                            $instructor_amount = $amount - $commission_amount;
                            
                            $comm_stmt = $conn->prepare("
                                INSERT INTO commissions (instructor_id, course_id, payment_id, course_price, commission_rate, commission_amount, instructor_amount, status) 
                                VALUES (?, ?, ?, ?, ?, ?, ?, 'pending')
                            ");
                            $comm_stmt->execute([
                                $instructor['instructor_id'],
                                $course['id'],
                                $payment_id,
                                $amount,
                                $commission_rate,
                                $commission_amount,
                                $instructor_amount
                            ]);
                            
                            echo "<p>💰 تم إنشاء عمولة: $commission_amount ر.س للمنصة، $instructor_amount ر.س للمدرب</p>";
                        }
                    }
                }
            }
        }
    } else {
        echo "<p>⚠️ لا توجد طلاب أو كورسات كافية لإنشاء مدفوعات تجريبية</p>";
        
        // إنشاء مدفوعات تجريبية بسيطة
        $sample_payments = [
            ['student_name' => 'طالب تجريبي 1', 'course_title' => 'كورس تجريبي 1', 'amount' => 299.00],
            ['student_name' => 'طالب تجريبي 2', 'course_title' => 'كورس تجريبي 2', 'amount' => 199.00],
            ['student_name' => 'طالب تجريبي 3', 'course_title' => 'كورس تجريبي 3', 'amount' => 399.00]
        ];
        
        foreach ($sample_payments as $payment) {
            $stmt = $conn->prepare("
                INSERT INTO payments (student_id, course_id, amount, payment_method, status, payment_date, transaction_id) 
                VALUES (1, 1, ?, 'credit_card', 'completed', ?, ?)
            ");
            $stmt->execute([
                $payment['amount'],
                date('Y-m-d H:i:s', strtotime('-' . rand(1, 15) . ' days')),
                'TXN_' . strtoupper(uniqid())
            ]);
            
            echo "<p>✅ تم إضافة دفعة تجريبية: " . $payment['amount'] . " ر.س</p>";
        }
    }
    
    // عرض بنية الجداول
    echo "<h3>بنية جدول payments:</h3>";
    $stmt = $conn->query("DESCRIBE payments");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // عرض الإحصائيات
    echo "<h3>الإحصائيات الحالية:</h3>";
    
    $stats = [
        'payments' => $conn->query("SELECT COUNT(*) FROM payments")->fetchColumn(),
        'completed_payments' => $conn->query("SELECT COUNT(*) FROM payments WHERE status = 'completed'")->fetchColumn(),
        'total_revenue' => $conn->query("SELECT COALESCE(SUM(amount), 0) FROM payments WHERE status = 'completed'")->fetchColumn(),
        'commissions' => $conn->query("SELECT COUNT(*) FROM commissions")->fetchColumn(),
        'total_commissions' => $conn->query("SELECT COALESCE(SUM(commission_amount), 0) FROM commissions")->fetchColumn()
    ];
    
    echo "<ul>";
    echo "<li>إجمالي المدفوعات: " . $stats['payments'] . "</li>";
    echo "<li>المدفوعات المكتملة: " . $stats['completed_payments'] . "</li>";
    echo "<li>إجمالي الإيرادات: " . number_format($stats['total_revenue'], 2) . " ر.س</li>";
    echo "<li>العمولات: " . $stats['commissions'] . "</li>";
    echo "<li>إجمالي العمولات: " . number_format($stats['total_commissions'], 2) . " ر.س</li>";
    echo "</ul>";
    
    echo "<h3>✅ تم إعداد جداول المدفوعات والعمولات بنجاح!</h3>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
}
?>

<p><a href="payments.php">عرض صفحة المدفوعات</a></p>
<p><a href="commissions.php">عرض صفحة العمولات</a></p>
<p><a href="dashboard.php">لوحة التحكم</a></p>
