<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات المدرب
if (!isLoggedIn() || !isInstructor()) {
    header('Location: ../login.php');
    exit;
}

$pageTitle = 'رفع فيديو جديد';
$breadcrumbs = [
    ['title' => 'الفيديوهات', 'url' => 'videos.php'],
    ['title' => 'رفع فيديو جديد']
];

// معالجة رفع الفيديو
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $course_id = $_POST['course_id'] ?? 0;
    $title = trim($_POST['title'] ?? '');
    $description = trim($_POST['description'] ?? '');
    $chapter = trim($_POST['chapter'] ?? '');
    $section = trim($_POST['section'] ?? '');
    $duration = $_POST['duration'] ?? 0;
    $video_type = $_POST['video_type'] ?? 'upload';
    $video_url = trim($_POST['video_url'] ?? '');
    $is_free = isset($_POST['is_free']) ? 1 : 0;
    $order_number = $_POST['order_number'] ?? 1;
    
    $errors = [];
    
    // التحقق من صحة البيانات
    if (empty($title)) {
        $errors[] = 'عنوان الفيديو مطلوب';
    }
    
    if (empty($course_id)) {
        $errors[] = 'يجب اختيار كورس';
    }
    
    if ($video_type === 'url' && empty($video_url)) {
        $errors[] = 'رابط الفيديو مطلوب';
    }
    
    // معالجة رفع الملف
    $video_path = '';
    if ($video_type === 'upload' && isset($_FILES['video_file']) && $_FILES['video_file']['error'] === UPLOAD_ERR_OK) {
        $upload_dir = '../uploads/videos/';
        if (!is_dir($upload_dir)) {
            mkdir($upload_dir, 0755, true);
        }
        
        $file_info = pathinfo($_FILES['video_file']['name']);
        $allowed_extensions = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm'];
        
        if (!in_array(strtolower($file_info['extension']), $allowed_extensions)) {
            $errors[] = 'نوع الملف غير مدعوم. الأنواع المدعومة: ' . implode(', ', $allowed_extensions);
        } else {
            $file_name = uniqid() . '_' . time() . '.' . $file_info['extension'];
            $video_path = $upload_dir . $file_name;
            
            if (!move_uploaded_file($_FILES['video_file']['tmp_name'], $video_path)) {
                $errors[] = 'فشل في رفع الملف';
            }
        }
    } elseif ($video_type === 'upload') {
        $errors[] = 'يرجى اختيار ملف فيديو للرفع';
    }
    
    if (empty($errors)) {
        try {
            // التحقق من أن الكورس ينتمي للمدرب
            $stmt = $conn->prepare("SELECT id, title FROM courses WHERE id = ? AND instructor_id = ?");
            $stmt->execute([$course_id, $_SESSION['user_id']]);
            $course = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$course) {
                $errors[] = 'الكورس غير موجود أو لا تملك صلاحية للوصول إليه';
            } else {
                // إدراج الفيديو الجديد
                $stmt = $conn->prepare("
                    INSERT INTO course_videos (
                        course_id, title, description, chapter, section, duration_minutes,
                        video_type, video_path, video_url, is_free, order_number, 
                        uploaded_by, status
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'active')
                ");
                
                $final_video_path = $video_type === 'upload' ? str_replace('../', '', $video_path) : null;
                $final_video_url = $video_type === 'url' ? $video_url : null;
                
                $stmt->execute([
                    $course_id, $title, $description, $chapter, $section, $duration,
                    $video_type, $final_video_path, $final_video_url, $is_free, $order_number,
                    $_SESSION['user_id']
                ]);
                
                $video_id = $conn->lastInsertId();
                
                // إضافة سجل نشاط
                logActivity($conn, $_SESSION['user_id'], 'upload_video', "تم رفع فيديو جديد: $title");
                
                $success_message = 'تم رفع الفيديو بنجاح';
                
                // إعادة توجيه بعد النجاح
                header("Location: videos.php?success=1");
                exit;
            }
        } catch (PDOException $e) {
            $errors[] = 'حدث خطأ أثناء حفظ الفيديو: ' . $e->getMessage();
        }
    }
}

// جلب الكورسات الخاصة بالمدرب
try {
    $stmt = $conn->prepare("
        SELECT id, title 
        FROM courses 
        WHERE instructor_id = ? AND status = 'active'
        ORDER BY title
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $instructor_courses = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $instructor_courses = [];
}

include 'includes/header.php';
?>

<!-- رأس الصفحة -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="h3 mb-2">
            <i class="fas fa-video text-danger me-2"></i>
            رفع فيديو جديد
        </h2>
        <p class="text-muted mb-0">إضافة فيديو تعليمي جديد للكورس</p>
    </div>
    <div class="d-flex gap-2">
        <a href="videos.php" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>العودة للفيديوهات
        </a>
        <a href="add-material.php" class="btn btn-outline-primary">
            <i class="fas fa-file-upload me-1"></i>إضافة مادة
        </a>
    </div>
</div>

<!-- رسائل الخطأ -->
<?php if (!empty($errors)): ?>
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <i class="fas fa-exclamation-triangle me-2"></i>
    <ul class="mb-0">
        <?php foreach ($errors as $error): ?>
        <li><?php echo htmlspecialchars($error); ?></li>
        <?php endforeach; ?>
    </ul>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<?php if (isset($success_message)): ?>
<div class="alert alert-success alert-dismissible fade show" role="alert">
    <i class="fas fa-check-circle me-2"></i>
    <?php echo $success_message; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<!-- نموذج رفع الفيديو -->
<div class="row">
    <div class="col-lg-8">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-bottom">
                <h5 class="mb-0">
                    <i class="fas fa-upload me-2"></i>
                    معلومات الفيديو
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data" id="videoForm">
                    <!-- معلومات أساسية -->
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="course_id" class="form-label">الكورس <span class="text-danger">*</span></label>
                            <select name="course_id" id="course_id" class="form-select" required>
                                <option value="">-- اختر كورس --</option>
                                <?php foreach ($instructor_courses as $course): ?>
                                <option value="<?php echo $course['id']; ?>" 
                                        <?php echo (isset($_POST['course_id']) && $_POST['course_id'] == $course['id']) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($course['title']); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="title" class="form-label">عنوان الفيديو <span class="text-danger">*</span></label>
                            <input type="text" name="title" id="title" class="form-control" 
                                   value="<?php echo htmlspecialchars($_POST['title'] ?? ''); ?>" 
                                   placeholder="مثال: الدرس الأول - مقدمة في البرمجة" required>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="description" class="form-label">وصف الفيديو</label>
                            <textarea name="description" id="description" class="form-control" rows="4" 
                                      placeholder="وصف مختصر عن محتوى الفيديو وما سيتم تعلمه..."><?php echo htmlspecialchars($_POST['description'] ?? ''); ?></textarea>
                        </div>
                    </div>
                    
                    <!-- تنظيم المحتوى -->
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="chapter" class="form-label">الفصل/الوحدة</label>
                            <input type="text" name="chapter" id="chapter" class="form-control" 
                                   value="<?php echo htmlspecialchars($_POST['chapter'] ?? ''); ?>" 
                                   placeholder="مثال: الفصل الأول">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="section" class="form-label">القسم</label>
                            <input type="text" name="section" id="section" class="form-control" 
                                   value="<?php echo htmlspecialchars($_POST['section'] ?? ''); ?>" 
                                   placeholder="مثال: المقدمة">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="duration" class="form-label">مدة الفيديو (بالدقائق)</label>
                            <input type="number" name="duration" id="duration" class="form-control" 
                                   value="<?php echo $_POST['duration'] ?? ''; ?>" 
                                   placeholder="مثال: 15" min="1">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="order_number" class="form-label">ترتيب الفيديو</label>
                            <input type="number" name="order_number" id="order_number" class="form-control" 
                                   value="<?php echo $_POST['order_number'] ?? '1'; ?>" 
                                   min="1" required>
                        </div>
                    </div>
                    
                    <!-- نوع الفيديو -->
                    <div class="mb-3">
                        <label class="form-label">نوع الفيديو</label>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="video_type" id="upload_type" 
                                           value="upload" <?php echo (!isset($_POST['video_type']) || $_POST['video_type'] === 'upload') ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="upload_type">
                                        <i class="fas fa-upload text-primary me-2"></i>
                                        رفع ملف فيديو
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="video_type" id="url_type" 
                                           value="url" <?php echo (isset($_POST['video_type']) && $_POST['video_type'] === 'url') ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="url_type">
                                        <i class="fas fa-link text-info me-2"></i>
                                        رابط فيديو (YouTube, Vimeo, إلخ)
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- رفع الملف -->
                    <div id="uploadSection" class="mb-3">
                        <label for="video_file" class="form-label">ملف الفيديو</label>
                        <div class="upload-area" id="uploadArea">
                            <input type="file" name="video_file" id="video_file" class="form-control" 
                                   accept="video/*" style="display: none;">
                            <div class="upload-content text-center py-4">
                                <i class="fas fa-cloud-upload-alt text-primary" style="font-size: 3rem;"></i>
                                <h5 class="mt-3">اسحب الملف هنا أو انقر للاختيار</h5>
                                <p class="text-muted">الأنواع المدعومة: MP4, AVI, MOV, WMV, FLV, WebM</p>
                                <p class="text-muted">الحد الأقصى: 500 ميجابايت</p>
                            </div>
                        </div>
                        <div id="uploadProgress" class="mt-3" style="display: none;">
                            <div class="progress">
                                <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                            </div>
                            <small class="text-muted">جاري الرفع...</small>
                        </div>
                    </div>
                    
                    <!-- رابط الفيديو -->
                    <div id="urlSection" class="mb-3" style="display: none;">
                        <label for="video_url" class="form-label">رابط الفيديو</label>
                        <input type="url" name="video_url" id="video_url" class="form-control" 
                               value="<?php echo htmlspecialchars($_POST['video_url'] ?? ''); ?>" 
                               placeholder="https://www.youtube.com/watch?v=...">
                        <small class="form-text text-muted">
                            يدعم روابط من YouTube, Vimeo, Dailymotion وغيرها
                        </small>
                    </div>
                    
                    <!-- إعدادات إضافية -->
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="is_free" id="is_free" 
                                       <?php echo (isset($_POST['is_free']) && $_POST['is_free']) ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="is_free">
                                    <i class="fas fa-unlock text-success me-2"></i>
                                    فيديو مجاني (متاح للجميع)
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <button type="button" class="btn btn-outline-secondary" onclick="resetForm()">
                            <i class="fas fa-undo me-1"></i>إعادة تعيين
                        </button>
                        <div class="d-flex gap-2">
                            <button type="submit" name="save_draft" class="btn btn-outline-primary">
                                <i class="fas fa-save me-1"></i>حفظ كمسودة
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-upload me-1"></i>رفع الفيديو
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- الشريط الجانبي -->
    <div class="col-lg-4">
        <!-- نصائح -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-light border-bottom">
                <h6 class="mb-0">
                    <i class="fas fa-lightbulb text-warning me-2"></i>
                    نصائح لفيديو ناجح
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        استخدم جودة عالية (1080p أو أكثر)
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        تأكد من وضوح الصوت
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        اجعل المحتوى مركزاً ومفيداً
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        أضف مقدمة وخاتمة واضحة
                    </li>
                    <li class="mb-0">
                        <i class="fas fa-check text-success me-2"></i>
                        استخدم عناوين وصفية
                    </li>
                </ul>
            </div>
        </div>
        
        <!-- معلومات تقنية -->
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-light border-bottom">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle text-info me-2"></i>
                    المواصفات التقنية
                </h6>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-12">
                        <small class="text-muted d-block">الأنواع المدعومة:</small>
                        <span class="badge bg-light text-dark me-1">MP4</span>
                        <span class="badge bg-light text-dark me-1">AVI</span>
                        <span class="badge bg-light text-dark me-1">MOV</span>
                        <span class="badge bg-light text-dark">WebM</span>
                    </div>
                    <div class="col-12">
                        <small class="text-muted d-block">الحد الأقصى للحجم:</small>
                        <strong class="text-primary">500 ميجابايت</strong>
                    </div>
                    <div class="col-12">
                        <small class="text-muted d-block">الجودة المنصوح بها:</small>
                        <strong class="text-success">1080p (Full HD)</strong>
                    </div>
                    <div class="col-12">
                        <small class="text-muted d-block">معدل الإطارات:</small>
                        <strong class="text-info">30 fps</strong>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// التبديل بين رفع الملف والرابط
document.querySelectorAll('input[name="video_type"]').forEach(radio => {
    radio.addEventListener('change', function() {
        const uploadSection = document.getElementById('uploadSection');
        const urlSection = document.getElementById('urlSection');
        
        if (this.value === 'upload') {
            uploadSection.style.display = 'block';
            urlSection.style.display = 'none';
        } else {
            uploadSection.style.display = 'none';
            urlSection.style.display = 'block';
        }
    });
});

// منطقة السحب والإفلات
const uploadArea = document.getElementById('uploadArea');
const fileInput = document.getElementById('video_file');

uploadArea.addEventListener('click', () => fileInput.click());

uploadArea.addEventListener('dragover', (e) => {
    e.preventDefault();
    uploadArea.classList.add('drag-over');
});

uploadArea.addEventListener('dragleave', () => {
    uploadArea.classList.remove('drag-over');
});

uploadArea.addEventListener('drop', (e) => {
    e.preventDefault();
    uploadArea.classList.remove('drag-over');
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        fileInput.files = files;
        handleFileSelect(files[0]);
    }
});

fileInput.addEventListener('change', (e) => {
    if (e.target.files.length > 0) {
        handleFileSelect(e.target.files[0]);
    }
});

// معالجة اختيار الملف
function handleFileSelect(file) {
    const uploadContent = uploadArea.querySelector('.upload-content');
    const maxSize = 500 * 1024 * 1024; // 500 MB
    
    if (file.size > maxSize) {
        alert('حجم الملف كبير جداً. الحد الأقصى 500 ميجابايت');
        return;
    }
    
    uploadContent.innerHTML = `
        <i class="fas fa-file-video text-success" style="font-size: 2rem;"></i>
        <h6 class="mt-2 mb-1">${file.name}</h6>
        <p class="text-muted mb-0">${(file.size / (1024 * 1024)).toFixed(2)} ميجابايت</p>
    `;
}

// إعادة تعيين النموذج
function resetForm() {
    if (confirm('هل أنت متأكد من إعادة تعيين جميع البيانات؟')) {
        document.getElementById('videoForm').reset();
        
        // إعادة تعيين منطقة الرفع
        const uploadContent = uploadArea.querySelector('.upload-content');
        uploadContent.innerHTML = `
            <i class="fas fa-cloud-upload-alt text-primary" style="font-size: 3rem;"></i>
            <h5 class="mt-3">اسحب الملف هنا أو انقر للاختيار</h5>
            <p class="text-muted">الأنواع المدعومة: MP4, AVI, MOV, WMV, FLV, WebM</p>
            <p class="text-muted">الحد الأقصى: 500 ميجابايت</p>
        `;
        
        // إظهار قسم الرفع
        document.getElementById('uploadSection').style.display = 'block';
        document.getElementById('urlSection').style.display = 'none';
    }
}

// التحقق من صحة النموذج قبل الإرسال
document.getElementById('videoForm').addEventListener('submit', function(e) {
    const videoType = document.querySelector('input[name="video_type"]:checked').value;
    
    if (videoType === 'upload') {
        const fileInput = document.getElementById('video_file');
        if (!fileInput.files.length) {
            e.preventDefault();
            alert('يرجى اختيار ملف فيديو للرفع');
            return false;
        }
    } else if (videoType === 'url') {
        const urlInput = document.getElementById('video_url');
        if (!urlInput.value.trim()) {
            e.preventDefault();
            alert('يرجى إدخال رابط الفيديو');
            return false;
        }
    }
});

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // إظهار القسم المناسب حسب النوع المحدد
    const selectedType = document.querySelector('input[name="video_type"]:checked').value;
    if (selectedType === 'url') {
        document.getElementById('uploadSection').style.display = 'none';
        document.getElementById('urlSection').style.display = 'block';
    }
});
</script>

<style>
.upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.upload-area:hover,
.upload-area.drag-over {
    border-color: var(--bs-primary);
    background-color: rgba(var(--bs-primary-rgb), 0.05);
}

.upload-content {
    padding: 2rem;
}

.form-check-label {
    cursor: pointer;
}

.badge {
    font-size: 0.75rem;
}
</style>

<?php include 'includes/footer.php'; ?>
