<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';
require_once '../includes/activity_logger.php';

// التحقق من صلاحيات المدرب
if (!isLoggedIn() || !isInstructor()) {
    header('Location: ../login.php');
    exit;
}

$course_id = isset($_GET['course_id']) ? (int)$_GET['course_id'] : 0;
$instructor_id = $_SESSION['user_id'];

// التحقق من ملكية الكورس
try {
    $stmt = $conn->prepare("SELECT * FROM courses WHERE id = ? AND instructor_id = ?");
    $stmt->execute([$course_id, $instructor_id]);
    $course = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$course) {
        header('Location: courses.php');
        exit;
    }
} catch (PDOException $e) {
    header('Location: courses.php');
    exit;
}

// جلب الطلاب المسجلين
$students = [];
try {
    // التحقق من وجود جدول course_enrollments
    $stmt = $conn->query("SHOW TABLES LIKE 'course_enrollments'");
    if ($stmt->rowCount() > 0) {
        $stmt = $conn->prepare("
            SELECT
                ce.*,
                u.name as student_name,
                u.email as student_email,
                u.phone as student_phone,
                u.created_at as registration_date,
                (SELECT COUNT(*) FROM session_attendance sa
                 JOIN sessions s ON sa.session_id = s.id
                 WHERE s.course_id = ? AND sa.student_id = u.id) as total_attendance,
                (SELECT COUNT(*) FROM sessions WHERE course_id = ?) as total_sessions,
                (SELECT AVG(grade) FROM student_grades WHERE course_id = ? AND student_id = u.id) as avg_grade
            FROM course_enrollments ce
            JOIN users u ON ce.student_id = u.id
            WHERE ce.course_id = ?
            ORDER BY ce.created_at DESC
        ");
        $stmt->execute([$course_id, $course_id, $course_id, $course_id]);
        $students = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
} catch (PDOException $e) {
    $error = 'حدث خطأ في جلب بيانات الطلاب: ' . $e->getMessage();
}

$pageTitle = 'إدارة طلاب الكورس: ' . $course['title'];
include 'includes/header.php';

?>

<div class="container-fluid py-4">
    <!-- معلومات الكورس -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="mb-0">إدارة طلاب الكورس</h5>
                        <small><?php echo htmlspecialchars($course['title']); ?></small>
                    </div>
                    <div>
                        <a href="course-details.php?id=<?php echo $course_id; ?>" class="btn btn-light btn-sm">
                            <i class="fas fa-arrow-left"></i> العودة
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h3><?php echo count($students); ?></h3>
                    <p class="mb-0">طالب مسجل</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h3><?php echo count(array_filter($students, function($s) { return $s['status'] === 'active'; })); ?></h3>
                    <p class="mb-0">طالب نشط</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h3><?php echo count(array_filter($students, function($s) { return $s['status'] === 'completed'; })); ?></h3>
                    <p class="mb-0">مكتمل</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <?php 
                    $avg_attendance = 0;
                    if (!empty($students)) {
                        $total_attendance = 0;
                        $total_sessions = 0;
                        foreach ($students as $student) {
                            $total_attendance += $student['total_attendance'];
                            $total_sessions += $student['total_sessions'];
                        }
                        $avg_attendance = $total_sessions > 0 ? round(($total_attendance / ($total_sessions * count($students))) * 100, 1) : 0;
                    }
                    ?>
                    <h3><?php echo $avg_attendance; ?>%</h3>
                    <p class="mb-0">متوسط الحضور</p>
                </div>
            </div>
        </div>
    </div>

    <!-- أزرار الإجراءات -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-success" onclick="addStudent()">
                            <i class="fas fa-user-plus"></i> إضافة طالب
                        </button>
                        <button type="button" class="btn btn-primary" onclick="exportStudents()">
                            <i class="fas fa-download"></i> تصدير القائمة
                        </button>
                        <button type="button" class="btn btn-warning" onclick="sendMessage()">
                            <i class="fas fa-envelope"></i> إرسال رسالة
                        </button>
                        <a href="join-requests.php?course_id=<?php echo $course_id; ?>" class="btn btn-info">
                            <i class="fas fa-user-clock"></i> طلبات الانضمام
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- قائمة الطلاب -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">قائمة الطلاب المسجلين</h6>
                </div>
                <div class="card-body">
                    <?php if (isset($error)): ?>
                        <div class="alert alert-danger"><?php echo $error; ?></div>
                    <?php endif; ?>

                    <?php if (empty($students)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا يوجد طلاب مسجلين</h5>
                            <p class="text-muted">لم يتم تسجيل أي طلاب في هذا الكورس بعد</p>
                            <div class="mt-3">
                                <button class="btn btn-primary" onclick="addStudent()">
                                    <i class="fas fa-user-plus"></i> إضافة طالب
                                </button>
                                <a href="join-requests.php?course_id=<?php echo $course_id; ?>" class="btn btn-success">
                                    <i class="fas fa-user-clock"></i> عرض طلبات الانضمام
                                </a>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover" id="studentsTable">
                                <thead class="table-light">
                                    <tr>
                                        <th>الطالب</th>
                                        <th>البريد الإلكتروني</th>
                                        <th>تاريخ التسجيل</th>
                                        <th>الحضور</th>
                                        <th>متوسط الدرجات</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($students as $student): ?>
                                        <?php
                                        $attendance_percentage = $student['total_sessions'] > 0 ? 
                                            round(($student['total_attendance'] / $student['total_sessions']) * 100, 1) : 0;
                                        
                                        $avg_grade = $student['avg_grade'] ? round($student['avg_grade'], 1) : null;
                                        
                                        // تحديد لون الحالة
                                        $status_class = '';
                                        $status_text = '';
                                        switch($student['status']) {
                                            case 'active':
                                                $status_class = 'success';
                                                $status_text = 'نشط';
                                                break;
                                            case 'completed':
                                                $status_class = 'info';
                                                $status_text = 'مكتمل';
                                                break;
                                            case 'dropped':
                                                $status_class = 'warning';
                                                $status_text = 'منسحب';
                                                break;
                                            case 'suspended':
                                                $status_class = 'danger';
                                                $status_text = 'موقوف';
                                                break;
                                        }
                                        ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 40px; height: 40px;">
                                                        <?php echo strtoupper(substr($student['student_name'], 0, 1)); ?>
                                                    </div>
                                                    <div>
                                                        <strong><?php echo htmlspecialchars($student['student_name']); ?></strong>
                                                        <?php if ($student['student_phone']): ?>
                                                            <br><small class="text-muted"><?php echo htmlspecialchars($student['student_phone']); ?></small>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </td>
                                            <td><?php echo htmlspecialchars($student['student_email']); ?></td>
                                            <td><?php echo date('Y-m-d', strtotime($student['created_at'])); ?></td>
                                            <td>
                                                <div class="progress" style="height: 20px;">
                                                    <div class="progress-bar" role="progressbar" style="width: <?php echo $attendance_percentage; ?>%">
                                                        <?php echo $attendance_percentage; ?>%
                                                    </div>
                                                </div>
                                                <small class="text-muted"><?php echo $student['total_attendance']; ?>/<?php echo $student['total_sessions']; ?> جلسة</small>
                                            </td>
                                            <td>
                                                <?php if ($avg_grade !== null): ?>
                                                    <?php
                                                    $grade_class = $avg_grade >= 80 ? 'success' : ($avg_grade >= 60 ? 'warning' : 'danger');
                                                    ?>
                                                    <span class="badge bg-<?php echo $grade_class; ?>"><?php echo $avg_grade; ?>%</span>
                                                <?php else: ?>
                                                    <span class="text-muted">لا توجد درجات</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php echo $status_class; ?>"><?php echo $status_text; ?></span>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-primary" onclick="viewStudentDetails(<?php echo $student['student_id']; ?>)" title="عرض التفاصيل">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button class="btn btn-outline-success" onclick="addGrade(<?php echo $student['student_id']; ?>)" title="إضافة درجة">
                                                        <i class="fas fa-star"></i>
                                                    </button>
                                                    <button class="btn btn-outline-warning" onclick="editStudent(<?php echo $student['student_id']; ?>)" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button class="btn btn-outline-danger" onclick="removeStudent(<?php echo $student['student_id']; ?>)" title="إزالة">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
function viewStudentDetails(studentId) {
    window.open(`student-details.php?id=${studentId}&course_id=<?php echo $course_id; ?>`, 
                '_blank', 'width=800,height=600,scrollbars=yes');
}

function addGrade(studentId) {
    window.location.href = `add-grade.php?student_id=${studentId}&course_id=<?php echo $course_id; ?>`;
}

function editStudent(studentId) {
    window.location.href = `edit-student.php?id=${studentId}&course_id=<?php echo $course_id; ?>`;
}

function removeStudent(studentId) {
    if (confirm('هل أنت متأكد من إزالة هذا الطالب من الكورس؟')) {
        window.location.href = `remove-student.php?id=${studentId}&course_id=<?php echo $course_id; ?>`;
    }
}

function addStudent() {
    window.location.href = `add-student.php?course_id=<?php echo $course_id; ?>`;
}

function exportStudents() {
    window.location.href = `export-students.php?course_id=<?php echo $course_id; ?>`;
}

function sendMessage() {
    window.location.href = `send-message.php?course_id=<?php echo $course_id; ?>`;
}

// تهيئة DataTable إذا كان متوفر
document.addEventListener('DOMContentLoaded', function() {
    const table = document.getElementById('studentsTable');
    if (table && typeof DataTable !== 'undefined') {
        new DataTable(table, {
            order: [[2, 'desc']],
            pageLength: 25,
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/ar.json'
            }
        });
    }
});
</script>

<?php require_once '../includes/footer.php'; ?>
