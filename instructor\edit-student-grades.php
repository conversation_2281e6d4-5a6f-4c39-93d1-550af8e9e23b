<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من صلاحيات المدرب
if (!isLoggedIn() || !isInstructor()) {
    header('Location: ../login.php');
    exit;
}

$student_id = (int)($_GET['student_id'] ?? 0);
$course_id = (int)($_GET['course_id'] ?? 0);
$error = '';
$success = '';

// التحقق من أن الكورس ينتمي للمدرب
try {
    $stmt = $conn->prepare("SELECT title FROM courses WHERE id = ? AND instructor_id = ?");
    $stmt->execute([$course_id, $_SESSION['user_id']]);
    $course = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$course) {
        header('Location: courses.php');
        exit;
    }
} catch (PDOException $e) {
    $error = 'خطأ في التحقق من الكورس';
}

// جلب بيانات الطالب
try {
    $stmt = $conn->prepare("
        SELECT u.*, e.enrollment_date 
        FROM users u 
        JOIN enrollments e ON u.id = e.student_id 
        WHERE u.id = ? AND e.course_id = ?
    ");
    $stmt->execute([$student_id, $course_id]);
    $student = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$student) {
        header('Location: students.php?course_id=' . $course_id);
        exit;
    }
} catch (PDOException $e) {
    $error = 'خطأ في جلب بيانات الطالب';
}

// جلب جميع الدرجات
try {
    $stmt = $conn->prepare("
        SELECT * FROM grades 
        WHERE student_id = ? AND course_id = ? AND instructor_id = ?
        ORDER BY created_at DESC
    ");
    $stmt->execute([$student_id, $course_id, $_SESSION['user_id']]);
    $grades = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $grades = [];
}

// معالجة تحديث الدرجات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $updated_grades = $_POST['grades'] ?? [];
    $errors = [];
    $updated_count = 0;
    
    try {
        $conn->beginTransaction();
        
        foreach ($updated_grades as $grade_id => $grade_data) {
            $grade_id = (int)$grade_id;
            $new_grade = (float)($grade_data['grade'] ?? 0);
            $new_max_grade = (float)($grade_data['max_grade'] ?? 100);
            $new_notes = trim($grade_data['notes'] ?? '');
            
            // التحقق من صحة البيانات
            if ($new_grade < 0 || $new_grade > $new_max_grade) {
                $errors[] = "الدرجة رقم $grade_id غير صحيحة";
                continue;
            }
            
            // تحديث الدرجة
            $stmt = $conn->prepare("
                UPDATE grades 
                SET grade = ?, max_grade = ?, notes = ?, updated_at = NOW() 
                WHERE id = ? AND student_id = ? AND course_id = ? AND instructor_id = ?
            ");
            $result = $stmt->execute([
                $new_grade, 
                $new_max_grade, 
                $new_notes, 
                $grade_id, 
                $student_id, 
                $course_id, 
                $_SESSION['user_id']
            ]);
            
            if ($result && $stmt->rowCount() > 0) {
                $updated_count++;
            }
        }
        
        $conn->commit();
        
        if ($updated_count > 0) {
            $success = "تم تحديث $updated_count درجة بنجاح";
            
            // إعادة جلب الدرجات المحدثة
            $stmt = $conn->prepare("
                SELECT * FROM grades 
                WHERE student_id = ? AND course_id = ? AND instructor_id = ?
                ORDER BY created_at DESC
            ");
            $stmt->execute([$student_id, $course_id, $_SESSION['user_id']]);
            $grades = $stmt->fetchAll(PDO::FETCH_ASSOC);
        }
        
        if (!empty($errors)) {
            $error = implode('<br>', $errors);
        }
        
    } catch (PDOException $e) {
        $conn->rollBack();
        $error = 'خطأ في تحديث الدرجات: ' . $e->getMessage();
    }
}

$pageTitle = 'تعديل درجات الطالب - ' . $student['name'];
$breadcrumbs = [
    ['title' => 'الكورسات', 'url' => 'courses.php'],
    ['title' => $course['title'], 'url' => 'course-details.php?id=' . $course_id],
    ['title' => 'الطلاب', 'url' => 'students.php?course_id=' . $course_id],
    ['title' => 'تعديل الدرجات']
];

include 'includes/header.php';
?>

<div class="row">
    <div class="col-md-8">
        <!-- نموذج تعديل الدرجات -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-edit me-2"></i>
                    تعديل درجات الطالب
                </h5>
            </div>
            <div class="card-body">
                <?php if ($error): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?php echo $error; ?>
                    </div>
                <?php endif; ?>

                <?php if ($success): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        <?php echo htmlspecialchars($success); ?>
                    </div>
                <?php endif; ?>

                <?php if (empty($grades)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد درجات للتعديل</h5>
                        <p class="text-muted">لم يتم تسجيل أي درجات لهذا الطالب بعد</p>
                        <a href="add-grade.php?student_id=<?php echo $student_id; ?>&course_id=<?php echo $course_id; ?>" 
                           class="btn btn-success">
                            <i class="fas fa-plus me-2"></i>
                            إضافة درجة جديدة
                        </a>
                    </div>
                <?php else: ?>
                    <form method="POST" action="" id="gradesForm">
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead class="table-light">
                                    <tr>
                                        <th width="25%">التكليف/الاختبار</th>
                                        <th width="15%">النوع</th>
                                        <th width="15%">الدرجة</th>
                                        <th width="15%">الدرجة الكاملة</th>
                                        <th width="10%">النسبة</th>
                                        <th width="20%">ملاحظات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($grades as $grade): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo htmlspecialchars($grade['assignment_name']); ?></strong>
                                                <br>
                                                <small class="text-muted">
                                                    <?php echo date('Y-m-d', strtotime($grade['created_at'])); ?>
                                                </small>
                                            </td>
                                            <td>
                                                <?php
                                                $type_labels = [
                                                    'assignment' => 'واجب',
                                                    'quiz' => 'اختبار قصير',
                                                    'exam' => 'امتحان',
                                                    'project' => 'مشروع',
                                                    'participation' => 'مشاركة',
                                                    'attendance' => 'حضور'
                                                ];
                                                ?>
                                                <span class="badge bg-primary">
                                                    <?php echo $type_labels[$grade['grade_type']] ?? $grade['grade_type']; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <input type="number" 
                                                       class="form-control grade-input" 
                                                       name="grades[<?php echo $grade['id']; ?>][grade]" 
                                                       value="<?php echo $grade['grade']; ?>"
                                                       min="0" 
                                                       step="0.5"
                                                       data-grade-id="<?php echo $grade['id']; ?>"
                                                       required>
                                            </td>
                                            <td>
                                                <input type="number" 
                                                       class="form-control max-grade-input" 
                                                       name="grades[<?php echo $grade['id']; ?>][max_grade]" 
                                                       value="<?php echo $grade['max_grade']; ?>"
                                                       min="1" 
                                                       step="0.5"
                                                       data-grade-id="<?php echo $grade['id']; ?>"
                                                       required>
                                            </td>
                                            <td>
                                                <span class="percentage-display badge bg-info" 
                                                      id="percentage-<?php echo $grade['id']; ?>">
                                                    <?php echo round(($grade['grade'] / $grade['max_grade']) * 100, 1); ?>%
                                                </span>
                                            </td>
                                            <td>
                                                <textarea class="form-control" 
                                                          name="grades[<?php echo $grade['id']; ?>][notes]" 
                                                          rows="2" 
                                                          placeholder="ملاحظات..."><?php echo htmlspecialchars($grade['notes']); ?></textarea>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <div class="d-flex gap-2 mt-4">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save me-2"></i>
                                حفظ جميع التغييرات
                            </button>
                            <button type="button" class="btn btn-warning" id="resetForm">
                                <i class="fas fa-undo me-2"></i>
                                إعادة تعيين
                            </button>
                            <a href="student-grades.php?student_id=<?php echo $student_id; ?>&course_id=<?php echo $course_id; ?>" 
                               class="btn btn-secondary">
                                <i class="fas fa-arrow-right me-2"></i>
                                العودة لعرض الدرجات
                            </a>
                        </div>
                    </form>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <!-- معلومات الطالب -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-user me-2"></i>
                    معلومات الطالب
                </h6>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <div class="avatar-lg mx-auto mb-3">
                        <?php echo strtoupper(substr($student['name'], 0, 1)); ?>
                    </div>
                    <h6><?php echo htmlspecialchars($student['name']); ?></h6>
                    <small class="text-muted"><?php echo htmlspecialchars($student['email']); ?></small>
                </div>
                
                <hr>
                
                <div class="text-center">
                    <h6 class="text-primary"><?php echo count($grades); ?></h6>
                    <small class="text-muted">درجة قابلة للتعديل</small>
                </div>
            </div>
        </div>

        <!-- نصائح التعديل -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>
                    نصائح التعديل
                </h6>
            </div>
            <div class="card-body">
                <div class="small">
                    <div class="mb-3">
                        <i class="fas fa-check-circle text-success me-2"></i>
                        <strong>التحقق التلقائي:</strong>
                        <p class="mb-0">سيتم التحقق من صحة الدرجات تلقائياً</p>
                    </div>
                    
                    <div class="mb-3">
                        <i class="fas fa-calculator text-info me-2"></i>
                        <strong>النسبة المئوية:</strong>
                        <p class="mb-0">يتم حساب النسبة المئوية تلقائياً عند التعديل</p>
                    </div>
                    
                    <div class="mb-3">
                        <i class="fas fa-save text-warning me-2"></i>
                        <strong>الحفظ:</strong>
                        <p class="mb-0">لا تنس حفظ التغييرات بعد التعديل</p>
                    </div>
                    
                    <div class="mb-0">
                        <i class="fas fa-history text-secondary me-2"></i>
                        <strong>السجل:</strong>
                        <p class="mb-0">سيتم تسجيل تاريخ آخر تعديل</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-lg {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--instructor-primary), var(--instructor-secondary));
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: bold;
}

.card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.card-header {
    background: var(--instructor-light);
    border-bottom: 1px solid var(--gray-200);
    border-radius: 12px 12px 0 0 !important;
}

.form-control:focus {
    border-color: var(--instructor-primary);
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.table th {
    border-top: none;
    font-weight: 600;
    color: var(--gray-700);
}

.grade-input, .max-grade-input {
    text-align: center;
    font-weight: bold;
}

.percentage-display {
    font-size: 0.9rem;
    font-weight: bold;
}
</style>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('gradesForm');
    const gradeInputs = document.querySelectorAll('.grade-input');
    const maxGradeInputs = document.querySelectorAll('.max-grade-input');
    const resetBtn = document.getElementById('resetForm');
    
    // تحديث النسبة المئوية عند تغيير الدرجة
    function updatePercentage(gradeId) {
        const gradeInput = document.querySelector(`input[data-grade-id="${gradeId}"].grade-input`);
        const maxGradeInput = document.querySelector(`input[data-grade-id="${gradeId}"].max-grade-input`);
        const percentageDisplay = document.getElementById(`percentage-${gradeId}`);
        
        if (gradeInput && maxGradeInput && percentageDisplay) {
            const grade = parseFloat(gradeInput.value) || 0;
            const maxGrade = parseFloat(maxGradeInput.value) || 100;
            const percentage = maxGrade > 0 ? ((grade / maxGrade) * 100) : 0;
            
            percentageDisplay.textContent = percentage.toFixed(1) + '%';
            
            // تغيير لون النسبة حسب القيمة
            percentageDisplay.className = 'percentage-display badge ';
            if (percentage >= 90) {
                percentageDisplay.className += 'bg-success';
            } else if (percentage >= 70) {
                percentageDisplay.className += 'bg-warning';
            } else if (percentage >= 50) {
                percentageDisplay.className += 'bg-info';
            } else {
                percentageDisplay.className += 'bg-danger';
            }
        }
    }
    
    // إضافة مستمعي الأحداث لجميع حقول الدرجات
    gradeInputs.forEach(input => {
        const gradeId = input.getAttribute('data-grade-id');
        
        input.addEventListener('input', function() {
            updatePercentage(gradeId);
            
            // التحقق من أن الدرجة لا تتجاوز الدرجة الكاملة
            const maxGradeInput = document.querySelector(`input[data-grade-id="${gradeId}"].max-grade-input`);
            const maxGrade = parseFloat(maxGradeInput.value) || 100;
            const grade = parseFloat(this.value) || 0;
            
            if (grade > maxGrade) {
                this.value = maxGrade;
                updatePercentage(gradeId);
            }
        });
    });
    
    maxGradeInputs.forEach(input => {
        const gradeId = input.getAttribute('data-grade-id');
        
        input.addEventListener('input', function() {
            updatePercentage(gradeId);
            
            // التحقق من أن الدرجة الحالية لا تتجاوز الدرجة الكاملة الجديدة
            const gradeInput = document.querySelector(`input[data-grade-id="${gradeId}"].grade-input`);
            const grade = parseFloat(gradeInput.value) || 0;
            const maxGrade = parseFloat(this.value) || 100;
            
            if (grade > maxGrade) {
                gradeInput.value = maxGrade;
                updatePercentage(gradeId);
            }
        });
    });
    
    // إعادة تعيين النموذج
    resetBtn.addEventListener('click', function() {
        if (confirm('هل أنت متأكد من إعادة تعيين جميع التغييرات؟')) {
            form.reset();
            
            // إعادة حساب النسب المئوية
            gradeInputs.forEach(input => {
                const gradeId = input.getAttribute('data-grade-id');
                updatePercentage(gradeId);
            });
        }
    });
    
    // التحقق من صحة النموذج قبل الإرسال
    form.addEventListener('submit', function(e) {
        let hasErrors = false;
        const errors = [];
        
        gradeInputs.forEach(input => {
            const gradeId = input.getAttribute('data-grade-id');
            const maxGradeInput = document.querySelector(`input[data-grade-id="${gradeId}"].max-grade-input`);
            
            const grade = parseFloat(input.value) || 0;
            const maxGrade = parseFloat(maxGradeInput.value) || 100;
            
            if (grade < 0) {
                errors.push(`الدرجة لا يمكن أن تكون سالبة`);
                hasErrors = true;
            }
            
            if (grade > maxGrade) {
                errors.push(`الدرجة لا يمكن أن تتجاوز الدرجة الكاملة`);
                hasErrors = true;
            }
            
            if (maxGrade <= 0) {
                errors.push(`الدرجة الكاملة يجب أن تكون أكبر من صفر`);
                hasErrors = true;
            }
        });
        
        if (hasErrors) {
            e.preventDefault();
            alert('يرجى تصحيح الأخطاء التالية:\n' + errors.join('\n'));
        }
    });
});
</script>

</body>
</html>
