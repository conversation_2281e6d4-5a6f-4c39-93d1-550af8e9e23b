<?php
/**
 * إعداد جداول الاختبارات
 * Setup Quiz Tables
 */

require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>إعداد جداول الاختبارات</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css' rel='stylesheet'>";
echo "<link href='https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap' rel='stylesheet'>";
echo "<style>";
echo "body { font-family: 'Cairo', sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }";
echo ".setup-container { max-width: 900px; margin: 50px auto; padding: 20px; }";
echo ".setup-card { background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); overflow: hidden; }";
echo ".setup-header { background: linear-gradient(135deg, #667eea, #764ba2); color: white; padding: 30px; text-align: center; }";
echo ".setup-body { padding: 30px; }";
echo ".step { padding: 15px; margin: 10px 0; border-radius: 10px; border-right: 4px solid; }";
echo ".step-success { background: #d4edda; color: #155724; border-right-color: #28a745; }";
echo ".step-error { background: #f8d7da; color: #721c24; border-right-color: #dc3545; }";
echo ".step-info { background: #d1ecf1; color: #0c5460; border-right-color: #17a2b8; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='setup-container'>";
echo "<div class='setup-card'>";
echo "<div class='setup-header'>";
echo "<h1><i class='fas fa-question-circle me-3'></i>إعداد نظام الاختبارات</h1>";
echo "<p class='mb-0'>جاري إنشاء جداول الاختبارات وإعداد البيانات الأساسية...</p>";
echo "</div>";

echo "<div class='setup-body'>";

// خطوة 1: إنشاء جدول الاختبارات
echo "<div class='step step-info'>";
echo "<h5><i class='fas fa-table me-2'></i>الخطوة 1: إنشاء جدول الاختبارات</h5>";
try {
    $conn->exec("CREATE TABLE IF NOT EXISTS quizzes (
        id INT AUTO_INCREMENT PRIMARY KEY,
        course_id INT NOT NULL,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        instructions TEXT,
        time_limit INT DEFAULT NULL,
        max_attempts INT DEFAULT 1,
        passing_grade DECIMAL(5,2) DEFAULT 60.00,
        total_marks DECIMAL(5,2) DEFAULT 100.00,
        available_from DATETIME DEFAULT NULL,
        available_until DATETIME DEFAULT NULL,
        is_randomized BOOLEAN DEFAULT FALSE,
        show_results BOOLEAN DEFAULT TRUE,
        status ENUM('draft', 'published', 'closed') DEFAULT 'draft',
        created_by INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
        FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE,
        INDEX idx_course (course_id),
        INDEX idx_status (status),
        INDEX idx_available (available_from, available_until)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "<p class='mb-0'>✅ تم إنشاء جدول الاختبارات بنجاح</p>";
} catch (Exception $e) {
    echo "<p class='mb-0'>❌ خطأ في إنشاء جدول الاختبارات: " . $e->getMessage() . "</p>";
}
echo "</div>";

// خطوة 2: إنشاء جدول أسئلة الاختبارات
echo "<div class='step step-info'>";
echo "<h5><i class='fas fa-list me-2'></i>الخطوة 2: إنشاء جدول أسئلة الاختبارات</h5>";
try {
    $conn->exec("CREATE TABLE IF NOT EXISTS quiz_questions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        quiz_id INT NOT NULL,
        question_text TEXT NOT NULL,
        question_type ENUM('multiple_choice', 'true_false', 'short_answer', 'essay') DEFAULT 'multiple_choice',
        points DECIMAL(5,2) DEFAULT 1.00,
        correct_answer TEXT DEFAULT NULL,
        explanation TEXT DEFAULT NULL,
        question_order INT DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (quiz_id) REFERENCES quizzes(id) ON DELETE CASCADE,
        INDEX idx_quiz (quiz_id),
        INDEX idx_order (question_order)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "<p class='mb-0'>✅ تم إنشاء جدول أسئلة الاختبارات بنجاح</p>";
} catch (Exception $e) {
    echo "<p class='mb-0'>❌ خطأ في إنشاء جدول أسئلة الاختبارات: " . $e->getMessage() . "</p>";
}
echo "</div>";

// خطوة 3: إنشاء جدول خيارات الأسئلة
echo "<div class='step step-info'>";
echo "<h5><i class='fas fa-check-square me-2'></i>الخطوة 3: إنشاء جدول خيارات الأسئلة</h5>";
try {
    $conn->exec("CREATE TABLE IF NOT EXISTS quiz_question_options (
        id INT AUTO_INCREMENT PRIMARY KEY,
        question_id INT NOT NULL,
        option_text TEXT NOT NULL,
        is_correct BOOLEAN DEFAULT FALSE,
        option_order INT DEFAULT 1,
        FOREIGN KEY (question_id) REFERENCES quiz_questions(id) ON DELETE CASCADE,
        INDEX idx_question (question_id),
        INDEX idx_order (option_order)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "<p class='mb-0'>✅ تم إنشاء جدول خيارات الأسئلة بنجاح</p>";
} catch (Exception $e) {
    echo "<p class='mb-0'>❌ خطأ في إنشاء جدول خيارات الأسئلة: " . $e->getMessage() . "</p>";
}
echo "</div>";

// خطوة 4: إنشاء جدول محاولات الاختبارات
echo "<div class='step step-info'>";
echo "<h5><i class='fas fa-user-clock me-2'></i>الخطوة 4: إنشاء جدول محاولات الاختبارات</h5>";
try {
    $conn->exec("CREATE TABLE IF NOT EXISTS quiz_attempts (
        id INT AUTO_INCREMENT PRIMARY KEY,
        quiz_id INT NOT NULL,
        student_id INT NOT NULL,
        attempt_number INT DEFAULT 1,
        started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        completed_at TIMESTAMP NULL,
        score DECIMAL(5,2) DEFAULT NULL,
        total_marks DECIMAL(5,2) DEFAULT NULL,
        time_taken INT DEFAULT NULL,
        status ENUM('in_progress', 'completed', 'abandoned') DEFAULT 'in_progress',
        FOREIGN KEY (quiz_id) REFERENCES quizzes(id) ON DELETE CASCADE,
        FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
        INDEX idx_quiz (quiz_id),
        INDEX idx_student (student_id),
        INDEX idx_status (status),
        UNIQUE KEY unique_attempt (quiz_id, student_id, attempt_number)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "<p class='mb-0'>✅ تم إنشاء جدول محاولات الاختبارات بنجاح</p>";
} catch (Exception $e) {
    echo "<p class='mb-0'>❌ خطأ في إنشاء جدول محاولات الاختبارات: " . $e->getMessage() . "</p>";
}
echo "</div>";

// خطوة 5: إنشاء جدول إجابات الاختبارات
echo "<div class='step step-info'>";
echo "<h5><i class='fas fa-edit me-2'></i>الخطوة 5: إنشاء جدول إجابات الاختبارات</h5>";
try {
    $conn->exec("CREATE TABLE IF NOT EXISTS quiz_answers (
        id INT AUTO_INCREMENT PRIMARY KEY,
        attempt_id INT NOT NULL,
        question_id INT NOT NULL,
        answer_text TEXT DEFAULT NULL,
        selected_option_id INT DEFAULT NULL,
        is_correct BOOLEAN DEFAULT NULL,
        points_earned DECIMAL(5,2) DEFAULT 0.00,
        answered_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (attempt_id) REFERENCES quiz_attempts(id) ON DELETE CASCADE,
        FOREIGN KEY (question_id) REFERENCES quiz_questions(id) ON DELETE CASCADE,
        FOREIGN KEY (selected_option_id) REFERENCES quiz_question_options(id) ON DELETE SET NULL,
        UNIQUE KEY unique_answer (attempt_id, question_id),
        INDEX idx_attempt (attempt_id),
        INDEX idx_question (question_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "<p class='mb-0'>✅ تم إنشاء جدول إجابات الاختبارات بنجاح</p>";
} catch (Exception $e) {
    echo "<p class='mb-0'>❌ خطأ في إنشاء جدول إجابات الاختبارات: " . $e->getMessage() . "</p>";
}
echo "</div>";

// خطوة 6: إنشاء اختبار تجريبي
echo "<div class='step step-info'>";
echo "<h5><i class='fas fa-plus me-2'></i>الخطوة 6: إنشاء اختبار تجريبي</h5>";
try {
    // البحث عن مدرب وكورس
    $stmt = $conn->prepare("SELECT id FROM users WHERE role = 'instructor' LIMIT 1");
    $stmt->execute();
    $instructor = $stmt->fetch();
    
    if ($instructor) {
        $stmt = $conn->prepare("SELECT id FROM courses WHERE instructor_id = ? LIMIT 1");
        $stmt->execute([$instructor['id']]);
        $course = $stmt->fetch();
        
        if ($course) {
            // إنشاء اختبار تجريبي
            $stmt = $conn->prepare("SELECT id FROM quizzes WHERE title = 'اختبار تجريبي - أساسيات البرمجة' LIMIT 1");
            $stmt->execute();
            $existing_quiz = $stmt->fetch();
            
            if (!$existing_quiz) {
                $stmt = $conn->prepare("
                    INSERT INTO quizzes (course_id, title, description, time_limit, max_attempts, passing_grade, 
                                       total_marks, is_randomized, show_results, status, created_by) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'published', ?)
                ");
                $stmt->execute([
                    $course['id'],
                    'اختبار تجريبي - أساسيات البرمجة',
                    'اختبار تجريبي لقياس فهم الطلاب لأساسيات البرمجة',
                    30, // 30 دقيقة
                    2,  // محاولتان
                    70, // درجة النجاح 70%
                    10, // 10 نقاط
                    0,  // غير عشوائي
                    1,  // إظهار النتائج
                    $instructor['id']
                ]);
                
                $quiz_id = $conn->lastInsertId();
                
                // إضافة أسئلة تجريبية
                $questions = [
                    [
                        'text' => 'ما هي لغة البرمجة المستخدمة لتطوير مواقع الويب؟',
                        'type' => 'multiple_choice',
                        'points' => 2,
                        'options' => [
                            ['text' => 'HTML', 'correct' => false],
                            ['text' => 'CSS', 'correct' => false],
                            ['text' => 'JavaScript', 'correct' => true],
                            ['text' => 'Photoshop', 'correct' => false]
                        ]
                    ],
                    [
                        'text' => 'HTML تعني HyperText Markup Language',
                        'type' => 'true_false',
                        'points' => 1,
                        'correct_answer' => 'true'
                    ],
                    [
                        'text' => 'ما الفرق بين HTML و CSS؟',
                        'type' => 'short_answer',
                        'points' => 3,
                        'correct_answer' => 'HTML للهيكل و CSS للتنسيق'
                    ],
                    [
                        'text' => 'اشرح مفهوم البرمجة الكائنية',
                        'type' => 'essay',
                        'points' => 4
                    ]
                ];
                
                foreach ($questions as $index => $question) {
                    $stmt = $conn->prepare("
                        INSERT INTO quiz_questions (quiz_id, question_text, question_type, points, correct_answer, question_order) 
                        VALUES (?, ?, ?, ?, ?, ?)
                    ");
                    $stmt->execute([
                        $quiz_id,
                        $question['text'],
                        $question['type'],
                        $question['points'],
                        $question['correct_answer'] ?? null,
                        $index + 1
                    ]);
                    
                    $question_id = $conn->lastInsertId();
                    
                    // إضافة خيارات للأسئلة متعددة الاختيارات
                    if ($question['type'] === 'multiple_choice' && isset($question['options'])) {
                        foreach ($question['options'] as $opt_index => $option) {
                            $stmt = $conn->prepare("
                                INSERT INTO quiz_question_options (question_id, option_text, is_correct, option_order) 
                                VALUES (?, ?, ?, ?)
                            ");
                            $stmt->execute([
                                $question_id,
                                $option['text'],
                                $option['correct'] ? 1 : 0,
                                $opt_index + 1
                            ]);
                        }
                    }
                }
                
                echo "<p class='mb-0'>✅ تم إنشاء اختبار تجريبي مع 4 أسئلة</p>";
            } else {
                echo "<p class='mb-0'>✅ الاختبار التجريبي موجود بالفعل</p>";
            }
        } else {
            echo "<p class='mb-0'>⚠️ لا توجد كورسات لإنشاء اختبار تجريبي</p>";
        }
    } else {
        echo "<p class='mb-0'>⚠️ لا يوجد مدرب لإنشاء اختبار تجريبي</p>";
    }
} catch (Exception $e) {
    echo "<p class='mb-0'>❌ خطأ في إنشاء الاختبار التجريبي: " . $e->getMessage() . "</p>";
}
echo "</div>";

// النتيجة النهائية
echo "<div class='step step-success'>";
echo "<h5><i class='fas fa-check-circle me-2'></i>تم إعداد نظام الاختبارات بنجاح!</h5>";
echo "<p class='mb-3'>جميع جداول الاختبارات جاهزة للاستخدام</p>";

echo "<div class='row'>";
echo "<div class='col-md-4 mb-3'>";
echo "<div class='card border-primary'>";
echo "<div class='card-body text-center'>";
echo "<h6 class='card-title'>إدارة الاختبارات</h6>";
echo "<a href='instructor/quizzes.php' class='btn btn-primary'>";
echo "<i class='fas fa-question-circle me-1'></i> الاختبارات";
echo "</a>";
echo "</div></div></div>";

echo "<div class='col-md-4 mb-3'>";
echo "<div class='card border-success'>";
echo "<div class='card-body text-center'>";
echo "<h6 class='card-title'>لوحة المدرب</h6>";
echo "<a href='instructor/dashboard.php' class='btn btn-success'>";
echo "<i class='fas fa-chalkboard-teacher me-1'></i> المدرب";
echo "</a>";
echo "</div></div></div>";

echo "<div class='col-md-4 mb-3'>";
echo "<div class='card border-info'>";
echo "<div class='card-body text-center'>";
echo "<h6 class='card-title'>لوحة الطالب</h6>";
echo "<a href='student/quizzes.php' class='btn btn-info'>";
echo "<i class='fas fa-user-graduate me-1'></i> الطالب";
echo "</a>";
echo "</div></div></div>";
echo "</div>";

echo "<div class='alert alert-info mt-3'>";
echo "<h6><i class='fas fa-info-circle me-2'></i>معلومات مهمة:</h6>";
echo "<ul class='mb-0'>";
echo "<li>تم إنشاء جميع جداول الاختبارات بنجاح</li>";
echo "<li>تم إنشاء اختبار تجريبي للاختبار</li>";
echo "<li>يمكن للمدربين الآن إنشاء اختبارات جديدة</li>";
echo "<li>يمكن للطلاب حل الاختبارات المتاحة</li>";
echo "</ul>";
echo "</div>";

echo "</div>";

echo "</div>";
echo "</div>";
echo "</div>";

echo "</body>";
echo "</html>";
?>
