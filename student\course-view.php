<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من تسجيل الدخول كطالب
if (!isLoggedIn() || !isStudent()) {
    header('Location: ../login.php');
    exit;
}

$student_id = $_SESSION['user_id'];
$course_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
$error = '';
$success = '';

// التحقق من تسجيل الطالب في الكورس
try {
    $stmt = $conn->prepare("
        SELECT c.*, u.name as instructor_name, u.email as instructor_email, ce.enrollment_date, ce.status as enrollment_status
        FROM course_enrollments ce
        JOIN courses c ON ce.course_id = c.id
        JOIN users u ON c.instructor_id = u.id
        WHERE ce.course_id = ? AND ce.student_id = ? AND ce.status = 'active'
    ");
    $stmt->execute([$course_id, $student_id]);
    $course = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$course) {
        header('Location: dashboard.php');
        exit;
    }
} catch (PDOException $e) {
    header('Location: dashboard.php');
    exit;
}

// جلب جلسات الكورس
try {
    $stmt = $conn->prepare("
        SELECT s.*, 
               CASE 
                   WHEN s.session_date < CURDATE() THEN 'completed'
                   WHEN s.session_date = CURDATE() AND s.start_time <= CURTIME() THEN 'live'
                   ELSE 'upcoming'
               END as session_status
        FROM sessions s
        WHERE s.course_id = ?
        ORDER BY s.session_date ASC, s.start_time ASC
    ");
    $stmt->execute([$course_id]);
    $sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // تصنيف الجلسات
    $completed_sessions = array_filter($sessions, function($s) { return $s['session_status'] === 'completed'; });
    $live_sessions = array_filter($sessions, function($s) { return $s['session_status'] === 'live'; });
    $upcoming_sessions = array_filter($sessions, function($s) { return $s['session_status'] === 'upcoming'; });
    
} catch (PDOException $e) {
    $sessions = [];
    $completed_sessions = $live_sessions = $upcoming_sessions = [];
}

// جلب درجات الطالب في الكورس
try {
    $stmt = $conn->prepare("
        SELECT sg.*, u.name as graded_by_name
        FROM student_grades sg
        LEFT JOIN users u ON sg.graded_by = u.id
        WHERE sg.course_id = ? AND sg.student_id = ?
        ORDER BY sg.grade_date DESC
    ");
    $stmt->execute([$course_id, $student_id]);
    $grades = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // حساب المعدل
    $total_grades = 0;
    $total_max = 0;
    foreach ($grades as $grade) {
        $total_grades += $grade['grade'];
        $total_max += $grade['max_grade'];
    }
    $average = $total_max > 0 ? round(($total_grades / $total_max) * 100, 1) : 0;
    
} catch (PDOException $e) {
    $grades = [];
    $average = 0;
}

// جلب مواد الكورس (إذا كانت متاحة)
try {
    $stmt = $conn->prepare("
        SELECT cm.*
        FROM course_materials cm
        WHERE cm.course_id = ? AND cm.status = 'active'
        ORDER BY cm.created_at DESC
    ");
    $stmt->execute([$course_id]);
    $materials = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $materials = [];
}

$pageTitle = 'كورس: ' . $course['title'];
require_once '../includes/header.php';
?>

<div class="container-fluid py-4">
    <!-- معلومات الكورس -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h5 class="mb-1"><?php echo htmlspecialchars($course['title']); ?></h5>
                            <small>
                                <i class="fas fa-user me-1"></i>
                                المدرب: <?php echo htmlspecialchars($course['instructor_name']); ?>
                                <span class="ms-3">
                                    <i class="fas fa-calendar me-1"></i>
                                    تاريخ التسجيل: <?php echo date('Y-m-d', strtotime($course['enrollment_date'])); ?>
                                </span>
                            </small>
                        </div>
                        <div class="col-md-4 text-end">
                            <a href="dashboard.php" class="btn btn-light btn-sm">
                                <i class="fas fa-arrow-left"></i> العودة للوحة التحكم
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <p class="mb-0"><?php echo htmlspecialchars($course['description']); ?></p>
                </div>
            </div>
        </div>
    </div>

    <!-- الإحصائيات -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h4><?php echo count($sessions); ?></h4>
                    <p class="mb-0">إجمالي الجلسات</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h4><?php echo count($completed_sessions); ?></h4>
                    <p class="mb-0">جلسات مكتملة</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h4><?php echo count($grades); ?></h4>
                    <p class="mb-0">التقييمات</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-secondary text-white">
                <div class="card-body text-center">
                    <h4><?php echo $average; ?>%</h4>
                    <p class="mb-0">المعدل العام</p>
                </div>
            </div>
        </div>
    </div>

    <!-- علامات التبويب -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <ul class="nav nav-tabs card-header-tabs" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" data-bs-toggle="tab" href="#sessions" role="tab">
                                <i class="fas fa-video me-1"></i>
                                الجلسات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="tab" href="#grades" role="tab">
                                <i class="fas fa-star me-1"></i>
                                الدرجات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="tab" href="#materials" role="tab">
                                <i class="fas fa-file-download me-1"></i>
                                المواد التعليمية
                            </a>
                        </li>
                    </ul>
                </div>
                <div class="card-body">
                    <div class="tab-content">
                        <!-- تبويب الجلسات -->
                        <div class="tab-pane fade show active" id="sessions" role="tabpanel">
                            <!-- الجلسات المباشرة -->
                            <?php if (!empty($live_sessions)): ?>
                                <div class="alert alert-success">
                                    <h6><i class="fas fa-broadcast-tower me-2"></i>جلسات مباشرة الآن</h6>
                                    <?php foreach ($live_sessions as $session): ?>
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <strong><?php echo htmlspecialchars($session['title']); ?></strong>
                                                <br>
                                                <small><?php echo htmlspecialchars($session['description']); ?></small>
                                            </div>
                                            <a href="join-session.php?id=<?php echo $session['id']; ?>" class="btn btn-success btn-sm">
                                                <i class="fas fa-play"></i> انضم الآن
                                            </a>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>

                            <!-- جميع الجلسات -->
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>الجلسة</th>
                                            <th>التاريخ</th>
                                            <th>الوقت</th>
                                            <th>المدة</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php if (empty($sessions)): ?>
                                            <tr>
                                                <td colspan="6" class="text-center text-muted py-4">
                                                    <i class="fas fa-video fa-2x mb-2"></i>
                                                    <br>
                                                    لا توجد جلسات متاحة حالياً
                                                </td>
                                            </tr>
                                        <?php else: ?>
                                            <?php foreach ($sessions as $session): ?>
                                                <tr>
                                                    <td>
                                                        <div>
                                                            <strong><?php echo htmlspecialchars($session['title']); ?></strong>
                                                            <?php if ($session['description']): ?>
                                                                <br>
                                                                <small class="text-muted"><?php echo htmlspecialchars($session['description']); ?></small>
                                                            <?php endif; ?>
                                                        </div>
                                                    </td>
                                                    <td><?php echo $session['session_date']; ?></td>
                                                    <td>
                                                        <?php echo date('H:i', strtotime($session['start_time'])); ?>
                                                        <?php if ($session['end_time']): ?>
                                                            - <?php echo date('H:i', strtotime($session['end_time'])); ?>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <?php 
                                                        if ($session['start_time'] && $session['end_time']) {
                                                            $start = new DateTime($session['start_time']);
                                                            $end = new DateTime($session['end_time']);
                                                            $duration = $start->diff($end);
                                                            echo $duration->format('%h ساعة %i دقيقة');
                                                        } else {
                                                            echo 'غير محدد';
                                                        }
                                                        ?>
                                                    </td>
                                                    <td>
                                                        <?php
                                                        $status_class = '';
                                                        $status_text = '';
                                                        switch($session['session_status']) {
                                                            case 'completed':
                                                                $status_class = 'success';
                                                                $status_text = 'مكتملة';
                                                                break;
                                                            case 'live':
                                                                $status_class = 'danger';
                                                                $status_text = 'مباشرة الآن';
                                                                break;
                                                            case 'upcoming':
                                                                $status_class = 'warning';
                                                                $status_text = 'قادمة';
                                                                break;
                                                        }
                                                        ?>
                                                        <span class="badge bg-<?php echo $status_class; ?>">
                                                            <?php echo $status_text; ?>
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <?php if ($session['session_status'] === 'live'): ?>
                                                            <a href="join-session.php?id=<?php echo $session['id']; ?>" class="btn btn-success btn-sm">
                                                                <i class="fas fa-play"></i> انضم
                                                            </a>
                                                        <?php elseif ($session['session_status'] === 'completed'): ?>
                                                            <a href="session-recording.php?id=<?php echo $session['id']; ?>" class="btn btn-info btn-sm">
                                                                <i class="fas fa-play-circle"></i> التسجيل
                                                            </a>
                                                        <?php else: ?>
                                                            <span class="text-muted">قريباً</span>
                                                        <?php endif; ?>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- تبويب الدرجات -->
                        <div class="tab-pane fade" id="grades" role="tabpanel">
                            <?php if (empty($grades)): ?>
                                <div class="text-center py-5">
                                    <i class="fas fa-star fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">لا توجد درجات</h5>
                                    <p class="text-muted">لم يتم تقييمك في هذا الكورس بعد</p>
                                </div>
                            <?php else: ?>
                                <div class="row mb-4">
                                    <div class="col-md-4">
                                        <div class="card bg-primary text-white text-center">
                                            <div class="card-body">
                                                <h3><?php echo $average; ?>%</h3>
                                                <p class="mb-0">المعدل العام</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="card bg-success text-white text-center">
                                            <div class="card-body">
                                                <h3><?php echo $total_grades; ?></h3>
                                                <p class="mb-0">إجمالي النقاط</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="card bg-info text-white text-center">
                                            <div class="card-body">
                                                <h3><?php echo count($grades); ?></h3>
                                                <p class="mb-0">عدد التقييمات</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead class="table-light">
                                            <tr>
                                                <th>التقييم</th>
                                                <th>الدرجة</th>
                                                <th>النسبة المئوية</th>
                                                <th>تاريخ التقييم</th>
                                                <th>المقيم</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($grades as $grade): ?>
                                                <tr>
                                                    <td><?php echo htmlspecialchars($grade['assignment_name']); ?></td>
                                                    <td>
                                                        <strong><?php echo $grade['grade']; ?></strong> / <?php echo $grade['max_grade']; ?>
                                                    </td>
                                                    <td>
                                                        <?php 
                                                        $percentage = round(($grade['grade'] / $grade['max_grade']) * 100, 1);
                                                        $color = $percentage >= 90 ? 'success' : ($percentage >= 70 ? 'warning' : 'danger');
                                                        ?>
                                                        <span class="badge bg-<?php echo $color; ?>">
                                                            <?php echo $percentage; ?>%
                                                        </span>
                                                    </td>
                                                    <td><?php echo date('Y-m-d', strtotime($grade['grade_date'])); ?></td>
                                                    <td><?php echo htmlspecialchars($grade['graded_by_name'] ?? 'غير محدد'); ?></td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- تبويب المواد التعليمية -->
                        <div class="tab-pane fade" id="materials" role="tabpanel">
                            <?php if (empty($materials)): ?>
                                <div class="text-center py-5">
                                    <i class="fas fa-file-download fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">لا توجد مواد تعليمية</h5>
                                    <p class="text-muted">لم يتم رفع أي مواد تعليمية لهذا الكورس بعد</p>
                                </div>
                            <?php else: ?>
                                <div class="row">
                                    <?php foreach ($materials as $material): ?>
                                        <div class="col-md-6 mb-3">
                                            <div class="card">
                                                <div class="card-body">
                                                    <h6 class="card-title">
                                                        <i class="fas fa-file me-2"></i>
                                                        <?php echo htmlspecialchars($material['title']); ?>
                                                    </h6>
                                                    <?php if ($material['description']): ?>
                                                        <p class="card-text"><?php echo htmlspecialchars($material['description']); ?></p>
                                                    <?php endif; ?>
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <small class="text-muted">
                                                            <?php echo date('Y-m-d', strtotime($material['created_at'])); ?>
                                                        </small>
                                                        <a href="../<?php echo htmlspecialchars($material['file_path']); ?>" 
                                                           class="btn btn-primary btn-sm" download>
                                                            <i class="fas fa-download"></i> تحميل
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once '../includes/footer.php'; ?>
