<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';
require_once '../includes/activity_logger.php';

// التحقق من صلاحيات المدرب
if (!isLoggedIn() || !isInstructor()) {
    header('Location: ../login.php');
    exit;
}

$course_id = isset($_GET['course_id']) ? (int)$_GET['course_id'] : 0;
$instructor_id = $_SESSION['user_id'];

// التحقق من ملكية الكورس
try {
    $stmt = $conn->prepare("SELECT * FROM courses WHERE id = ? AND instructor_id = ?");
    $stmt->execute([$course_id, $instructor_id]);
    $course = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$course) {
        header('Location: courses.php');
        exit;
    }
} catch (PDOException $e) {
    header('Location: courses.php');
    exit;
}

// معالجة إضافة درجة جديدة
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_grade'])) {
    $student_id = (int)$_POST['student_id'];
    $assignment_name = trim($_POST['assignment_name']);
    $grade = (float)$_POST['grade'];
    $max_grade = (float)$_POST['max_grade'];
    $notes = trim($_POST['notes']);
    
    if (!empty($assignment_name) && $grade >= 0 && $max_grade > 0) {
        try {
            $stmt = $conn->prepare("
                INSERT INTO student_grades (course_id, student_id, assignment_name, grade, max_grade, notes, graded_by) 
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([$course_id, $student_id, $assignment_name, $grade, $max_grade, $notes, $instructor_id]);
            
            logUserActivity('إضافة درجة', "تم إضافة درجة للطالب في الكورس: {$course['title']}");
            $success = 'تم إضافة الدرجة بنجاح';
        } catch (PDOException $e) {
            $error = 'حدث خطأ أثناء إضافة الدرجة';
        }
    } else {
        $error = 'يرجى ملء جميع الحقول المطلوبة بشكل صحيح';
    }
}

// جلب الطلاب والدرجات
$students_grades = [];
try {
    // التحقق من وجود الجداول المطلوبة
    $stmt = $conn->query("SHOW TABLES LIKE 'course_enrollments'");
    $enrollments_exist = $stmt->rowCount() > 0;
    
    $stmt = $conn->query("SHOW TABLES LIKE 'student_grades'");
    $grades_exist = $stmt->rowCount() > 0;
    
    if ($enrollments_exist && $grades_exist) {
        $stmt = $conn->prepare("
            SELECT 
                u.id as student_id,
                u.name as student_name,
                u.email as student_email,
                ce.enrollment_date,
                ce.status,
                (SELECT COUNT(*) FROM student_grades WHERE course_id = ? AND student_id = u.id) as total_assignments,
                (SELECT AVG(grade/max_grade*100) FROM student_grades WHERE course_id = ? AND student_id = u.id) as avg_percentage
            FROM course_enrollments ce
            JOIN users u ON ce.student_id = u.id
            WHERE ce.course_id = ? AND ce.status = 'active'
            ORDER BY u.name
        ");
        $stmt->execute([$course_id, $course_id, $course_id]);
        $students_grades = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
} catch (PDOException $e) {
    $error = 'حدث خطأ في جلب بيانات الدرجات: ' . $e->getMessage();
}

// جلب جميع الدرجات للكورس
$all_grades = [];
try {
    if ($grades_exist) {
        $stmt = $conn->prepare("
            SELECT 
                sg.*,
                u.name as student_name,
                u.email as student_email
            FROM student_grades sg
            JOIN users u ON sg.student_id = u.id
            WHERE sg.course_id = ?
            ORDER BY sg.grade_date DESC
        ");
        $stmt->execute([$course_id]);
        $all_grades = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
} catch (PDOException $e) {
    // تجاهل الخطأ
}

$pageTitle = 'إدارة درجات الكورس: ' . $course['title'];
include 'includes/header.php';

?>

<div class="container-fluid py-4">
    <!-- معلومات الكورس -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-warning text-dark d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="mb-0">إدارة درجات الكورس</h5>
                        <small><?php echo htmlspecialchars($course['title']); ?></small>
                    </div>
                    <div>
                        <a href="course-details.php?id=<?php echo $course_id; ?>" class="btn btn-dark btn-sm">
                            <i class="fas fa-arrow-left"></i> العودة
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php if (isset($success)): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo $success; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($error)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- إحصائيات الدرجات -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h3><?php echo count($students_grades); ?></h3>
                    <p class="mb-0">طالب</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h3><?php echo count($all_grades); ?></h3>
                    <p class="mb-0">درجة مسجلة</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <?php
                    $avg_course = 0;
                    if (!empty($students_grades)) {
                        $total_avg = array_sum(array_column($students_grades, 'avg_percentage'));
                        $avg_course = round($total_avg / count($students_grades), 1);
                    }
                    ?>
                    <h3><?php echo $avg_course; ?>%</h3>
                    <p class="mb-0">متوسط الكورس</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-dark">
                <div class="card-body text-center">
                    <?php
                    $assignments_count = 0;
                    if (!empty($all_grades)) {
                        $assignments = array_unique(array_column($all_grades, 'assignment_name'));
                        $assignments_count = count($assignments);
                    }
                    ?>
                    <h3><?php echo $assignments_count; ?></h3>
                    <p class="mb-0">تقييم</p>
                </div>
            </div>
        </div>
    </div>

    <!-- أزرار الإجراءات -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addGradeModal">
                            <i class="fas fa-plus"></i> إضافة درجة
                        </button>
                        <button type="button" class="btn btn-primary" onclick="exportGrades()">
                            <i class="fas fa-download"></i> تصدير الدرجات
                        </button>
                        <button type="button" class="btn btn-info" onclick="gradeStatistics()">
                            <i class="fas fa-chart-bar"></i> إحصائيات
                        </button>
                        <button type="button" class="btn btn-warning" onclick="bulkGrade()">
                            <i class="fas fa-edit"></i> درجات متعددة
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- علامات التبويب -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <ul class="nav nav-tabs card-header-tabs" id="gradesTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="students-grades-tab" data-bs-toggle="tab" data-bs-target="#students-grades" type="button" role="tab">
                                درجات الطلاب
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="all-grades-tab" data-bs-toggle="tab" data-bs-target="#all-grades" type="button" role="tab">
                                جميع الدرجات
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="assignments-tab" data-bs-toggle="tab" data-bs-target="#assignments" type="button" role="tab">
                                التقييمات
                            </button>
                        </li>
                    </ul>
                </div>
                <div class="card-body">
                    <div class="tab-content" id="gradesTabsContent">
                        <!-- تبويب درجات الطلاب -->
                        <div class="tab-pane fade show active" id="students-grades" role="tabpanel">
                            <?php if (empty($students_grades)): ?>
                                <div class="text-center py-5">
                                    <i class="fas fa-star fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">لا يوجد طلاب مسجلين</h5>
                                    <p class="text-muted">لا يوجد طلاب في هذا الكورس لإضافة درجات لهم</p>
                                    <a href="course-students.php?course_id=<?php echo $course_id; ?>" class="btn btn-primary">
                                        <i class="fas fa-users"></i> إدارة الطلاب
                                    </a>
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead class="table-light">
                                            <tr>
                                                <th>الطالب</th>
                                                <th>البريد الإلكتروني</th>
                                                <th>عدد التقييمات</th>
                                                <th>المتوسط</th>
                                                <th>الحالة</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($students_grades as $student): ?>
                                                <tr>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <div class="avatar-sm bg-warning text-dark rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 40px; height: 40px;">
                                                                <?php echo strtoupper(substr($student['student_name'], 0, 1)); ?>
                                                            </div>
                                                            <strong><?php echo htmlspecialchars($student['student_name']); ?></strong>
                                                        </div>
                                                    </td>
                                                    <td><?php echo htmlspecialchars($student['student_email']); ?></td>
                                                    <td>
                                                        <span class="badge bg-info"><?php echo $student['total_assignments']; ?></span>
                                                    </td>
                                                    <td>
                                                        <?php if ($student['avg_percentage']): ?>
                                                            <?php
                                                            $avg = round($student['avg_percentage'], 1);
                                                            $grade_class = $avg >= 80 ? 'success' : ($avg >= 60 ? 'warning' : 'danger');
                                                            ?>
                                                            <span class="badge bg-<?php echo $grade_class; ?>"><?php echo $avg; ?>%</span>
                                                        <?php else: ?>
                                                            <span class="text-muted">لا توجد درجات</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-success"><?php echo $student['status']; ?></span>
                                                    </td>
                                                    <td>
                                                        <div class="btn-group btn-group-sm">
                                                            <button class="btn btn-outline-success" onclick="addGradeForStudent(<?php echo $student['student_id']; ?>)" title="إضافة درجة">
                                                                <i class="fas fa-plus"></i>
                                                            </button>
                                                            <button class="btn btn-outline-primary" onclick="viewStudentGrades(<?php echo $student['student_id']; ?>)" title="عرض الدرجات">
                                                                <i class="fas fa-eye"></i>
                                                            </button>
                                                            <button class="btn btn-outline-warning" onclick="editStudentGrades(<?php echo $student['student_id']; ?>)" title="تعديل">
                                                                <i class="fas fa-edit"></i>
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- تبويب جميع الدرجات -->
                        <div class="tab-pane fade" id="all-grades" role="tabpanel">
                            <?php if (empty($all_grades)): ?>
                                <div class="text-center py-5">
                                    <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">لا توجد درجات مسجلة</h5>
                                    <p class="text-muted">لم يتم تسجيل أي درجات في هذا الكورس بعد</p>
                                    <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addGradeModal">
                                        <i class="fas fa-plus"></i> إضافة أول درجة
                                    </button>
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead class="table-light">
                                            <tr>
                                                <th>الطالب</th>
                                                <th>التقييم</th>
                                                <th>الدرجة</th>
                                                <th>النسبة المئوية</th>
                                                <th>التاريخ</th>
                                                <th>ملاحظات</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($all_grades as $grade): ?>
                                                <tr>
                                                    <td><?php echo htmlspecialchars($grade['student_name']); ?></td>
                                                    <td><?php echo htmlspecialchars($grade['assignment_name']); ?></td>
                                                    <td><?php echo $grade['grade']; ?>/<?php echo $grade['max_grade']; ?></td>
                                                    <td>
                                                        <?php
                                                        $percentage = round(($grade['grade'] / $grade['max_grade']) * 100, 1);
                                                        $grade_class = $percentage >= 80 ? 'success' : ($percentage >= 60 ? 'warning' : 'danger');
                                                        ?>
                                                        <span class="badge bg-<?php echo $grade_class; ?>"><?php echo $percentage; ?>%</span>
                                                    </td>
                                                    <td><?php echo date('Y-m-d H:i', strtotime($grade['grade_date'])); ?></td>
                                                    <td><?php echo htmlspecialchars($grade['notes'] ?? ''); ?></td>
                                                    <td>
                                                        <div class="btn-group btn-group-sm">
                                                            <button class="btn btn-outline-warning" onclick="editGrade(<?php echo $grade['id']; ?>)" title="تعديل">
                                                                <i class="fas fa-edit"></i>
                                                            </button>
                                                            <button class="btn btn-outline-danger" onclick="deleteGrade(<?php echo $grade['id']; ?>)" title="حذف">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- تبويب التقييمات -->
                        <div class="tab-pane fade" id="assignments" role="tabpanel">
                            <div class="text-center py-5">
                                <i class="fas fa-tasks fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">إدارة التقييمات</h5>
                                <p class="text-muted">قريباً - إدارة أنواع التقييمات والواجبات</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal إضافة درجة -->
<div class="modal fade" id="addGradeModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة درجة جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="student_id" class="form-label">الطالب</label>
                        <select class="form-control" id="student_id" name="student_id" required>
                            <option value="">اختر الطالب...</option>
                            <?php foreach ($students_grades as $student): ?>
                                <option value="<?php echo $student['student_id']; ?>">
                                    <?php echo htmlspecialchars($student['student_name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="assignment_name" class="form-label">اسم التقييم</label>
                        <input type="text" class="form-control" id="assignment_name" name="assignment_name" required
                               placeholder="مثال: امتحان منتصف الفصل">
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <label for="grade" class="form-label">الدرجة المحصلة</label>
                            <input type="number" class="form-control" id="grade" name="grade" min="0" step="0.1" required>
                        </div>
                        <div class="col-md-6">
                            <label for="max_grade" class="form-label">الدرجة الكاملة</label>
                            <input type="number" class="form-control" id="max_grade" name="max_grade" value="100" min="1" step="0.1" required>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="notes" name="notes" rows="2" placeholder="ملاحظات اختيارية"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" name="add_grade" class="btn btn-success">حفظ الدرجة</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
function addGradeForStudent(studentId) {
    document.getElementById('student_id').value = studentId;
    const modal = new bootstrap.Modal(document.getElementById('addGradeModal'));
    modal.show();
}

function viewStudentGrades(studentId) {
    window.open(`student-grades.php?student_id=${studentId}&course_id=<?php echo $course_id; ?>`, 
                '_blank', 'width=800,height=600,scrollbars=yes');
}

function editStudentGrades(studentId) {
    window.location.href = `edit-student-grades.php?student_id=${studentId}&course_id=<?php echo $course_id; ?>`;
}

function editGrade(gradeId) {
    window.location.href = `edit-grade.php?id=${gradeId}&course_id=<?php echo $course_id; ?>`;
}

function deleteGrade(gradeId) {
    if (confirm('هل أنت متأكد من حذف هذه الدرجة؟')) {
        window.location.href = `delete-grade.php?id=${gradeId}&course_id=<?php echo $course_id; ?>`;
    }
}

function exportGrades() {
    window.location.href = `export-grades.php?course_id=<?php echo $course_id; ?>`;
}

function gradeStatistics() {
    window.open(`grade-statistics.php?course_id=<?php echo $course_id; ?>`, 
                '_blank', 'width=1000,height=700,scrollbars=yes');
}

function bulkGrade() {
    window.location.href = `bulk-grade.php?course_id=<?php echo $course_id; ?>`;
}
</script>

<?php require_once '../includes/footer.php'; ?>
