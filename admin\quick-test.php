<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

echo "<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>اختبار سريع</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
</head>
<body>
<div class='container mt-5'>
    <div class='card'>
        <div class='card-header bg-success text-white'>
            <h4>اختبار سريع - لوحة التحكم</h4>
        </div>
        <div class='card-body'>";

try {
    echo "<h5>اختبار المتغيرات:</h5>";
    
    // اختبار getSystemSetting
    $platform_commission = (float)getSystemSetting('platform_commission', 30);
    echo "<div class='alert alert-info'>عمولة المنصة: $platform_commission%</div>";
    
    // اختبار الإحصائيات
    $stmt = $conn->query("
        SELECT 
            COUNT(*) as total_users,
            COUNT(CASE WHEN role = 'student' THEN 1 END) as total_students,
            COUNT(CASE WHEN role = 'instructor' THEN 1 END) as total_instructors
        FROM users
    ");
    $user_stats = $stmt->fetch();
    
    echo "<div class='alert alert-success'>";
    echo "إجمالي المستخدمين: " . $user_stats['total_users'] . "<br>";
    echo "الطلاب: " . $user_stats['total_students'] . "<br>";
    echo "المدربين: " . $user_stats['total_instructors'];
    echo "</div>";
    
    // اختبار الكورسات
    $stmt = $conn->query("
        SELECT 
            COUNT(*) as total_courses,
            COUNT(CASE WHEN course_type = 'free' THEN 1 END) as free_courses,
            COUNT(CASE WHEN course_type = 'paid' THEN 1 END) as paid_courses
        FROM courses
    ");
    $course_stats = $stmt->fetch();
    
    echo "<div class='alert alert-warning'>";
    echo "إجمالي الكورسات: " . $course_stats['total_courses'] . "<br>";
    echo "كورسات مجانية: " . $course_stats['free_courses'] . "<br>";
    echo "كورسات مدفوعة: " . $course_stats['paid_courses'];
    echo "</div>";
    
    // اختبار الجلسات
    $stmt = $conn->query("SELECT COUNT(*) as total_sessions FROM sessions");
    $session_count = $stmt->fetchColumn();
    echo "<div class='alert alert-info'>إجمالي الجلسات: $session_count</div>";
    
    // اختبار طلبات الانضمام
    $stmt = $conn->query("SELECT COUNT(*) as pending_requests FROM join_requests WHERE status = 'pending'");
    $pending_count = $stmt->fetchColumn();
    echo "<div class='alert alert-secondary'>طلبات الانضمام المعلقة: $pending_count</div>";
    
    echo "<div class='alert alert-success mt-4'><strong>جميع الاختبارات نجحت!</strong></div>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>خطأ: " . htmlspecialchars($e->getMessage()) . "</div>";
}

echo "
        </div>
        <div class='card-footer'>
            <a href='dashboard.php' class='btn btn-primary'>الذهاب إلى لوحة التحكم</a>
            <a href='create-missing-tables.php' class='btn btn-warning'>إنشاء الجداول</a>
        </div>
    </div>
</div>
</body>
</html>";
?>
