<?php
/**
 * إعداد النظام وإنشاء البيانات الأساسية
 * System Setup and Initial Data Creation
 */

require_once 'config/database.php';
require_once 'includes/functions.php';

$success_messages = [];
$error_messages = [];

// التحقق من وجود حساب المدير
try {
    $stmt = $conn->prepare("SELECT id FROM users WHERE role = 'admin' LIMIT 1");
    $stmt->execute();
    $admin_exists = $stmt->fetch();
    
    if (!$admin_exists) {
        // إنشاء حساب المدير الافتراضي
        $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt = $conn->prepare("
            INSERT INTO users (name, email, username, password, role, status, email_verified, created_at) 
            VALUES (?, ?, ?, ?, 'admin', 'active', TRUE, NOW())
        ");
        $result = $stmt->execute([
            'مدير النظام',
            '<EMAIL>',
            'admin',
            $admin_password
        ]);
        
        if ($result) {
            $success_messages[] = '✅ تم إنشاء حساب المدير الافتراضي';
            $success_messages[] = 'البريد الإلكتروني: <EMAIL>';
            $success_messages[] = 'كلمة المرور: admin123';
        } else {
            $error_messages[] = '❌ فشل في إنشاء حساب المدير';
        }
    } else {
        $success_messages[] = '✅ حساب المدير موجود بالفعل';
    }
} catch (Exception $e) {
    $error_messages[] = '❌ خطأ في إنشاء حساب المدير: ' . $e->getMessage();
}

// إنشاء مدرب تجريبي
try {
    $stmt = $conn->prepare("SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1");
    $stmt->execute();
    $instructor_exists = $stmt->fetch();
    
    if (!$instructor_exists) {
        $instructor_password = password_hash('instructor123', PASSWORD_DEFAULT);
        $stmt = $conn->prepare("
            INSERT INTO users (name, email, username, phone, password, role, status, email_verified, created_at) 
            VALUES (?, ?, ?, ?, ?, 'instructor', 'active', TRUE, NOW())
        ");
        $result = $stmt->execute([
            'أحمد محمد - مدرب',
            '<EMAIL>',
            'instructor_demo',
            '0501234567',
            $instructor_password
        ]);
        
        if ($result) {
            $success_messages[] = '✅ تم إنشاء حساب المدرب التجريبي';
            $success_messages[] = 'البريد الإلكتروني: <EMAIL>';
            $success_messages[] = 'كلمة المرور: instructor123';
        }
    } else {
        $success_messages[] = '✅ حساب المدرب التجريبي موجود بالفعل';
    }
} catch (Exception $e) {
    $error_messages[] = '❌ خطأ في إنشاء حساب المدرب: ' . $e->getMessage();
}

// إنشاء طالب تجريبي
try {
    $stmt = $conn->prepare("SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1");
    $stmt->execute();
    $student_exists = $stmt->fetch();
    
    if (!$student_exists) {
        $student_password = password_hash('student123', PASSWORD_DEFAULT);
        $stmt = $conn->prepare("
            INSERT INTO users (name, email, username, phone, password, role, status, email_verified, created_at) 
            VALUES (?, ?, ?, ?, ?, 'student', 'active', TRUE, NOW())
        ");
        $result = $stmt->execute([
            'سارة أحمد - طالبة',
            '<EMAIL>',
            'student_demo',
            '0507654321',
            $student_password
        ]);
        
        if ($result) {
            $success_messages[] = '✅ تم إنشاء حساب الطالب التجريبي';
            $success_messages[] = 'البريد الإلكتروني: <EMAIL>';
            $success_messages[] = 'كلمة المرور: student123';
        }
    } else {
        $success_messages[] = '✅ حساب الطالب التجريبي موجود بالفعل';
    }
} catch (Exception $e) {
    $error_messages[] = '❌ خطأ في إنشاء حساب الطالب: ' . $e->getMessage();
}

// إنشاء كورس تجريبي
try {
    $stmt = $conn->prepare("SELECT id FROM courses WHERE title = 'كورس البرمجة الأساسية' LIMIT 1");
    $stmt->execute();
    $course_exists = $stmt->fetch();
    
    if (!$course_exists) {
        // الحصول على معرف المدرب
        $stmt = $conn->prepare("SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1");
        $stmt->execute();
        $instructor = $stmt->fetch();
        
        if ($instructor) {
            $stmt = $conn->prepare("
                INSERT INTO courses (title, description, instructor_id, max_students, price, status, created_at) 
                VALUES (?, ?, ?, ?, ?, 'active', NOW())
            ");
            $result = $stmt->execute([
                'كورس البرمجة الأساسية',
                'تعلم أساسيات البرمجة من الصفر حتى الاحتراف. يشمل الكورس HTML, CSS, JavaScript وأساسيات قواعد البيانات.',
                $instructor['id'],
                30,
                299.99
            ]);
            
            if ($result) {
                $success_messages[] = '✅ تم إنشاء كورس تجريبي';
            }
        }
    } else {
        $success_messages[] = '✅ الكورس التجريبي موجود بالفعل';
    }
} catch (Exception $e) {
    $error_messages[] = '❌ خطأ في إنشاء الكورس: ' . $e->getMessage();
}

// إنشاء جلسة تجريبية
try {
    $stmt = $conn->prepare("SELECT id FROM courses WHERE title = 'كورس البرمجة الأساسية' LIMIT 1");
    $stmt->execute();
    $course = $stmt->fetch();
    
    if ($course) {
        $stmt = $conn->prepare("SELECT id FROM sessions WHERE course_id = ? LIMIT 1");
        $stmt->execute([$course['id']]);
        $session_exists = $stmt->fetch();
        
        if (!$session_exists) {
            $stmt = $conn->prepare("
                INSERT INTO sessions (course_id, title, description, start_time, duration, status, created_at) 
                VALUES (?, ?, ?, ?, ?, 'scheduled', NOW())
            ");
            $result = $stmt->execute([
                $course['id'],
                'الجلسة الأولى: مقدمة في البرمجة',
                'في هذه الجلسة سنتعرف على أساسيات البرمجة ولغة HTML',
                date('Y-m-d H:i:s', strtotime('+1 week')),
                90 // 90 دقيقة
            ]);
            
            if ($result) {
                $success_messages[] = '✅ تم إنشاء جلسة تجريبية';
            }
        } else {
            $success_messages[] = '✅ الجلسة التجريبية موجودة بالفعل';
        }
    }
} catch (Exception $e) {
    $error_messages[] = '❌ خطأ في إنشاء الجلسة: ' . $e->getMessage();
}

?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد النظام</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .setup-container {
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
        }
        .setup-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .setup-header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .setup-body {
            padding: 30px;
        }
        .message-item {
            padding: 10px 15px;
            margin: 5px 0;
            border-radius: 8px;
            border-right: 4px solid;
        }
        .success-message {
            background: #d4edda;
            color: #155724;
            border-right-color: #28a745;
        }
        .error-message {
            background: #f8d7da;
            color: #721c24;
            border-right-color: #dc3545;
        }
        .btn-continue {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-continue:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            color: white;
        }
    </style>
</head>
<body>
    <div class="setup-container">
        <div class="setup-card">
            <div class="setup-header">
                <h1><i class="fas fa-cogs me-3"></i>إعداد النظام</h1>
                <p class="mb-0">تم إعداد قاعدة البيانات وإنشاء البيانات الأساسية</p>
            </div>
            
            <div class="setup-body">
                <?php if (!empty($success_messages)): ?>
                    <h5 class="text-success mb-3"><i class="fas fa-check-circle me-2"></i>العمليات الناجحة:</h5>
                    <?php foreach ($success_messages as $message): ?>
                        <div class="message-item success-message"><?php echo $message; ?></div>
                    <?php endforeach; ?>
                <?php endif; ?>
                
                <?php if (!empty($error_messages)): ?>
                    <h5 class="text-danger mb-3 mt-4"><i class="fas fa-exclamation-triangle me-2"></i>الأخطاء:</h5>
                    <?php foreach ($error_messages as $message): ?>
                        <div class="message-item error-message"><?php echo $message; ?></div>
                    <?php endforeach; ?>
                <?php endif; ?>
                
                <div class="text-center mt-4">
                    <h5 class="mb-3">النظام جاهز للاستخدام!</h5>
                    <div class="d-flex gap-3 justify-content-center flex-wrap">
                        <a href="login.php" class="btn btn-continue">
                            <i class="fas fa-sign-in-alt me-2"></i>
                            تسجيل الدخول
                        </a>
                        <a href="register.php" class="btn btn-outline-primary">
                            <i class="fas fa-user-plus me-2"></i>
                            تسجيل حساب جديد
                        </a>
                        <a href="admin/dashboard.php" class="btn btn-outline-success">
                            <i class="fas fa-tachometer-alt me-2"></i>
                            لوحة الإدارة
                        </a>
                    </div>
                </div>
                
                <div class="alert alert-info mt-4">
                    <h6><i class="fas fa-info-circle me-2"></i>معلومات مهمة:</h6>
                    <ul class="mb-0">
                        <li>تأكد من تغيير كلمات المرور الافتراضية</li>
                        <li>يمكنك حذف هذا الملف بعد الانتهاء من الإعداد</li>
                        <li>تحقق من إعدادات قاعدة البيانات في config/database.php</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
