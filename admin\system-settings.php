<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات المدير
if (!isLoggedIn() || !isAdmin()) {
    header('Location: ../login.php');
    exit;
}

$pageTitle = 'إعدادات النظام';
$breadcrumbs = [
    ['title' => 'إعدادات النظام']
];

$success = '';
$error = '';

// معالجة حفظ الإعدادات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $conn->beginTransaction();
        
        foreach ($_POST as $key => $value) {
            if ($key === 'action') continue;
            
            // التحقق من صحة البيانات
            if ($key === 'platform_commission') {
                $value = (float)$value;
                if ($value < 0 || $value > 100) {
                    throw new Exception('عمولة المنصة يجب أن تكون بين 0 و 100');
                }
            }
            
            if ($key === 'max_file_size') {
                $value = (int)$value;
                if ($value < 1 || $value > 1000) {
                    throw new Exception('حجم الملف يجب أن يكون بين 1 و 1000 ميجابايت');
                }
            }
            
            // تحديث أو إدراج الإعداد
            $stmt = $conn->prepare("
                INSERT INTO system_settings (setting_key, setting_value, updated_by) 
                VALUES (?, ?, ?) 
                ON DUPLICATE KEY UPDATE 
                setting_value = VALUES(setting_value), 
                updated_by = VALUES(updated_by),
                updated_at = CURRENT_TIMESTAMP
            ");
            $stmt->execute([$key, $value, $_SESSION['user_id']]);
        }
        
        $conn->commit();
        $success = 'تم حفظ الإعدادات بنجاح';
        
        // تسجيل النشاط
        logUserActivity($_SESSION['user_id'], 'تحديث إعدادات النظام', 'تم تحديث إعدادات النظام');
        
    } catch (Exception $e) {
        $conn->rollBack();
        $error = 'حدث خطأ أثناء حفظ الإعدادات: ' . $e->getMessage();
    }
}

// جلب الإعدادات الحالية
try {
    // التحقق من وجود جدول system_settings وإنشاؤه إذا لم يكن موجود
    $conn->exec("CREATE TABLE IF NOT EXISTS system_settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        setting_key VARCHAR(100) NOT NULL UNIQUE,
        setting_value TEXT NOT NULL,
        description TEXT NULL,
        updated_by INT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_setting_key (setting_key)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

    $stmt = $conn->query("SELECT setting_key, setting_value, description FROM system_settings ORDER BY setting_key");
    $settings_raw = $stmt->fetchAll();

    $settings = [];
    foreach ($settings_raw as $setting) {
        $settings[$setting['setting_key']] = $setting;
    }

    // إضافة إعدادات افتراضية إذا لم توجد
    if (empty($settings)) {
        $default_settings = [
            ['site_name', 'منصة زووم التعليمية', 'اسم الموقع'],
            ['site_description', 'منصة تعليمية متقدمة لتعلم المهارات الرقمية', 'وصف الموقع'],
            ['admin_email', '<EMAIL>', 'بريد المدير'],
            ['timezone', 'Asia/Riyadh', 'المنطقة الزمنية'],
            ['language', 'ar', 'اللغة الافتراضية'],
            ['currency', 'SAR', 'العملة الافتراضية'],
            ['platform_commission', '30', 'عمولة المنصة'],
            ['min_commission', '15', 'أقل عمولة'],
            ['max_commission', '50', 'أعلى عمولة'],
            ['registration_enabled', 'true', 'تفعيل التسجيل'],
            ['email_verification', 'true', 'تفعيل التحقق من البريد'],
            ['auto_approve_instructors', 'false', 'الموافقة التلقائية على المدربين'],
            ['auto_approve_courses', 'false', 'الموافقة التلقائية على الكورسات'],
            ['max_file_size', '100', 'أقصى حجم ملف بالميجابايت'],
            ['allowed_file_types', '["jpg","jpeg","png","pdf","doc","docx","mp4"]', 'أنواع الملفات المسموحة'],
            ['smtp_host', '', 'خادم SMTP'],
            ['smtp_port', '587', 'منفذ SMTP'],
            ['smtp_username', '', 'اسم مستخدم SMTP'],
            ['smtp_password', '', 'كلمة مرور SMTP'],
            ['smtp_encryption', 'tls', 'نوع تشفير SMTP'],
            ['backup_frequency', 'daily', 'تكرار النسخ الاحتياطي'],
            ['backup_retention', '30', 'مدة الاحتفاظ بالنسخ'],
            ['maintenance_mode', 'false', 'وضع الصيانة']
        ];

        foreach ($default_settings as $setting) {
            $stmt = $conn->prepare("INSERT IGNORE INTO system_settings (setting_key, setting_value, description) VALUES (?, ?, ?)");
            $stmt->execute($setting);
            $settings[$setting[0]] = ['setting_value' => $setting[1], 'description' => $setting[2]];
        }
    }

} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء جلب الإعدادات: ' . $e->getMessage();
    $settings = [];
}

include 'includes/header.php';
?>

<?php if ($success): ?>
<div class="alert alert-success alert-dismissible fade show" data-aos="fade-down">
    <i class="fas fa-check-circle me-2"></i>
    <?php echo htmlspecialchars($success); ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<?php if ($error): ?>
<div class="alert alert-danger alert-dismissible fade show" data-aos="fade-down">
    <i class="fas fa-exclamation-circle me-2"></i>
    <?php echo htmlspecialchars($error); ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<form method="POST" id="settingsForm">
    <input type="hidden" name="action" value="save_settings">
    
    <div class="row">
        <!-- الإعدادات العامة -->
        <div class="col-lg-6 mb-4" data-aos="fade-up">
            <div class="card-admin">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-cog me-2"></i>
                        الإعدادات العامة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">اسم الموقع</label>
                        <input type="text" name="site_name" class="form-control" 
                               value="<?php echo htmlspecialchars($settings['site_name']['setting_value'] ?? ''); ?>" required>
                        <small class="form-text text-muted">اسم المنصة التعليمية</small>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">وصف الموقع</label>
                        <textarea name="site_description" class="form-control" rows="3"><?php echo htmlspecialchars($settings['site_description']['setting_value'] ?? ''); ?></textarea>
                        <small class="form-text text-muted">وصف مختصر للمنصة</small>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">بريد المدير الإلكتروني</label>
                        <input type="email" name="admin_email" class="form-control" 
                               value="<?php echo htmlspecialchars($settings['admin_email']['setting_value'] ?? ''); ?>" required>
                        <small class="form-text text-muted">البريد الإلكتروني الرئيسي للمدير</small>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">المنطقة الزمنية</label>
                        <select name="timezone" class="form-select">
                            <option value="Asia/Riyadh" <?php echo ($settings['timezone']['setting_value'] ?? '') === 'Asia/Riyadh' ? 'selected' : ''; ?>>الرياض (GMT+3)</option>
                            <option value="Asia/Dubai" <?php echo ($settings['timezone']['setting_value'] ?? '') === 'Asia/Dubai' ? 'selected' : ''; ?>>دبي (GMT+4)</option>
                            <option value="Asia/Kuwait" <?php echo ($settings['timezone']['setting_value'] ?? '') === 'Asia/Kuwait' ? 'selected' : ''; ?>>الكويت (GMT+3)</option>
                            <option value="Asia/Qatar" <?php echo ($settings['timezone']['setting_value'] ?? '') === 'Asia/Qatar' ? 'selected' : ''; ?>>قطر (GMT+3)</option>
                            <option value="Asia/Bahrain" <?php echo ($settings['timezone']['setting_value'] ?? '') === 'Asia/Bahrain' ? 'selected' : ''; ?>>البحرين (GMT+3)</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">اللغة الافتراضية</label>
                        <select name="language" class="form-select">
                            <option value="ar" <?php echo ($settings['language']['setting_value'] ?? '') === 'ar' ? 'selected' : ''; ?>>العربية</option>
                            <option value="en" <?php echo ($settings['language']['setting_value'] ?? '') === 'en' ? 'selected' : ''; ?>>English</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">العملة الافتراضية</label>
                        <select name="currency" class="form-select">
                            <option value="SAR" <?php echo ($settings['currency']['setting_value'] ?? '') === 'SAR' ? 'selected' : ''; ?>>ريال سعودي (SAR)</option>
                            <option value="AED" <?php echo ($settings['currency']['setting_value'] ?? '') === 'AED' ? 'selected' : ''; ?>>درهم إماراتي (AED)</option>
                            <option value="KWD" <?php echo ($settings['currency']['setting_value'] ?? '') === 'KWD' ? 'selected' : ''; ?>>دينار كويتي (KWD)</option>
                            <option value="QAR" <?php echo ($settings['currency']['setting_value'] ?? '') === 'QAR' ? 'selected' : ''; ?>>ريال قطري (QAR)</option>
                            <option value="BHD" <?php echo ($settings['currency']['setting_value'] ?? '') === 'BHD' ? 'selected' : ''; ?>>دينار بحريني (BHD)</option>
                            <option value="USD" <?php echo ($settings['currency']['setting_value'] ?? '') === 'USD' ? 'selected' : ''; ?>>دولار أمريكي (USD)</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- الإعدادات المالية -->
        <div class="col-lg-6 mb-4" data-aos="fade-up" data-aos-delay="100">
            <div class="card-admin">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-money-bill-wave me-2"></i>
                        الإعدادات المالية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">عمولة المنصة (%)</label>
                        <div class="input-group">
                            <input type="number" name="platform_commission" class="form-control" 
                                   min="0" max="100" step="0.1"
                                   value="<?php echo htmlspecialchars($settings['platform_commission']['setting_value'] ?? '30'); ?>" required>
                            <span class="input-group-text">%</span>
                        </div>
                        <small class="form-text text-muted">النسبة المئوية التي تحصل عليها المنصة من كل عملية بيع</small>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">أقل عمولة مسموحة (%)</label>
                        <div class="input-group">
                            <input type="number" name="min_commission" class="form-control" 
                                   min="0" max="100" step="0.1"
                                   value="<?php echo htmlspecialchars($settings['min_commission']['setting_value'] ?? '15'); ?>" required>
                            <span class="input-group-text">%</span>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">أعلى عمولة مسموحة (%)</label>
                        <div class="input-group">
                            <input type="number" name="max_commission" class="form-control" 
                                   min="0" max="100" step="0.1"
                                   value="<?php echo htmlspecialchars($settings['max_commission']['setting_value'] ?? '50'); ?>" required>
                            <span class="input-group-text">%</span>
                        </div>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>ملاحظة:</strong> تغيير العمولة سيؤثر على الكورسات الجديدة فقط
                    </div>
                </div>
            </div>
        </div>

        <!-- إعدادات المستخدمين -->
        <div class="col-lg-6 mb-4" data-aos="fade-up" data-aos-delay="200">
            <div class="card-admin">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-users me-2"></i>
                        إعدادات المستخدمين
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" name="registration_enabled" value="true"
                                   <?php echo ($settings['registration_enabled']['setting_value'] ?? 'true') === 'true' ? 'checked' : ''; ?>>
                            <label class="form-check-label">تفعيل التسجيل</label>
                        </div>
                        <small class="form-text text-muted">السماح للمستخدمين الجدد بالتسجيل</small>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" name="email_verification" value="true"
                                   <?php echo ($settings['email_verification']['setting_value'] ?? 'true') === 'true' ? 'checked' : ''; ?>>
                            <label class="form-check-label">تفعيل التحقق من البريد الإلكتروني</label>
                        </div>
                        <small class="form-text text-muted">يتطلب من المستخدمين تأكيد بريدهم الإلكتروني</small>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" name="auto_approve_instructors" value="true"
                                   <?php echo ($settings['auto_approve_instructors']['setting_value'] ?? 'false') === 'true' ? 'checked' : ''; ?>>
                            <label class="form-check-label">الموافقة التلقائية على المدربين</label>
                        </div>
                        <small class="form-text text-muted">الموافقة تلقائياً على طلبات المدربين الجدد</small>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" name="auto_approve_courses" value="true"
                                   <?php echo ($settings['auto_approve_courses']['setting_value'] ?? 'false') === 'true' ? 'checked' : ''; ?>>
                            <label class="form-check-label">الموافقة التلقائية على الكورسات</label>
                        </div>
                        <small class="form-text text-muted">الموافقة تلقائياً على الكورسات الجديدة</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- إعدادات الملفات -->
        <div class="col-lg-6 mb-4" data-aos="fade-up" data-aos-delay="300">
            <div class="card-admin">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-file-upload me-2"></i>
                        إعدادات الملفات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">أقصى حجم ملف (ميجابايت)</label>
                        <div class="input-group">
                            <input type="number" name="max_file_size" class="form-control" 
                                   min="1" max="1000"
                                   value="<?php echo htmlspecialchars($settings['max_file_size']['setting_value'] ?? '100'); ?>" required>
                            <span class="input-group-text">MB</span>
                        </div>
                        <small class="form-text text-muted">الحد الأقصى لحجم الملفات المرفوعة</small>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">أنواع الملفات المسموحة</label>
                        <div class="row">
                            <?php
                            $allowed_types = json_decode($settings['allowed_file_types']['setting_value'] ?? '[]', true);
                            $file_types = [
                                'jpg' => 'صور JPG',
                                'jpeg' => 'صور JPEG', 
                                'png' => 'صور PNG',
                                'gif' => 'صور GIF',
                                'pdf' => 'ملفات PDF',
                                'doc' => 'مستندات Word',
                                'docx' => 'مستندات Word الحديثة',
                                'ppt' => 'عروض PowerPoint',
                                'pptx' => 'عروض PowerPoint الحديثة',
                                'mp4' => 'فيديو MP4',
                                'mp3' => 'صوت MP3',
                                'zip' => 'ملفات مضغوطة ZIP',
                                'rar' => 'ملفات مضغوطة RAR'
                            ];
                            ?>
                            <?php foreach ($file_types as $ext => $name): ?>
                            <div class="col-md-6 mb-2">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="file_types[]" value="<?php echo $ext; ?>"
                                           <?php echo in_array($ext, $allowed_types) ? 'checked' : ''; ?>>
                                    <label class="form-check-label"><?php echo $name; ?></label>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- إعدادات البريد الإلكتروني -->
        <div class="col-lg-6 mb-4" data-aos="fade-up" data-aos-delay="400">
            <div class="card-admin">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-envelope me-2"></i>
                        إعدادات البريد الإلكتروني
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">خادم SMTP</label>
                        <input type="text" name="smtp_host" class="form-control" 
                               value="<?php echo htmlspecialchars($settings['smtp_host']['setting_value'] ?? ''); ?>"
                               placeholder="smtp.gmail.com">
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">منفذ SMTP</label>
                        <input type="number" name="smtp_port" class="form-control" 
                               value="<?php echo htmlspecialchars($settings['smtp_port']['setting_value'] ?? '587'); ?>"
                               placeholder="587">
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">اسم المستخدم</label>
                        <input type="text" name="smtp_username" class="form-control" 
                               value="<?php echo htmlspecialchars($settings['smtp_username']['setting_value'] ?? ''); ?>"
                               placeholder="<EMAIL>">
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">كلمة المرور</label>
                        <input type="password" name="smtp_password" class="form-control" 
                               value="<?php echo htmlspecialchars($settings['smtp_password']['setting_value'] ?? ''); ?>"
                               placeholder="كلمة مرور البريد الإلكتروني">
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">نوع التشفير</label>
                        <select name="smtp_encryption" class="form-select">
                            <option value="tls" <?php echo ($settings['smtp_encryption']['setting_value'] ?? 'tls') === 'tls' ? 'selected' : ''; ?>>TLS</option>
                            <option value="ssl" <?php echo ($settings['smtp_encryption']['setting_value'] ?? '') === 'ssl' ? 'selected' : ''; ?>>SSL</option>
                            <option value="none" <?php echo ($settings['smtp_encryption']['setting_value'] ?? '') === 'none' ? 'selected' : ''; ?>>بدون تشفير</option>
                        </select>
                    </div>
                    
                    <button type="button" class="btn btn-outline-info btn-sm" onclick="testEmailSettings()">
                        <i class="fas fa-paper-plane me-1"></i>
                        اختبار الإعدادات
                    </button>
                </div>
            </div>
        </div>

        <!-- إعدادات النسخ الاحتياطي -->
        <div class="col-lg-6 mb-4" data-aos="fade-up" data-aos-delay="500">
            <div class="card-admin">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-database me-2"></i>
                        إعدادات النسخ الاحتياطي
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">تكرار النسخ الاحتياطي</label>
                        <select name="backup_frequency" class="form-select">
                            <option value="daily" <?php echo ($settings['backup_frequency']['setting_value'] ?? 'daily') === 'daily' ? 'selected' : ''; ?>>يومي</option>
                            <option value="weekly" <?php echo ($settings['backup_frequency']['setting_value'] ?? '') === 'weekly' ? 'selected' : ''; ?>>أسبوعي</option>
                            <option value="monthly" <?php echo ($settings['backup_frequency']['setting_value'] ?? '') === 'monthly' ? 'selected' : ''; ?>>شهري</option>
                            <option value="manual" <?php echo ($settings['backup_frequency']['setting_value'] ?? '') === 'manual' ? 'selected' : ''; ?>>يدوي فقط</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">مدة الاحتفاظ بالنسخ (أيام)</label>
                        <input type="number" name="backup_retention" class="form-control" 
                               min="1" max="365"
                               value="<?php echo htmlspecialchars($settings['backup_retention']['setting_value'] ?? '30'); ?>" required>
                        <small class="form-text text-muted">عدد الأيام للاحتفاظ بالنسخ الاحتياطية</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- إعدادات النظام -->
        <div class="col-lg-6 mb-4" data-aos="fade-up" data-aos-delay="600">
            <div class="card-admin">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-cogs me-2"></i>
                        إعدادات النظام المتقدمة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" name="maintenance_mode" value="true"
                                   <?php echo ($settings['maintenance_mode']['setting_value'] ?? 'false') === 'true' ? 'checked' : ''; ?>>
                            <label class="form-check-label">وضع الصيانة</label>
                        </div>
                        <small class="form-text text-muted">تفعيل وضع الصيانة يمنع الوصول للموقع مؤقتاً</small>
                    </div>
                    
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>تحذير:</strong> تفعيل وضع الصيانة سيمنع جميع المستخدمين من الوصول للموقع باستثناء المديرين
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- أزرار الحفظ -->
    <div class="row">
        <div class="col-12" data-aos="fade-up">
            <div class="card-admin">
                <div class="card-body text-center">
                    <button type="submit" class="btn btn-admin-primary btn-lg me-3">
                        <i class="fas fa-save me-2"></i>
                        حفظ جميع الإعدادات
                    </button>
                    <button type="button" class="btn btn-outline-secondary btn-lg" onclick="resetForm()">
                        <i class="fas fa-undo me-2"></i>
                        إعادة تعيين
                    </button>
                </div>
            </div>
        </div>
    </div>
</form>

<script>
// تفعيل الحفظ التلقائي
autoSave('settingsForm', 'api/auto-save-settings.php');

// اختبار إعدادات البريد الإلكتروني
function testEmailSettings() {
    const formData = new FormData(document.getElementById('settingsForm'));
    
    showLoading();
    
    fetch('api/test-email.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.success) {
            showSuccess('تم إرسال بريد اختبار بنجاح');
        } else {
            showError('فشل في إرسال البريد: ' + data.message);
        }
    })
    .catch(error => {
        hideLoading();
        showError('حدث خطأ أثناء اختبار البريد الإلكتروني');
    });
}

// إعادة تعيين النموذج
function resetForm() {
    if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات؟')) {
        location.reload();
    }
}

// معالجة أنواع الملفات
document.addEventListener('DOMContentLoaded', function() {
    const fileTypeCheckboxes = document.querySelectorAll('input[name="file_types[]"]');
    const hiddenInput = document.createElement('input');
    hiddenInput.type = 'hidden';
    hiddenInput.name = 'allowed_file_types';
    document.getElementById('settingsForm').appendChild(hiddenInput);
    
    function updateFileTypes() {
        const selectedTypes = Array.from(fileTypeCheckboxes)
            .filter(cb => cb.checked)
            .map(cb => cb.value);
        hiddenInput.value = JSON.stringify(selectedTypes);
    }
    
    fileTypeCheckboxes.forEach(cb => {
        cb.addEventListener('change', updateFileTypes);
    });
    
    updateFileTypes(); // تحديث أولي
});
</script>

<?php include 'includes/footer.php'; ?>
