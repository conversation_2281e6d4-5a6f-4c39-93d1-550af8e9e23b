/**
 * Service Worker للمنصة التعليمية
 * Learning Platform Service Worker
 */

const CACHE_NAME = 'learning-platform-v2.0.0';
const OFFLINE_URL = '/offline.html';

// الملفات المطلوب تخزينها مؤقتاً
const STATIC_CACHE_URLS = [
    '/',
    '/index.php',
    '/login.php',
    '/register.php',
    '/offline.html',
    '/assets/css/main.css',
    '/assets/css/components.css',
    '/assets/js/main.js',
    '/assets/js/enhanced.js',
    '/manifest.json',
    // Bootstrap و Font Awesome
    'https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css',
    'https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js',
    'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css',
    // الخطوط
    'https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap',
    // AOS
    'https://unpkg.com/aos@2.3.1/dist/aos.css',
    'https://unpkg.com/aos@2.3.1/dist/aos.js'
];

// الملفات الديناميكية
const DYNAMIC_CACHE_URLS = [
    '/course-details.php',
    '/student/dashboard.php',
    '/instructor/dashboard.php',
    '/admin/dashboard.php'
];

// تثبيت Service Worker
self.addEventListener('install', event => {
    console.log('Service Worker: Installing...');
    
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then(cache => {
                console.log('Service Worker: Caching static files');
                return cache.addAll(STATIC_CACHE_URLS);
            })
            .then(() => {
                console.log('Service Worker: Static files cached successfully');
                return self.skipWaiting();
            })
            .catch(error => {
                console.error('Service Worker: Error caching static files', error);
            })
    );
});

// تفعيل Service Worker
self.addEventListener('activate', event => {
    console.log('Service Worker: Activating...');
    
    event.waitUntil(
        caches.keys()
            .then(cacheNames => {
                return Promise.all(
                    cacheNames.map(cacheName => {
                        if (cacheName !== CACHE_NAME) {
                            console.log('Service Worker: Deleting old cache', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            })
            .then(() => {
                console.log('Service Worker: Activated successfully');
                return self.clients.claim();
            })
    );
});

// اعتراض الطلبات
self.addEventListener('fetch', event => {
    // تجاهل الطلبات غير HTTP/HTTPS
    if (!event.request.url.startsWith('http')) {
        return;
    }

    // تجاهل طلبات POST
    if (event.request.method !== 'GET') {
        return;
    }

    event.respondWith(
        caches.match(event.request)
            .then(cachedResponse => {
                // إذا وُجد في الكاش، أرجعه
                if (cachedResponse) {
                    return cachedResponse;
                }

                // إذا لم يوجد، جرب الشبكة
                return fetch(event.request)
                    .then(response => {
                        // تحقق من صحة الاستجابة
                        if (!response || response.status !== 200 || response.type !== 'basic') {
                            return response;
                        }

                        // نسخ الاستجابة للكاش
                        const responseToCache = response.clone();
                        
                        caches.open(CACHE_NAME)
                            .then(cache => {
                                // تخزين الملفات الديناميكية
                                if (shouldCacheDynamically(event.request.url)) {
                                    cache.put(event.request, responseToCache);
                                }
                            });

                        return response;
                    })
                    .catch(() => {
                        // إذا فشلت الشبكة، أرجع صفحة offline
                        if (event.request.destination === 'document') {
                            return caches.match(OFFLINE_URL);
                        }
                        
                        // للصور، أرجع صورة افتراضية
                        if (event.request.destination === 'image') {
                            return new Response(
                                '<svg xmlns="http://www.w3.org/2000/svg" width="200" height="200" viewBox="0 0 200 200"><rect width="200" height="200" fill="#f0f0f0"/><text x="50%" y="50%" text-anchor="middle" dy=".3em" fill="#999">صورة غير متاحة</text></svg>',
                                { headers: { 'Content-Type': 'image/svg+xml' } }
                            );
                        }
                    });
            })
    );
});

// تحديد ما إذا كان يجب تخزين الملف ديناميكياً
function shouldCacheDynamically(url) {
    return DYNAMIC_CACHE_URLS.some(pattern => url.includes(pattern)) ||
           url.includes('/assets/') ||
           url.includes('.css') ||
           url.includes('.js') ||
           url.includes('.png') ||
           url.includes('.jpg') ||
           url.includes('.jpeg') ||
           url.includes('.gif') ||
           url.includes('.webp');
}

// معالجة الرسائل من الصفحة الرئيسية
self.addEventListener('message', event => {
    if (event.data && event.data.type === 'SKIP_WAITING') {
        self.skipWaiting();
    }
    
    if (event.data && event.data.type === 'GET_VERSION') {
        event.ports[0].postMessage({ version: CACHE_NAME });
    }
    
    if (event.data && event.data.type === 'CACHE_URLS') {
        const urls = event.data.urls;
        caches.open(CACHE_NAME)
            .then(cache => cache.addAll(urls))
            .then(() => {
                event.ports[0].postMessage({ success: true });
            })
            .catch(error => {
                event.ports[0].postMessage({ success: false, error: error.message });
            });
    }
});

// معالجة الإشعارات Push
self.addEventListener('push', event => {
    console.log('Service Worker: Push notification received');
    
    const options = {
        body: 'لديك إشعار جديد من منصة التعلم',
        icon: '/assets/images/icons/icon-192x192.png',
        badge: '/assets/images/icons/badge-72x72.png',
        vibrate: [100, 50, 100],
        data: {
            dateOfArrival: Date.now(),
            primaryKey: 1
        },
        actions: [
            {
                action: 'explore',
                title: 'عرض التفاصيل',
                icon: '/assets/images/icons/checkmark.png'
            },
            {
                action: 'close',
                title: 'إغلاق',
                icon: '/assets/images/icons/xmark.png'
            }
        ],
        requireInteraction: true,
        silent: false,
        tag: 'learning-platform-notification'
    };

    if (event.data) {
        const data = event.data.json();
        options.body = data.body || options.body;
        options.title = data.title || 'منصة التعلم';
        options.data = { ...options.data, ...data };
    }

    event.waitUntil(
        self.registration.showNotification('منصة التعلم', options)
    );
});

// معالجة النقر على الإشعارات
self.addEventListener('notificationclick', event => {
    console.log('Service Worker: Notification clicked');
    
    event.notification.close();

    if (event.action === 'explore') {
        event.waitUntil(
            clients.openWindow('/student/dashboard.php')
        );
    } else if (event.action === 'close') {
        // لا تفعل شيئاً، الإشعار مُغلق بالفعل
    } else {
        // النقر على الإشعار نفسه
        event.waitUntil(
            clients.matchAll({ type: 'window' })
                .then(clientList => {
                    for (let client of clientList) {
                        if (client.url === '/' && 'focus' in client) {
                            return client.focus();
                        }
                    }
                    if (clients.openWindow) {
                        return clients.openWindow('/');
                    }
                })
        );
    }
});

// معالجة المزامنة في الخلفية
self.addEventListener('sync', event => {
    console.log('Service Worker: Background sync triggered');
    
    if (event.tag === 'background-sync') {
        event.waitUntil(doBackgroundSync());
    }
});

// تنفيذ المزامنة في الخلفية
function doBackgroundSync() {
    return new Promise((resolve, reject) => {
        // هنا يمكن إضافة منطق المزامنة
        // مثل إرسال البيانات المحفوظة محلياً إلى الخادم
        console.log('Service Worker: Performing background sync');
        resolve();
    });
}

// تنظيف الكاش القديم
function cleanupOldCaches() {
    return caches.keys()
        .then(cacheNames => {
            return Promise.all(
                cacheNames
                    .filter(cacheName => cacheName.startsWith('learning-platform-') && cacheName !== CACHE_NAME)
                    .map(cacheName => caches.delete(cacheName))
            );
        });
}

// تحديث الكاش دورياً
setInterval(() => {
    cleanupOldCaches();
}, 24 * 60 * 60 * 1000); // كل 24 ساعة

console.log('Service Worker: Script loaded successfully');
