<?php
// تكوين إعدادات الجلسة - يجب أن يكون هذا قبل أي session_start
if (session_status() === PHP_SESSION_NONE) {
    // تعيين إعدادات الجلسة فقط إذا لم تكن الجلسة نشطة
    ini_set('session.cookie_httponly', 1);
    ini_set('session.use_only_cookies', 1);

    // تعيين cookie_secure فقط إذا كان الموقع يستخدم HTTPS
    $is_https = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off')
                || $_SERVER['SERVER_PORT'] == 443
                || (isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] === 'https');

    if ($is_https) {
        ini_set('session.cookie_secure', 1);
        ini_set('session.cookie_samesite', 'Strict');
    } else {
        ini_set('session.cookie_secure', 0);
        ini_set('session.cookie_samesite', 'Lax');
    }

    // بدء الجلسة
    session_start();
}

// تعيين الإعدادات في php.ini
/*
; في ملف php.ini
session.cookie_httponly = 1
session.use_only_cookies = 1
session.cookie_secure = 1
session.cookie_samesite = "Strict"
*/
