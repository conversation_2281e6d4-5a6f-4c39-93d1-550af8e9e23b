<?php
require_once '../config/database.php';

echo "<h2>إنشاء بيانات تجريبية للتصنيفات والكورسات</h2>";

try {
    // إنشاء بعض الكورسات التجريبية إذا لم تكن موجودة
    $sample_courses = [
        [
            'title' => 'أساسيات البرمجة بـ PHP',
            'description' => 'تعلم أساسيات البرمجة باستخدام لغة PHP من الصفر',
            'price' => 299.00,
            'level' => 'beginner',
            'specialization' => 'البرمجة',
            'status' => 'active'
        ],
        [
            'title' => 'تصميم المواقع بـ HTML و CSS',
            'description' => 'تعلم تصميم المواقع الحديثة باستخدام HTML5 و CSS3',
            'price' => 199.00,
            'level' => 'beginner',
            'specialization' => 'التصميم',
            'status' => 'active'
        ],
        [
            'title' => 'التسويق الرقمي المتقدم',
            'description' => 'استراتيجيات التسويق الرقمي الحديثة ووسائل التواصل الاجتماعي',
            'price' => 399.00,
            'level' => 'intermediate',
            'specialization' => 'التسويق',
            'status' => 'active'
        ],
        [
            'title' => 'إدارة المشاريع الاحترافية',
            'description' => 'تعلم إدارة المشاريع باستخدام أحدث الأساليب والأدوات',
            'price' => 499.00,
            'level' => 'advanced',
            'specialization' => 'إدارة الأعمال',
            'status' => 'active'
        ],
        [
            'title' => 'تعلم اللغة الإنجليزية للمبتدئين',
            'description' => 'كورس شامل لتعلم اللغة الإنجليزية من الأساسيات',
            'price' => 149.00,
            'level' => 'beginner',
            'specialization' => 'اللغات',
            'status' => 'active'
        ]
    ];
    
    // الحصول على مدرب افتراضي (أول مدرب في النظام)
    $instructor_stmt = $conn->query("SELECT id FROM users WHERE role = 'instructor' LIMIT 1");
    $instructor = $instructor_stmt->fetch();
    
    if (!$instructor) {
        // إنشاء مدرب افتراضي إذا لم يوجد
        $stmt = $conn->prepare("INSERT INTO users (name, email, username, password, role, status) VALUES (?, ?, ?, ?, 'instructor', 'active')");
        $stmt->execute([
            'المدرب الافتراضي',
            '<EMAIL>',
            'instructor1',
            password_hash('12345678', PASSWORD_DEFAULT)
        ]);
        $instructor_id = $conn->lastInsertId();
        echo "<p>✅ تم إنشاء مدرب افتراضي</p>";
    } else {
        $instructor_id = $instructor['id'];
        echo "<p>✅ تم العثور على مدرب موجود</p>";
    }
    
    // إنشاء الكورسات
    foreach ($sample_courses as $course) {
        // التحقق من عدم وجود الكورس
        $stmt = $conn->prepare("SELECT id FROM courses WHERE title = ?");
        $stmt->execute([$course['title']]);
        
        if ($stmt->rowCount() == 0) {
            $stmt = $conn->prepare("
                INSERT INTO courses (title, description, instructor_id, price, level, specialization, status, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
            ");
            $stmt->execute([
                $course['title'],
                $course['description'],
                $instructor_id,
                $course['price'],
                $course['level'],
                $course['specialization'],
                $course['status']
            ]);
            
            $course_id = $conn->lastInsertId();
            echo "<p>✅ تم إنشاء كورس: " . htmlspecialchars($course['title']) . "</p>";
            
            // ربط الكورس بالتصنيف المناسب
            $category_mapping = [
                'البرمجة' => 'programming',
                'التصميم' => 'design',
                'التسويق' => 'marketing',
                'إدارة الأعمال' => 'business',
                'اللغات' => 'languages'
            ];
            
            $category_slug = $category_mapping[$course['specialization']] ?? null;
            if ($category_slug) {
                $cat_stmt = $conn->prepare("SELECT id FROM categories WHERE slug = ?");
                $cat_stmt->execute([$category_slug]);
                $category = $cat_stmt->fetch();
                
                if ($category) {
                    // ربط الكورس بالتصنيف
                    $link_stmt = $conn->prepare("INSERT IGNORE INTO course_categories (course_id, category_id) VALUES (?, ?)");
                    $link_stmt->execute([$course_id, $category['id']]);
                    echo "<p>🔗 تم ربط الكورس بتصنيف: " . $course['specialization'] . "</p>";
                }
            }
            
        } else {
            echo "<p>⚠️ الكورس موجود مسبقاً: " . htmlspecialchars($course['title']) . "</p>";
        }
    }
    
    // إنشاء بعض الجلسات للكورسات
    $stmt = $conn->query("SELECT id, title FROM courses LIMIT 3");
    $courses = $stmt->fetchAll();
    
    foreach ($courses as $course) {
        // التحقق من وجود جلسات للكورس
        $session_check = $conn->prepare("SELECT COUNT(*) FROM sessions WHERE course_id = ?");
        $session_check->execute([$course['id']]);
        
        if ($session_check->fetchColumn() == 0) {
            // إنشاء 3 جلسات لكل كورس
            for ($i = 1; $i <= 3; $i++) {
                $session_stmt = $conn->prepare("
                    INSERT INTO sessions (course_id, title, description, session_date, start_time, end_time, status) 
                    VALUES (?, ?, ?, ?, ?, ?, 'scheduled')
                ");
                $session_stmt->execute([
                    $course['id'],
                    "الجلسة $i - " . $course['title'],
                    "وصف الجلسة رقم $i للكورس",
                    date('Y-m-d', strtotime("+$i week")),
                    '19:00:00',
                    '21:00:00'
                ]);
            }
            echo "<p>📅 تم إنشاء 3 جلسات للكورس: " . htmlspecialchars($course['title']) . "</p>";
        }
    }
    
    // عرض إحصائيات
    echo "<h3>الإحصائيات الحالية:</h3>";
    
    $stats = [
        'categories' => $conn->query("SELECT COUNT(*) FROM categories")->fetchColumn(),
        'courses' => $conn->query("SELECT COUNT(*) FROM courses")->fetchColumn(),
        'sessions' => $conn->query("SELECT COUNT(*) FROM sessions")->fetchColumn(),
        'course_categories' => $conn->query("SELECT COUNT(*) FROM course_categories")->fetchColumn(),
        'instructors' => $conn->query("SELECT COUNT(*) FROM users WHERE role = 'instructor'")->fetchColumn()
    ];
    
    echo "<ul>";
    echo "<li>التصنيفات: " . $stats['categories'] . "</li>";
    echo "<li>الكورسات: " . $stats['courses'] . "</li>";
    echo "<li>الجلسات: " . $stats['sessions'] . "</li>";
    echo "<li>ارتباطات التصنيفات: " . $stats['course_categories'] . "</li>";
    echo "<li>المدربين: " . $stats['instructors'] . "</li>";
    echo "</ul>";
    
    echo "<h3>✅ تم إنشاء البيانات التجريبية بنجاح!</h3>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
}
?>

<p><a href="manage-categories.php">عرض إدارة التصنيفات</a></p>
<p><a href="manage-courses.php">عرض إدارة الكورسات</a></p>
<p><a href="dashboard.php">لوحة التحكم</a></p>
