/* ملف التحسينات المتقدمة لواجهة المدرب */

:root {
    /* الألوان الأساسية */
    --primary-gradient: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    --secondary-gradient: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    --success-gradient: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    --warning-gradient: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
    --danger-gradient: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
    --info-gradient: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
    
    /* الظلال */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* الانتقالات */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
    
    /* نصف القطر */
    --radius-sm: 0.375rem;
    --radius: 0.5rem;
    --radius-md: 0.75rem;
    --radius-lg: 1rem;
    --radius-xl: 1.5rem;
}

/* تحسينات عامة */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--gray-800);
    background-color: #f8fafc;
    overflow-x: hidden;
}

/* تحسينات البطاقات */
.card-enhanced {
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow);
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: all var(--transition-normal);
    overflow: hidden;
    position: relative;
}

.card-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
    transform: scaleX(0);
    transition: transform var(--transition-normal);
}

.card-enhanced:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.card-enhanced:hover::before {
    transform: scaleX(1);
}

/* تحسينات الأزرار */
.btn-enhanced {
    position: relative;
    overflow: hidden;
    border-radius: var(--radius);
    font-weight: 600;
    transition: all var(--transition-normal);
    border: none;
    cursor: pointer;
}

.btn-enhanced::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.btn-enhanced:active::before {
    width: 300px;
    height: 300px;
}

.btn-primary-enhanced {
    background: var(--primary-gradient);
    color: white;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.btn-primary-enhanced:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

.btn-secondary-enhanced {
    background: var(--secondary-gradient);
    color: white;
    box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
}

.btn-success-enhanced {
    background: var(--success-gradient);
    color: white;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.btn-warning-enhanced {
    background: var(--warning-gradient);
    color: white;
    box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
}

.btn-danger-enhanced {
    background: var(--danger-gradient);
    color: white;
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
}

.btn-info-enhanced {
    background: var(--info-gradient);
    color: white;
    box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3);
}

/* تحسينات النماذج */
.form-control-enhanced {
    border: 2px solid #e2e8f0;
    border-radius: var(--radius);
    padding: 0.75rem 1rem;
    transition: all var(--transition-normal);
    background: white;
    font-size: 1rem;
}

.form-control-enhanced:focus {
    border-color: var(--instructor-primary);
    box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);
    outline: none;
}

.form-control-enhanced:hover {
    border-color: #cbd5e0;
}

.form-label-enhanced {
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: 0.5rem;
    display: block;
}

/* تحسينات الجداول */
.table-enhanced {
    background: white;
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow);
}

.table-enhanced thead th {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border: none;
    font-weight: 600;
    color: var(--gray-700);
    padding: 1rem;
}

.table-enhanced tbody tr {
    transition: background-color var(--transition-fast);
    border: none;
}

.table-enhanced tbody tr:hover {
    background-color: rgba(40, 167, 69, 0.05);
}

.table-enhanced tbody td {
    padding: 1rem;
    border-top: 1px solid #f1f5f9;
}

/* تحسينات الشارات */
.badge-enhanced {
    padding: 0.5rem 1rem;
    border-radius: var(--radius-xl);
    font-weight: 600;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-primary-enhanced {
    background: var(--primary-gradient);
    color: white;
}

.badge-success-enhanced {
    background: var(--success-gradient);
    color: white;
}

.badge-warning-enhanced {
    background: var(--warning-gradient);
    color: white;
}

.badge-danger-enhanced {
    background: var(--danger-gradient);
    color: white;
}

.badge-info-enhanced {
    background: var(--info-gradient);
    color: white;
}

/* تحسينات التنبيهات */
.alert-enhanced {
    border: none;
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    position: relative;
    overflow: hidden;
}

.alert-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
}

.alert-success-enhanced {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.1) 0%, rgba(32, 201, 151, 0.1) 100%);
    color: #155724;
}

.alert-success-enhanced::before {
    background: var(--success-gradient);
}

.alert-warning-enhanced {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(253, 126, 20, 0.1) 100%);
    color: #856404;
}

.alert-warning-enhanced::before {
    background: var(--warning-gradient);
}

.alert-danger-enhanced {
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.1) 0%, rgba(232, 62, 140, 0.1) 100%);
    color: #721c24;
}

.alert-danger-enhanced::before {
    background: var(--danger-gradient);
}

.alert-info-enhanced {
    background: linear-gradient(135deg, rgba(23, 162, 184, 0.1) 0%, rgba(111, 66, 193, 0.1) 100%);
    color: #0c5460;
}

.alert-info-enhanced::before {
    background: var(--info-gradient);
}

/* تحسينات شريط التقدم */
.progress-enhanced {
    height: 1rem;
    background: #e2e8f0;
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

.progress-bar-enhanced {
    background: var(--primary-gradient);
    transition: width 1s ease-in-out;
    position: relative;
    overflow: hidden;
}

.progress-bar-enhanced::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: linear-gradient(
        45deg,
        rgba(255, 255, 255, 0.2) 25%,
        transparent 25%,
        transparent 50%,
        rgba(255, 255, 255, 0.2) 50%,
        rgba(255, 255, 255, 0.2) 75%,
        transparent 75%,
        transparent
    );
    background-size: 1rem 1rem;
    animation: progress-stripes 1s linear infinite;
}

@keyframes progress-stripes {
    0% { background-position: 0 0; }
    100% { background-position: 1rem 0; }
}

/* تحسينات النوافذ المنبثقة */
.modal-enhanced .modal-content {
    border: none;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    overflow: hidden;
}

.modal-enhanced .modal-header {
    background: var(--primary-gradient);
    color: white;
    border: none;
    padding: 1.5rem;
}

.modal-enhanced .modal-body {
    padding: 2rem;
}

.modal-enhanced .modal-footer {
    border: none;
    padding: 1.5rem 2rem;
    background: #f8fafc;
}

/* تحسينات القوائم المنسدلة */
.dropdown-menu-enhanced {
    border: none;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    padding: 0.5rem;
    margin-top: 0.5rem;
}

.dropdown-item-enhanced {
    border-radius: var(--radius);
    padding: 0.75rem 1rem;
    transition: all var(--transition-fast);
    margin-bottom: 0.25rem;
}

.dropdown-item-enhanced:hover {
    background: var(--primary-gradient);
    color: white;
    transform: translateX(5px);
}

/* تحسينات التبويبات */
.nav-tabs-enhanced {
    border: none;
    background: #f8fafc;
    border-radius: var(--radius-lg);
    padding: 0.5rem;
}

.nav-tabs-enhanced .nav-link {
    border: none;
    border-radius: var(--radius);
    color: var(--gray-600);
    font-weight: 600;
    transition: all var(--transition-normal);
}

.nav-tabs-enhanced .nav-link.active {
    background: white;
    color: var(--instructor-primary);
    box-shadow: var(--shadow);
}

/* تحسينات الأيقونات */
.icon-enhanced {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    border-radius: var(--radius);
    transition: all var(--transition-normal);
}

.icon-primary-enhanced {
    background: rgba(40, 167, 69, 0.1);
    color: var(--instructor-primary);
}

.icon-success-enhanced {
    background: rgba(40, 167, 69, 0.1);
    color: #28a745;
}

.icon-warning-enhanced {
    background: rgba(255, 193, 7, 0.1);
    color: #ffc107;
}

.icon-danger-enhanced {
    background: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

.icon-info-enhanced {
    background: rgba(23, 162, 184, 0.1);
    color: #17a2b8;
}

/* تحسينات الحاويات */
.container-enhanced {
    background: white;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow);
    padding: 2rem;
    margin-bottom: 2rem;
}

/* تحسينات النصوص */
.text-gradient {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}

/* تحسينات الحركة */
.animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

.animate-slide-up {
    animation: slideUp 0.5s ease-in-out;
}

.animate-scale-in {
    animation: scaleIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from { 
        opacity: 0;
        transform: translateY(20px);
    }
    to { 
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes scaleIn {
    from { 
        opacity: 0;
        transform: scale(0.9);
    }
    to { 
        opacity: 1;
        transform: scale(1);
    }
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
    .container-enhanced {
        padding: 1rem;
        margin-bottom: 1rem;
    }
    
    .btn-enhanced {
        width: 100%;
        margin-bottom: 0.5rem;
    }
    
    .table-enhanced {
        font-size: 0.875rem;
    }
}
