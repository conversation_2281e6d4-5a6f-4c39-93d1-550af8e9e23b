<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من صلاحيات المدير
if (!isLoggedIn() || !isAdmin()) {
    header('Location: ../login.php');
    exit();
}

echo "<h2>إعداد نظام النسخ الاحتياطي</h2>";

try {
    // إنشاء جدول النسخ الاحتياطية
    $conn->exec("CREATE TABLE IF NOT EXISTS backups (
        id INT AUTO_INCREMENT PRIMARY KEY,
        filename VARCHAR(255) NOT NULL,
        file_path VARCHAR(500) NOT NULL,
        backup_type ENUM('full', 'structure', 'data', 'partial') DEFAULT 'full',
        status ENUM('in_progress', 'completed', 'failed', 'cancelled') DEFAULT 'in_progress',
        tables_included JSON NULL,
        compression_type ENUM('none', 'gzip', 'zip') DEFAULT 'gzip',
        file_size BIGINT NULL,
        progress_percentage DECIMAL(5,2) DEFAULT 0.00,
        error_message TEXT NULL,
        created_by INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        completed_at TIMESTAMP NULL,
        INDEX idx_status (status),
        INDEX idx_created_by (created_by),
        INDEX idx_created_at (created_at),
        FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    
    echo "<p>✅ تم إنشاء جدول backups</p>";
    
    // إنشاء مجلد النسخ الاحتياطية
    $backup_dir = '../backups/';
    if (!is_dir($backup_dir)) {
        if (mkdir($backup_dir, 0755, true)) {
            echo "<p>✅ تم إنشاء مجلد النسخ الاحتياطية: $backup_dir</p>";
        } else {
            echo "<p>❌ فشل في إنشاء مجلد النسخ الاحتياطية</p>";
        }
    } else {
        echo "<p>⚠️ مجلد النسخ الاحتياطية موجود مسبقاً</p>";
    }
    
    // إنشاء ملف .htaccess لحماية مجلد النسخ الاحتياطية
    $htaccess_content = "Order Deny,Allow\nDeny from all\n";
    file_put_contents($backup_dir . '.htaccess', $htaccess_content);
    echo "<p>✅ تم إنشاء ملف الحماية .htaccess</p>";
    
    // إنشاء ملف index.php فارغ لمنع عرض محتويات المجلد
    file_put_contents($backup_dir . 'index.php', '<?php header("Location: ../admin/"); exit; ?>');
    echo "<p>✅ تم إنشاء ملف الحماية index.php</p>";
    
    // إنشاء نسخة احتياطية تجريبية
    if (isset($_GET['create_sample'])) {
        echo "<h3>إنشاء نسخة احتياطية تجريبية:</h3>";
        
        $timestamp = date('Y-m-d_H-i-s');
        $filename = "sample_backup_$timestamp.sql.gz";
        $file_path = $backup_dir . $filename;
        
        // محتوى تجريبي للنسخة الاحتياطية
        $sample_content = "-- نسخة احتياطية تجريبية\n";
        $sample_content .= "-- تم إنشاؤها في: " . date('Y-m-d H:i:s') . "\n";
        $sample_content .= "-- هذه نسخة تجريبية للاختبار\n\n";
        $sample_content .= "SET FOREIGN_KEY_CHECKS = 0;\n";
        $sample_content .= "-- محتوى تجريبي\n";
        $sample_content .= "SET FOREIGN_KEY_CHECKS = 1;\n";
        
        // ضغط المحتوى وحفظه
        $gz = gzopen($file_path, 'w9');
        gzwrite($gz, $sample_content);
        gzclose($gz);
        
        // تسجيل النسخة في قاعدة البيانات
        $stmt = $conn->prepare("
            INSERT INTO backups (filename, file_path, backup_type, status, compression_type, file_size, progress_percentage, created_by, completed_at)
            VALUES (?, ?, 'full', 'completed', 'gzip', ?, 100.00, ?, NOW())
        ");
        $stmt->execute([
            $filename,
            $file_path,
            filesize($file_path),
            $_SESSION['user_id']
        ]);
        
        echo "<p>✅ تم إنشاء نسخة احتياطية تجريبية: $filename</p>";
        echo "<p>📁 حجم الملف: " . formatFileSize(filesize($file_path)) . "</p>";
    }
    
    // عرض إحصائيات النسخ الاحتياطية
    echo "<h3>إحصائيات النسخ الاحتياطية:</h3>";
    
    $stats = [
        'total' => $conn->query("SELECT COUNT(*) FROM backups")->fetchColumn(),
        'completed' => $conn->query("SELECT COUNT(*) FROM backups WHERE status = 'completed'")->fetchColumn(),
        'failed' => $conn->query("SELECT COUNT(*) FROM backups WHERE status = 'failed'")->fetchColumn(),
        'total_size' => $conn->query("SELECT COALESCE(SUM(file_size), 0) FROM backups WHERE status = 'completed'")->fetchColumn()
    ];
    
    echo "<ul>";
    echo "<li>إجمالي النسخ: " . $stats['total'] . "</li>";
    echo "<li>النسخ المكتملة: " . $stats['completed'] . "</li>";
    echo "<li>النسخ الفاشلة: " . $stats['failed'] . "</li>";
    echo "<li>الحجم الإجمالي: " . formatFileSize($stats['total_size']) . "</li>";
    echo "</ul>";
    
    // عرض بنية جدول النسخ الاحتياطية
    echo "<h3>بنية جدول backups:</h3>";
    $stmt = $conn->query("DESCRIBE backups");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // اختبار دوال النسخ الاحتياطي
    echo "<h3>اختبار دوال النسخ الاحتياطي:</h3>";
    
    // اختبار دالة تنسيق حجم الملف
    echo "<p>اختبار تنسيق الأحجام:</p>";
    echo "<ul>";
    echo "<li>1024 بايت = " . formatFileSize(1024) . "</li>";
    echo "<li>1048576 بايت = " . formatFileSize(1048576) . "</li>";
    echo "<li>1073741824 بايت = " . formatFileSize(1073741824) . "</li>";
    echo "</ul>";
    
    // اختبار قراءة الجداول
    try {
        $tables = $conn->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
        echo "<p>✅ تم جلب " . count($tables) . " جدول من قاعدة البيانات</p>";
        echo "<p>الجداول الموجودة: " . implode(', ', $tables) . "</p>";
    } catch (Exception $e) {
        echo "<p>❌ خطأ في جلب الجداول: " . $e->getMessage() . "</p>";
    }
    
    echo "<h3>✅ تم إعداد نظام النسخ الاحتياطي بنجاح!</h3>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
}

// دالة تنسيق حجم الملف
function formatFileSize($bytes) {
    if ($bytes == 0) return '0 B';
    
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    $factor = floor((strlen($bytes) - 1) / 3);
    
    return sprintf("%.2f %s", $bytes / pow(1024, $factor), $units[$factor]);
}
?>

<p><a href="?create_sample=1">إنشاء نسخة احتياطية تجريبية</a></p>
<p><a href="backup.php">الذهاب إلى صفحة النسخ الاحتياطي</a></p>
<p><a href="dashboard.php">لوحة التحكم</a></p>
