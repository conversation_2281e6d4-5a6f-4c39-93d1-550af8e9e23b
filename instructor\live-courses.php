<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات المدرب
if (!isLoggedIn() || !isInstructor()) {
    header('Location: ../login.php');
    exit;
}

$pageTitle = 'الكورسات المباشرة';
$breadcrumbs = [
    ['title' => 'الكورسات المباشرة']
];

// جلب الكورسات المباشرة
try {
    $stmt = $conn->prepare("
        SELECT 
            c.*,
            COUNT(DISTINCT ce.student_id) as enrolled_students,
            COUNT(DISTINCT s.id) as total_sessions,
            COUNT(DISTINCT CASE WHEN s.status = 'scheduled' AND s.session_date >= CURDATE() THEN s.id END) as upcoming_sessions,
            COUNT(DISTINCT CASE WHEN s.status = 'live' THEN s.id END) as live_sessions,
            MAX(s.session_date) as last_session_date,
            MIN(CASE WHEN s.session_date >= CURDATE() AND s.status = 'scheduled' THEN s.session_date END) as next_session_date
        FROM courses c
        LEFT JOIN course_enrollments ce ON c.id = ce.course_id AND ce.status = 'active'
        LEFT JOIN sessions s ON c.id = s.course_id
        WHERE c.instructor_id = ? AND c.status = 'active'
        GROUP BY c.id
        HAVING total_sessions > 0
        ORDER BY next_session_date ASC, c.created_at DESC
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $live_courses = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // إحصائيات سريعة
    $stmt = $conn->prepare("
        SELECT 
            COUNT(DISTINCT c.id) as total_live_courses,
            COUNT(DISTINCT CASE WHEN s.status = 'live' THEN c.id END) as currently_live,
            COUNT(DISTINCT CASE WHEN s.session_date = CURDATE() AND s.status = 'scheduled' THEN c.id END) as today_courses,
            COUNT(DISTINCT ce.student_id) as total_students
        FROM courses c
        LEFT JOIN sessions s ON c.id = s.course_id
        LEFT JOIN course_enrollments ce ON c.id = ce.course_id AND ce.status = 'active'
        WHERE c.instructor_id = ? AND c.status = 'active'
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
} catch (PDOException $e) {
    $error_message = 'حدث خطأ أثناء جلب البيانات';
    $live_courses = [];
    $stats = ['total_live_courses' => 0, 'currently_live' => 0, 'today_courses' => 0, 'total_students' => 0];
}

include 'includes/header.php';
?>

<!-- رأس الصفحة -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="h3 mb-2">
            <i class="fas fa-video text-danger me-2"></i>
            الكورسات المباشرة
        </h2>
        <p class="text-muted mb-0">إدارة ومتابعة الكورسات التي تحتوي على جلسات مباشرة</p>
    </div>
    <div class="d-flex gap-2">
        <a href="create-session.php" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i>جلسة جديدة
        </a>
        <a href="recorded-courses.php" class="btn btn-outline-secondary">
            <i class="fas fa-play-circle me-1"></i>الكورسات المسجلة
        </a>
    </div>
</div>

<!-- بطاقات الإحصائيات -->
<div class="row g-4 mb-4">
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-primary bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-graduation-cap text-primary fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h5 class="mb-1"><?php echo $stats['total_live_courses']; ?></h5>
                        <p class="text-muted mb-0">كورسات مباشرة</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-danger bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-broadcast-tower text-danger fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h5 class="mb-1"><?php echo $stats['currently_live']; ?></h5>
                        <p class="text-muted mb-0">مباشر الآن</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-warning bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-calendar-day text-warning fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h5 class="mb-1"><?php echo $stats['today_courses']; ?></h5>
                        <p class="text-muted mb-0">جلسات اليوم</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-success bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-users text-success fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h5 class="mb-1"><?php echo $stats['total_students']; ?></h5>
                        <p class="text-muted mb-0">إجمالي الطلاب</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- فلاتر البحث -->
<div class="card border-0 shadow-sm mb-4">
    <div class="card-body">
        <div class="row g-3 align-items-end">
            <div class="col-md-4">
                <label for="searchCourse" class="form-label">البحث في الكورسات</label>
                <input type="text" id="searchCourse" class="form-control" placeholder="اسم الكورس...">
            </div>
            <div class="col-md-3">
                <label for="statusFilter" class="form-label">حالة الجلسات</label>
                <select id="statusFilter" class="form-select">
                    <option value="">جميع الحالات</option>
                    <option value="live">مباشر الآن</option>
                    <option value="upcoming">قادمة</option>
                    <option value="completed">مكتملة</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="dateFilter" class="form-label">التاريخ</label>
                <select id="dateFilter" class="form-select">
                    <option value="">جميع التواريخ</option>
                    <option value="today">اليوم</option>
                    <option value="tomorrow">غداً</option>
                    <option value="this_week">هذا الأسبوع</option>
                    <option value="next_week">الأسبوع القادم</option>
                </select>
            </div>
            <div class="col-md-2">
                <button class="btn btn-outline-primary w-100" onclick="resetFilters()">
                    <i class="fas fa-undo me-1"></i>إعادة تعيين
                </button>
            </div>
        </div>
    </div>
</div>

<!-- قائمة الكورسات المباشرة -->
<?php if (empty($live_courses)): ?>
<div class="card border-0 shadow-sm">
    <div class="card-body text-center py-5">
        <i class="fas fa-video text-muted" style="font-size: 4rem;"></i>
        <h4 class="mt-4 text-muted">لا توجد كورسات مباشرة</h4>
        <p class="text-muted mb-4">ابدأ بإنشاء جلسات مباشرة لكورساتك</p>
        <a href="create-session.php" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>إنشاء جلسة مباشرة
        </a>
    </div>
</div>
<?php else: ?>
<div class="row g-4" id="coursesContainer">
    <?php foreach ($live_courses as $course): ?>
    <div class="col-lg-6 course-card" 
         data-course-name="<?php echo strtolower($course['title']); ?>"
         data-next-session="<?php echo $course['next_session_date']; ?>"
         data-live-sessions="<?php echo $course['live_sessions']; ?>">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-white border-bottom d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><?php echo htmlspecialchars($course['title']); ?></h5>
                <div class="d-flex gap-1">
                    <?php if ($course['live_sessions'] > 0): ?>
                    <span class="badge bg-danger">
                        <i class="fas fa-circle me-1"></i>مباشر
                    </span>
                    <?php elseif ($course['next_session_date'] == date('Y-m-d')): ?>
                    <span class="badge bg-warning">
                        <i class="fas fa-clock me-1"></i>اليوم
                    </span>
                    <?php elseif ($course['upcoming_sessions'] > 0): ?>
                    <span class="badge bg-info">
                        <i class="fas fa-calendar me-1"></i>قادم
                    </span>
                    <?php else: ?>
                    <span class="badge bg-secondary">
                        <i class="fas fa-pause me-1"></i>متوقف
                    </span>
                    <?php endif; ?>
                </div>
            </div>
            <div class="card-body">
                <?php if ($course['description']): ?>
                <p class="text-muted mb-3"><?php echo mb_substr(htmlspecialchars($course['description']), 0, 100) . '...'; ?></p>
                <?php endif; ?>
                
                <!-- إحصائيات الكورس -->
                <div class="row g-3 mb-3">
                    <div class="col-6">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-users text-primary me-2"></i>
                            <span class="small"><?php echo $course['enrolled_students']; ?> طالب</span>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-video text-info me-2"></i>
                            <span class="small"><?php echo $course['total_sessions']; ?> جلسة</span>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-calendar-check text-success me-2"></i>
                            <span class="small"><?php echo $course['upcoming_sessions']; ?> قادمة</span>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-broadcast-tower text-danger me-2"></i>
                            <span class="small"><?php echo $course['live_sessions']; ?> مباشر</span>
                        </div>
                    </div>
                </div>
                
                <!-- معلومات الجلسة القادمة -->
                <?php if ($course['next_session_date']): ?>
                <div class="alert alert-light border-start border-primary border-3 mb-3">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-calendar-alt text-primary me-2"></i>
                        <div>
                            <small class="text-muted d-block">الجلسة القادمة:</small>
                            <strong><?php echo date('Y-m-d', strtotime($course['next_session_date'])); ?></strong>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
                
                <!-- آخر جلسة -->
                <?php if ($course['last_session_date']): ?>
                <div class="text-muted small mb-3">
                    <i class="fas fa-history me-1"></i>
                    آخر جلسة: <?php echo date('Y-m-d', strtotime($course['last_session_date'])); ?>
                </div>
                <?php endif; ?>
            </div>
            <div class="card-footer bg-white border-top">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="btn-group" role="group">
                        <a href="course-sessions.php?course_id=<?php echo $course['id']; ?>" 
                           class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-list me-1"></i>الجلسات
                        </a>
                        <a href="course-students.php?course_id=<?php echo $course['id']; ?>" 
                           class="btn btn-sm btn-outline-success">
                            <i class="fas fa-users me-1"></i>الطلاب
                        </a>
                    </div>
                    <div class="btn-group" role="group">
                        <?php if ($course['live_sessions'] > 0): ?>
                        <a href="join-live-session.php?course_id=<?php echo $course['id']; ?>" 
                           class="btn btn-sm btn-danger">
                            <i class="fas fa-play me-1"></i>انضمام
                        </a>
                        <?php else: ?>
                        <a href="create-session.php?course_id=<?php echo $course['id']; ?>" 
                           class="btn btn-sm btn-primary">
                            <i class="fas fa-plus me-1"></i>جلسة جديدة
                        </a>
                        <?php endif; ?>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                    data-bs-toggle="dropdown">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="course-details.php?id=<?php echo $course['id']; ?>">
                                    <i class="fas fa-eye me-2"></i>عرض التفاصيل
                                </a></li>
                                <li><a class="dropdown-item" href="course-analytics.php?id=<?php echo $course['id']; ?>">
                                    <i class="fas fa-chart-bar me-2"></i>الإحصائيات
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="edit-course.php?id=<?php echo $course['id']; ?>">
                                    <i class="fas fa-edit me-2"></i>تعديل الكورس
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endforeach; ?>
</div>
<?php endif; ?>

<!-- إحصائيات سريعة -->
<div class="row mt-5">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-light border-bottom">
                <h6 class="mb-0">
                    <i class="fas fa-chart-line text-primary me-2"></i>
                    إحصائيات سريعة
                </h6>
            </div>
            <div class="card-body">
                <div class="row g-4">
                    <div class="col-md-3 text-center">
                        <div class="border-end">
                            <h4 class="text-primary mb-1"><?php echo array_sum(array_column($live_courses, 'total_sessions')); ?></h4>
                            <small class="text-muted">إجمالي الجلسات</small>
                        </div>
                    </div>
                    <div class="col-md-3 text-center">
                        <div class="border-end">
                            <h4 class="text-success mb-1"><?php echo array_sum(array_column($live_courses, 'enrolled_students')); ?></h4>
                            <small class="text-muted">إجمالي المشتركين</small>
                        </div>
                    </div>
                    <div class="col-md-3 text-center">
                        <div class="border-end">
                            <h4 class="text-warning mb-1"><?php echo array_sum(array_column($live_courses, 'upcoming_sessions')); ?></h4>
                            <small class="text-muted">الجلسات القادمة</small>
                        </div>
                    </div>
                    <div class="col-md-3 text-center">
                        <h4 class="text-danger mb-1"><?php echo array_sum(array_column($live_courses, 'live_sessions')); ?></h4>
                        <small class="text-muted">الجلسات المباشرة</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// البحث في الكورسات
document.getElementById('searchCourse').addEventListener('input', function() {
    filterCourses();
});

// فلترة حسب الحالة
document.getElementById('statusFilter').addEventListener('change', function() {
    filterCourses();
});

// فلترة حسب التاريخ
document.getElementById('dateFilter').addEventListener('change', function() {
    filterCourses();
});

// دالة الفلترة
function filterCourses() {
    const searchTerm = document.getElementById('searchCourse').value.toLowerCase();
    const statusFilter = document.getElementById('statusFilter').value;
    const dateFilter = document.getElementById('dateFilter').value;
    const courseCards = document.querySelectorAll('.course-card');
    
    courseCards.forEach(card => {
        let showCard = true;
        
        // فلترة النص
        if (searchTerm && !card.dataset.courseName.includes(searchTerm)) {
            showCard = false;
        }
        
        // فلترة الحالة
        if (statusFilter) {
            const liveSessions = parseInt(card.dataset.liveSessions);
            const nextSession = card.dataset.nextSession;
            const today = new Date().toISOString().split('T')[0];
            
            switch (statusFilter) {
                case 'live':
                    if (liveSessions === 0) showCard = false;
                    break;
                case 'upcoming':
                    if (!nextSession || nextSession < today) showCard = false;
                    break;
                case 'completed':
                    if (nextSession && nextSession >= today) showCard = false;
                    break;
            }
        }
        
        // فلترة التاريخ
        if (dateFilter && dateFilter !== '') {
            const nextSession = card.dataset.nextSession;
            const today = new Date();
            const tomorrow = new Date(today);
            tomorrow.setDate(tomorrow.getDate() + 1);
            
            switch (dateFilter) {
                case 'today':
                    if (nextSession !== today.toISOString().split('T')[0]) showCard = false;
                    break;
                case 'tomorrow':
                    if (nextSession !== tomorrow.toISOString().split('T')[0]) showCard = false;
                    break;
                // يمكن إضافة المزيد من الفلاتر
            }
        }
        
        card.style.display = showCard ? 'block' : 'none';
    });
}

// إعادة تعيين الفلاتر
function resetFilters() {
    document.getElementById('searchCourse').value = '';
    document.getElementById('statusFilter').value = '';
    document.getElementById('dateFilter').value = '';
    
    document.querySelectorAll('.course-card').forEach(card => {
        card.style.display = 'block';
    });
}

// تحديث الصفحة كل دقيقة للحصول على أحدث المعلومات
setInterval(function() {
    // يمكن إضافة AJAX لتحديث البيانات
    console.log('تحديث البيانات...');
}, 60000);

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // إضافة تأثيرات بصرية للبطاقات
    const cards = document.querySelectorAll('.card');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
            this.style.transition = 'transform 0.3s ease';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
});
</script>

<style>
.course-card {
    transition: all 0.3s ease;
}

.course-card .card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.course-card .card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15) !important;
}

.border-start {
    border-left: 3px solid var(--bs-primary) !important;
}

.badge {
    font-size: 0.75rem;
}

.btn-group .btn {
    border-radius: 0.375rem;
}

.btn-group .btn:not(:last-child) {
    margin-right: 0.25rem;
}

@media (max-width: 768px) {
    .btn-group {
        display: flex;
        flex-direction: column;
        width: 100%;
    }
    
    .btn-group .btn {
        margin-right: 0;
        margin-bottom: 0.25rem;
    }
}
</style>

<?php include 'includes/footer.php'; ?>
