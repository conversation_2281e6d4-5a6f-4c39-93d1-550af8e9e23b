<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات المدرب
if (!isLoggedIn() || !isInstructor()) {
    header('Location: ../login.php');
    exit;
}

$pageTitle = 'الأرباح والمدفوعات';
$breadcrumbs = [
    ['title' => 'الأرباح والمدفوعات']
];

// فلاتر التاريخ
$start_date = $_GET['start_date'] ?? date('Y-m-01'); // بداية الشهر الحالي
$end_date = $_GET['end_date'] ?? date('Y-m-t'); // نهاية الشهر الحالي

// جلب إحصائيات الأرباح
try {
    // إجمالي الأرباح
    $stmt = $conn->prepare("
        SELECT 
            SUM(p.amount) as total_revenue,
            SUM(p.amount * 0.7) as instructor_earnings,
            SUM(p.amount * 0.3) as platform_commission,
            COUNT(DISTINCT p.id) as total_sales,
            COUNT(DISTINCT p.user_id) as unique_customers
        FROM payments p
        INNER JOIN enrollments e ON p.enrollment_id = e.id
        INNER JOIN courses c ON e.course_id = c.id
        WHERE c.instructor_id = ? AND p.status = 'completed'
        AND p.created_at BETWEEN ? AND ?
    ");
    $stmt->execute([$_SESSION['user_id'], $start_date . ' 00:00:00', $end_date . ' 23:59:59']);
    $earnings_stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // الأرباح الشهرية (آخر 12 شهر)
    $stmt = $conn->prepare("
        SELECT 
            DATE_FORMAT(p.created_at, '%Y-%m') as month,
            SUM(p.amount * 0.7) as earnings,
            COUNT(p.id) as sales_count
        FROM payments p
        INNER JOIN enrollments e ON p.enrollment_id = e.id
        INNER JOIN courses c ON e.course_id = c.id
        WHERE c.instructor_id = ? AND p.status = 'completed'
        AND p.created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
        GROUP BY DATE_FORMAT(p.created_at, '%Y-%m')
        ORDER BY month DESC
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $monthly_earnings = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // أفضل الكورسات ربحاً
    $stmt = $conn->prepare("
        SELECT 
            c.id,
            c.title,
            c.price,
            COUNT(p.id) as sales_count,
            SUM(p.amount * 0.7) as total_earnings
        FROM courses c
        INNER JOIN enrollments e ON c.id = e.course_id
        INNER JOIN payments p ON e.id = p.enrollment_id
        WHERE c.instructor_id = ? AND p.status = 'completed'
        AND p.created_at BETWEEN ? AND ?
        GROUP BY c.id
        ORDER BY total_earnings DESC
        LIMIT 10
    ");
    $stmt->execute([$_SESSION['user_id'], $start_date . ' 00:00:00', $end_date . ' 23:59:59']);
    $top_courses = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // المعاملات الأخيرة
    $stmt = $conn->prepare("
        SELECT 
            p.*,
            u.name as student_name,
            c.title as course_title,
            (p.amount * 0.7) as instructor_share
        FROM payments p
        INNER JOIN enrollments e ON p.enrollment_id = e.id
        INNER JOIN courses c ON e.course_id = c.id
        INNER JOIN users u ON p.user_id = u.id
        WHERE c.instructor_id = ? AND p.status = 'completed'
        ORDER BY p.created_at DESC
        LIMIT 20
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $recent_transactions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // معلومات السحب
    $stmt = $conn->prepare("
        SELECT 
            SUM(CASE WHEN status = 'pending' THEN amount ELSE 0 END) as pending_withdrawals,
            SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END) as completed_withdrawals,
            MAX(created_at) as last_withdrawal_date
        FROM instructor_withdrawals
        WHERE instructor_id = ?
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $withdrawal_info = $stmt->fetch(PDO::FETCH_ASSOC);
    
} catch (PDOException $e) {
    $error_message = 'حدث خطأ أثناء جلب البيانات';
    $earnings_stats = ['total_revenue' => 0, 'instructor_earnings' => 0, 'platform_commission' => 0, 'total_sales' => 0, 'unique_customers' => 0];
    $monthly_earnings = [];
    $top_courses = [];
    $recent_transactions = [];
    $withdrawal_info = ['pending_withdrawals' => 0, 'completed_withdrawals' => 0, 'last_withdrawal_date' => null];
}

include 'includes/header.php';
?>

<!-- رأس الصفحة -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="h3 mb-2">
            <i class="fas fa-dollar-sign text-success me-2"></i>
            الأرباح والمدفوعات
        </h2>
        <p class="text-muted mb-0">متابعة الأرباح والمبيعات وطلبات السحب</p>
    </div>
    <div class="d-flex gap-2">
        <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#withdrawalModal">
            <i class="fas fa-money-bill-wave me-1"></i>طلب سحب
        </button>
        <button class="btn btn-outline-primary" onclick="exportEarnings()">
            <i class="fas fa-download me-1"></i>تصدير التقرير
        </button>
    </div>
</div>

<!-- فلاتر التاريخ -->
<div class="card border-0 shadow-sm mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3 align-items-end">
            <div class="col-md-4">
                <label for="start_date" class="form-label">من تاريخ</label>
                <input type="date" name="start_date" id="start_date" class="form-control" 
                       value="<?php echo $start_date; ?>">
            </div>
            <div class="col-md-4">
                <label for="end_date" class="form-label">إلى تاريخ</label>
                <input type="date" name="end_date" id="end_date" class="form-control" 
                       value="<?php echo $end_date; ?>">
            </div>
            <div class="col-md-4">
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-search me-1"></i>تطبيق الفلتر
                </button>
            </div>
        </form>
    </div>
</div>

<!-- إحصائيات الأرباح -->
<div class="row g-4 mb-4">
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-success bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-dollar-sign text-success fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h5 class="mb-1">$<?php echo number_format($earnings_stats['instructor_earnings'], 2); ?></h5>
                        <p class="text-muted mb-0">أرباحي</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-primary bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-chart-line text-primary fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h5 class="mb-1">$<?php echo number_format($earnings_stats['total_revenue'], 2); ?></h5>
                        <p class="text-muted mb-0">إجمالي المبيعات</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-info bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-shopping-cart text-info fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h5 class="mb-1"><?php echo $earnings_stats['total_sales']; ?></h5>
                        <p class="text-muted mb-0">عدد المبيعات</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-warning bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-users text-warning fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h5 class="mb-1"><?php echo $earnings_stats['unique_customers']; ?></h5>
                        <p class="text-muted mb-0">عملاء فريدون</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- الرسم البياني والجداول -->
<div class="row">
    <div class="col-lg-8">
        <!-- الرسم البياني للأرباح الشهرية -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white border-bottom">
                <h5 class="mb-0">
                    <i class="fas fa-chart-area me-2"></i>
                    الأرباح الشهرية
                </h5>
            </div>
            <div class="card-body">
                <canvas id="earningsChart" height="300"></canvas>
            </div>
        </div>
        
        <!-- أفضل الكورسات ربحاً -->
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-bottom">
                <h5 class="mb-0">
                    <i class="fas fa-trophy me-2"></i>
                    أفضل الكورسات ربحاً
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($top_courses)): ?>
                <p class="text-muted mb-0">لا توجد مبيعات في الفترة المحددة</p>
                <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>الكورس</th>
                                <th>السعر</th>
                                <th>المبيعات</th>
                                <th>الأرباح</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($top_courses as $course): ?>
                            <tr>
                                <td>
                                    <a href="course-details.php?id=<?php echo $course['id']; ?>" class="text-decoration-none">
                                        <?php echo htmlspecialchars($course['title']); ?>
                                    </a>
                                </td>
                                <td>$<?php echo number_format($course['price'], 2); ?></td>
                                <td>
                                    <span class="badge bg-primary"><?php echo $course['sales_count']; ?></span>
                                </td>
                                <td>
                                    <strong class="text-success">$<?php echo number_format($course['total_earnings'], 2); ?></strong>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- معلومات السحب -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white border-bottom">
                <h6 class="mb-0">
                    <i class="fas fa-wallet me-2"></i>
                    معلومات السحب
                </h6>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span class="text-muted">الرصيد المتاح</span>
                    <strong class="text-success">
                        $<?php echo number_format($earnings_stats['instructor_earnings'] - $withdrawal_info['completed_withdrawals'] - $withdrawal_info['pending_withdrawals'], 2); ?>
                    </strong>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span class="text-muted">طلبات معلقة</span>
                    <span class="text-warning">$<?php echo number_format($withdrawal_info['pending_withdrawals'], 2); ?></span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span class="text-muted">إجمالي المسحوب</span>
                    <span class="text-info">$<?php echo number_format($withdrawal_info['completed_withdrawals'], 2); ?></span>
                </div>
                <?php if ($withdrawal_info['last_withdrawal_date']): ?>
                <div class="d-flex justify-content-between align-items-center">
                    <span class="text-muted">آخر سحب</span>
                    <small class="text-muted"><?php echo date('Y-m-d', strtotime($withdrawal_info['last_withdrawal_date'])); ?></small>
                </div>
                <?php endif; ?>
                
                <hr>
                
                <button class="btn btn-success w-100" data-bs-toggle="modal" data-bs-target="#withdrawalModal">
                    <i class="fas fa-money-bill-wave me-1"></i>طلب سحب جديد
                </button>
            </div>
        </div>
        
        <!-- المعاملات الأخيرة -->
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-bottom">
                <h6 class="mb-0">
                    <i class="fas fa-history me-2"></i>
                    المعاملات الأخيرة
                </h6>
            </div>
            <div class="card-body">
                <?php if (empty($recent_transactions)): ?>
                <p class="text-muted mb-0">لا توجد معاملات</p>
                <?php else: ?>
                <div class="list-group list-group-flush">
                    <?php foreach (array_slice($recent_transactions, 0, 5) as $transaction): ?>
                    <div class="list-group-item px-0">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <h6 class="mb-1"><?php echo htmlspecialchars($transaction['student_name']); ?></h6>
                                <small class="text-muted"><?php echo htmlspecialchars($transaction['course_title']); ?></small>
                                <br><small class="text-muted"><?php echo date('Y-m-d H:i', strtotime($transaction['created_at'])); ?></small>
                            </div>
                            <div class="text-end">
                                <strong class="text-success">+$<?php echo number_format($transaction['instructor_share'], 2); ?></strong>
                                <br><small class="text-muted">من $<?php echo number_format($transaction['amount'], 2); ?></small>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                <div class="text-center mt-3">
                    <a href="transactions.php" class="btn btn-sm btn-outline-primary">عرض جميع المعاملات</a>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Modal طلب سحب -->
<div class="modal fade" id="withdrawalModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">طلب سحب جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="process-withdrawal.php">
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        الرصيد المتاح للسحب: <strong>$<?php echo number_format($earnings_stats['instructor_earnings'] - $withdrawal_info['completed_withdrawals'] - $withdrawal_info['pending_withdrawals'], 2); ?></strong>
                    </div>

                    <div class="mb-3">
                        <label for="withdrawal_amount" class="form-label">مبلغ السحب <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text">$</span>
                            <input type="number" name="amount" id="withdrawal_amount" class="form-control"
                                   min="10" step="0.01" required placeholder="0.00">
                        </div>
                        <div class="form-text">الحد الأدنى للسحب: $10</div>
                    </div>

                    <div class="mb-3">
                        <label for="payment_method" class="form-label">طريقة الدفع <span class="text-danger">*</span></label>
                        <select name="payment_method" id="payment_method" class="form-select" required>
                            <option value="">-- اختر طريقة الدفع --</option>
                            <option value="bank_transfer">تحويل بنكي</option>
                            <option value="paypal">PayPal</option>
                            <option value="wise">Wise</option>
                        </select>
                    </div>

                    <div id="bankDetails" style="display: none;">
                        <div class="mb-3">
                            <label for="bank_name" class="form-label">اسم البنك</label>
                            <input type="text" name="bank_name" id="bank_name" class="form-control">
                        </div>
                        <div class="mb-3">
                            <label for="account_number" class="form-label">رقم الحساب</label>
                            <input type="text" name="account_number" id="account_number" class="form-control">
                        </div>
                    </div>

                    <div id="paypalDetails" style="display: none;">
                        <div class="mb-3">
                            <label for="paypal_email" class="form-label">بريد PayPal الإلكتروني</label>
                            <input type="email" name="paypal_email" id="paypal_email" class="form-control">
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="withdrawal_notes" class="form-label">ملاحظات (اختياري)</label>
                        <textarea name="notes" id="withdrawal_notes" class="form-control" rows="3"></textarea>
                    </div>

                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>ملاحظة:</strong> سيتم مراجعة طلب السحب خلال 3-5 أيام عمل.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-success">إرسال طلب السحب</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// رسم بياني للأرباح الشهرية
document.addEventListener('DOMContentLoaded', function() {
    const ctx = document.getElementById('earningsChart').getContext('2d');

    // بيانات الأرباح الشهرية
    const monthlyData = <?php echo json_encode(array_reverse($monthly_earnings)); ?>;

    const labels = monthlyData.map(item => {
        const date = new Date(item.month + '-01');
        return date.toLocaleDateString('ar-SA', { year: 'numeric', month: 'long' });
    });

    const earnings = monthlyData.map(item => parseFloat(item.earnings) || 0);

    new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: 'الأرباح الشهرية ($)',
                data: earnings,
                borderColor: '#28a745',
                backgroundColor: 'rgba(40, 167, 69, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '$' + value.toFixed(2);
                        }
                    }
                }
            }
        }
    });
});

// تبديل تفاصيل طريقة الدفع
document.getElementById('payment_method').addEventListener('change', function() {
    const bankDetails = document.getElementById('bankDetails');
    const paypalDetails = document.getElementById('paypalDetails');

    bankDetails.style.display = 'none';
    paypalDetails.style.display = 'none';

    if (this.value === 'bank_transfer') {
        bankDetails.style.display = 'block';
    } else if (this.value === 'paypal') {
        paypalDetails.style.display = 'block';
    }
});

// تصدير التقرير
function exportEarnings() {
    const startDate = document.getElementById('start_date').value;
    const endDate = document.getElementById('end_date').value;

    const url = `export-earnings.php?start_date=${startDate}&end_date=${endDate}`;
    window.open(url, '_blank');
}
</script>

<style>
.card {
    transition: all 0.3s ease;
    border-radius: 0.5rem;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1) !important;
}

.table tbody tr:hover {
    background-color: rgba(0,123,255,0.05);
}

.btn:hover {
    transform: translateY(-1px);
}

@media (max-width: 768px) {
    .modal-dialog {
        margin: 0.5rem;
    }
}
</style>

<?php include 'includes/footer.php'; ?>
