<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات المدرب
if (!isLoggedIn() || !isInstructor()) {
    header('Location: ../login.php');
    exit;
}

$pageTitle = 'الكورسات المسجلة';
$breadcrumbs = [
    ['title' => 'الكورسات المسجلة']
];

// جلب الكورسات المسجلة
try {
    $stmt = $conn->prepare("
        SELECT 
            c.*,
            COUNT(DISTINCT ce.student_id) as enrolled_students,
            COUNT(DISTINCT cv.id) as total_videos,
            COUNT(DISTINCT cm.id) as total_materials,
            SUM(cv.duration_minutes) as total_duration,
            COUNT(DISTINCT CASE WHEN cv.is_free = 1 THEN cv.id END) as free_videos,
            AVG(CASE WHEN cr.rating IS NOT NULL THEN cr.rating END) as avg_rating,
            COUNT(DISTINCT cr.id) as total_reviews
        FROM courses c
        LEFT JOIN course_enrollments ce ON c.id = ce.course_id AND ce.status = 'active'
        LEFT JOIN course_videos cv ON c.id = cv.course_id AND cv.status = 'active'
        LEFT JOIN course_materials cm ON c.id = cm.course_id AND cm.status = 'active'
        LEFT JOIN course_reviews cr ON c.id = cr.course_id
        WHERE c.instructor_id = ? AND c.status = 'active'
        GROUP BY c.id
        HAVING total_videos > 0 OR total_materials > 0
        ORDER BY c.created_at DESC
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $recorded_courses = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // إحصائيات سريعة
    $stmt = $conn->prepare("
        SELECT 
            COUNT(DISTINCT c.id) as total_recorded_courses,
            COUNT(DISTINCT cv.id) as total_videos,
            COUNT(DISTINCT cm.id) as total_materials,
            COUNT(DISTINCT ce.student_id) as total_students,
            SUM(cv.duration_minutes) as total_duration
        FROM courses c
        LEFT JOIN course_videos cv ON c.id = cv.course_id AND cv.status = 'active'
        LEFT JOIN course_materials cm ON c.id = cm.course_id AND cm.status = 'active'
        LEFT JOIN course_enrollments ce ON c.id = ce.course_id AND ce.status = 'active'
        WHERE c.instructor_id = ? AND c.status = 'active'
        AND (cv.id IS NOT NULL OR cm.id IS NOT NULL)
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
} catch (PDOException $e) {
    $error_message = 'حدث خطأ أثناء جلب البيانات';
    $recorded_courses = [];
    $stats = ['total_recorded_courses' => 0, 'total_videos' => 0, 'total_materials' => 0, 'total_students' => 0, 'total_duration' => 0];
}

include 'includes/header.php';
?>

<!-- رأس الصفحة -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="h3 mb-2">
            <i class="fas fa-play-circle text-success me-2"></i>
            الكورسات المسجلة
        </h2>
        <p class="text-muted mb-0">إدارة ومتابعة الكورسات التي تحتوي على فيديوهات ومواد مسجلة</p>
    </div>
    <div class="d-flex gap-2">
        <a href="upload-video.php" class="btn btn-primary">
            <i class="fas fa-upload me-1"></i>رفع فيديو
        </a>
        <a href="add-material.php" class="btn btn-outline-primary">
            <i class="fas fa-file-upload me-1"></i>إضافة مادة
        </a>
        <a href="live-courses.php" class="btn btn-outline-secondary">
            <i class="fas fa-video me-1"></i>الكورسات المباشرة
        </a>
    </div>
</div>

<!-- بطاقات الإحصائيات -->
<div class="row g-4 mb-4">
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-success bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-graduation-cap text-success fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h5 class="mb-1"><?php echo $stats['total_recorded_courses']; ?></h5>
                        <p class="text-muted mb-0">كورسات مسجلة</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-primary bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-play text-primary fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h5 class="mb-1"><?php echo $stats['total_videos']; ?></h5>
                        <p class="text-muted mb-0">إجمالي الفيديوهات</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-info bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-file-alt text-info fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h5 class="mb-1"><?php echo $stats['total_materials']; ?></h5>
                        <p class="text-muted mb-0">المواد التعليمية</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-warning bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-clock text-warning fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h5 class="mb-1"><?php echo number_format($stats['total_duration'] / 60, 1); ?></h5>
                        <p class="text-muted mb-0">ساعات المحتوى</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- فلاتر البحث -->
<div class="card border-0 shadow-sm mb-4">
    <div class="card-body">
        <div class="row g-3 align-items-end">
            <div class="col-md-4">
                <label for="searchCourse" class="form-label">البحث في الكورسات</label>
                <input type="text" id="searchCourse" class="form-control" placeholder="اسم الكورس...">
            </div>
            <div class="col-md-3">
                <label for="contentFilter" class="form-label">نوع المحتوى</label>
                <select id="contentFilter" class="form-select">
                    <option value="">جميع الأنواع</option>
                    <option value="videos">فيديوهات فقط</option>
                    <option value="materials">مواد فقط</option>
                    <option value="both">فيديوهات ومواد</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="sortBy" class="form-label">ترتيب حسب</label>
                <select id="sortBy" class="form-select">
                    <option value="newest">الأحدث</option>
                    <option value="oldest">الأقدم</option>
                    <option value="most_students">الأكثر طلاباً</option>
                    <option value="most_content">الأكثر محتوى</option>
                    <option value="highest_rated">الأعلى تقييماً</option>
                </select>
            </div>
            <div class="col-md-2">
                <button class="btn btn-outline-primary w-100" onclick="resetFilters()">
                    <i class="fas fa-undo me-1"></i>إعادة تعيين
                </button>
            </div>
        </div>
    </div>
</div>

<!-- قائمة الكورسات المسجلة -->
<?php if (empty($recorded_courses)): ?>
<div class="card border-0 shadow-sm">
    <div class="card-body text-center py-5">
        <i class="fas fa-play-circle text-muted" style="font-size: 4rem;"></i>
        <h4 class="mt-4 text-muted">لا توجد كورسات مسجلة</h4>
        <p class="text-muted mb-4">ابدأ بإضافة فيديوهات ومواد تعليمية لكورساتك</p>
        <div class="d-flex gap-2 justify-content-center">
            <a href="upload-video.php" class="btn btn-primary">
                <i class="fas fa-upload me-2"></i>رفع فيديو
            </a>
            <a href="add-material.php" class="btn btn-outline-primary">
                <i class="fas fa-file-upload me-2"></i>إضافة مادة
            </a>
        </div>
    </div>
</div>
<?php else: ?>
<div class="row g-4" id="coursesContainer">
    <?php foreach ($recorded_courses as $course): ?>
    <div class="col-lg-6 course-card" 
         data-course-name="<?php echo strtolower($course['title']); ?>"
         data-videos="<?php echo $course['total_videos']; ?>"
         data-materials="<?php echo $course['total_materials']; ?>"
         data-students="<?php echo $course['enrolled_students']; ?>"
         data-rating="<?php echo $course['avg_rating'] ?? 0; ?>"
         data-created="<?php echo $course['created_at']; ?>">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-white border-bottom d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><?php echo htmlspecialchars($course['title']); ?></h5>
                <div class="d-flex gap-1">
                    <?php if ($course['total_videos'] > 0 && $course['total_materials'] > 0): ?>
                    <span class="badge bg-success">
                        <i class="fas fa-check-double me-1"></i>مكتمل
                    </span>
                    <?php elseif ($course['total_videos'] > 0): ?>
                    <span class="badge bg-primary">
                        <i class="fas fa-play me-1"></i>فيديوهات
                    </span>
                    <?php elseif ($course['total_materials'] > 0): ?>
                    <span class="badge bg-info">
                        <i class="fas fa-file-alt me-1"></i>مواد
                    </span>
                    <?php endif; ?>
                    
                    <?php if ($course['avg_rating']): ?>
                    <span class="badge bg-warning">
                        <i class="fas fa-star me-1"></i><?php echo number_format($course['avg_rating'], 1); ?>
                    </span>
                    <?php endif; ?>
                </div>
            </div>
            <div class="card-body">
                <?php if ($course['description']): ?>
                <p class="text-muted mb-3"><?php echo mb_substr(htmlspecialchars($course['description']), 0, 100) . '...'; ?></p>
                <?php endif; ?>
                
                <!-- إحصائيات المحتوى -->
                <div class="row g-3 mb-3">
                    <div class="col-6">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-play text-primary me-2"></i>
                            <span class="small"><?php echo $course['total_videos']; ?> فيديو</span>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-file-alt text-info me-2"></i>
                            <span class="small"><?php echo $course['total_materials']; ?> مادة</span>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-users text-success me-2"></i>
                            <span class="small"><?php echo $course['enrolled_students']; ?> طالب</span>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-clock text-warning me-2"></i>
                            <span class="small"><?php echo number_format($course['total_duration'] / 60, 1); ?> ساعة</span>
                        </div>
                    </div>
                </div>
                
                <!-- شريط التقدم -->
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-1">
                        <small class="text-muted">اكتمال المحتوى</small>
                        <small class="text-muted">
                            <?php 
                            $total_content = $course['total_videos'] + $course['total_materials'];
                            $completion = $total_content > 0 ? min(100, ($total_content / 10) * 100) : 0;
                            echo number_format($completion, 0); 
                            ?>%
                        </small>
                    </div>
                    <div class="progress" style="height: 6px;">
                        <div class="progress-bar bg-success" style="width: <?php echo $completion; ?>%"></div>
                    </div>
                </div>
                
                <!-- معلومات إضافية -->
                <div class="row g-2">
                    <?php if ($course['free_videos'] > 0): ?>
                    <div class="col-12">
                        <small class="text-success">
                            <i class="fas fa-unlock me-1"></i>
                            <?php echo $course['free_videos']; ?> فيديو مجاني
                        </small>
                    </div>
                    <?php endif; ?>
                    
                    <?php if ($course['total_reviews'] > 0): ?>
                    <div class="col-12">
                        <small class="text-muted">
                            <i class="fas fa-comments me-1"></i>
                            <?php echo $course['total_reviews']; ?> تقييم
                        </small>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            <div class="card-footer bg-white border-top">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="btn-group" role="group">
                        <a href="videos.php?course_id=<?php echo $course['id']; ?>" 
                           class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-play me-1"></i>الفيديوهات
                        </a>
                        <a href="materials.php?course_id=<?php echo $course['id']; ?>" 
                           class="btn btn-sm btn-outline-info">
                            <i class="fas fa-file-alt me-1"></i>المواد
                        </a>
                        <a href="course-students.php?course_id=<?php echo $course['id']; ?>" 
                           class="btn btn-sm btn-outline-success">
                            <i class="fas fa-users me-1"></i>الطلاب
                        </a>
                    </div>
                    <div class="btn-group" role="group">
                        <a href="upload-video.php?course_id=<?php echo $course['id']; ?>" 
                           class="btn btn-sm btn-primary">
                            <i class="fas fa-plus me-1"></i>إضافة محتوى
                        </a>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                    data-bs-toggle="dropdown">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="course-details.php?id=<?php echo $course['id']; ?>">
                                    <i class="fas fa-eye me-2"></i>عرض التفاصيل
                                </a></li>
                                <li><a class="dropdown-item" href="course-analytics.php?id=<?php echo $course['id']; ?>">
                                    <i class="fas fa-chart-bar me-2"></i>الإحصائيات
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="edit-course.php?id=<?php echo $course['id']; ?>">
                                    <i class="fas fa-edit me-2"></i>تعديل الكورس
                                </a></li>
                                <li><a class="dropdown-item text-danger" href="#" onclick="archiveCourse(<?php echo $course['id']; ?>)">
                                    <i class="fas fa-archive me-2"></i>أرشفة الكورس
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endforeach; ?>
</div>
<?php endif; ?>

<!-- إحصائيات تفصيلية -->
<div class="row mt-5">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-light border-bottom">
                <h6 class="mb-0">
                    <i class="fas fa-chart-pie text-primary me-2"></i>
                    تحليل المحتوى
                </h6>
            </div>
            <div class="card-body">
                <div class="row g-4">
                    <div class="col-md-3 text-center">
                        <div class="border-end">
                            <h4 class="text-primary mb-1"><?php echo number_format($stats['total_duration'] / 60, 1); ?></h4>
                            <small class="text-muted">ساعات المحتوى</small>
                        </div>
                    </div>
                    <div class="col-md-3 text-center">
                        <div class="border-end">
                            <h4 class="text-success mb-1"><?php echo $stats['total_videos']; ?></h4>
                            <small class="text-muted">إجمالي الفيديوهات</small>
                        </div>
                    </div>
                    <div class="col-md-3 text-center">
                        <div class="border-end">
                            <h4 class="text-info mb-1"><?php echo $stats['total_materials']; ?></h4>
                            <small class="text-muted">إجمالي المواد</small>
                        </div>
                    </div>
                    <div class="col-md-3 text-center">
                        <h4 class="text-warning mb-1"><?php echo $stats['total_students']; ?></h4>
                        <small class="text-muted">إجمالي الطلاب</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// البحث والفلترة
document.getElementById('searchCourse').addEventListener('input', filterCourses);
document.getElementById('contentFilter').addEventListener('change', filterCourses);
document.getElementById('sortBy').addEventListener('change', sortCourses);

function filterCourses() {
    const searchTerm = document.getElementById('searchCourse').value.toLowerCase();
    const contentFilter = document.getElementById('contentFilter').value;
    const courseCards = document.querySelectorAll('.course-card');
    
    courseCards.forEach(card => {
        let showCard = true;
        
        // فلترة النص
        if (searchTerm && !card.dataset.courseName.includes(searchTerm)) {
            showCard = false;
        }
        
        // فلترة المحتوى
        if (contentFilter) {
            const videos = parseInt(card.dataset.videos);
            const materials = parseInt(card.dataset.materials);
            
            switch (contentFilter) {
                case 'videos':
                    if (videos === 0) showCard = false;
                    break;
                case 'materials':
                    if (materials === 0) showCard = false;
                    break;
                case 'both':
                    if (videos === 0 || materials === 0) showCard = false;
                    break;
            }
        }
        
        card.style.display = showCard ? 'block' : 'none';
    });
}

function sortCourses() {
    const sortBy = document.getElementById('sortBy').value;
    const container = document.getElementById('coursesContainer');
    const cards = Array.from(container.children);
    
    cards.sort((a, b) => {
        switch (sortBy) {
            case 'newest':
                return new Date(b.dataset.created) - new Date(a.dataset.created);
            case 'oldest':
                return new Date(a.dataset.created) - new Date(b.dataset.created);
            case 'most_students':
                return parseInt(b.dataset.students) - parseInt(a.dataset.students);
            case 'most_content':
                const aContent = parseInt(a.dataset.videos) + parseInt(a.dataset.materials);
                const bContent = parseInt(b.dataset.videos) + parseInt(b.dataset.materials);
                return bContent - aContent;
            case 'highest_rated':
                return parseFloat(b.dataset.rating) - parseFloat(a.dataset.rating);
            default:
                return 0;
        }
    });
    
    cards.forEach(card => container.appendChild(card));
}

function resetFilters() {
    document.getElementById('searchCourse').value = '';
    document.getElementById('contentFilter').value = '';
    document.getElementById('sortBy').value = 'newest';
    
    document.querySelectorAll('.course-card').forEach(card => {
        card.style.display = 'block';
    });
    
    sortCourses();
}

function archiveCourse(courseId) {
    if (confirm('هل أنت متأكد من أرشفة هذا الكورس؟ سيصبح غير متاح للطلاب الجدد.')) {
        // يمكن تنفيذ هذا عبر AJAX
        alert('سيتم تنفيذ هذه الميزة قريباً');
    }
}

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // إضافة تأثيرات بصرية للبطاقات
    const cards = document.querySelectorAll('.card');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
            this.style.transition = 'transform 0.3s ease';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
    
    // ترتيب أولي
    sortCourses();
});
</script>

<style>
.course-card {
    transition: all 0.3s ease;
}

.course-card .card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.course-card .card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15) !important;
}

.progress {
    border-radius: 10px;
}

.progress-bar {
    border-radius: 10px;
}

.badge {
    font-size: 0.75rem;
}

.btn-group .btn {
    border-radius: 0.375rem;
}

.btn-group .btn:not(:last-child) {
    margin-right: 0.25rem;
}

@media (max-width: 768px) {
    .btn-group {
        display: flex;
        flex-direction: column;
        width: 100%;
    }
    
    .btn-group .btn {
        margin-right: 0;
        margin-bottom: 0.25rem;
    }
}
</style>

<?php include 'includes/footer.php'; ?>
