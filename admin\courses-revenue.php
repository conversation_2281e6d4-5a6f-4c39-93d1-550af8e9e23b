<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات المدير
if (!isLoggedIn() || !isAdmin()) {
    header('Location: ../login.php');
    exit;
}

$error = '';
$success = '';

// جلب إعدادات العمولة
try {
    $stmt = $conn->query("SELECT setting_value FROM commission_settings WHERE setting_name = 'platform_commission'");
    $platform_commission = $stmt ? $stmt->fetchColumn() : 30;
    $instructor_commission = 100 - $platform_commission;
} catch (Exception $e) {
    $platform_commission = 30;
    $instructor_commission = 70;
}

// جلب جميع الكورسات مع تفاصيل الإيرادات
try {
    $stmt = $conn->query("
        SELECT 
            c.*,
            u.name as instructor_name,
            u.email as instructor_email,
            (SELECT COUNT(*) FROM course_enrollments WHERE course_id = c.id) as enrolled_students,
            (SELECT COUNT(*) FROM sessions WHERE course_id = c.id) as total_sessions,
            CASE 
                WHEN c.course_type = 'paid' THEN c.price * ($platform_commission / 100)
                ELSE 0 
            END as platform_earnings,
            CASE 
                WHEN c.course_type = 'paid' THEN c.price * ($instructor_commission / 100)
                ELSE 0 
            END as instructor_earnings
        FROM courses c
        JOIN users u ON c.instructor_id = u.id
        ORDER BY c.course_type DESC, c.price DESC, c.created_at DESC
    ");
    $courses = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // إحصائيات عامة
    $stmt = $conn->query("
        SELECT 
            COUNT(*) as total_courses,
            COUNT(CASE WHEN course_type = 'free' THEN 1 END) as free_courses,
            COUNT(CASE WHEN course_type = 'paid' THEN 1 END) as paid_courses,
            SUM(CASE WHEN course_type = 'paid' THEN price ELSE 0 END) as total_revenue,
            AVG(CASE WHEN course_type = 'paid' THEN price ELSE NULL END) as avg_price,
            MIN(CASE WHEN course_type = 'paid' THEN price ELSE NULL END) as min_price,
            MAX(CASE WHEN course_type = 'paid' THEN price ELSE NULL END) as max_price
        FROM courses
    ");
    $stats = $stmt->fetch(PDO::FETCH_ASSOC);

    // إحصائيات المدربين
    $stmt = $conn->query("
        SELECT 
            u.name as instructor_name,
            COUNT(c.id) as total_courses,
            COUNT(CASE WHEN c.course_type = 'paid' THEN 1 END) as paid_courses,
            SUM(CASE WHEN c.course_type = 'paid' THEN c.price ELSE 0 END) as total_course_value,
            SUM(CASE WHEN c.course_type = 'paid' THEN c.price * ($instructor_commission / 100) ELSE 0 END) as instructor_earnings,
            SUM(CASE WHEN c.course_type = 'paid' THEN c.price * ($platform_commission / 100) ELSE 0 END) as platform_earnings
        FROM users u
        LEFT JOIN courses c ON u.id = c.instructor_id
        WHERE u.role = 'instructor'
        GROUP BY u.id, u.name
        ORDER BY total_course_value DESC
    ");
    $instructor_stats = $stmt->fetchAll(PDO::FETCH_ASSOC);

} catch (Exception $e) {
    $error = 'حدث خطأ في جلب البيانات: ' . $e->getMessage();
    $courses = [];
    $stats = ['total_courses' => 0, 'free_courses' => 0, 'paid_courses' => 0, 'total_revenue' => 0, 'avg_price' => 0, 'min_price' => 0, 'max_price' => 0];
    $instructor_stats = [];
}

$pageTitle = 'إيرادات الكورسات';
require_once '../includes/header.php';
?>

<div class="container-fluid py-4">
    <!-- العنوان -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="mb-0">💰 إيرادات الكورسات والأسعار</h5>
                        <small>تقرير مفصل عن جميع الكورسات والإيرادات المتوقعة</small>
                    </div>
                    <div>
                        <a href="commission-settings.php" class="btn btn-warning btn-sm me-2">
                            <i class="fas fa-cog"></i> إعدادات العمولة
                        </a>
                        <a href="dashboard.php" class="btn btn-light btn-sm">
                            <i class="fas fa-arrow-left"></i> العودة
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- الإحصائيات العامة -->
    <div class="row mb-4">
        <div class="col-12">
            <h6 class="text-muted mb-3">الإحصائيات العامة</h6>
        </div>
        <div class="col-md-2">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h4><?php echo $stats['total_courses']; ?></h4>
                    <p class="mb-0">إجمالي الكورسات</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h4><?php echo $stats['free_courses']; ?></h4>
                    <p class="mb-0">كورسات مجانية</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h4><?php echo $stats['paid_courses']; ?></h4>
                    <p class="mb-0">كورسات مدفوعة</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h4><?php echo number_format($stats['total_revenue'], 0); ?></h4>
                    <p class="mb-0">إجمالي الإيرادات (ريال)</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-secondary text-white">
                <div class="card-body text-center">
                    <h4><?php echo number_format($stats['avg_price'], 0); ?></h4>
                    <p class="mb-0">متوسط السعر (ريال)</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-dark text-white">
                <div class="card-body text-center">
                    <h4><?php echo $platform_commission; ?>%</h4>
                    <p class="mb-0">عمولة المنصة</p>
                </div>
            </div>
        </div>
    </div>

    <!-- تفاصيل الأرباح -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">توزيع الأرباح</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <h5 class="text-warning">
                                <?php echo number_format($stats['total_revenue'] * ($platform_commission / 100), 0); ?> ريال
                            </h5>
                            <p class="mb-0">أرباح المنصة (<?php echo $platform_commission; ?>%)</p>
                        </div>
                        <div class="col-6">
                            <h5 class="text-success">
                                <?php echo number_format($stats['total_revenue'] * ($instructor_commission / 100), 0); ?> ريال
                            </h5>
                            <p class="mb-0">أرباح المدربين (<?php echo $instructor_commission; ?>%)</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">نطاق الأسعار</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <h5 class="text-info">
                                <?php echo number_format($stats['min_price'], 0); ?> ريال
                            </h5>
                            <p class="mb-0">أقل سعر</p>
                        </div>
                        <div class="col-6">
                            <h5 class="text-danger">
                                <?php echo number_format($stats['max_price'], 0); ?> ريال
                            </h5>
                            <p class="mb-0">أعلى سعر</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- جدول الكورسات -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">جميع الكورسات مع تفاصيل الأسعار</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover" id="coursesTable">
                            <thead class="table-light">
                                <tr>
                                    <th>الكورس</th>
                                    <th>المدرب</th>
                                    <th>النوع</th>
                                    <th>السعر</th>
                                    <th>الطلاب</th>
                                    <th>الجلسات</th>
                                    <th>أرباح المنصة</th>
                                    <th>أرباح المدرب</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($courses as $course): ?>
                                    <tr>
                                        <td>
                                            <div>
                                                <strong><?php echo htmlspecialchars($course['title']); ?></strong>
                                                <br>
                                                <small class="text-muted">
                                                    <?php echo htmlspecialchars(substr($course['description'], 0, 50)) . '...'; ?>
                                                </small>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">
                                                <?php echo htmlspecialchars($course['instructor_name']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php if ($course['course_type'] === 'paid'): ?>
                                                <span class="badge bg-warning">مدفوع</span>
                                            <?php else: ?>
                                                <span class="badge bg-success">مجاني</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($course['course_type'] === 'paid'): ?>
                                                <strong class="text-success">
                                                    <?php echo number_format($course['price'], 0); ?> ريال
                                                </strong>
                                            <?php else: ?>
                                                <span class="text-muted">مجاني</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-primary">
                                                <?php echo $course['enrolled_students']; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary">
                                                <?php echo $course['total_sessions']; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php if ($course['course_type'] === 'paid'): ?>
                                                <strong class="text-warning">
                                                    <?php echo number_format($course['platform_earnings'], 0); ?> ريال
                                                </strong>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($course['course_type'] === 'paid'): ?>
                                                <strong class="text-success">
                                                    <?php echo number_format($course['instructor_earnings'], 0); ?> ريال
                                                </strong>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <small class="text-muted">
                                                <?php echo date('Y-m-d', strtotime($course['created_at'])); ?>
                                            </small>
                                        </td>
                                        <td>
                                            <a href="course-details.php?id=<?php echo $course['id']; ?>" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات المدربين -->
    <?php if (!empty($instructor_stats)): ?>
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">إحصائيات المدربين</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover" id="instructorsTable">
                                <thead class="table-light">
                                    <tr>
                                        <th>المدرب</th>
                                        <th>إجمالي الكورسات</th>
                                        <th>الكورسات المدفوعة</th>
                                        <th>قيمة الكورسات</th>
                                        <th>أرباح المدرب</th>
                                        <th>أرباح المنصة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($instructor_stats as $stat): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo htmlspecialchars($stat['instructor_name']); ?></strong>
                                            </td>
                                            <td>
                                                <span class="badge bg-primary"><?php echo $stat['total_courses']; ?></span>
                                            </td>
                                            <td>
                                                <span class="badge bg-warning"><?php echo $stat['paid_courses']; ?></span>
                                            </td>
                                            <td>
                                                <strong><?php echo number_format($stat['total_course_value'], 0); ?> ريال</strong>
                                            </td>
                                            <td>
                                                <strong class="text-success">
                                                    <?php echo number_format($stat['instructor_earnings'], 0); ?> ريال
                                                </strong>
                                            </td>
                                            <td>
                                                <strong class="text-warning">
                                                    <?php echo number_format($stat['platform_earnings'], 0); ?> ريال
                                                </strong>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<script>
$(document).ready(function() {
    $('#coursesTable').DataTable({
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json"
        },
        "order": [[ 3, "desc" ]], // ترتيب حسب السعر
        "pageLength": 25
    });
    
    $('#instructorsTable').DataTable({
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json"
        },
        "order": [[ 3, "desc" ]], // ترتيب حسب قيمة الكورسات
        "pageLength": 10
    });
});
</script>

<?php require_once '../includes/footer.php'; ?>
