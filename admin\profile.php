<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات المدير
if (!isLoggedIn() || !isAdmin()) {
    header('Location: ../login.php');
    exit;
}

$success_message = '';
$error_message = '';

// جلب بيانات المدير الحالي
try {
    $stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$admin) {
        header('Location: ../login.php');
        exit;
    }
} catch (Exception $e) {
    $error_message = 'حدث خطأ أثناء جلب بيانات الملف الشخصي';
}

// معالجة تحديث الملف الشخصي
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'update_profile') {
        $name = sanitize($_POST['name']);
        $email = filter_var($_POST['email'], FILTER_SANITIZE_EMAIL);
        $phone = sanitize($_POST['phone']);
        
        // التحقق من البيانات
        if (empty($name) || empty($email)) {
            $error_message = 'الاسم والبريد الإلكتروني مطلوبان';
        } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $error_message = 'البريد الإلكتروني غير صالح';
        } else {
            try {
                // التحقق من عدم تكرار البريد الإلكتروني
                $stmt = $conn->prepare("SELECT id FROM users WHERE email = ? AND id != ?");
                $stmt->execute([$email, $_SESSION['user_id']]);
                
                if ($stmt->rowCount() > 0) {
                    $error_message = 'البريد الإلكتروني مستخدم من قبل مستخدم آخر';
                } else {
                    // تحديث البيانات
                    $stmt = $conn->prepare("
                        UPDATE users 
                        SET name = ?, email = ?, phone = ?, updated_at = NOW() 
                        WHERE id = ?
                    ");
                    $stmt->execute([$name, $email, $phone, $_SESSION['user_id']]);
                    
                    logUserActivity($_SESSION['user_id'], 'تحديث الملف الشخصي', 'تم تحديث بيانات الملف الشخصي');
                    $success_message = 'تم تحديث الملف الشخصي بنجاح';
                    
                    // تحديث البيانات المحلية
                    $admin['name'] = $name;
                    $admin['email'] = $email;
                    $admin['phone'] = $phone;
                }
            } catch (Exception $e) {
                $error_message = 'حدث خطأ أثناء تحديث الملف الشخصي: ' . $e->getMessage();
            }
        }
    } elseif ($action === 'change_password') {
        $current_password = $_POST['current_password'];
        $new_password = $_POST['new_password'];
        $confirm_password = $_POST['confirm_password'];
        
        // التحقق من البيانات
        if (empty($current_password) || empty($new_password) || empty($confirm_password)) {
            $error_message = 'جميع حقول كلمة المرور مطلوبة';
        } elseif ($new_password !== $confirm_password) {
            $error_message = 'كلمة المرور الجديدة غير متطابقة';
        } elseif (strlen($new_password) < 8) {
            $error_message = 'كلمة المرور يجب أن تكون 8 أحرف على الأقل';
        } else {
            try {
                // التحقق من كلمة المرور الحالية
                if (!password_verify($current_password, $admin['password'])) {
                    $error_message = 'كلمة المرور الحالية غير صحيحة';
                } else {
                    // تحديث كلمة المرور
                    $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                    $stmt = $conn->prepare("UPDATE users SET password = ?, updated_at = NOW() WHERE id = ?");
                    $stmt->execute([$hashed_password, $_SESSION['user_id']]);
                    
                    logUserActivity($_SESSION['user_id'], 'تغيير كلمة المرور', 'تم تغيير كلمة المرور');
                    $success_message = 'تم تغيير كلمة المرور بنجاح';
                }
            } catch (Exception $e) {
                $error_message = 'حدث خطأ أثناء تغيير كلمة المرور: ' . $e->getMessage();
            }
        }
    }
}

// جلب إحصائيات المدير
try {
    $admin_stats = [
        'total_users' => $conn->query("SELECT COUNT(*) FROM users")->fetchColumn(),
        'total_courses' => $conn->query("SELECT COUNT(*) FROM courses")->fetchColumn(),
        'total_sessions' => $conn->query("SELECT COUNT(*) FROM sessions")->fetchColumn(),
        'login_count' => 0
    ];

    // جلب عدد مرات تسجيل الدخول للمدير
    try {
        $login_stmt = $conn->prepare("SELECT COUNT(*) FROM activity_logs WHERE user_id = ? AND action LIKE '%تسجيل دخول%'");
        $login_stmt->execute([$_SESSION['user_id']]);
        $admin_stats['login_count'] = $login_stmt->fetchColumn();
    } catch (Exception $e) {
        $admin_stats['login_count'] = 0;
    }

} catch (Exception $e) {
    $admin_stats = ['total_users' => 0, 'total_courses' => 0, 'total_sessions' => 0, 'login_count' => 0];
}

$pageTitle = 'الملف الشخصي';
$pageSubtitle = 'إدارة بيانات الملف الشخصي وإعدادات الحساب';
require_once 'includes/admin-header.php';
?>

<div class="container-fluid py-4">
    <!-- رسائل النجاح والخطأ -->
    <?php if ($success_message): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>
    
    <?php if ($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <!-- معلومات الملف الشخصي -->
        <div class="col-lg-4 mb-4">
            <div class="admin-card">
                <div class="card-body text-center">
                    <div class="profile-avatar mb-3">
                        <i class="fas fa-user-shield fa-5x text-primary"></i>
                    </div>
                    <h4 class="mb-1"><?php echo htmlspecialchars($admin['name']); ?></h4>
                    <p class="text-muted mb-3"><?php echo htmlspecialchars($admin['email']); ?></p>
                    <span class="badge bg-primary fs-6">مدير النظام</span>
                    
                    <hr class="my-4">
                    
                    <div class="row text-center">
                        <div class="col-6">
                            <h5 class="mb-0"><?php echo number_format($admin_stats['total_users']); ?></h5>
                            <small class="text-muted">المستخدمين</small>
                        </div>
                        <div class="col-6">
                            <h5 class="mb-0"><?php echo number_format($admin_stats['total_courses']); ?></h5>
                            <small class="text-muted">الدورات</small>
                        </div>
                    </div>
                    
                    <hr class="my-4">
                    
                    <div class="text-start">
                        <p class="mb-2"><i class="fas fa-calendar me-2"></i>تاريخ الانضمام: <?php echo date('Y-m-d', strtotime($admin['created_at'])); ?></p>
                        <p class="mb-2"><i class="fas fa-clock me-2"></i>آخر تسجيل دخول: <?php echo $admin['last_login'] ? date('Y-m-d H:i', strtotime($admin['last_login'])) : 'لم يسجل دخول من قبل'; ?></p>
                        <p class="mb-0"><i class="fas fa-phone me-2"></i>الهاتف: <?php echo htmlspecialchars($admin['phone'] ?? 'غير محدد'); ?></p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- تحديث البيانات الشخصية -->
        <div class="col-lg-8 mb-4">
            <div class="admin-card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-edit me-2"></i>تحديث البيانات الشخصية</h6>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <input type="hidden" name="action" value="update_profile">
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الاسم الكامل</label>
                                <input type="text" class="form-control" name="name" 
                                       value="<?php echo htmlspecialchars($admin['name']); ?>" required>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" name="email" 
                                       value="<?php echo htmlspecialchars($admin['email']); ?>" required>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">رقم الهاتف</label>
                                <input type="text" class="form-control" name="phone" 
                                       value="<?php echo htmlspecialchars($admin['phone'] ?? ''); ?>">
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الدور</label>
                                <input type="text" class="form-control" value="مدير النظام" readonly>
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>حفظ التغييرات
                        </button>
                    </form>
                </div>
            </div>
            
            <!-- تغيير كلمة المرور -->
            <div class="admin-card mt-4">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-lock me-2"></i>تغيير كلمة المرور</h6>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <input type="hidden" name="action" value="change_password">
                        
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label class="form-label">كلمة المرور الحالية</label>
                                <input type="password" class="form-control" name="current_password" required>
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label class="form-label">كلمة المرور الجديدة</label>
                                <input type="password" class="form-control" name="new_password" 
                                       minlength="8" required>
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label class="form-label">تأكيد كلمة المرور</label>
                                <input type="password" class="form-control" name="confirm_password" 
                                       minlength="8" required>
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-warning">
                            <i class="fas fa-key me-1"></i>تغيير كلمة المرور
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.profile-avatar {
    margin-bottom: 1rem;
}

.admin-card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 0 20px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.admin-card:hover {
    transform: translateY(-5px);
}
</style>

<script>
// التحقق من تطابق كلمة المرور
document.addEventListener('DOMContentLoaded', function() {
    const newPassword = document.querySelector('input[name="new_password"]');
    const confirmPassword = document.querySelector('input[name="confirm_password"]');
    
    function checkPasswordMatch() {
        if (newPassword.value !== confirmPassword.value) {
            confirmPassword.setCustomValidity('كلمة المرور غير متطابقة');
        } else {
            confirmPassword.setCustomValidity('');
        }
    }
    
    newPassword.addEventListener('input', checkPasswordMatch);
    confirmPassword.addEventListener('input', checkPasswordMatch);
});
</script>

<?php require_once 'includes/admin-footer.php'; ?>
