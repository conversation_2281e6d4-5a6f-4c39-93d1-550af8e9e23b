<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات المدرب
if (!isLoggedIn() || !isInstructor()) {
    header('Location: ../login.php');
    exit;
}

$quiz_id = $_GET['quiz_id'] ?? 0;

// التحقق من وجود الاختبار وأنه ينتمي للمدرب
try {
    $stmt = $conn->prepare("
        SELECT q.*, c.title as course_title
        FROM quizzes q
        INNER JOIN courses c ON q.course_id = c.id
        WHERE q.id = ? AND c.instructor_id = ?
    ");
    $stmt->execute([$quiz_id, $_SESSION['user_id']]);
    $quiz = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$quiz) {
        header('Location: quizzes.php');
        exit;
    }
} catch (PDOException $e) {
    header('Location: quizzes.php');
    exit;
}

$pageTitle = 'إدارة أسئلة الاختبار - ' . $quiz['title'];
$breadcrumbs = [
    ['title' => 'الاختبارات', 'url' => 'quizzes.php'],
    ['title' => 'إدارة الأسئلة']
];

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'add_question') {
        $question_text = trim($_POST['question_text'] ?? '');
        $question_type = $_POST['question_type'] ?? 'multiple_choice';
        $points = $_POST['points'] ?? 1;
        $explanation = trim($_POST['explanation'] ?? '');
        
        $errors = [];
        
        if (empty($question_text)) {
            $errors[] = 'نص السؤال مطلوب';
        }
        
        if (empty($errors)) {
            try {
                $stmt = $conn->prepare("
                    INSERT INTO quiz_questions (quiz_id, question_text, question_type, points, explanation) 
                    VALUES (?, ?, ?, ?, ?)
                ");
                $stmt->execute([$quiz_id, $question_text, $question_type, $points, $explanation]);
                
                $question_id = $conn->lastInsertId();
                
                // إضافة الخيارات للأسئلة متعددة الاختيارات
                if ($question_type === 'multiple_choice') {
                    $options = $_POST['options'] ?? [];
                    $correct_options = $_POST['correct_options'] ?? [];
                    
                    foreach ($options as $index => $option_text) {
                        if (!empty(trim($option_text))) {
                            $is_correct = in_array($index, $correct_options) ? 1 : 0;
                            $stmt = $conn->prepare("
                                INSERT INTO quiz_question_options (question_id, option_text, is_correct) 
                                VALUES (?, ?, ?)
                            ");
                            $stmt->execute([$question_id, trim($option_text), $is_correct]);
                        }
                    }
                }
                
                $success_message = 'تم إضافة السؤال بنجاح';
            } catch (PDOException $e) {
                $error_message = 'حدث خطأ أثناء إضافة السؤال';
            }
        }
    }
    
    if ($action === 'delete_question') {
        $question_id = $_POST['question_id'] ?? 0;
        
        try {
            $stmt = $conn->prepare("
                DELETE qq FROM quiz_questions qq
                INNER JOIN quizzes q ON qq.quiz_id = q.id
                INNER JOIN courses c ON q.course_id = c.id
                WHERE qq.id = ? AND c.instructor_id = ?
            ");
            $stmt->execute([$question_id, $_SESSION['user_id']]);
            
            $success_message = 'تم حذف السؤال بنجاح';
        } catch (PDOException $e) {
            $error_message = 'حدث خطأ أثناء حذف السؤال';
        }
    }
    
    if ($action === 'update_order') {
        $question_orders = $_POST['question_orders'] ?? [];
        
        try {
            foreach ($question_orders as $question_id => $order) {
                $stmt = $conn->prepare("
                    UPDATE quiz_questions qq
                    INNER JOIN quizzes q ON qq.quiz_id = q.id
                    INNER JOIN courses c ON q.course_id = c.id
                    SET qq.order_number = ?
                    WHERE qq.id = ? AND c.instructor_id = ?
                ");
                $stmt->execute([$order, $question_id, $_SESSION['user_id']]);
            }
            
            $success_message = 'تم تحديث ترتيب الأسئلة بنجاح';
        } catch (PDOException $e) {
            $error_message = 'حدث خطأ أثناء تحديث الترتيب';
        }
    }
}

// جلب أسئلة الاختبار
try {
    $stmt = $conn->prepare("
        SELECT 
            qq.*,
            COUNT(qqo.id) as options_count
        FROM quiz_questions qq
        LEFT JOIN quiz_question_options qqo ON qq.id = qqo.question_id
        WHERE qq.quiz_id = ?
        GROUP BY qq.id
        ORDER BY qq.order_number ASC, qq.id ASC
    ");
    $stmt->execute([$quiz_id]);
    $questions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // جلب خيارات كل سؤال
    foreach ($questions as &$question) {
        if ($question['question_type'] === 'multiple_choice') {
            $stmt = $conn->prepare("
                SELECT * FROM quiz_question_options 
                WHERE question_id = ? 
                ORDER BY id ASC
            ");
            $stmt->execute([$question['id']]);
            $question['options'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
        }
    }
    
} catch (PDOException $e) {
    $questions = [];
}

include 'includes/header.php';

// التحقق من وجود معامل "new" للاختبار الجديد
$is_new_quiz = isset($_GET['new']) && $_GET['new'] == '1';
?>

<!-- رأس الصفحة -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="h3 mb-2">
            <i class="fas fa-list-ul text-info me-2"></i>
            إدارة أسئلة الاختبار
        </h2>
        <p class="text-muted mb-0">
            <strong><?php echo htmlspecialchars($quiz['title']); ?></strong> - 
            <?php echo htmlspecialchars($quiz['course_title']); ?>
        </p>
    </div>
    <div class="d-flex gap-2">
        <a href="quizzes.php" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>العودة للاختبارات
        </a>
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addQuestionModal">
            <i class="fas fa-plus me-1"></i>إضافة سؤال
        </button>
        <a href="quiz-preview.php?id=<?php echo $quiz_id; ?>" class="btn btn-success" target="_blank">
            <i class="fas fa-eye me-1"></i>معاينة الاختبار
        </a>
    </div>
</div>

<!-- رسالة ترحيب للاختبار الجديد -->
<?php if ($is_new_quiz && empty($questions)): ?>
<div class="alert alert-info border-0 shadow-sm mb-4" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h5 class="text-white mb-2">
                <i class="fas fa-party-horn me-2"></i>
                مبروك! تم إنشاء الاختبار بنجاح
            </h5>
            <p class="text-white-50 mb-0">
                الآن يمكنك إضافة الأسئلة لاختبارك. ابدأ بإضافة أول سؤال لجعل الاختبار جاهزاً للطلاب.
            </p>
        </div>
        <div class="col-md-4 text-end">
            <button class="btn btn-light btn-lg shadow" data-bs-toggle="modal" data-bs-target="#addQuestionModal">
                <i class="fas fa-plus-circle me-2 text-primary"></i>
                <strong>إضافة أول سؤال</strong>
            </button>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- رسائل النجاح والخطأ -->
<?php if (isset($success_message)): ?>
<div class="alert alert-success alert-dismissible fade show" role="alert">
    <i class="fas fa-check-circle me-2"></i>
    <?php echo $success_message; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<?php if (isset($error_message)): ?>
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <i class="fas fa-exclamation-triangle me-2"></i>
    <?php echo $error_message; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<!-- معلومات الاختبار -->
<div class="card border-0 shadow-sm mb-4">
    <div class="card-header bg-info text-white">
        <h6 class="mb-0">
            <i class="fas fa-info-circle me-2"></i>
            معلومات الاختبار
        </h6>
    </div>
    <div class="card-body">
        <div class="row g-3">
            <div class="col-md-3">
                <div class="text-center">
                    <h5 class="text-primary mb-1"><?php echo count($questions); ?></h5>
                    <small class="text-muted">عدد الأسئلة</small>
                </div>
            </div>
            <div class="col-md-3">
                <div class="text-center">
                    <h5 class="text-success mb-1"><?php echo array_sum(array_column($questions, 'points')); ?></h5>
                    <small class="text-muted">إجمالي النقاط</small>
                </div>
            </div>
            <div class="col-md-3">
                <div class="text-center">
                    <h5 class="text-warning mb-1"><?php echo $quiz['time_limit'] ?: 'غير محدود'; ?></h5>
                    <small class="text-muted">المدة (دقيقة)</small>
                </div>
            </div>
            <div class="col-md-3">
                <div class="text-center">
                    <h5 class="text-info mb-1"><?php echo $quiz['passing_grade']; ?>%</h5>
                    <small class="text-muted">درجة النجاح</small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- قائمة الأسئلة -->
<div class="card border-0 shadow-sm">
    <div class="card-header bg-white border-bottom">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>
                قائمة الأسئلة
            </h5>
            <?php if (!empty($questions)): ?>
            <div class="d-flex gap-2">
                <button class="btn btn-sm btn-outline-primary" onclick="enableSorting()">
                    <i class="fas fa-sort me-1"></i>إعادة ترتيب
                </button>
                <button class="btn btn-sm btn-outline-success" onclick="previewQuiz()">
                    <i class="fas fa-play me-1"></i>معاينة
                </button>
            </div>
            <?php endif; ?>
        </div>
    </div>
    <div class="card-body p-0">
        <?php if (empty($questions)): ?>
        <div class="text-center py-5">
            <div class="empty-state-container">
                <div class="empty-state-icon mb-4">
                    <i class="fas fa-clipboard-question text-primary" style="font-size: 4rem; opacity: 0.7;"></i>
                </div>
                <h4 class="text-primary mb-3">ابدأ بإضافة الأسئلة!</h4>
                <p class="text-muted mb-4 mx-auto" style="max-width: 500px;">
                    اختبارك جاهز الآن لإضافة الأسئلة. أضف أسئلة متنوعة لتقييم فهم طلابك بشكل شامل.
                </p>

                <div class="d-flex gap-3 justify-content-center flex-wrap mb-4">
                    <button class="btn btn-primary btn-lg shadow-sm" data-bs-toggle="modal" data-bs-target="#addQuestionModal">
                        <i class="fas fa-plus-circle me-2"></i>إضافة أول سؤال
                    </button>
                    <button class="btn btn-outline-info btn-lg" onclick="showQuestionGuide()">
                        <i class="fas fa-lightbulb me-2"></i>دليل الأسئلة
                    </button>
                </div>

                <!-- نصائح سريعة -->
                <div class="row g-3 mt-3">
                    <div class="col-md-3">
                        <div class="card border-0 bg-light h-100">
                            <div class="card-body text-center p-3">
                                <i class="fas fa-check-circle text-primary mb-2" style="font-size: 1.5rem;"></i>
                                <h6 class="mb-1">اختيار متعدد</h6>
                                <small class="text-muted">أسئلة مع عدة خيارات</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card border-0 bg-light h-100">
                            <div class="card-body text-center p-3">
                                <i class="fas fa-toggle-on text-success mb-2" style="font-size: 1.5rem;"></i>
                                <h6 class="mb-1">صح/خطأ</h6>
                                <small class="text-muted">أسئلة ثنائية الخيار</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card border-0 bg-light h-100">
                            <div class="card-body text-center p-3">
                                <i class="fas fa-edit text-info mb-2" style="font-size: 1.5rem;"></i>
                                <h6 class="mb-1">إجابة قصيرة</h6>
                                <small class="text-muted">إجابات نصية مختصرة</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card border-0 bg-light h-100">
                            <div class="card-body text-center p-3">
                                <i class="fas fa-file-alt text-warning mb-2" style="font-size: 1.5rem;"></i>
                                <h6 class="mb-1">مقال</h6>
                                <small class="text-muted">إجابات مفصلة وطويلة</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php else: ?>
        <div id="questionsList">
            <?php foreach ($questions as $index => $question): ?>
            <div class="question-item border-bottom p-4" data-question-id="<?php echo $question['id']; ?>">
                <div class="d-flex justify-content-between align-items-start">
                    <div class="flex-grow-1">
                        <div class="d-flex align-items-center mb-2">
                            <span class="badge bg-primary me-2">السؤال <?php echo $index + 1; ?></span>
                            <span class="badge bg-secondary me-2"><?php echo $question['points']; ?> نقطة</span>
                            <span class="badge bg-info">
                                <?php 
                                $types = [
                                    'multiple_choice' => 'اختيار متعدد',
                                    'true_false' => 'صح/خطأ',
                                    'short_answer' => 'إجابة قصيرة',
                                    'essay' => 'مقال'
                                ];
                                echo $types[$question['question_type']] ?? $question['question_type'];
                                ?>
                            </span>
                        </div>
                        
                        <h6 class="mb-3"><?php echo htmlspecialchars($question['question_text']); ?></h6>
                        
                        <?php if ($question['question_type'] === 'multiple_choice' && !empty($question['options'])): ?>
                        <div class="options-list">
                            <?php foreach ($question['options'] as $option): ?>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="radio" disabled 
                                       <?php echo $option['is_correct'] ? 'checked' : ''; ?>>
                                <label class="form-check-label <?php echo $option['is_correct'] ? 'text-success fw-bold' : ''; ?>">
                                    <?php echo htmlspecialchars($option['option_text']); ?>
                                    <?php if ($option['is_correct']): ?>
                                    <i class="fas fa-check text-success ms-1"></i>
                                    <?php endif; ?>
                                </label>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        <?php endif; ?>
                        
                        <?php if ($question['explanation']): ?>
                        <div class="mt-3 p-3 bg-light rounded">
                            <small class="text-muted d-block mb-1">التفسير:</small>
                            <small><?php echo htmlspecialchars($question['explanation']); ?></small>
                        </div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="ms-3">
                        <div class="btn-group-vertical" role="group">
                            <button class="btn btn-sm btn-outline-primary" 
                                    onclick="editQuestion(<?php echo $question['id']; ?>)" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-warning" 
                                    onclick="duplicateQuestion(<?php echo $question['id']; ?>)" title="نسخ">
                                <i class="fas fa-copy"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger" 
                                    onclick="deleteQuestion(<?php echo $question['id']; ?>)" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- Modal إضافة سؤال جديد -->
<div class="modal fade" id="addQuestionModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة سؤال جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="addQuestionForm">
                <div class="modal-body">
                    <input type="hidden" name="action" value="add_question">

                    <div class="row">
                        <div class="col-md-8 mb-3">
                            <label for="question_text" class="form-label">نص السؤال <span class="text-danger">*</span></label>
                            <textarea name="question_text" id="question_text" class="form-control" rows="3"
                                      placeholder="اكتب السؤال هنا..." required></textarea>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="question_type" class="form-label">نوع السؤال</label>
                            <select name="question_type" id="question_type" class="form-select" onchange="toggleQuestionType()">
                                <option value="multiple_choice">اختيار متعدد</option>
                                <option value="true_false">صح/خطأ</option>
                                <option value="short_answer">إجابة قصيرة</option>
                                <option value="essay">مقال</option>
                            </select>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="points" class="form-label">النقاط</label>
                            <input type="number" name="points" id="points" class="form-control"
                                   value="1" min="0.5" step="0.5" required>
                        </div>
                    </div>

                    <!-- خيارات الاختيار المتعدد -->
                    <div id="multipleChoiceOptions">
                        <label class="form-label">الخيارات <span class="text-danger">*</span></label>
                        <div id="optionsList">
                            <div class="option-item mb-2">
                                <div class="input-group">
                                    <div class="input-group-text">
                                        <input class="form-check-input" type="checkbox" name="correct_options[]" value="0">
                                    </div>
                                    <input type="text" name="options[]" class="form-control" placeholder="الخيار الأول">
                                    <button type="button" class="btn btn-outline-danger" onclick="removeOption(this)" disabled>
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="option-item mb-2">
                                <div class="input-group">
                                    <div class="input-group-text">
                                        <input class="form-check-input" type="checkbox" name="correct_options[]" value="1">
                                    </div>
                                    <input type="text" name="options[]" class="form-control" placeholder="الخيار الثاني">
                                    <button type="button" class="btn btn-outline-danger" onclick="removeOption(this)" disabled>
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="addOption()">
                            <i class="fas fa-plus me-1"></i>إضافة خيار
                        </button>
                        <small class="form-text text-muted d-block mt-2">
                            حدد الخيارات الصحيحة بوضع علامة ✓
                        </small>
                    </div>

                    <div class="row mt-3">
                        <div class="col-md-12 mb-3">
                            <label for="explanation" class="form-label">التفسير (اختياري)</label>
                            <textarea name="explanation" id="explanation" class="form-control" rows="2"
                                      placeholder="تفسير الإجابة الصحيحة..."></textarea>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">إضافة السؤال</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal حذف سؤال -->
<div class="modal fade" id="deleteQuestionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title text-danger">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="delete_question">
                    <input type="hidden" name="question_id" id="deleteQuestionId">

                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        هل أنت متأكد من حذف هذا السؤال؟
                    </div>

                    <p class="text-muted">
                        سيتم حذف السؤال وجميع خياراته نهائياً. هذا الإجراء لا يمكن التراجع عنه.
                    </p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-danger">تأكيد الحذف</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal دليل الأسئلة -->
<div class="modal fade" id="questionGuideModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">
                    <i class="fas fa-lightbulb me-2"></i>
                    دليل إنشاء الأسئلة
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary mb-3">
                            <i class="fas fa-check-circle me-2"></i>
                            أسئلة الاختيار المتعدد
                        </h6>
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <i class="fas fa-arrow-left text-primary me-2"></i>
                                أضف 2-5 خيارات للسؤال
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-arrow-left text-primary me-2"></i>
                                حدد الإجابة/الإجابات الصحيحة
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-arrow-left text-primary me-2"></i>
                                اجعل الخيارات متشابهة في الطول
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-arrow-left text-primary me-2"></i>
                                تجنب خيارات "جميع ما سبق"
                            </li>
                        </ul>

                        <h6 class="text-success mb-3 mt-4">
                            <i class="fas fa-toggle-on me-2"></i>
                            أسئلة صح/خطأ
                        </h6>
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <i class="fas fa-arrow-left text-success me-2"></i>
                                اكتب عبارات واضحة ومحددة
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-arrow-left text-success me-2"></i>
                                تجنب الكلمات المطلقة (دائماً، أبداً)
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-arrow-left text-success me-2"></i>
                                اجعل العبارة تحتوي على فكرة واحدة
                            </li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-info mb-3">
                            <i class="fas fa-edit me-2"></i>
                            الأسئلة النصية
                        </h6>
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <i class="fas fa-arrow-left text-info me-2"></i>
                                حدد طول الإجابة المطلوبة
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-arrow-left text-info me-2"></i>
                                استخدم كلمات واضحة (اشرح، قارن، حلل)
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-arrow-left text-info me-2"></i>
                                حدد النقاط المطلوب تغطيتها
                            </li>
                        </ul>

                        <h6 class="text-warning mb-3 mt-4">
                            <i class="fas fa-star me-2"></i>
                            نصائح عامة
                        </h6>
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <i class="fas fa-arrow-left text-warning me-2"></i>
                                اكتب أسئلة واضحة ومباشرة
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-arrow-left text-warning me-2"></i>
                                تنوع في مستوى الصعوبة
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-arrow-left text-warning me-2"></i>
                                أضف تفسيرات للإجابات
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-arrow-left text-warning me-2"></i>
                                راجع الأسئلة قبل النشر
                            </li>
                        </ul>
                    </div>
                </div>

                <div class="alert alert-info mt-4">
                    <h6 class="alert-heading">
                        <i class="fas fa-graduation-cap me-2"></i>
                        أمثلة على أسئلة جيدة
                    </h6>
                    <div class="row">
                        <div class="col-md-6">
                            <strong>اختيار متعدد:</strong>
                            <p class="mb-2 small">"ما هي عاصمة فرنسا؟"</p>
                            <ul class="small mb-3">
                                <li>لندن</li>
                                <li>برلين</li>
                                <li>باريس ✓</li>
                                <li>روما</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <strong>صح/خطأ:</strong>
                            <p class="mb-2 small">"تحتوي الخلية النباتية على جدار خلوي." ✓</p>

                            <strong>إجابة قصيرة:</strong>
                            <p class="mb-0 small">"اذكر ثلاث فوائد للرياضة."</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary" data-bs-dismiss="modal" data-bs-toggle="modal" data-bs-target="#addQuestionModal">
                    <i class="fas fa-plus me-1"></i>إضافة سؤال الآن
                </button>
            </div>
        </div>
    </div>
</div>

<style>
/* تحسينات CSS للصفحة */
.empty-state-container {
    padding: 2rem;
}

.empty-state-icon {
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.question-item {
    transition: all 0.3s ease;
}

.question-item:hover {
    background-color: rgba(0,123,255,0.05);
}

.btn-group-vertical .btn {
    transition: all 0.2s ease;
}

.btn-group-vertical .btn:hover {
    transform: scale(1.05);
}

.card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.option-item {
    transition: all 0.3s ease;
}

.option-item:hover {
    background-color: rgba(0,0,0,0.02);
    border-radius: 5px;
}

/* تحسين النوافذ المنبثقة */
.modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.modal-header {
    border-radius: 15px 15px 0 0;
    border-bottom: none;
}

.modal-footer {
    border-top: none;
    border-radius: 0 0 15px 15px;
}

/* تحسين للشاشات الصغيرة */
@media (max-width: 768px) {
    .empty-state-container {
        padding: 1rem;
    }

    .btn-lg {
        font-size: 1rem;
        padding: 0.75rem 1.5rem;
    }

    .btn-group-vertical {
        flex-direction: row;
    }

    .btn-group-vertical .btn {
        margin: 0 2px;
    }
}
</style>

<script src="https://cdn.jsdelivr.net/npm/sortablejs@latest/Sortable.min.js"></script>
<script>
let optionCounter = 2;

// دليل الأسئلة
function showQuestionGuide() {
    const guideModal = new bootstrap.Modal(document.getElementById('questionGuideModal'));
    guideModal.show();
}

// تأثيرات بصرية عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تأثير ترحيب للاختبار الجديد
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('new') === '1') {
        // إزالة معامل "new" من الرابط
        const newUrl = window.location.pathname + '?quiz_id=' + urlParams.get('quiz_id');
        window.history.replaceState({}, '', newUrl);

        // تأثير ترحيب
        setTimeout(() => {
            const welcomeAlert = document.querySelector('.alert-info');
            if (welcomeAlert) {
                welcomeAlert.style.animation = 'slideInDown 0.5s ease-out';
            }
        }, 500);
    }

    // تأثيرات للأزرار
    const buttons = document.querySelectorAll('.btn-primary, .btn-lg');
    buttons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
            this.style.boxShadow = '0 4px 15px rgba(0,123,255,0.3)';
        });

        button.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '';
        });
    });

    // تحسين تجربة النموذج
    const addQuestionModal = document.getElementById('addQuestionModal');
    if (addQuestionModal) {
        addQuestionModal.addEventListener('shown.bs.modal', function() {
            const firstInput = this.querySelector('#question_text');
            if (firstInput) {
                firstInput.focus();
            }
        });

        addQuestionModal.addEventListener('hidden.bs.modal', function() {
            const form = this.querySelector('form');
            if (form) {
                form.reset();
                optionCounter = 2;
                // إعادة تعيين الخيارات
                const optionsList = document.getElementById('optionsList');
                if (optionsList) {
                    optionsList.innerHTML = `
                        <div class="option-item mb-2">
                            <div class="input-group">
                                <div class="input-group-text">
                                    <input class="form-check-input" type="checkbox" name="correct_options[]" value="0">
                                </div>
                                <input type="text" name="options[]" class="form-control" placeholder="الخيار الأول">
                                <button type="button" class="btn btn-outline-danger" onclick="removeOption(this)" disabled>
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                        <div class="option-item mb-2">
                            <div class="input-group">
                                <div class="input-group-text">
                                    <input class="form-check-input" type="checkbox" name="correct_options[]" value="1">
                                </div>
                                <input type="text" name="options[]" class="form-control" placeholder="الخيار الثاني">
                                <button type="button" class="btn btn-outline-danger" onclick="removeOption(this)" disabled>
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    `;
                }
                toggleQuestionType();
            }
        });
    }
});

// إضافة CSS للتأثيرات
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInDown {
        from {
            transform: translateY(-100%);
            opacity: 0;
        }
        to {
            transform: translateY(0);
            opacity: 1;
        }
    }

    .btn {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
`;
document.head.appendChild(style);

// تبديل نوع السؤال
function toggleQuestionType() {
    const questionType = document.getElementById('question_type').value;
    const multipleChoiceOptions = document.getElementById('multipleChoiceOptions');

    if (questionType === 'multiple_choice') {
        multipleChoiceOptions.style.display = 'block';
    } else {
        multipleChoiceOptions.style.display = 'none';
    }
}

// إضافة خيار جديد
function addOption() {
    const optionsList = document.getElementById('optionsList');
    const optionItem = document.createElement('div');
    optionItem.className = 'option-item mb-2';
    optionItem.innerHTML = `
        <div class="input-group">
            <div class="input-group-text">
                <input class="form-check-input" type="checkbox" name="correct_options[]" value="${optionCounter}">
            </div>
            <input type="text" name="options[]" class="form-control" placeholder="خيار جديد">
            <button type="button" class="btn btn-outline-danger" onclick="removeOption(this)">
                <i class="fas fa-trash"></i>
            </button>
        </div>
    `;

    optionsList.appendChild(optionItem);
    optionCounter++;
    updateRemoveButtons();
}

// إزالة خيار
function removeOption(button) {
    button.closest('.option-item').remove();
    updateRemoveButtons();
}

// تحديث أزرار الإزالة
function updateRemoveButtons() {
    const removeButtons = document.querySelectorAll('#optionsList .btn-outline-danger');
    removeButtons.forEach(button => {
        button.disabled = removeButtons.length <= 2;
    });
}

// حذف سؤال
function deleteQuestion(questionId) {
    document.getElementById('deleteQuestionId').value = questionId;
    new bootstrap.Modal(document.getElementById('deleteQuestionModal')).show();
}

// تعديل سؤال
function editQuestion(questionId) {
    window.location.href = `edit-question.php?id=${questionId}`;
}

// نسخ سؤال
function duplicateQuestion(questionId) {
    if (confirm('هل تريد إنشاء نسخة من هذا السؤال؟')) {
        window.location.href = `duplicate-question.php?id=${questionId}`;
    }
}

// تفعيل إعادة الترتيب
function enableSorting() {
    const questionsList = document.getElementById('questionsList');

    if (questionsList.classList.contains('sortable-enabled')) {
        // إلغاء تفعيل الترتيب
        questionsList.classList.remove('sortable-enabled');
        document.querySelector('[onclick="enableSorting()"]').innerHTML = '<i class="fas fa-sort me-1"></i>إعادة ترتيب';
        return;
    }

    // تفعيل الترتيب
    questionsList.classList.add('sortable-enabled');
    document.querySelector('[onclick="enableSorting()"]').innerHTML = '<i class="fas fa-save me-1"></i>حفظ الترتيب';

    Sortable.create(questionsList, {
        animation: 150,
        ghostClass: 'sortable-ghost',
        onEnd: function(evt) {
            saveQuestionOrder();
        }
    });
}

// حفظ ترتيب الأسئلة
function saveQuestionOrder() {
    const questionItems = document.querySelectorAll('.question-item');
    const orders = {};

    questionItems.forEach((item, index) => {
        const questionId = item.dataset.questionId;
        orders[questionId] = index + 1;
    });

    // إرسال البيانات عبر AJAX
    const formData = new FormData();
    formData.append('action', 'update_order');
    Object.keys(orders).forEach(questionId => {
        formData.append(`question_orders[${questionId}]`, orders[questionId]);
    });

    fetch(window.location.href, {
        method: 'POST',
        body: formData
    })
    .then(response => response.text())
    .then(() => {
        // إعادة تحميل الصفحة لإظهار الترتيب الجديد
        location.reload();
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ أثناء حفظ الترتيب');
    });
}

// معاينة الاختبار
function previewQuiz() {
    window.open(`quiz-preview.php?id=<?php echo $quiz_id; ?>`, '_blank');
}

// التحقق من صحة النموذج
document.getElementById('addQuestionForm').addEventListener('submit', function(e) {
    const questionType = document.getElementById('question_type').value;

    if (questionType === 'multiple_choice') {
        const options = document.querySelectorAll('input[name="options[]"]');
        const correctOptions = document.querySelectorAll('input[name="correct_options[]"]:checked');

        // التحقق من وجود خيارين على الأقل
        let filledOptions = 0;
        options.forEach(option => {
            if (option.value.trim() !== '') {
                filledOptions++;
            }
        });

        if (filledOptions < 2) {
            e.preventDefault();
            alert('يجب إضافة خيارين على الأقل');
            return false;
        }

        // التحقق من وجود إجابة صحيحة واحدة على الأقل
        if (correctOptions.length === 0) {
            e.preventDefault();
            alert('يجب تحديد إجابة صحيحة واحدة على الأقل');
            return false;
        }
    }
});

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    toggleQuestionType();
    updateRemoveButtons();

    // إضافة تأثيرات بصرية للأسئلة
    const questionItems = document.querySelectorAll('.question-item');
    questionItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.backgroundColor = 'rgba(0,123,255,0.05)';
        });

        item.addEventListener('mouseleave', function() {
            this.style.backgroundColor = '';
        });
    });
});
</script>

<style>
.question-item {
    transition: background-color 0.3s ease;
}

.question-item:hover {
    background-color: rgba(0,123,255,0.05);
}

.sortable-enabled .question-item {
    cursor: move;
    border: 2px dashed #dee2e6;
    margin-bottom: 0.5rem;
    border-radius: 0.375rem;
}

.sortable-ghost {
    opacity: 0.5;
    background-color: rgba(0,123,255,0.1);
}

.options-list .form-check-label {
    cursor: pointer;
}

.btn-group-vertical .btn {
    border-radius: 0.375rem;
    margin-bottom: 0.25rem;
}

.badge {
    font-size: 0.75rem;
}

.option-item .input-group {
    align-items: center;
}

.input-group-text {
    border: 1px solid #ced4da;
}

@media (max-width: 768px) {
    .btn-group-vertical {
        flex-direction: row;
    }

    .btn-group-vertical .btn {
        margin-bottom: 0;
        margin-right: 0.25rem;
    }

    .modal-dialog {
        margin: 0.5rem;
    }
}

/* تحسين عرض الخيارات */
.form-check-input:checked {
    background-color: #198754;
    border-color: #198754;
}

.text-success.fw-bold {
    font-weight: 600 !important;
}
</style>

<?php include 'includes/footer.php'; ?>
