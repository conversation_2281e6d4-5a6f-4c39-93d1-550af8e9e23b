# 🚀 دليل الإعداد السريع - Quick Setup Guide

## 📋 خطوات الإعداد

### 1. الإعداد التلقائي (الأسرع)
قم بزيارة أحد الروابط التالية لإعداد النظام تلقائياً:

```
http://localhost/Zoom/quick_setup.php
```
أو
```
http://localhost/Zoom/setup_system.php
```

### 2. الحسابات الافتراضية

بعد الإعداد، ستحصل على الحسابات التالية:

#### 👨‍💼 المدير
- **البريد الإلكتروني**: `<EMAIL>`
- **كلمة المرور**: `admin123`
- **الرابط**: [لوحة الإدارة](http://localhost/Zoom/admin/dashboard.php)

#### 👨‍🏫 المدرب التجريبي
- **الب<PERSON>يد الإلكتروني**: `<EMAIL>`
- **كلمة المرور**: `instructor123`
- **الرابط**: [لوحة المدرب](http://localhost/Zoom/instructor/dashboard.php)

#### 👨‍🎓 الطالب التجريبي
- **البريد الإلكتروني**: `<EMAIL>`
- **كلمة المرور**: `student123`
- **الرابط**: [لوحة الطالب](http://localhost/Zoom/student/dashboard.php)

### 3. الروابط المهمة

#### 🔐 تسجيل الدخول والتسجيل
- [تسجيل الدخول](http://localhost/Zoom/login.php)
- [تسجيل حساب جديد](http://localhost/Zoom/register.php)
- [فحص حالة التسجيل](http://localhost/Zoom/registration-status.php)

#### 📊 لوحات الإدارة
- [لوحة المدير](http://localhost/Zoom/admin/dashboard.php)
- [إدارة طلبات التسجيل](http://localhost/Zoom/admin/manage_registrations.php)
- [إدارة طلبات الانضمام](http://localhost/Zoom/admin/manage_join_requests.php)

## 🔧 المميزات الجديدة

### ✅ نظام التسجيل المحسن
- **طلبات الموافقة**: جميع التسجيلات الجديدة تحتاج موافقة المدير
- **فحص حالة التسجيل**: يمكن للمستخدمين فحص حالة طلباتهم
- **إشعارات تلقائية**: إشعار المستخدمين عند الموافقة أو الرفض

### 🛡️ الأمان المتقدم
- **قفل الحسابات**: بعد 5 محاولات فاشلة لمدة 30 دقيقة
- **تشفير كلمات المرور**: باستخدام bcrypt
- **حماية من الهجمات**: SQL Injection, XSS, CSRF

### 📱 واجهة محسنة
- **تصميم متجاوب**: يعمل على جميع الأجهزة
- **تأثيرات بصرية**: رسوم متحركة وتأثيرات جذابة
- **دعم عربي كامل**: RTL وخطوط عربية

## 🔄 تدفق التسجيل الجديد

### للمستخدمين الجدد:
1. **التسجيل**: ملء نموذج التسجيل
2. **الانتظار**: حالة "قيد المراجعة"
3. **الفحص**: استخدام صفحة فحص الحالة
4. **الموافقة**: تفعيل الحساب من المدير
5. **تسجيل الدخول**: الوصول للنظام

### للمديرين:
1. **مراجعة الطلبات**: في لوحة الإدارة
2. **الموافقة/الرفض**: مع إمكانية إضافة ملاحظات
3. **الموافقة الجماعية**: قبول جميع الطلبات دفعة واحدة

## 🛠️ استكشاف الأخطاء

### مشاكل شائعة:

#### ❌ خطأ في قاعدة البيانات
```
الحل: تأكد من تشغيل XAMPP/WAMP وأن MySQL يعمل
```

#### ❌ صفحة فارغة
```
الحل: تحقق من ملف config/database.php وتأكد من صحة البيانات
```

#### ❌ لا يمكن تسجيل الدخول
```
الحل: استخدم الحسابات الافتراضية المذكورة أعلاه
```

## 📞 الدعم

### 🔍 فحص السجلات
- تحقق من ملفات السجل في مجلد `logs/`
- راجع أخطاء PHP في لوحة التحكم

### 🔧 إعادة الإعداد
إذا واجهت مشاكل، يمكنك:
1. حذف قاعدة البيانات
2. تشغيل `quick_setup.php` مرة أخرى
3. استخدام الحسابات الافتراضية

## ⚠️ تذكيرات مهمة

### 🔒 الأمان
- **غيّر كلمات المرور الافتراضية فوراً**
- احذف ملفات الإعداد بعد الانتهاء:
  - `quick_setup.php`
  - `setup_system.php`

### 🚀 الإنتاج
- استخدم HTTPS
- قم بتحديث إعدادات قاعدة البيانات
- فعّل وضع الإنتاج في الإعدادات

## 📈 الخطوات التالية

بعد الإعداد الناجح:

1. **للمديرين**:
   - راجع طلبات التسجيل المعلقة
   - أضف مدربين جدد
   - أنشئ كورسات

2. **للمدربين**:
   - أنشئ كورسات جديدة
   - اجدول جلسات
   - راجع طلبات الانضمام

3. **للطلاب**:
   - تصفح الكورسات
   - اطلب الانضمام
   - احضر الجلسات

---

**🎉 مبروك! النظام جاهز للاستخدام**

للمساعدة الإضافية، راجع ملف `README.md` الأصلي أو تواصل مع فريق الدعم.
