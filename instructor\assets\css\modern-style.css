/* ===== Modern Instructor Dashboard Styles ===== */

:root {
    /* Primary Colors - Modern Gradient Palette */
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    --danger-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    
    /* Glass Effect Colors */
    --glass-bg: rgba(255, 255, 255, 0.25);
    --glass-border: rgba(255, 255, 255, 0.18);
    --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
    
    /* Dark Theme */
    --dark-bg: #0f0f23;
    --dark-card: #1a1a2e;
    --dark-text: #eee;
    
    /* Spacing */
    --border-radius: 20px;
    --border-radius-sm: 12px;
    --shadow-light: 0 4px 20px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 8px 30px rgba(0, 0, 0, 0.15);
    --shadow-heavy: 0 15px 50px rgba(0, 0, 0, 0.2);
    
    /* Animations */
    --transition-smooth: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    --transition-bounce: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* ===== Global Styles ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-attachment: fixed;
    min-height: 100vh;
    overflow-x: hidden;
}

/* Animated Background */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
    z-index: -1;
    animation: backgroundShift 20s ease-in-out infinite;
}

@keyframes backgroundShift {
    0%, 100% { transform: scale(1) rotate(0deg); }
    50% { transform: scale(1.1) rotate(5deg); }
}

/* ===== Modern Navbar ===== */
.instructor-navbar {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--glass-border);
    box-shadow: var(--glass-shadow);
    padding: 0;
    position: sticky;
    top: 0;
    z-index: 1000;
}

.navbar-brand-instructor {
    display: flex;
    align-items: center;
    gap: 15px;
    color: white !important;
    font-weight: 800;
    font-size: 1.4rem;
    text-decoration: none;
    padding: 1.2rem;
    transition: var(--transition-smooth);
}

.navbar-brand-instructor:hover {
    transform: scale(1.05);
    color: rgba(255,255,255,0.9) !important;
}

.brand-icon {
    width: 50px;
    height: 50px;
    background: var(--primary-gradient);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    box-shadow: var(--shadow-medium);
    animation: iconFloat 3s ease-in-out infinite;
}

@keyframes iconFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-5px); }
}

.nav-link-instructor {
    color: rgba(255,255,255,0.9) !important;
    font-weight: 600;
    padding: 1.2rem 1.8rem !important;
    transition: var(--transition-smooth);
    position: relative;
    display: flex;
    align-items: center;
    gap: 10px;
    border-radius: var(--border-radius-sm);
    margin: 0 5px;
}

.nav-link-instructor::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--primary-gradient);
    border-radius: var(--border-radius-sm);
    opacity: 0;
    transition: var(--transition-smooth);
    z-index: -1;
}

.nav-link-instructor:hover::before,
.nav-link-instructor.active::before {
    opacity: 0.2;
}

.nav-link-instructor:hover,
.nav-link-instructor.active {
    color: white !important;
    transform: translateY(-2px);
}

/* ===== Modern Cards ===== */
.modern-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    box-shadow: var(--glass-shadow);
    transition: var(--transition-smooth);
    overflow: hidden;
    position: relative;
}

.modern-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: var(--primary-gradient);
    transform: scaleX(0);
    transition: var(--transition-smooth);
}

.modern-card:hover::before {
    transform: scaleX(1);
}

.modern-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
}

/* ===== Stat Cards ===== */
.stat-card-modern {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    padding: 2rem;
    text-align: center;
    transition: var(--transition-bounce);
    position: relative;
    overflow: hidden;
    box-shadow: var(--glass-shadow);
}

.stat-card-modern::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(from 0deg, transparent, rgba(255,255,255,0.1), transparent);
    animation: rotate 4s linear infinite;
    opacity: 0;
    transition: var(--transition-smooth);
}

.stat-card-modern:hover::before {
    opacity: 1;
}

@keyframes rotate {
    100% { transform: rotate(360deg); }
}

.stat-card-modern:hover {
    transform: translateY(-15px) scale(1.02);
    box-shadow: 0 25px 70px rgba(0, 0, 0, 0.25);
}

.stat-icon-modern {
    width: 80px;
    height: 80px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: white;
    font-size: 2rem;
    position: relative;
    z-index: 2;
    transition: var(--transition-smooth);
}

.stat-icon-modern.bg-primary { background: var(--primary-gradient); }
.stat-icon-modern.bg-success { background: var(--success-gradient); }
.stat-icon-modern.bg-info { background: var(--warning-gradient); }
.stat-icon-modern.bg-warning { background: var(--danger-gradient); }

.stat-number-modern {
    font-size: 3rem;
    font-weight: 900;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 1rem;
    position: relative;
    z-index: 2;
}

.stat-label-modern {
    font-weight: 700;
    color: white;
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
    position: relative;
    z-index: 2;
}

.stat-detail-modern {
    font-size: 0.9rem;
    color: rgba(255,255,255,0.8);
    position: relative;
    z-index: 2;
}

/* ===== Modern Buttons ===== */
.btn-modern {
    background: var(--primary-gradient);
    border: none;
    border-radius: var(--border-radius-sm);
    color: white;
    font-weight: 600;
    padding: 12px 30px;
    transition: var(--transition-bounce);
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow: var(--shadow-medium);
}

.btn-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: var(--transition-smooth);
}

.btn-modern:hover::before {
    left: 100%;
}

.btn-modern:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
    color: white;
}

.btn-modern:active {
    transform: translateY(-1px);
}

/* ===== Welcome Section ===== */
.welcome-section-modern {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    padding: 3rem;
    margin-bottom: 3rem;
    position: relative;
    overflow: hidden;
    box-shadow: var(--glass-shadow);
}

.welcome-section-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="25" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="25" cy="75" r="1" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
    z-index: 1;
}

.welcome-section-modern > * {
    position: relative;
    z-index: 2;
}

.welcome-title {
    font-size: 2.5rem;
    font-weight: 900;
    color: white;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.welcome-subtitle {
    font-size: 1.2rem;
    color: rgba(255,255,255,0.9);
    margin-bottom: 0;
}

/* ===== Table Styles ===== */
.table-modern {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--glass-shadow);
}

.table-modern thead {
    background: rgba(255,255,255,0.1);
}

.table-modern th {
    border: none;
    color: white;
    font-weight: 700;
    padding: 1.5rem 1rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: 0.85rem;
}

.table-modern td {
    border: none;
    color: white;
    padding: 1.2rem 1rem;
    vertical-align: middle;
}

.table-modern tbody tr {
    border-bottom: 1px solid rgba(255,255,255,0.1);
    transition: var(--transition-smooth);
}

.table-modern tbody tr:hover {
    background: rgba(255,255,255,0.1);
    transform: scale(1.01);
}

/* ===== Badges ===== */
.badge-modern {
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: var(--shadow-light);
}

.badge-modern.bg-success { background: var(--success-gradient) !important; }
.badge-modern.bg-warning { background: var(--warning-gradient) !important; }
.badge-modern.bg-danger { background: var(--danger-gradient) !important; }
.badge-modern.bg-info { background: var(--primary-gradient) !important; }

/* ===== Animations ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.animate-fadeInUp {
    animation: fadeInUp 0.6s ease-out;
}

.animate-slideInRight {
    animation: slideInRight 0.6s ease-out;
}

.animate-pulse {
    animation: pulse 2s infinite;
}

/* ===== Responsive Design ===== */
@media (max-width: 768px) {
    .welcome-section-modern {
        padding: 2rem;
        text-align: center;
    }
    
    .welcome-title {
        font-size: 2rem;
    }
    
    .stat-card-modern {
        margin-bottom: 1.5rem;
    }
    
    .navbar-brand-instructor {
        font-size: 1.2rem;
    }
    
    .brand-icon {
        width: 40px;
        height: 40px;
    }
}

/* ===== Loading Animation ===== */
.loading-spinner {
    width: 50px;
    height: 50px;
    border: 3px solid rgba(255,255,255,0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* ===== Scrollbar Styling ===== */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255,255,255,0.1);
}

::-webkit-scrollbar-thumb {
    background: var(--primary-gradient);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-gradient);
}
