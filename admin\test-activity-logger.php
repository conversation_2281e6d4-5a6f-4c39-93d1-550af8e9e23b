<?php
require_once '../config/database.php';
require_once '../includes/activity_logger.php';

echo "<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>اختبار نظام تسجيل الأنشطة</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css' rel='stylesheet'>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f8f9fa; }
        .container { max-width: 1000px; margin: 30px auto; }
        .log-item { padding: 8px; margin: 3px 0; border-radius: 5px; font-size: 14px; }
        .log-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .log-error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .log-info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .log-warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
    </style>
</head>
<body>
<div class='container'>
    <div class='card'>
        <div class='card-header bg-info text-white'>
            <h4 class='mb-0'><i class='fas fa-vial me-2'></i>اختبار نظام تسجيل الأنشطة</h4>
        </div>
        <div class='card-body'>";

function logMessage($message, $type = 'info') {
    $icon = $type === 'success' ? 'check-circle' : ($type === 'error' ? 'exclamation-circle' : ($type === 'warning' ? 'exclamation-triangle' : 'info-circle'));
    echo "<div class='log-item log-$type'><i class='fas fa-$icon me-2'></i>$message</div>";
    flush();
    ob_flush();
}

try {
    logMessage('🧪 بدء اختبار نظام تسجيل الأنشطة...', 'info');
    
    // اختبار 1: تسجيل محاولة دخول ناجحة
    logMessage('=== اختبار تسجيل محاولة الدخول ===', 'info');
    
    $test_email = '<EMAIL>';
    $result1 = logLoginAttempt($test_email, true);
    
    if ($result1) {
        logMessage('✓ تم تسجيل محاولة دخول ناجحة', 'success');
    } else {
        logMessage('❌ فشل في تسجيل محاولة الدخول', 'error');
    }
    
    // اختبار 2: تسجيل محاولة دخول فاشلة
    $result2 = logLoginAttempt($test_email, false, 'كلمة مرور خاطئة');
    
    if ($result2) {
        logMessage('✓ تم تسجيل محاولة دخول فاشلة', 'success');
    } else {
        logMessage('❌ فشل في تسجيل محاولة الدخول الفاشلة', 'error');
    }
    
    // اختبار 3: تسجيل نشاط مستخدم
    logMessage('=== اختبار تسجيل نشاط المستخدم ===', 'info');
    
    $result3 = logUserActivity('test_action', 'هذا اختبار لتسجيل نشاط المستخدم', 1);
    
    if ($result3) {
        logMessage('✓ تم تسجيل نشاط المستخدم', 'success');
    } else {
        logMessage('❌ فشل في تسجيل نشاط المستخدم', 'error');
    }
    
    // اختبار 4: اختبار دالة logActivity
    logMessage('=== اختبار دالة logActivity ===', 'info');
    
    $result4 = logActivity(1, 'test_log_activity', 'اختبار دالة logActivity');
    
    if ($result4) {
        logMessage('✓ تعمل دالة logActivity بشكل صحيح', 'success');
    } else {
        logMessage('❌ دالة logActivity لا تعمل', 'error');
    }
    
    // اختبار 5: اختبار الدوال المتخصصة
    logMessage('=== اختبار الدوال المتخصصة ===', 'info');
    
    $result5a = logAdminActivity('admin_test', 'اختبار نشاط المدير');
    $result5b = logCourseActivity('course_test', 123, 'اختبار نشاط الكورس');
    $result5c = logSessionActivity('session_test', 456, 'اختبار نشاط الجلسة');
    $result5d = logStudentActivity('student_test', 789, 'اختبار نشاط الطالب');
    
    if ($result5a && $result5b && $result5c && $result5d) {
        logMessage('✓ جميع الدوال المتخصصة تعمل بشكل صحيح', 'success');
    } else {
        logMessage('⚠ بعض الدوال المتخصصة قد لا تعمل بشكل صحيح', 'warning');
    }
    
    // اختبار 6: جلب الأنشطة الحديثة
    logMessage('=== اختبار جلب الأنشطة ===', 'info');
    
    $activities = getRecentActivities(5);
    logMessage('عدد الأنشطة المسترجعة: ' . count($activities), 'info');
    
    foreach ($activities as $activity) {
        logMessage("• {$activity['action']}: {$activity['description']} ({$activity['created_at']})", 'info');
    }
    
    // اختبار 7: جلب محاولات تسجيل الدخول
    logMessage('=== اختبار جلب محاولات تسجيل الدخول ===', 'info');
    
    $login_attempts = getLoginAttempts(5);
    logMessage('عدد محاولات تسجيل الدخول: ' . count($login_attempts), 'info');
    
    foreach ($login_attempts as $attempt) {
        $status = $attempt['success'] ? 'ناجحة' : 'فاشلة';
        logMessage("• {$attempt['email']}: $status ({$attempt['attempt_time']})", 'info');
    }
    
    // اختبار 8: إحصائيات الأنشطة
    logMessage('=== إحصائيات الأنشطة ===', 'info');
    
    $stats = getActivityStats();
    
    if (!empty($stats)) {
        logMessage("إجمالي الأنشطة: {$stats['total_activities']}", 'info');
        logMessage("أنشطة اليوم: {$stats['today_activities']}", 'info');
        logMessage("تسجيلات دخول ناجحة: {$stats['successful_logins']}", 'info');
        logMessage("تسجيلات دخول فاشلة: {$stats['failed_logins']}", 'info');
        
        if (!empty($stats['top_actions'])) {
            logMessage('أكثر الأنشطة:', 'info');
            foreach ($stats['top_actions'] as $action) {
                logMessage("  - {$action['action']}: {$action['count']} مرة", 'info');
            }
        }
    }
    
    // التحقق من الجداول
    logMessage('=== التحقق من الجداول ===', 'info');
    
    $tables = ['activity_logs', 'login_attempts'];
    foreach ($tables as $table) {
        $stmt = $conn->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            $count = $conn->query("SELECT COUNT(*) FROM $table")->fetchColumn();
            logMessage("✓ جدول $table موجود ويحتوي على $count سجل", 'success');
        } else {
            logMessage("❌ جدول $table غير موجود", 'error');
        }
    }
    
    logMessage('🎉 انتهى اختبار نظام تسجيل الأنشطة!', 'success');
    
} catch (Exception $e) {
    logMessage('❌ خطأ في الاختبار: ' . $e->getMessage(), 'error');
}

echo "
        </div>
        <div class='mt-4 text-center'>
            <div class='alert alert-info'>
                <h5><i class='fas fa-info-circle me-2'></i>انتهى الاختبار</h5>
                <p class='mb-0'>تم اختبار جميع وظائف نظام تسجيل الأنشطة</p>
            </div>
            <a href='../login.php' class='btn btn-primary btn-lg me-2'>
                <i class='fas fa-sign-in-alt me-2'></i>اختبار تسجيل الدخول
            </a>
            <a href='dashboard.php' class='btn btn-success me-2'>
                <i class='fas fa-tachometer-alt me-2'></i>لوحة التحكم
            </a>
            <a href='activity-logs.php' class='btn btn-info'>
                <i class='fas fa-list me-2'></i>سجل الأنشطة
            </a>
        </div>
    </div>
</div>
</body>
</html>";
?>
