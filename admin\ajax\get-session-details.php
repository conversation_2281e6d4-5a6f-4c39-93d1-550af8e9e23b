<?php
require_once '../../includes/session_config.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';
require_once '../../includes/security.php';

// التحقق من صلاحيات المدير
if (!isLoggedIn() || !isAdmin()) {
    echo json_encode(['success' => false, 'message' => 'غير مصرح لك بهذا الإجراء']);
    exit;
}

$session_id = $_GET['session_id'] ?? 0;

if (!$session_id) {
    echo json_encode(['success' => false, 'message' => 'معرف الجلسة مطلوب']);
    exit;
}

try {
    // جلب تفاصيل الجلسة
    $stmt = $conn->prepare("
        SELECT s.*,
               COALESCE((SELECT COUNT(*) FROM session_attendance WHERE session_id = s.id), 0) as attendance_count
        FROM sessions s
        WHERE s.id = ?
    ");
    $stmt->execute([$session_id]);
    $session = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$session) {
        throw new Exception('الجلسة غير موجودة');
    }
    
    // تنسيق التاريخ
    $session['session_date'] = date('Y-m-d H:i', strtotime($session['session_date']));
    
    // ترجمة الحالة
    $status_text = [
        'scheduled' => 'مجدولة',
        'live' => 'مباشرة',
        'completed' => 'مكتملة',
        'cancelled' => 'ملغية'
    ];
    $session['status'] = $status_text[$session['status']] ?? $session['status'];
    
    echo json_encode([
        'success' => true,
        'session' => $session
    ]);
    
} catch (Exception $e) {
    error_log("Error getting session details: " . $e->getMessage());
    echo json_encode([
        'success' => false, 
        'message' => 'حدث خطأ في جلب تفاصيل الجلسة: ' . $e->getMessage()
    ]);
}
?>
