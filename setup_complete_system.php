<?php
/**
 * إعداد النظام الكامل مع البيانات والأنشطة
 * Complete System Setup with Data and Activities
 */

session_start();
require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>إعداد النظام الكامل</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<style>body{font-family:'Segoe UI',Tahoma,Geneva,Verdana,sans-serif;}</style>";
echo "</head>";
echo "<body>";
echo "<div class='container mt-4'>";
echo "<h2>🚀 إعداد النظام الكامل</h2>";

try {
    // 1. إنشاء جدول activity_logs
    echo "<h4>📋 إنشاء جدول سجل الأنشطة...</h4>";
    $conn->exec("
        CREATE TABLE IF NOT EXISTS activity_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT DEFAULT NULL,
            action VARCHAR(255) NOT NULL,
            description TEXT,
            ip_address VARCHAR(45),
            user_agent TEXT,
            additional_data JSON DEFAULT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
            INDEX idx_user_id (user_id),
            INDEX idx_action (action),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "<div class='alert alert-success'>✅ تم إنشاء جدول activity_logs</div>";

    // 2. إنشاء جدول login_attempts
    echo "<h4>🔐 إنشاء جدول محاولات تسجيل الدخول...</h4>";
    $conn->exec("
        CREATE TABLE IF NOT EXISTS login_attempts (
            id INT AUTO_INCREMENT PRIMARY KEY,
            email VARCHAR(255) NOT NULL,
            ip_address VARCHAR(45) NOT NULL,
            user_agent TEXT,
            success BOOLEAN DEFAULT FALSE,
            failure_reason VARCHAR(255) DEFAULT NULL,
            attempt_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            
            INDEX idx_email (email),
            INDEX idx_ip (ip_address),
            INDEX idx_time (attempt_time),
            INDEX idx_success (success)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "<div class='alert alert-success'>✅ تم إنشاء جدول login_attempts</div>";

    // 3. إنشاء جدول join_requests
    echo "<h4>📝 إنشاء جدول طلبات الانضمام...</h4>";
    $conn->exec("
        CREATE TABLE IF NOT EXISTS join_requests (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            email VARCHAR(255) NOT NULL UNIQUE,
            phone VARCHAR(20),
            course_interest TEXT,
            message TEXT,
            status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
            assigned_course_id INT DEFAULT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            
            FOREIGN KEY (assigned_course_id) REFERENCES courses(id) ON DELETE SET NULL,
            INDEX idx_status (status),
            INDEX idx_email (email),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "<div class='alert alert-success'>✅ تم إنشاء جدول join_requests</div>";

    // 4. إضافة مستخدمين إضافيين
    echo "<h4>👥 إضافة مستخدمين إضافيين...</h4>";
    $users = [
        ['أحمد محمد', '<EMAIL>', 'instructor', '0501234567'],
        ['فاطمة علي', '<EMAIL>', 'instructor', '0507654321'],
        ['محمد سالم', '<EMAIL>', 'student', '0509876543'],
        ['نورا أحمد', '<EMAIL>', 'student', '0502468135'],
        ['خالد عبدالله', '<EMAIL>', 'student', '0508642097'],
        ['سارة محمد', '<EMAIL>', 'student', '0503691472']
    ];

    $insertUser = $conn->prepare("
        INSERT IGNORE INTO users (name, email, password, role, phone, status) 
        VALUES (?, ?, ?, ?, ?, 'active')
    ");

    foreach ($users as $user) {
        $hashedPassword = password_hash('123456', PASSWORD_DEFAULT);
        $insertUser->execute([$user[0], $user[1], $hashedPassword, $user[2], $user[3]]);
    }
    echo "<div class='alert alert-success'>✅ تم إضافة " . count($users) . " مستخدم</div>";

    // 5. إضافة كورسات
    echo "<h4>📚 إضافة كورسات...</h4>";
    $courses = [
        ['تطوير تطبيقات الويب بـ PHP', 'تعلم تطوير تطبيقات الويب الحديثة باستخدام PHP و MySQL', 30],
        ['أساسيات التصميم الجرافيكي', 'تعلم أساسيات التصميم الجرافيكي باستخدام Adobe Photoshop', 25],
        ['التسويق الرقمي', 'استراتيجيات التسويق الرقمي ووسائل التواصل الاجتماعي', 20],
        ['إدارة المشاريع', 'تعلم إدارة المشاريع الاحترافية وأدوات إدارة الفرق', 35],
        ['اللغة الإنجليزية للأعمال', 'تطوير مهارات اللغة الإنجليزية في بيئة العمل', 40]
    ];

    $insertCourse = $conn->prepare("
        INSERT IGNORE INTO courses (title, description, max_students, instructor_id, status) 
        VALUES (?, ?, ?, 1, 'active')
    ");

    foreach ($courses as $course) {
        $insertCourse->execute([$course[0], $course[1], $course[2]]);
    }
    echo "<div class='alert alert-success'>✅ تم إضافة " . count($courses) . " كورس</div>";

    // 6. إضافة طلبات انضمام
    echo "<h4>📋 إضافة طلبات انضمام...</h4>";
    $joinRequests = [
        ['علي أحمد السالم', '<EMAIL>', '0501112233', 'تطوير تطبيقات الويب', 'أرغب في تعلم تطوير المواقع الإلكترونية', 'pending'],
        ['مريم عبدالله', '<EMAIL>', '0502223344', 'التصميم الجرافيكي', 'أحب التصميم وأريد تطوير مهاراتي', 'pending'],
        ['يوسف محمد', '<EMAIL>', '0503334455', 'التسويق الرقمي', 'أعمل في التسويق وأريد تطوير خبراتي', 'approved'],
        ['هند سالم', '<EMAIL>', '0504445566', 'إدارة المشاريع', 'أريد تعلم إدارة المشاريع بطريقة احترافية', 'pending'],
        ['عمر خالد', '<EMAIL>', '0505556677', 'اللغة الإنجليزية', 'أحتاج تحسين لغتي الإنجليزية للعمل', 'approved'],
        ['ليلى أحمد', '<EMAIL>', '0506667788', 'تطوير تطبيقات الويب', 'أريد تغيير مجال عملي إلى البرمجة', 'rejected'],
        ['سعد محمد', '<EMAIL>', '0507778899', 'التصميم الجرافيكي', 'أعمل في الإعلان وأحتاج مهارات التصميم', 'pending'],
        ['نادية علي', '<EMAIL>', '0508889900', 'التسويق الرقمي', 'أريد بدء عمل تجاري إلكتروني', 'pending']
    ];

    $insertJoinRequest = $conn->prepare("
        INSERT INTO join_requests (name, email, phone, course_interest, message, status) 
        VALUES (?, ?, ?, ?, ?, ?)
    ");

    foreach ($joinRequests as $request) {
        $insertJoinRequest->execute($request);
    }
    echo "<div class='alert alert-success'>✅ تم إضافة " . count($joinRequests) . " طلب انضمام</div>";

    // 7. إضافة أنشطة متنوعة
    echo "<h4>📊 إضافة أنشطة متنوعة...</h4>";
    
    // الحصول على معرفات المستخدمين
    $stmt = $conn->query("SELECT id, name, role FROM users");
    $users = $stmt->fetchAll();

    $activities = [
        ['login', 'تسجيل دخول ناجح للنظام'],
        ['logout', 'تسجيل خروج من النظام'],
        ['profile_view', 'عرض الملف الشخصي'],
        ['dashboard_view', 'عرض لوحة التحكم'],
        ['join_request_submitted', 'تقديم طلب انضمام جديد'],
        ['join_request_approved', 'الموافقة على طلب انضمام'],
        ['join_request_rejected', 'رفض طلب انضمام'],
        ['course_view', 'عرض صفحة الكورسات'],
        ['session_view', 'عرض صفحة الجلسات'],
        ['student_management', 'إدارة الطلاب'],
        ['course_created', 'إنشاء كورس جديد'],
        ['session_created', 'إنشاء جلسة جديدة'],
        ['report_generated', 'إنشاء تقرير'],
        ['settings_updated', 'تحديث الإعدادات'],
        ['password_changed', 'تغيير كلمة المرور']
    ];

    $ipAddresses = ['*************', '*************', '*********', '************'];
    $userAgents = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36'
    ];

    $insertActivity = $conn->prepare("
        INSERT INTO activity_logs (user_id, action, description, ip_address, user_agent, created_at) 
        VALUES (?, ?, ?, ?, ?, ?)
    ");

    // إضافة 80 نشاط عشوائي
    for ($i = 0; $i < 80; $i++) {
        $user = $users[array_rand($users)];
        $activity = $activities[array_rand($activities)];
        $ip = $ipAddresses[array_rand($ipAddresses)];
        $userAgent = $userAgents[array_rand($userAgents)];
        $createdAt = date('Y-m-d H:i:s', strtotime('-' . rand(1, 30) . ' days -' . rand(0, 23) . ' hours'));
        
        $description = $activity[1] . ' - ' . $user['name'] . ' (' . $user['role'] . ')';
        
        $insertActivity->execute([
            $user['id'],
            $activity[0],
            $description,
            $ip,
            $userAgent,
            $createdAt
        ]);
    }
    echo "<div class='alert alert-success'>✅ تم إضافة 80 نشاط</div>";

    // 8. إضافة محاولات تسجيل دخول
    echo "<h4>🔐 إضافة محاولات تسجيل دخول...</h4>";
    
    $insertLoginAttempt = $conn->prepare("
        INSERT INTO login_attempts (email, ip_address, user_agent, success, failure_reason, attempt_time) 
        VALUES (?, ?, ?, ?, ?, ?)
    ");

    $failureReasons = ['كلمة مرور خاطئة', 'مستخدم غير موجود', 'حساب غير مفعل'];

    // إضافة 40 محاولة تسجيل دخول
    for ($i = 0; $i < 40; $i++) {
        $user = $users[array_rand($users)];
        $ip = $ipAddresses[array_rand($ipAddresses)];
        $userAgent = $userAgents[array_rand($userAgents)];
        $success = $i < 25; // 25 ناجحة، 15 فاشلة
        $failureReason = $success ? null : $failureReasons[array_rand($failureReasons)];
        $attemptTime = date('Y-m-d H:i:s', strtotime('-' . rand(1, 7) . ' days -' . rand(0, 23) . ' hours'));
        
        $insertLoginAttempt->execute([
            $user['email'],
            $ip,
            $userAgent,
            $success ? 1 : 0,
            $failureReason,
            $attemptTime
        ]);
    }
    echo "<div class='alert alert-success'>✅ تم إضافة 40 محاولة تسجيل دخول</div>";

    // 9. إحصائيات نهائية
    echo "<h4>📈 الإحصائيات النهائية:</h4>";
    
    $stats = [];
    $stmt = $conn->query("SELECT COUNT(*) FROM activity_logs");
    $stats['activities'] = $stmt->fetchColumn();
    
    $stmt = $conn->query("SELECT COUNT(*) FROM join_requests");
    $stats['join_requests'] = $stmt->fetchColumn();
    
    $stmt = $conn->query("SELECT COUNT(*) FROM join_requests WHERE status = 'pending'");
    $stats['pending_requests'] = $stmt->fetchColumn();
    
    $stmt = $conn->query("SELECT COUNT(*) FROM login_attempts WHERE success = 1");
    $stats['successful_logins'] = $stmt->fetchColumn();
    
    $stmt = $conn->query("SELECT COUNT(*) FROM users");
    $stats['total_users'] = $stmt->fetchColumn();
    
    $stmt = $conn->query("SELECT COUNT(*) FROM courses");
    $stats['total_courses'] = $stmt->fetchColumn();

    echo "<div class='row'>";
    echo "<div class='col-md-2'><div class='card bg-primary text-white'><div class='card-body text-center'><h3>{$stats['activities']}</h3><p>الأنشطة</p></div></div></div>";
    echo "<div class='col-md-2'><div class='card bg-warning text-white'><div class='card-body text-center'><h3>{$stats['join_requests']}</h3><p>طلبات الانضمام</p></div></div></div>";
    echo "<div class='col-md-2'><div class='card bg-danger text-white'><div class='card-body text-center'><h3>{$stats['pending_requests']}</h3><p>طلبات معلقة</p></div></div></div>";
    echo "<div class='col-md-2'><div class='card bg-success text-white'><div class='card-body text-center'><h3>{$stats['successful_logins']}</h3><p>تسجيلات دخول</p></div></div></div>";
    echo "<div class='col-md-2'><div class='card bg-info text-white'><div class='card-body text-center'><h3>{$stats['total_users']}</h3><p>المستخدمين</p></div></div></div>";
    echo "<div class='col-md-2'><div class='card bg-secondary text-white'><div class='card-body text-center'><h3>{$stats['total_courses']}</h3><p>الكورسات</p></div></div></div>";
    echo "</div>";

    echo "<div class='mt-4'>";
    echo "<a href='admin/activity-logs.php' class='btn btn-primary btn-lg me-2'>📋 سجل الأنشطة</a>";
    echo "<a href='admin/manage_join_requests.php' class='btn btn-warning btn-lg me-2'>📝 طلبات الانضمام</a>";
    echo "<a href='admin/dashboard.php' class='btn btn-success btn-lg me-2'>📊 لوحة التحكم</a>";
    echo "<a href='login.php' class='btn btn-info btn-lg'>🔐 تسجيل الدخول</a>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h5>❌ حدث خطأ</h5>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "</div>";
echo "</body>";
echo "</html>";
?>
