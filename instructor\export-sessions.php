<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات المدرب
if (!isLoggedIn() || !isInstructor()) {
    header('Location: ../login.php');
    exit;
}

$course_id = isset($_GET['course_id']) ? (int)$_GET['course_id'] : 0;
$instructor_id = $_SESSION['user_id'];
$format = isset($_GET['format']) ? $_GET['format'] : 'excel';

// التحقق من ملكية الكورس
try {
    $stmt = $conn->prepare("SELECT * FROM courses WHERE id = ? AND instructor_id = ?");
    $stmt->execute([$course_id, $instructor_id]);
    $course = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$course) {
        header('Location: courses.php');
        exit;
    }
} catch (PDOException $e) {
    header('Location: courses.php');
    exit;
}

// جلب بيانات الجلسات
$sessions = [];
try {
    $stmt = $conn->prepare("
        SELECT 
            s.*,
            (SELECT COUNT(*) FROM session_attendance WHERE session_id = s.id) as attendance_count,
            (SELECT COUNT(DISTINCT student_id) FROM course_enrollments WHERE course_id = s.course_id) as total_students
        FROM sessions s
        WHERE s.course_id = ?
        ORDER BY s.session_date ASC, s.start_time ASC
    ");
    $stmt->execute([$course_id]);
    $sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $sessions = [];
}

// إذا كان التصدير مطلوب
if (isset($_GET['export']) && $_GET['export'] === '1') {
    
    if ($format === 'excel') {
        // تصدير Excel
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment; filename="sessions_course_' . $course_id . '_' . date('Y-m-d') . '.xls"');
        header('Cache-Control: max-age=0');
        
        echo '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel">';
        echo '<head><meta charset="UTF-8"></head>';
        echo '<body>';
        echo '<table border="1">';
        echo '<tr>';
        echo '<th>رقم الجلسة</th>';
        echo '<th>عنوان الجلسة</th>';
        echo '<th>الوصف</th>';
        echo '<th>تاريخ الجلسة</th>';
        echo '<th>وقت البدء</th>';
        echo '<th>وقت الانتهاء</th>';
        echo '<th>الحالة</th>';
        echo '<th>عدد الحضور</th>';
        echo '<th>إجمالي الطلاب</th>';
        echo '<th>نسبة الحضور</th>';
        echo '<th>رابط Zoom</th>';
        echo '<th>Meeting ID</th>';
        echo '<th>تاريخ الإنشاء</th>';
        echo '</tr>';
        
        foreach ($sessions as $session) {
            $attendance_percentage = $session['total_students'] > 0 ? 
                round(($session['attendance_count'] / $session['total_students']) * 100, 1) : 0;
            
            echo '<tr>';
            echo '<td>' . $session['id'] . '</td>';
            echo '<td>' . htmlspecialchars($session['title']) . '</td>';
            echo '<td>' . htmlspecialchars($session['description'] ?? '') . '</td>';
            echo '<td>' . (isset($session['session_date']) ? $session['session_date'] : '') . '</td>';
            echo '<td>' . (isset($session['start_time']) ? $session['start_time'] : '') . '</td>';
            echo '<td>' . (isset($session['end_time']) ? $session['end_time'] : '') . '</td>';
            echo '<td>' . $session['status'] . '</td>';
            echo '<td>' . $session['attendance_count'] . '</td>';
            echo '<td>' . $session['total_students'] . '</td>';
            echo '<td>' . $attendance_percentage . '%</td>';
            echo '<td>' . htmlspecialchars($session['zoom_link'] ?? '') . '</td>';
            echo '<td>' . htmlspecialchars($session['meeting_id'] ?? '') . '</td>';
            echo '<td>' . (isset($session['created_at']) ? $session['created_at'] : '') . '</td>';
            echo '</tr>';
        }
        
        echo '</table>';
        echo '</body></html>';
        exit;
        
    } elseif ($format === 'csv') {
        // تصدير CSV
        header('Content-Type: text/csv; charset=UTF-8');
        header('Content-Disposition: attachment; filename="sessions_course_' . $course_id . '_' . date('Y-m-d') . '.csv"');
        header('Cache-Control: max-age=0');
        
        // إضافة BOM للدعم العربي
        echo "\xEF\xBB\xBF";
        
        $output = fopen('php://output', 'w');
        
        // العناوين
        fputcsv($output, [
            'رقم الجلسة',
            'عنوان الجلسة', 
            'الوصف',
            'تاريخ الجلسة',
            'وقت البدء',
            'وقت الانتهاء',
            'الحالة',
            'عدد الحضور',
            'إجمالي الطلاب',
            'نسبة الحضور',
            'رابط Zoom',
            'Meeting ID',
            'تاريخ الإنشاء'
        ]);
        
        // البيانات
        foreach ($sessions as $session) {
            $attendance_percentage = $session['total_students'] > 0 ? 
                round(($session['attendance_count'] / $session['total_students']) * 100, 1) : 0;
            
            fputcsv($output, [
                $session['id'],
                $session['title'],
                $session['description'] ?? '',
                isset($session['session_date']) ? $session['session_date'] : '',
                isset($session['start_time']) ? $session['start_time'] : '',
                isset($session['end_time']) ? $session['end_time'] : '',
                $session['status'],
                $session['attendance_count'],
                $session['total_students'],
                $attendance_percentage . '%',
                $session['zoom_link'] ?? '',
                $session['meeting_id'] ?? '',
                isset($session['created_at']) ? $session['created_at'] : ''
            ]);
        }
        
        fclose($output);
        exit;
        
    } elseif ($format === 'pdf') {
        // تصدير PDF (مبسط)
        header('Content-Type: application/pdf');
        header('Content-Disposition: attachment; filename="sessions_course_' . $course_id . '_' . date('Y-m-d') . '.pdf"');
        
        // هنا يمكن استخدام مكتبة PDF مثل TCPDF أو FPDF
        // للبساطة، سنعرض HTML يمكن طباعته
        echo '<!DOCTYPE html>';
        echo '<html dir="rtl" lang="ar">';
        echo '<head>';
        echo '<meta charset="UTF-8">';
        echo '<title>تقرير الجلسات</title>';
        echo '<style>';
        echo 'body { font-family: Arial, sans-serif; direction: rtl; }';
        echo 'table { width: 100%; border-collapse: collapse; margin: 20px 0; }';
        echo 'th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }';
        echo 'th { background-color: #f2f2f2; }';
        echo '.header { text-align: center; margin-bottom: 30px; }';
        echo '</style>';
        echo '</head>';
        echo '<body>';
        echo '<div class="header">';
        echo '<h1>تقرير جلسات الكورس</h1>';
        echo '<h2>' . htmlspecialchars($course['title']) . '</h2>';
        echo '<p>تاريخ التقرير: ' . date('Y-m-d H:i') . '</p>';
        echo '</div>';
        
        echo '<table>';
        echo '<thead>';
        echo '<tr>';
        echo '<th>رقم الجلسة</th>';
        echo '<th>عنوان الجلسة</th>';
        echo '<th>تاريخ الجلسة</th>';
        echo '<th>الوقت</th>';
        echo '<th>الحالة</th>';
        echo '<th>عدد الحضور</th>';
        echo '</tr>';
        echo '</thead>';
        echo '<tbody>';
        
        foreach ($sessions as $session) {
            echo '<tr>';
            echo '<td>' . $session['id'] . '</td>';
            echo '<td>' . htmlspecialchars($session['title']) . '</td>';
            echo '<td>' . (isset($session['session_date']) ? $session['session_date'] : '') . '</td>';
            echo '<td>' . (isset($session['start_time']) && isset($session['end_time']) ? 
                $session['start_time'] . ' - ' . $session['end_time'] : '') . '</td>';
            echo '<td>' . $session['status'] . '</td>';
            echo '<td>' . $session['attendance_count'] . '</td>';
            echo '</tr>';
        }
        
        echo '</tbody>';
        echo '</table>';
        echo '</body>';
        echo '</html>';
        exit;
    }
}

$pageTitle = 'تصدير الجلسات - ' . $course['title'];
include 'includes/header.php';

?>

<div class="container-fluid py-4">
    <!-- معلومات الكورس -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="mb-0">تصدير جلسات الكورس</h5>
                        <small><?php echo htmlspecialchars($course['title']); ?></small>
                    </div>
                    <div>
                        <a href="course-sessions.php?course_id=<?php echo $course_id; ?>" class="btn btn-light btn-sm">
                            <i class="fas fa-arrow-left"></i> العودة
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h3><?php echo count($sessions); ?></h3>
                    <p class="mb-0">إجمالي الجلسات</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h3><?php echo count(array_filter($sessions, function($s) { return $s['status'] === 'completed'; })); ?></h3>
                    <p class="mb-0">جلسة مكتملة</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h3><?php echo count(array_filter($sessions, function($s) { return $s['status'] === 'scheduled'; })); ?></h3>
                    <p class="mb-0">جلسة مجدولة</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-secondary text-white">
                <div class="card-body text-center">
                    <?php
                    $total_attendance = array_sum(array_column($sessions, 'attendance_count'));
                    ?>
                    <h3><?php echo $total_attendance; ?></h3>
                    <p class="mb-0">إجمالي الحضور</p>
                </div>
            </div>
        </div>
    </div>

    <!-- خيارات التصدير -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">خيارات التصدير</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="card border-success">
                                <div class="card-body text-center">
                                    <i class="fas fa-file-excel fa-3x text-success mb-3"></i>
                                    <h5>Excel</h5>
                                    <p class="text-muted">تصدير البيانات بصيغة Excel للتحليل والمعالجة</p>
                                    <a href="?course_id=<?php echo $course_id; ?>&export=1&format=excel" class="btn btn-success">
                                        <i class="fas fa-download"></i> تحميل Excel
                                    </a>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="card border-info">
                                <div class="card-body text-center">
                                    <i class="fas fa-file-csv fa-3x text-info mb-3"></i>
                                    <h5>CSV</h5>
                                    <p class="text-muted">تصدير البيانات بصيغة CSV للاستيراد في برامج أخرى</p>
                                    <a href="?course_id=<?php echo $course_id; ?>&export=1&format=csv" class="btn btn-info">
                                        <i class="fas fa-download"></i> تحميل CSV
                                    </a>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="card border-danger">
                                <div class="card-body text-center">
                                    <i class="fas fa-file-pdf fa-3x text-danger mb-3"></i>
                                    <h5>PDF</h5>
                                    <p class="text-muted">تصدير تقرير مطبوع بصيغة PDF</p>
                                    <a href="?course_id=<?php echo $course_id; ?>&export=1&format=pdf" class="btn btn-danger" target="_blank">
                                        <i class="fas fa-download"></i> تحميل PDF
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- معاينة البيانات -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">معاينة البيانات المراد تصديرها</h6>
                </div>
                <div class="card-body">
                    <?php if (empty($sessions)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-video fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد جلسات للتصدير</h5>
                            <p class="text-muted">لا توجد جلسات في هذا الكورس للتصدير</p>
                            <a href="add-session.php?course_id=<?php echo $course_id; ?>" class="btn btn-primary">
                                <i class="fas fa-plus"></i> إضافة جلسة
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-sm">
                                <thead class="table-dark">
                                    <tr>
                                        <th>رقم الجلسة</th>
                                        <th>العنوان</th>
                                        <th>التاريخ</th>
                                        <th>الوقت</th>
                                        <th>الحالة</th>
                                        <th>الحضور</th>
                                        <th>نسبة الحضور</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($sessions as $session): ?>
                                        <?php
                                        $attendance_percentage = $session['total_students'] > 0 ? 
                                            round(($session['attendance_count'] / $session['total_students']) * 100, 1) : 0;
                                        ?>
                                        <tr>
                                            <td><?php echo $session['id']; ?></td>
                                            <td><?php echo htmlspecialchars($session['title']); ?></td>
                                            <td><?php echo isset($session['session_date']) ? $session['session_date'] : 'غير محدد'; ?></td>
                                            <td>
                                                <?php if (isset($session['start_time']) && isset($session['end_time'])): ?>
                                                    <?php echo date('H:i', strtotime($session['start_time'])); ?> - 
                                                    <?php echo date('H:i', strtotime($session['end_time'])); ?>
                                                <?php else: ?>
                                                    غير محدد
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php
                                                $status_class = '';
                                                switch($session['status']) {
                                                    case 'scheduled': $status_class = 'warning'; break;
                                                    case 'ongoing': $status_class = 'success'; break;
                                                    case 'completed': $status_class = 'info'; break;
                                                    case 'cancelled': $status_class = 'danger'; break;
                                                    default: $status_class = 'secondary';
                                                }
                                                ?>
                                                <span class="badge bg-<?php echo $status_class; ?>"><?php echo $session['status']; ?></span>
                                            </td>
                                            <td><?php echo $session['attendance_count']; ?>/<?php echo $session['total_students']; ?></td>
                                            <td>
                                                <div class="progress" style="height: 20px;">
                                                    <div class="progress-bar" role="progressbar" style="width: <?php echo $attendance_percentage; ?>%">
                                                        <?php echo $attendance_percentage; ?>%
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="mt-3">
                            <small class="text-muted">
                                إجمالي <?php echo count($sessions); ?> جلسة • 
                                آخر تحديث: <?php echo date('Y-m-d H:i'); ?>
                            </small>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<?php require_once '../includes/footer.php'; ?>
