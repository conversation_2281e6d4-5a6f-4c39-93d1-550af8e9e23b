<?php
/**
 * ملف اختبار الاتصال بقاعدة البيانات
 * Database Connection Test File
 */

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>اختبار قاعدة البيانات</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css' rel='stylesheet'>";
echo "</head>";
echo "<body class='bg-light'>";
echo "<div class='container mt-5'>";
echo "<div class='row justify-content-center'>";
echo "<div class='col-md-8'>";

echo "<div class='card shadow'>";
echo "<div class='card-header bg-primary text-white text-center'>";
echo "<h3><i class='fas fa-database me-2'></i>اختبار الاتصال بقاعدة البيانات</h3>";
echo "</div>";
echo "<div class='card-body'>";

// اختبار إعدادات قاعدة البيانات
echo "<h5><i class='fas fa-cog text-primary me-2'></i>إعدادات قاعدة البيانات:</h5>";
echo "<div class='alert alert-info'>";
echo "<strong>المضيف:</strong> localhost<br>";
echo "<strong>المستخدم:</strong> root<br>";
echo "<strong>كلمة المرور:</strong> (فارغة)<br>";
echo "<strong>اسم قاعدة البيانات:</strong> zoom_learning_system";
echo "</div>";

// اختبار الاتصال الأساسي
echo "<h5><i class='fas fa-plug text-warning me-2'></i>اختبار الاتصال الأساسي:</h5>";

try {
    // اختبار الاتصال بـ MySQL بدون قاعدة بيانات محددة
    $test_conn = new PDO("mysql:host=localhost;charset=utf8mb4", "root", "");
    $test_conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<div class='alert alert-success'>";
    echo "<i class='fas fa-check-circle me-2'></i>";
    echo "<strong>نجح الاتصال بخادم MySQL!</strong>";
    echo "</div>";
    
    // اختبار إنشاء قاعدة البيانات
    echo "<h5><i class='fas fa-database text-info me-2'></i>إنشاء قاعدة البيانات:</h5>";
    
    $test_conn->exec("CREATE DATABASE IF NOT EXISTS zoom_learning_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "<div class='alert alert-success'>";
    echo "<i class='fas fa-check-circle me-2'></i>";
    echo "<strong>تم إنشاء قاعدة البيانات بنجاح!</strong>";
    echo "</div>";
    
    // الاتصال بقاعدة البيانات المحددة
    $conn = new PDO("mysql:host=localhost;dbname=zoom_learning_system;charset=utf8mb4", "root", "");
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<div class='alert alert-success'>";
    echo "<i class='fas fa-check-circle me-2'></i>";
    echo "<strong>نجح الاتصال بقاعدة البيانات zoom_learning_system!</strong>";
    echo "</div>";
    
    // اختبار إنشاء الجداول الأساسية
    echo "<h5><i class='fas fa-table text-success me-2'></i>إنشاء الجداول الأساسية:</h5>";
    
    // جدول المستخدمين
    $conn->exec("CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255) NOT NULL UNIQUE,
        username VARCHAR(50) NULL,
        phone VARCHAR(20) NULL,
        password VARCHAR(255) NOT NULL,
        role ENUM('admin', 'instructor', 'student') NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        last_login TIMESTAMP NULL,
        status ENUM('active', 'inactive') DEFAULT 'active'
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    
    echo "<div class='alert alert-success'>";
    echo "<i class='fas fa-users me-2'></i>";
    echo "تم إنشاء جدول المستخدمين";
    echo "</div>";
    
    // جدول الكورسات
    $conn->exec("CREATE TABLE IF NOT EXISTS courses (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        instructor_id INT,
        max_students INT DEFAULT 50,
        price DECIMAL(10,2) DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        status ENUM('active', 'inactive') DEFAULT 'active',
        FOREIGN KEY (instructor_id) REFERENCES users(id) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    
    echo "<div class='alert alert-success'>";
    echo "<i class='fas fa-book me-2'></i>";
    echo "تم إنشاء جدول الكورسات";
    echo "</div>";
    
    // جدول الواجبات
    $conn->exec("CREATE TABLE IF NOT EXISTS assignments (
        id INT AUTO_INCREMENT PRIMARY KEY,
        course_id INT NOT NULL,
        instructor_id INT NOT NULL,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        instructions TEXT,
        due_date DATETIME NOT NULL,
        max_grade INT DEFAULT 100,
        is_required TINYINT(1) DEFAULT 0,
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_by INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
        FOREIGN KEY (instructor_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    
    echo "<div class='alert alert-success'>";
    echo "<i class='fas fa-tasks me-2'></i>";
    echo "تم إنشاء جدول الواجبات";
    echo "</div>";
    
    // اختبار إدراج مستخدم تجريبي
    echo "<h5><i class='fas fa-user-plus text-primary me-2'></i>إنشاء مستخدمين تجريبيين:</h5>";
    
    // التحقق من وجود المستخدمين
    $stmt = $conn->prepare("SELECT COUNT(*) FROM users");
    $stmt->execute();
    $user_count = $stmt->fetchColumn();
    
    if ($user_count == 0) {
        // إنشاء مدير
        $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
        $conn->exec("INSERT INTO users (name, email, username, password, role, status) VALUES 
                    ('المدير العام', '<EMAIL>', 'admin', '$admin_password', 'admin', 'active')");
        
        // إنشاء مدرب
        $instructor_password = password_hash('instructor123', PASSWORD_DEFAULT);
        $conn->exec("INSERT INTO users (name, email, username, password, role, status) VALUES 
                    ('أحمد محمد', '<EMAIL>', 'instructor1', '$instructor_password', 'instructor', 'active')");
        
        // إنشاء طالب
        $student_password = password_hash('student123', PASSWORD_DEFAULT);
        $conn->exec("INSERT INTO users (name, email, username, password, role, status) VALUES 
                    ('سارة أحمد', '<EMAIL>', 'student1', '$student_password', 'student', 'active')");
        
        echo "<div class='alert alert-success'>";
        echo "<i class='fas fa-check-circle me-2'></i>";
        echo "<strong>تم إنشاء المستخدمين التجريبيين بنجاح!</strong><br>";
        echo "<small>";
        echo "المدير: <EMAIL> / admin123<br>";
        echo "المدرب: <EMAIL> / instructor123<br>";
        echo "الطالب: <EMAIL> / student123";
        echo "</small>";
        echo "</div>";
    } else {
        echo "<div class='alert alert-info'>";
        echo "<i class='fas fa-info-circle me-2'></i>";
        echo "يوجد $user_count مستخدم في قاعدة البيانات";
        echo "</div>";
    }
    
    // عرض المستخدمين الموجودين
    echo "<h5><i class='fas fa-list text-info me-2'></i>المستخدمين الموجودين:</h5>";
    $stmt = $conn->query("SELECT id, name, email, role, status FROM users ORDER BY id");
    $users = $stmt->fetchAll();
    
    if (!empty($users)) {
        echo "<div class='table-responsive'>";
        echo "<table class='table table-striped'>";
        echo "<thead class='table-dark'>";
        echo "<tr><th>ID</th><th>الاسم</th><th>البريد الإلكتروني</th><th>الدور</th><th>الحالة</th></tr>";
        echo "</thead><tbody>";
        
        foreach ($users as $user) {
            $role_text = [
                'admin' => 'مدير',
                'instructor' => 'مدرب', 
                'student' => 'طالب'
            ];
            
            $status_class = $user['status'] == 'active' ? 'success' : 'danger';
            $status_text = $user['status'] == 'active' ? 'نشط' : 'غير نشط';
            
            echo "<tr>";
            echo "<td>{$user['id']}</td>";
            echo "<td>{$user['name']}</td>";
            echo "<td>{$user['email']}</td>";
            echo "<td><span class='badge bg-primary'>{$role_text[$user['role']]}</span></td>";
            echo "<td><span class='badge bg-{$status_class}'>{$status_text}</span></td>";
            echo "</tr>";
        }
        
        echo "</tbody></table>";
        echo "</div>";
    }
    
    echo "<div class='alert alert-success mt-4'>";
    echo "<h5><i class='fas fa-check-circle me-2'></i>تم الاختبار بنجاح!</h5>";
    echo "<p class='mb-0'>قاعدة البيانات تعمل بشكل صحيح. يمكنك الآن استخدام النظام.</p>";
    echo "</div>";
    
    echo "<div class='text-center mt-4'>";
    echo "<a href='login.php' class='btn btn-primary btn-lg me-2'>";
    echo "<i class='fas fa-sign-in-alt me-2'></i>تسجيل الدخول";
    echo "</a>";
    echo "<a href='index.php' class='btn btn-outline-primary btn-lg'>";
    echo "<i class='fas fa-home me-2'></i>الصفحة الرئيسية";
    echo "</a>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h5><i class='fas fa-exclamation-triangle me-2'></i>خطأ في الاتصال!</h5>";
    echo "<p><strong>رسالة الخطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    
    echo "<h6>الحلول المقترحة:</h6>";
    echo "<ul>";
    echo "<li>تأكد من تشغيل خادم MySQL (XAMPP/WAMP)</li>";
    echo "<li>تحقق من إعدادات قاعدة البيانات في config/database.php</li>";
    echo "<li>تأكد من صحة اسم المستخدم وكلمة المرور</li>";
    echo "<li>تحقق من أن المنفذ 3306 متاح</li>";
    echo "</ul>";
    echo "</div>";
}

echo "</div>";
echo "</div>";
echo "</div>";
echo "</div>";
echo "</div>";
echo "</body>";
echo "</html>";
?>
