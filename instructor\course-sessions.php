<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';
require_once '../includes/activity_logger.php';

// التحقق من صلاحيات المدرب
if (!isLoggedIn() || !isInstructor()) {
    header('Location: ../login.php');
    exit;
}

$course_id = isset($_GET['course_id']) ? (int)$_GET['course_id'] : 0;
$instructor_id = $_SESSION['user_id'];

// التحقق من ملكية الكورس
try {
    $stmt = $conn->prepare("SELECT * FROM courses WHERE id = ? AND instructor_id = ?");
    $stmt->execute([$course_id, $instructor_id]);
    $course = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$course) {
        header('Location: courses.php');
        exit;
    }
} catch (PDOException $e) {
    header('Location: courses.php');
    exit;
}

// جلب جلسات الكورس
$sessions = [];
try {
    // التحقق من وجود جدول sessions
    $stmt = $conn->query("SHOW TABLES LIKE 'sessions'");
    if ($stmt->rowCount() > 0) {
        // التحقق من وجود الأعمدة المطلوبة
        $stmt = $conn->query("SHOW COLUMNS FROM sessions");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $existing_columns = array_column($columns, 'Field');
        
        // بناء الاستعلام حسب الأعمدة المتوفرة
        $select_fields = ['s.id', 's.course_id', 's.title', 's.description', 's.status'];
        
        if (in_array('session_date', $existing_columns)) {
            $select_fields[] = 's.session_date';
        }
        if (in_array('start_time', $existing_columns)) {
            $select_fields[] = 's.start_time';
        }
        if (in_array('end_time', $existing_columns)) {
            $select_fields[] = 's.end_time';
        }
        if (in_array('zoom_link', $existing_columns)) {
            $select_fields[] = 's.zoom_link';
        }
        if (in_array('meeting_id', $existing_columns)) {
            $select_fields[] = 's.meeting_id';
        }
        if (in_array('session_file', $existing_columns)) {
            $select_fields[] = 's.session_file';
        }
        if (in_array('file_name', $existing_columns)) {
            $select_fields[] = 's.file_name';
        }
        if (in_array('created_at', $existing_columns)) {
            $select_fields[] = 's.created_at';
        }
        
        $select_query = implode(', ', $select_fields);
        
        $stmt = $conn->prepare("
            SELECT 
                $select_query,
                (SELECT COUNT(*) FROM session_attendance WHERE session_id = s.id) as attendance_count
            FROM sessions s
            WHERE s.course_id = ?
            ORDER BY " . (in_array('session_date', $existing_columns) ? 's.session_date DESC, s.start_time DESC' : 's.id DESC')
        );
        $stmt->execute([$course_id]);
        $sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
} catch (PDOException $e) {
    $error = 'حدث خطأ في جلب بيانات الجلسات: ' . $e->getMessage();
}

$pageTitle = 'إدارة جلسات الكورس: ' . $course['title'];
include 'includes/header.php';

?>

<div class="container-fluid py-4">
    <!-- معلومات الكورس -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="mb-0">إدارة جلسات الكورس</h5>
                        <small><?php echo htmlspecialchars($course['title']); ?></small>
                    </div>
                    <div>
                        <a href="course-details.php?id=<?php echo $course_id; ?>" class="btn btn-light btn-sm">
                            <i class="fas fa-arrow-left"></i> العودة
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php if (isset($error)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- إحصائيات الجلسات -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h3><?php echo count($sessions); ?></h3>
                    <p class="mb-0">إجمالي الجلسات</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h3><?php echo count(array_filter($sessions, function($s) { return $s['status'] === 'completed'; })); ?></h3>
                    <p class="mb-0">جلسة مكتملة</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h3><?php echo count(array_filter($sessions, function($s) { return $s['status'] === 'scheduled'; })); ?></h3>
                    <p class="mb-0">جلسة مجدولة</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <?php
                    $total_attendance = array_sum(array_column($sessions, 'attendance_count'));
                    $avg_attendance = count($sessions) > 0 ? round($total_attendance / count($sessions), 1) : 0;
                    ?>
                    <h3><?php echo $avg_attendance; ?></h3>
                    <p class="mb-0">متوسط الحضور</p>
                </div>
            </div>
        </div>
    </div>

    <!-- أزرار الإجراءات -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="btn-group" role="group">
                        <a href="add-session.php?course_id=<?php echo $course_id; ?>" class="btn btn-success">
                            <i class="fas fa-plus"></i> إضافة جلسة جديدة
                        </a>
                        <button type="button" class="btn btn-primary" onclick="exportSessions()">
                            <i class="fas fa-download"></i> تصدير الجلسات
                        </button>
                        <button type="button" class="btn btn-info" onclick="sessionStatistics()">
                            <i class="fas fa-chart-bar"></i> إحصائيات
                        </button>
                        <button type="button" class="btn btn-warning" onclick="bulkActions()">
                            <i class="fas fa-edit"></i> إجراءات متعددة
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- قائمة الجلسات -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">قائمة الجلسات</h6>
                </div>
                <div class="card-body">
                    <?php if (empty($sessions)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-video fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد جلسات</h5>
                            <p class="text-muted">لم يتم إنشاء أي جلسات لهذا الكورس بعد</p>
                            <a href="add-session.php?course_id=<?php echo $course_id; ?>" class="btn btn-success btn-lg">
                                <i class="fas fa-plus"></i> إضافة أول جلسة
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover" id="sessionsTable">
                                <thead class="table-light">
                                    <tr>
                                        <th>العنوان</th>
                                        <?php if (isset($sessions[0]['session_date'])): ?>
                                            <th>التاريخ والوقت</th>
                                        <?php endif; ?>
                                        <th>الحضور</th>
                                        <th>الحالة</th>
                                        <th>الملف</th>
                                        <th>رابط Zoom</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($sessions as $session): ?>
                                        <tr>
                                            <td>
                                                <div>
                                                    <strong><?php echo htmlspecialchars($session['title']); ?></strong>
                                                    <?php if (!empty($session['description'])): ?>
                                                        <br><small class="text-muted"><?php echo htmlspecialchars(substr($session['description'], 0, 100)); ?>...</small>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <?php if (isset($session['session_date'])): ?>
                                                <td>
                                                    <div>
                                                        <strong><?php echo date('Y-m-d', strtotime($session['session_date'])); ?></strong>
                                                        <?php if (isset($session['start_time']) && isset($session['end_time'])): ?>
                                                            <br><small class="text-muted">
                                                                <?php echo date('H:i', strtotime($session['start_time'])); ?> - 
                                                                <?php echo date('H:i', strtotime($session['end_time'])); ?>
                                                            </small>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                            <?php endif; ?>
                                            <td>
                                                <span class="badge bg-info"><?php echo $session['attendance_count']; ?></span>
                                                <small class="text-muted d-block">حاضر</small>
                                            </td>
                                            <td>
                                                <?php
                                                $status_class = '';
                                                $status_text = '';
                                                switch($session['status']) {
                                                    case 'scheduled':
                                                        $status_class = 'warning';
                                                        $status_text = 'مجدولة';
                                                        break;
                                                    case 'ongoing':
                                                        $status_class = 'success';
                                                        $status_text = 'جارية';
                                                        break;
                                                    case 'completed':
                                                        $status_class = 'info';
                                                        $status_text = 'مكتملة';
                                                        break;
                                                    case 'cancelled':
                                                        $status_class = 'danger';
                                                        $status_text = 'ملغاة';
                                                        break;
                                                    default:
                                                        $status_class = 'secondary';
                                                        $status_text = 'غير محدد';
                                                }
                                                ?>
                                                <span class="badge bg-<?php echo $status_class; ?>"><?php echo $status_text; ?></span>
                                            </td>
                                            <td>
                                                <?php if (isset($session['session_file']) && $session['session_file']): ?>
                                                    <a href="../<?php echo htmlspecialchars($session['session_file']); ?>" 
                                                       target="_blank" class="btn btn-sm btn-outline-primary" 
                                                       title="<?php echo htmlspecialchars($session['file_name'] ?? 'تحميل الملف'); ?>">
                                                        <i class="fas fa-download"></i>
                                                    </a>
                                                <?php else: ?>
                                                    <span class="text-muted">لا يوجد</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if (isset($session['zoom_link']) && $session['zoom_link']): ?>
                                                    <a href="<?php echo htmlspecialchars($session['zoom_link']); ?>" 
                                                       target="_blank" class="btn btn-sm btn-primary">
                                                        <i class="fas fa-video"></i>
                                                    </a>
                                                <?php else: ?>
                                                    <span class="text-muted">لا يوجد</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-info" onclick="viewSession(<?php echo $session['id']; ?>)" title="عرض التفاصيل">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button class="btn btn-outline-warning" onclick="editSession(<?php echo $session['id']; ?>)" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <?php if ($session['status'] === 'scheduled'): ?>
                                                        <button class="btn btn-outline-success" onclick="startSession(<?php echo $session['id']; ?>)" title="بدء الجلسة">
                                                            <i class="fas fa-play"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                    <button class="btn btn-outline-danger" onclick="deleteSession(<?php echo $session['id']; ?>)" title="حذف">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
function viewSession(sessionId) {
    window.open(`session-details.php?id=${sessionId}&course_id=<?php echo $course_id; ?>`, 
                '_blank', 'width=900,height=700,scrollbars=yes');
}

function editSession(sessionId) {
    window.location.href = `edit-session.php?id=${sessionId}&course_id=<?php echo $course_id; ?>`;
}

function startSession(sessionId) {
    if (confirm('هل تريد بدء هذه الجلسة الآن؟')) {
        window.location.href = `start-session.php?id=${sessionId}&course_id=<?php echo $course_id; ?>`;
    }
}

function deleteSession(sessionId) {
    if (confirm('هل أنت متأكد من حذف هذه الجلسة؟ سيتم حذف جميع بيانات الحضور المرتبطة بها.')) {
        window.location.href = `delete-session.php?id=${sessionId}&course_id=<?php echo $course_id; ?>`;
    }
}

function exportSessions() {
    window.location.href = `export-sessions.php?course_id=<?php echo $course_id; ?>`;
}

function sessionStatistics() {
    window.open(`session-statistics.php?course_id=<?php echo $course_id; ?>`, 
                '_blank', 'width=1000,height=700,scrollbars=yes');
}

function bulkActions() {
    alert('قريباً - إجراءات متعددة للجلسات');
}

// تهيئة DataTable إذا كان متوفر
document.addEventListener('DOMContentLoaded', function() {
    const table = document.getElementById('sessionsTable');
    if (table && typeof DataTable !== 'undefined') {
        new DataTable(table, {
            order: [[1, 'desc']],
            pageLength: 25,
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/ar.json'
            }
        });
    }
});
</script>

<?php require_once '../includes/footer.php'; ?>
