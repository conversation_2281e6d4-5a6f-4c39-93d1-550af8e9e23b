<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات الطالب
if (!isLoggedIn() || !isStudent()) {
    header('Location: ../login.php');
    exit;
}

$pageTitle = 'الدرجات';
$breadcrumbs = [
    ['title' => 'الدرجات']
];

$student_id = $_SESSION['user_id'];
$selected_course = $_GET['course_id'] ?? '';

try {
    // جلب الكورسات المسجل بها الطالب
    $stmt = $conn->prepare("
        SELECT c.id, c.title 
        FROM courses c
        JOIN course_enrollments ce ON c.id = ce.course_id
        WHERE ce.student_id = ? AND ce.status = 'active'
        ORDER BY c.title
    ");
    $stmt->execute([$student_id]);
    $enrolled_courses = $stmt->fetchAll();
    
    // جلب درجات الواجبات
    $assignment_where = $selected_course ? "AND c.id = ?" : "";
    $assignment_params = $selected_course ? [$student_id, $selected_course] : [$student_id];
    
    $stmt = $conn->prepare("
        SELECT asub.*, a.title as assignment_title, a.max_score as assignment_max_score,
               c.title as course_title, c.id as course_id,
               u.name as graded_by_name
        FROM assignment_submissions asub
        JOIN assignments a ON asub.assignment_id = a.id
        JOIN courses c ON a.course_id = c.id
        JOIN course_enrollments ce ON c.id = ce.course_id
        LEFT JOIN users u ON asub.graded_by = u.id
        WHERE ce.student_id = ? $assignment_where
        ORDER BY asub.submitted_at DESC
    ");
    $stmt->execute($assignment_params);
    $assignment_grades = $stmt->fetchAll();
    
    // جلب درجات الاختبارات
    $quiz_where = $selected_course ? "AND c.id = ?" : "";
    $quiz_params = $selected_course ? [$student_id, $selected_course] : [$student_id];
    
    $stmt = $conn->prepare("
        SELECT qa.*, q.title as quiz_title, q.total_marks as quiz_max_score,
               c.title as course_title, c.id as course_id
        FROM quiz_attempts qa
        JOIN quizzes q ON qa.quiz_id = q.id
        JOIN courses c ON q.course_id = c.id
        JOIN course_enrollments ce ON c.id = ce.course_id
        WHERE qa.student_id = ? AND qa.status = 'completed' $quiz_where
        ORDER BY qa.completed_at DESC
    ");
    $stmt->execute($quiz_params);
    $quiz_grades = $stmt->fetchAll();
    
    // حساب الإحصائيات العامة
    $stats = [
        'total_assignments' => count($assignment_grades),
        'graded_assignments' => count(array_filter($assignment_grades, fn($g) => $g['score'] !== null)),
        'total_quizzes' => count($quiz_grades),
        'passed_quizzes' => 0,
        'avg_assignment_score' => 0,
        'avg_quiz_score' => 0
    ];
    
    // حساب متوسط درجات الواجبات
    $graded_assignments = array_filter($assignment_grades, fn($g) => $g['score'] !== null);
    if (!empty($graded_assignments)) {
        $total_score = 0;
        $total_max = 0;
        foreach ($graded_assignments as $grade) {
            $total_score += $grade['score'];
            $total_max += $grade['assignment_max_score'];
        }
        $stats['avg_assignment_score'] = $total_max > 0 ? ($total_score / $total_max) * 100 : 0;
    }
    
    // حساب متوسط درجات الاختبارات
    if (!empty($quiz_grades)) {
        $total_score = array_sum(array_column($quiz_grades, 'score'));
        $stats['avg_quiz_score'] = $total_score / count($quiz_grades);
        
        // عد الاختبارات الناجحة (افتراض 60% للنجاح)
        $stats['passed_quizzes'] = count(array_filter($quiz_grades, fn($g) => $g['score'] >= 60));
    }
    
    // جلب درجات الكورسات (إجمالي)
    $course_stats = [];
    if ($selected_course) {
        $courses_to_check = [$selected_course];
    } else {
        $courses_to_check = array_column($enrolled_courses, 'id');
    }
    
    foreach ($courses_to_check as $course_id) {
        // درجات الواجبات للكورس
        $course_assignments = array_filter($assignment_grades, fn($g) => $g['course_id'] == $course_id);
        $course_quizzes = array_filter($quiz_grades, fn($g) => $g['course_id'] == $course_id);
        
        $course_name = '';
        foreach ($enrolled_courses as $course) {
            if ($course['id'] == $course_id) {
                $course_name = $course['title'];
                break;
            }
        }
        
        $assignment_avg = 0;
        $quiz_avg = 0;
        
        // حساب متوسط الواجبات
        $graded_course_assignments = array_filter($course_assignments, fn($g) => $g['score'] !== null);
        if (!empty($graded_course_assignments)) {
            $total_score = 0;
            $total_max = 0;
            foreach ($graded_course_assignments as $grade) {
                $total_score += $grade['score'];
                $total_max += $grade['assignment_max_score'];
            }
            $assignment_avg = $total_max > 0 ? ($total_score / $total_max) * 100 : 0;
        }
        
        // حساب متوسط الاختبارات
        if (!empty($course_quizzes)) {
            $quiz_avg = array_sum(array_column($course_quizzes, 'score')) / count($course_quizzes);
        }
        
        // حساب المتوسط العام (50% واجبات + 50% اختبارات)
        $overall_avg = 0;
        if ($assignment_avg > 0 && $quiz_avg > 0) {
            $overall_avg = ($assignment_avg + $quiz_avg) / 2;
        } elseif ($assignment_avg > 0) {
            $overall_avg = $assignment_avg;
        } elseif ($quiz_avg > 0) {
            $overall_avg = $quiz_avg;
        }
        
        $course_stats[$course_id] = [
            'name' => $course_name,
            'assignment_avg' => $assignment_avg,
            'quiz_avg' => $quiz_avg,
            'overall_avg' => $overall_avg,
            'assignment_count' => count($graded_course_assignments),
            'quiz_count' => count($course_quizzes)
        ];
    }
    
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء جلب الدرجات';
}

include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- العنوان والفلتر -->
        <div class="col-12 mb-4">
            <div class="d-flex justify-content-between align-items-center">
                <h2 class="text-primary">
                    <i class="fas fa-chart-line me-2"></i>
                    الدرجات والتقييمات
                </h2>
                <div class="d-flex gap-3">
                    <form method="GET" class="d-flex">
                        <select name="course_id" class="form-select me-2" onchange="this.form.submit()">
                            <option value="">جميع الكورسات</option>
                            <?php foreach ($enrolled_courses as $course): ?>
                            <option value="<?php echo $course['id']; ?>" 
                                    <?php echo $selected_course == $course['id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($course['title']); ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </form>
                </div>
            </div>
        </div>

        <!-- الإحصائيات العامة -->
        <div class="col-12 mb-4">
            <div class="row">
                <div class="col-md-3 mb-3">
                    <div class="card-student text-center">
                        <div class="card-body">
                            <i class="fas fa-tasks fa-2x text-primary mb-2"></i>
                            <h4 class="text-primary"><?php echo $stats['graded_assignments']; ?></h4>
                            <p class="text-muted mb-0">واجبات مُقيمة</p>
                            <small class="text-muted">من أصل <?php echo $stats['total_assignments']; ?></small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card-student text-center">
                        <div class="card-body">
                            <i class="fas fa-question-circle fa-2x text-success mb-2"></i>
                            <h4 class="text-success"><?php echo $stats['total_quizzes']; ?></h4>
                            <p class="text-muted mb-0">اختبارات مكتملة</p>
                            <small class="text-muted"><?php echo $stats['passed_quizzes']; ?> ناجح</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card-student text-center">
                        <div class="card-body">
                            <i class="fas fa-clipboard-check fa-2x text-warning mb-2"></i>
                            <h4 class="text-warning"><?php echo number_format($stats['avg_assignment_score'], 1); ?>%</h4>
                            <p class="text-muted mb-0">متوسط الواجبات</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card-student text-center">
                        <div class="card-body">
                            <i class="fas fa-chart-bar fa-2x text-info mb-2"></i>
                            <h4 class="text-info"><?php echo number_format($stats['avg_quiz_score'], 1); ?>%</h4>
                            <p class="text-muted mb-0">متوسط الاختبارات</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- إحصائيات الكورسات -->
        <?php if (!empty($course_stats)): ?>
        <div class="col-12 mb-4">
            <div class="card-student">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-graduation-cap me-2"></i>
                        أداء الكورسات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <?php foreach ($course_stats as $course_id => $stats): ?>
                        <div class="col-lg-6 mb-3">
                            <div class="border rounded p-3">
                                <h6 class="text-primary mb-3"><?php echo htmlspecialchars($stats['name']); ?></h6>
                                
                                <div class="row text-center">
                                    <div class="col-4">
                                        <div class="mb-2">
                                            <div class="h5 mb-1 text-warning">
                                                <?php echo number_format($stats['assignment_avg'], 1); ?>%
                                            </div>
                                            <small class="text-muted">الواجبات</small>
                                            <div class="text-xs text-muted">
                                                (<?php echo $stats['assignment_count']; ?> واجب)
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div class="mb-2">
                                            <div class="h5 mb-1 text-info">
                                                <?php echo number_format($stats['quiz_avg'], 1); ?>%
                                            </div>
                                            <small class="text-muted">الاختبارات</small>
                                            <div class="text-xs text-muted">
                                                (<?php echo $stats['quiz_count']; ?> اختبار)
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div class="mb-2">
                                            <div class="h5 mb-1 <?php echo $stats['overall_avg'] >= 60 ? 'text-success' : 'text-danger'; ?>">
                                                <?php echo number_format($stats['overall_avg'], 1); ?>%
                                            </div>
                                            <small class="text-muted">المتوسط العام</small>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- شريط التقدم -->
                                <div class="progress progress-student mt-2">
                                    <div class="progress-bar-student" style="width: <?php echo min($stats['overall_avg'], 100); ?>%"></div>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- تبويبات الدرجات -->
        <div class="col-12">
            <div class="card-student">
                <div class="card-header">
                    <ul class="nav nav-tabs card-header-tabs" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" data-bs-toggle="tab" href="#assignments-tab">
                                <i class="fas fa-tasks me-1"></i>
                                الواجبات (<?php echo count($assignment_grades); ?>)
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="tab" href="#quizzes-tab">
                                <i class="fas fa-question-circle me-1"></i>
                                الاختبارات (<?php echo count($quiz_grades); ?>)
                            </a>
                        </li>
                    </ul>
                </div>
                <div class="card-body">
                    <div class="tab-content">
                        <!-- تبويب الواجبات -->
                        <div class="tab-pane fade show active" id="assignments-tab">
                            <?php if (empty($assignment_grades)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-tasks fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">لا توجد واجبات</h5>
                                <p class="text-muted">لم يتم تسليم أي واجبات بعد</p>
                            </div>
                            <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>الواجب</th>
                                            <th>الكورس</th>
                                            <th>تاريخ التسليم</th>
                                            <th>الدرجة</th>
                                            <th>النسبة المئوية</th>
                                            <th>الحالة</th>
                                            <th>التعليق</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($assignment_grades as $grade): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo htmlspecialchars($grade['assignment_title']); ?></strong>
                                            </td>
                                            <td>
                                                <span class="badge bg-primary">
                                                    <?php echo htmlspecialchars($grade['course_title']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php echo date('Y-m-d H:i', strtotime($grade['submitted_at'])); ?>
                                            </td>
                                            <td>
                                                <?php if ($grade['score'] !== null): ?>
                                                    <strong><?php echo $grade['score']; ?></strong> / <?php echo $grade['assignment_max_score']; ?>
                                                <?php else: ?>
                                                    <span class="text-muted">لم يتم التقييم</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($grade['score'] !== null): ?>
                                                    <?php 
                                                    $percentage = ($grade['score'] / $grade['assignment_max_score']) * 100;
                                                    $color_class = $percentage >= 60 ? 'text-success' : 'text-danger';
                                                    ?>
                                                    <span class="<?php echo $color_class; ?> fw-bold">
                                                        <?php echo number_format($percentage, 1); ?>%
                                                    </span>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($grade['score'] !== null): ?>
                                                    <?php if ($grade['graded_at']): ?>
                                                        <span class="badge bg-success">مُقيم</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-info">مُسلم</span>
                                                    <?php endif; ?>
                                                <?php else: ?>
                                                    <span class="badge bg-warning">في الانتظار</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($grade['feedback']): ?>
                                                    <button class="btn btn-sm btn-outline-info" data-bs-toggle="tooltip" 
                                                            title="<?php echo htmlspecialchars($grade['feedback']); ?>">
                                                        <i class="fas fa-comment"></i>
                                                    </button>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            <?php endif; ?>
                        </div>

                        <!-- تبويب الاختبارات -->
                        <div class="tab-pane fade" id="quizzes-tab">
                            <?php if (empty($quiz_grades)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-question-circle fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">لا توجد اختبارات</h5>
                                <p class="text-muted">لم يتم إكمال أي اختبارات بعد</p>
                            </div>
                            <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>الاختبار</th>
                                            <th>الكورس</th>
                                            <th>تاريخ الإكمال</th>
                                            <th>المحاولة</th>
                                            <th>الوقت المستغرق</th>
                                            <th>الدرجة</th>
                                            <th>النسبة المئوية</th>
                                            <th>الحالة</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($quiz_grades as $grade): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo htmlspecialchars($grade['quiz_title']); ?></strong>
                                            </td>
                                            <td>
                                                <span class="badge bg-primary">
                                                    <?php echo htmlspecialchars($grade['course_title']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php echo date('Y-m-d H:i', strtotime($grade['completed_at'])); ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">#<?php echo $grade['attempt_number']; ?></span>
                                            </td>
                                            <td>
                                                <?php if ($grade['time_taken']): ?>
                                                    <?php echo gmdate('H:i:s', $grade['time_taken']); ?>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <strong><?php echo number_format($grade['score'], 1); ?>%</strong>
                                            </td>
                                            <td>
                                                <?php 
                                                $color_class = $grade['score'] >= 60 ? 'text-success' : 'text-danger';
                                                ?>
                                                <span class="<?php echo $color_class; ?> fw-bold">
                                                    <?php echo number_format($grade['score'], 1); ?>%
                                                </span>
                                            </td>
                                            <td>
                                                <?php if ($grade['score'] >= 60): ?>
                                                    <span class="badge bg-success">ناجح</span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger">راسب</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// تفعيل tooltips
document.addEventListener('DOMContentLoaded', function() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>

<?php include 'includes/footer.php'; ?>
