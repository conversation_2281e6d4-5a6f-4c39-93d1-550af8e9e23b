<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات المدرب
if (!isLoggedIn() || !isInstructor()) {
    header('Location: ../login.php');
    exit;
}

// تفعيل عرض الأخطاء للتطوير
error_reporting(E_ALL);
ini_set('display_errors', 1);

$pageTitle = 'إدارة الاختبارات';
$breadcrumbs = [
    ['title' => 'إدارة الاختبارات']
];

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'create_quiz') {
        $course_id = $_POST['course_id'] ?? 0;
        $title = trim($_POST['title'] ?? '');
        $description = trim($_POST['description'] ?? '');
        $time_limit = $_POST['time_limit'] ?? 0;
        $max_attempts = $_POST['max_attempts'] ?? 1;
        $passing_grade = $_POST['passing_grade'] ?? 60;
        $is_randomized = isset($_POST['is_randomized']) ? 1 : 0;
        $show_results = isset($_POST['show_results']) ? 1 : 0;
        $available_from = $_POST['available_from'] ?? '';
        $available_until = $_POST['available_until'] ?? '';
        
        $errors = [];
        
        if (empty($title)) {
            $errors[] = 'عنوان الاختبار مطلوب';
        }
        
        if (empty($course_id)) {
            $errors[] = 'يجب اختيار كورس';
        }
        
        if (empty($errors)) {
            try {
                // التحقق من أن الكورس ينتمي للمدرب
                $stmt = $conn->prepare("SELECT id FROM courses WHERE id = ? AND instructor_id = ?");
                $stmt->execute([$course_id, $_SESSION['user_id']]);
                
                if ($stmt->fetch()) {
                    $stmt = $conn->prepare("
                        INSERT INTO quizzes (course_id, title, description, time_limit, passing_score,
                                           shuffle_questions, show_results, start_date, end_date, created_by)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ");
                    $stmt->execute([$course_id, $title, $description, $time_limit, $passing_grade,
                                  $is_randomized, $show_results, $available_from ?: null, $available_until ?: null, $_SESSION['user_id']]);
                    
                    $quiz_id = $conn->lastInsertId();
                    $success_message = 'تم إنشاء الاختبار بنجاح';
                    
                    // إعادة توجيه لإضافة الأسئلة مع معامل الاختبار الجديد
                    header("Location: quiz-questions.php?quiz_id=$quiz_id&new=1");
                    exit;
                }
            } catch (PDOException $e) {
                $error_message = 'حدث خطأ أثناء إنشاء الاختبار';
            }
        }
    }
    
    if ($action === 'delete_quiz') {
        $quiz_id = $_POST['quiz_id'] ?? 0;
        
        try {
            $stmt = $conn->prepare("
                DELETE q FROM quizzes q
                INNER JOIN courses c ON q.course_id = c.id
                WHERE q.id = ? AND c.instructor_id = ?
            ");
            $stmt->execute([$quiz_id, $_SESSION['user_id']]);
            
            $success_message = 'تم حذف الاختبار بنجاح';
        } catch (PDOException $e) {
            $error_message = 'حدث خطأ أثناء حذف الاختبار';
        }
    }
}

// فلاتر البحث
$course_filter = $_GET['course_id'] ?? '';
$status_filter = $_GET['status'] ?? '';

// جلب الكورسات الخاصة بالمدرب
try {
    $stmt = $conn->prepare("
        SELECT id, title 
        FROM courses 
        WHERE instructor_id = ? AND status = 'active'
        ORDER BY title
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $instructor_courses = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $instructor_courses = [];
}

// جلب الاختبارات
try {
    $where_conditions = ["c.instructor_id = ?"];
    $params = [$_SESSION['user_id']];
    
    if (!empty($course_filter)) {
        $where_conditions[] = "c.id = ?";
        $params[] = $course_filter;
    }
    
    if (!empty($status_filter)) {
        switch ($status_filter) {
            case 'active':
                $where_conditions[] = "(q.start_date IS NULL OR q.start_date <= NOW()) AND (q.end_date IS NULL OR q.end_date >= NOW())";
                break;
            case 'upcoming':
                $where_conditions[] = "q.start_date > NOW()";
                break;
            case 'expired':
                $where_conditions[] = "q.end_date < NOW()";
                break;
        }
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    $stmt = $conn->prepare("
        SELECT
            q.id,
            q.title,
            q.description,
            q.time_limit,
            q.passing_score,
            q.status,
            q.created_at,
            q.start_date,
            q.end_date,
            c.title as course_title,
            (SELECT COUNT(*) FROM quiz_questions qq WHERE qq.quiz_id = q.id) as total_questions,
            (SELECT COUNT(*) FROM quiz_attempts qa WHERE qa.quiz_id = q.id) as total_attempts,
            (SELECT COUNT(*) FROM quiz_attempts qa WHERE qa.quiz_id = q.id AND qa.status = 'completed') as completed_attempts,
            (SELECT AVG(qa.score) FROM quiz_attempts qa WHERE qa.quiz_id = q.id AND qa.status = 'completed') as avg_score
        FROM quizzes q
        INNER JOIN courses c ON q.course_id = c.id
        WHERE $where_clause
        ORDER BY q.created_at DESC
    ");
    $stmt->execute($params);
    $quizzes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // إحصائيات سريعة
    $stmt = $conn->prepare("
        SELECT
            COUNT(DISTINCT q.id) as total_quizzes,
            COUNT(DISTINCT CASE WHEN (q.start_date IS NULL OR q.start_date <= NOW()) AND (q.end_date IS NULL OR q.end_date >= NOW()) THEN q.id END) as active_quizzes,
            COUNT(DISTINCT qa.id) as total_attempts,
            COALESCE(AVG(CASE WHEN qa.status = 'completed' THEN qa.score END), 0) as avg_score
        FROM quizzes q
        INNER JOIN courses c ON q.course_id = c.id
        LEFT JOIN quiz_attempts qa ON q.id = qa.quiz_id
        WHERE c.instructor_id = ?
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
} catch (PDOException $e) {
    $error_message = 'حدث خطأ أثناء جلب البيانات: ' . $e->getMessage();
    $quizzes = [];
    $stats = ['total_quizzes' => 0, 'active_quizzes' => 0, 'total_attempts' => 0, 'avg_score' => 0];

    // تسجيل الخطأ للمطور
    error_log("خطأ في صفحة الاختبارات: " . $e->getMessage());
}

include 'includes/header.php';
?>

<!-- شريط إنشاء اختبار سريع -->
<div class="alert alert-primary border-0 shadow-sm mb-4" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h5 class="text-white mb-2">
                <i class="fas fa-lightbulb me-2"></i>
                جاهز لإنشاء اختبار جديد؟
            </h5>
            <p class="text-white-50 mb-0">
                أنشئ اختبارات تفاعلية لطلابك مع أنواع أسئلة متعددة وتقييم تلقائي
            </p>
        </div>
        <div class="col-md-4 text-end">
            <a href="create-quiz.php" class="btn btn-light btn-lg shadow">
                <i class="fas fa-plus-circle me-2 text-primary"></i>
                <strong>إنشاء اختبار الآن</strong>
            </a>
        </div>
    </div>
</div>

<!-- رأس الصفحة -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="h3 mb-2">
            <i class="fas fa-question-circle text-info me-2"></i>
            إدارة الاختبارات
        </h2>
        <p class="text-muted mb-0">إنشاء وإدارة اختبارات الطلاب في الكورسات</p>
    </div>
    <div class="d-flex gap-2 flex-wrap">
        <a href="create-quiz.php" class="btn btn-primary btn-lg shadow-sm">
            <i class="fas fa-plus-circle me-2"></i>إنشاء اختبار جديد
        </a>
        <a href="quiz-templates.php" class="btn btn-outline-info">
            <i class="fas fa-copy me-1"></i>قوالب الاختبارات
        </a>
        <a href="assignments.php" class="btn btn-outline-primary">
            <i class="fas fa-tasks me-1"></i>الواجبات
        </a>
        <div class="dropdown">
            <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                <i class="fas fa-cog me-1"></i>المزيد
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="quiz-analytics.php">
                    <i class="fas fa-chart-line me-2"></i>تحليلات الاختبارات
                </a></li>
                <li><a class="dropdown-item" href="export-quizzes.php">
                    <i class="fas fa-download me-2"></i>تصدير الاختبارات
                </a></li>
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item" href="quiz-settings.php">
                    <i class="fas fa-sliders-h me-2"></i>إعدادات الاختبارات
                </a></li>
            </ul>
        </div>
    </div>
</div>

<!-- رسائل النجاح والخطأ -->
<?php if (isset($success_message)): ?>
<div class="alert alert-success alert-dismissible fade show" role="alert">
    <i class="fas fa-check-circle me-2"></i>
    <?php echo $success_message; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<?php if (isset($error_message)): ?>
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <i class="fas fa-exclamation-triangle me-2"></i>
    <?php echo $error_message; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<!-- بطاقات الإحصائيات -->
<div class="row g-4 mb-4">
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-info bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-question-circle text-info fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h5 class="mb-1"><?php echo $stats['total_quizzes']; ?></h5>
                        <p class="text-muted mb-0">إجمالي الاختبارات</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-success bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-play-circle text-success fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h5 class="mb-1"><?php echo $stats['active_quizzes']; ?></h5>
                        <p class="text-muted mb-0">اختبارات نشطة</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-primary bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-users text-primary fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h5 class="mb-1"><?php echo $stats['total_attempts']; ?></h5>
                        <p class="text-muted mb-0">إجمالي المحاولات</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-warning bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-star text-warning fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h5 class="mb-1"><?php echo number_format($stats['avg_score'], 1); ?>%</h5>
                        <p class="text-muted mb-0">متوسط النتائج</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- فلاتر البحث -->
<div class="card border-0 shadow-sm mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3 align-items-end">
            <div class="col-md-5">
                <label for="course_id" class="form-label">الكورس</label>
                <select name="course_id" id="course_id" class="form-select">
                    <option value="">جميع الكورسات</option>
                    <?php foreach ($instructor_courses as $course): ?>
                    <option value="<?php echo $course['id']; ?>" 
                            <?php echo $course_filter == $course['id'] ? 'selected' : ''; ?>>
                        <?php echo htmlspecialchars($course['title']); ?>
                    </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-4">
                <label for="status" class="form-label">الحالة</label>
                <select name="status" id="status" class="form-select">
                    <option value="">جميع الحالات</option>
                    <option value="active" <?php echo $status_filter == 'active' ? 'selected' : ''; ?>>نشطة</option>
                    <option value="upcoming" <?php echo $status_filter == 'upcoming' ? 'selected' : ''; ?>>قادمة</option>
                    <option value="expired" <?php echo $status_filter == 'expired' ? 'selected' : ''; ?>>منتهية</option>
                </select>
            </div>
            <div class="col-md-3">
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-search me-1"></i>بحث
                </button>
            </div>
        </form>
    </div>
</div>

<!-- قائمة الاختبارات -->
<div class="card border-0 shadow-sm">
    <div class="card-header bg-white border-bottom">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>
            قائمة الاختبارات
        </h5>
    </div>
    <div class="card-body p-0">
        <?php if (empty($quizzes)): ?>
        <div class="text-center py-5">
            <div class="empty-state-container">
                <div class="empty-state-icon mb-4">
                    <i class="fas fa-clipboard-question text-primary" style="font-size: 4rem; opacity: 0.7;"></i>
                </div>
                <h4 class="text-primary mb-3">ابدأ رحلة التقييم!</h4>
                <p class="text-muted mb-4 mx-auto" style="max-width: 500px;">
                    لم تقم بإنشاء أي اختبارات بعد. أنشئ اختبارك الأول لتقييم فهم طلابك وتتبع تقدمهم الأكاديمي.
                </p>

                <div class="d-flex gap-3 justify-content-center flex-wrap">
                    <a href="create-quiz.php" class="btn btn-primary btn-lg shadow-sm">
                        <i class="fas fa-plus-circle me-2"></i>إنشاء اختبار جديد
                    </a>
                    <button class="btn btn-outline-info btn-lg" onclick="showQuizGuide()">
                        <i class="fas fa-question-circle me-2"></i>دليل الاختبارات
                    </button>
                </div>

                <div class="mt-4">
                    <small class="text-muted">
                        <i class="fas fa-lightbulb me-1"></i>
                        نصيحة: يمكنك إنشاء أنواع مختلفة من الأسئلة مثل الاختيار المتعدد وصح/خطأ والأسئلة النصية
                    </small>
                </div>
            </div>
        </div>
        <?php else: ?>
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>الاختبار</th>
                        <th>الكورس</th>
                        <th>الأسئلة</th>
                        <th>المدة</th>
                        <th>الحالة</th>
                        <th>المحاولات</th>
                        <th>متوسط النتائج</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($quizzes as $quiz): ?>
                    <tr>
                        <td>
                            <div>
                                <h6 class="mb-1"><?php echo htmlspecialchars($quiz['title']); ?></h6>
                                <?php if ($quiz['description']): ?>
                                <small class="text-muted">
                                    <?php echo mb_substr(htmlspecialchars($quiz['description']), 0, 50) . '...'; ?>
                                </small>
                                <?php endif; ?>
                                <br>
                                <small class="text-info">
                                    درجة النجاح: <?php echo $quiz['passing_score'] ?? 60; ?>%
                                </small>
                            </div>
                        </td>
                        <td><?php echo htmlspecialchars($quiz['course_title']); ?></td>
                        <td>
                            <span class="badge bg-primary fs-6"><?php echo $quiz['total_questions']; ?></span>
                            <br><small class="text-muted">سؤال</small>
                        </td>
                        <td>
                            <?php if ($quiz['time_limit'] > 0): ?>
                            <span class="text-warning">
                                <i class="fas fa-clock me-1"></i>
                                <?php echo $quiz['time_limit']; ?> دقيقة
                            </span>
                            <?php else: ?>
                            <span class="text-muted">غير محدود</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <?php
                            $now = new DateTime();
                            $start_date = $quiz['start_date'] ? new DateTime($quiz['start_date']) : null;
                            $end_date = $quiz['end_date'] ? new DateTime($quiz['end_date']) : null;

                            if ($start_date && $start_date > $now) {
                                echo '<span class="badge bg-info">قادم</span>';
                            } elseif ($end_date && $end_date < $now) {
                                echo '<span class="badge bg-danger">منتهي</span>';
                            } else {
                                echo '<span class="badge bg-success">نشط</span>';
                            }
                            ?>
                        </td>
                        <td>
                            <div class="text-center">
                                <strong class="text-primary"><?php echo $quiz['completed_attempts']; ?></strong>
                                <span class="text-muted">/ <?php echo $quiz['total_attempts']; ?></span>
                                <br><small class="text-muted">مكتمل / إجمالي</small>
                            </div>
                        </td>
                        <td>
                            <?php if ($quiz['avg_score']): ?>
                            <div class="text-center">
                                <strong class="text-success"><?php echo number_format($quiz['avg_score'], 1); ?>%</strong>
                            </div>
                            <?php else: ?>
                            <span class="text-muted">لا توجد نتائج</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="quiz-questions.php?quiz_id=<?php echo $quiz['id']; ?>" 
                                   class="btn btn-sm btn-outline-primary" title="إدارة الأسئلة">
                                    <i class="fas fa-list"></i>
                                </a>
                                <a href="quiz-results.php?quiz_id=<?php echo $quiz['id']; ?>" 
                                   class="btn btn-sm btn-outline-success" title="النتائج">
                                    <i class="fas fa-chart-bar"></i>
                                </a>
                                <a href="edit-quiz.php?id=<?php echo $quiz['id']; ?>" 
                                   class="btn btn-sm btn-outline-warning" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button class="btn btn-sm btn-outline-danger" 
                                        onclick="deleteQuiz(<?php echo $quiz['id']; ?>, '<?php echo htmlspecialchars($quiz['title'], ENT_QUOTES); ?>')"
                                        title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php endif; ?>
    </div>
</div>



<!-- Modal حذف اختبار -->
<div class="modal fade" id="deleteQuizModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title text-danger">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="delete_quiz">
                    <input type="hidden" name="quiz_id" id="deleteQuizId">

                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        هل أنت متأكد من حذف الاختبار <strong id="deleteQuizTitle"></strong>؟
                    </div>

                    <p class="text-muted">
                        <strong>تحذير:</strong> سيتم حذف الاختبار وجميع الأسئلة والمحاولات والنتائج المرتبطة به نهائياً.
                        هذا الإجراء لا يمكن التراجع عنه.
                    </p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-danger">تأكيد الحذف</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal دليل الاختبارات -->
<div class="modal fade" id="quizGuideModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">
                    <i class="fas fa-graduation-cap me-2"></i>
                    دليل إنشاء الاختبارات
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary mb-3">
                            <i class="fas fa-list-ol me-2"></i>
                            خطوات إنشاء اختبار
                        </h6>
                        <ol class="list-group list-group-numbered">
                            <li class="list-group-item border-0 ps-0">
                                <strong>إنشاء الاختبار:</strong> حدد الكورس والعنوان والإعدادات
                            </li>
                            <li class="list-group-item border-0 ps-0">
                                <strong>إضافة الأسئلة:</strong> أضف أسئلة متنوعة مع خيارات متعددة
                            </li>
                            <li class="list-group-item border-0 ps-0">
                                <strong>المراجعة:</strong> راجع الاختبار في صفحة المعاينة
                            </li>
                            <li class="list-group-item border-0 ps-0">
                                <strong>النشر:</strong> فعّل الاختبار ليصبح متاحاً للطلاب
                            </li>
                            <li class="list-group-item border-0 ps-0">
                                <strong>المتابعة:</strong> راقب النتائج والإحصائيات
                            </li>
                        </ol>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-success mb-3">
                            <i class="fas fa-question-circle me-2"></i>
                            أنواع الأسئلة المدعومة
                        </h6>
                        <div class="list-group">
                            <div class="list-group-item border-0 ps-0">
                                <i class="fas fa-check-circle text-primary me-2"></i>
                                <strong>اختيار متعدد:</strong> أسئلة مع عدة خيارات
                            </div>
                            <div class="list-group-item border-0 ps-0">
                                <i class="fas fa-toggle-on text-success me-2"></i>
                                <strong>صح/خطأ:</strong> أسئلة ثنائية الخيار
                            </div>
                            <div class="list-group-item border-0 ps-0">
                                <i class="fas fa-edit text-info me-2"></i>
                                <strong>إجابة قصيرة:</strong> إجابات نصية مختصرة
                            </div>
                            <div class="list-group-item border-0 ps-0">
                                <i class="fas fa-file-alt text-warning me-2"></i>
                                <strong>مقال:</strong> إجابات مفصلة وطويلة
                            </div>
                        </div>

                        <h6 class="text-info mb-3 mt-4">
                            <i class="fas fa-cogs me-2"></i>
                            إعدادات متقدمة
                        </h6>
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <i class="fas fa-clock text-warning me-2"></i>
                                تحديد مدة زمنية للاختبار
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-redo text-primary me-2"></i>
                                عدد المحاولات المسموحة
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-calendar text-success me-2"></i>
                                جدولة تواريخ الإتاحة
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-random text-info me-2"></i>
                                ترتيب عشوائي للأسئلة
                            </li>
                        </ul>
                    </div>
                </div>

                <div class="alert alert-info mt-4">
                    <h6 class="alert-heading">
                        <i class="fas fa-lightbulb me-2"></i>
                        نصائح لاختبار فعال
                    </h6>
                    <ul class="mb-0">
                        <li>اكتب أسئلة واضحة ومحددة</li>
                        <li>تنوع في أنواع الأسئلة لتقييم شامل</li>
                        <li>حدد درجة نجاح مناسبة (عادة 60-70%)</li>
                        <li>اختبر الاختبار بنفسك قبل نشره</li>
                        <li>راجع النتائج لتحسين الأسئلة مستقبلاً</li>
                    </ul>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <a href="create-quiz.php" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>إنشاء اختبار الآن
                </a>
            </div>
        </div>
    </div>
</div>

<style>
/* تحسينات CSS للأزرار والتأثيرات */
.btn-lg {
    transition: all 0.3s ease;
    border-radius: 10px;
}

.empty-state-container {
    padding: 2rem;
}

.empty-state-icon {
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.alert {
    transition: all 0.3s ease;
}

.card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.btn-group .btn {
    transition: all 0.2s ease;
}

.btn-group .btn:hover {
    transform: scale(1.05);
}

/* تحسين شريط الإنشاء السريع */
.alert-primary {
    border-radius: 15px;
    border: none;
}

/* تحسين الجدول */
.table th {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border: none;
    font-weight: 600;
    color: #495057;
}

.table td {
    vertical-align: middle;
    border-color: #f1f3f4;
}

/* تحسين البطاقات الإحصائية */
.card-body {
    border-radius: 10px;
}

.bg-opacity-10 {
    border-radius: 12px;
}

/* تحسين النوافذ المنبثقة */
.modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.modal-header {
    border-radius: 15px 15px 0 0;
    border-bottom: none;
}

.modal-footer {
    border-top: none;
    border-radius: 0 0 15px 15px;
}

/* تحسين القوائم المنسدلة */
.dropdown-menu {
    border-radius: 10px;
    border: none;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.dropdown-item {
    border-radius: 5px;
    margin: 2px 5px;
    transition: all 0.2s ease;
}

.dropdown-item:hover {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    transform: translateX(5px);
}

/* تحسين الشارات */
.badge {
    border-radius: 8px;
    font-weight: 500;
}

/* تأثيرات الحركة */
@media (prefers-reduced-motion: no-preference) {
    .btn, .card, .alert {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }
}

/* تحسين للشاشات الصغيرة */
@media (max-width: 768px) {
    .empty-state-container {
        padding: 1rem;
    }

    .btn-lg {
        font-size: 1rem;
        padding: 0.75rem 1.5rem;
    }

    .alert-primary .row {
        text-align: center;
    }

    .alert-primary .col-md-4 {
        margin-top: 1rem;
    }
}

/* زر عائم لإنشاء اختبار */
.fab-create-quiz {
    position: fixed;
    bottom: 30px;
    left: 30px;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border: none;
    border-radius: 50%;
    color: white;
    font-size: 1.5rem;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    z-index: 1000;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
}

.fab-create-quiz:hover {
    transform: scale(1.1) rotate(90deg);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
    color: white;
}

.fab-create-quiz:active {
    transform: scale(0.95);
}

.fab-tooltip {
    position: absolute;
    right: 70px;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 0.875rem;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    pointer-events: none;
}

.fab-create-quiz:hover .fab-tooltip {
    opacity: 1;
    visibility: visible;
    right: 75px;
}

.fab-tooltip::after {
    content: '';
    position: absolute;
    left: 100%;
    top: 50%;
    transform: translateY(-50%);
    border: 5px solid transparent;
    border-left-color: rgba(0, 0, 0, 0.8);
}

/* إخفاء الزر العائم على الشاشات الصغيرة */
@media (max-width: 768px) {
    .fab-create-quiz {
        bottom: 20px;
        left: 20px;
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }
}

/* تحسين إضافي للأزرار */
.btn-primary.btn-lg {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border: none;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-primary.btn-lg:hover {
    background: linear-gradient(135deg, #5a6fd8, #6a4190);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}
</style>

<!-- زر عائم لإنشاء اختبار سريع -->
<a href="create-quiz.php" class="fab-create-quiz" title="إنشاء اختبار جديد">
    <i class="fas fa-plus"></i>
    <span class="fab-tooltip">إنشاء اختبار جديد</span>
</a>

<script>
// حذف اختبار
function deleteQuiz(quizId, quizTitle) {
    document.getElementById('deleteQuizId').value = quizId;
    document.getElementById('deleteQuizTitle').textContent = quizTitle;

    new bootstrap.Modal(document.getElementById('deleteQuizModal')).show();
}

// تحديث الحد الأدنى للتواريخ
document.addEventListener('DOMContentLoaded', function() {
    const availableFromInput = document.getElementById('available_from');
    const availableUntilInput = document.getElementById('available_until');

    if (availableFromInput && availableUntilInput) {
        // تعيين الحد الأدنى للتاريخ (الآن)
        const now = new Date();
        const nowString = now.toISOString().slice(0, 16);
        availableFromInput.min = nowString;
        availableUntilInput.min = nowString;

        // تحديث الحد الأدنى لتاريخ الانتهاء عند تغيير تاريخ البداية
        availableFromInput.addEventListener('change', function() {
            if (this.value) {
                availableUntilInput.min = this.value;
                if (availableUntilInput.value && availableUntilInput.value < this.value) {
                    availableUntilInput.value = '';
                }
            }
        });
    }

    // تهيئة DataTables إذا كان متوفراً
    if (typeof DataTable !== 'undefined' && document.querySelector('table')) {
        new DataTable('table', {
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/ar.json'
            },
            pageLength: 25,
            order: [[0, 'asc']], // ترتيب حسب اسم الاختبار
            columnDefs: [
                { orderable: false, targets: [7] } // عدم ترتيب عمود الإجراءات
            ]
        });
    }

    // إضافة تأثيرات بصرية للصفوف
    const rows = document.querySelectorAll('tbody tr');
    rows.forEach(row => {
        row.addEventListener('mouseenter', function() {
            this.style.backgroundColor = 'rgba(0,123,255,0.05)';
        });

        row.addEventListener('mouseleave', function() {
            this.style.backgroundColor = '';
        });
    });
});

// التحقق من صحة النموذج
document.querySelector('#createQuizModal form').addEventListener('submit', function(e) {
    const availableFrom = document.getElementById('available_from').value;
    const availableUntil = document.getElementById('available_until').value;

    if (availableFrom && availableUntil && availableFrom >= availableUntil) {
        e.preventDefault();
        alert('تاريخ الانتهاء يجب أن يكون بعد تاريخ البداية');
        return false;
    }

    const timeLimit = parseInt(document.getElementById('time_limit').value);
    if (timeLimit < 0) {
        e.preventDefault();
        alert('المدة الزمنية لا يمكن أن تكون سالبة');
        return false;
    }

    const maxAttempts = parseInt(document.getElementById('max_attempts').value);
    if (maxAttempts < 1 || maxAttempts > 10) {
        e.preventDefault();
        alert('عدد المحاولات يجب أن يكون بين 1 و 10');
        return false;
    }

    const passingGrade = parseInt(document.getElementById('passing_grade').value);
    if (passingGrade < 0 || passingGrade > 100) {
        e.preventDefault();
        alert('درجة النجاح يجب أن تكون بين 0 و 100');
        return false;
    }
});

// تحديث الصفحة كل 5 دقائق لتحديث حالة الاختبارات
setInterval(function() {
    // يمكن إضافة AJAX لتحديث البيانات
    console.log('تحديث حالة الاختبارات...');
}, 300000);

// دليل الاختبارات
function showQuizGuide() {
    const guideModal = new bootstrap.Modal(document.getElementById('quizGuideModal'));
    guideModal.show();
}

// تأثيرات بصرية للأزرار
document.addEventListener('DOMContentLoaded', function() {
    // تأثير hover للأزرار الرئيسية
    const primaryButtons = document.querySelectorAll('.btn-primary, .btn-lg');
    primaryButtons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
            this.style.boxShadow = '0 4px 15px rgba(0,123,255,0.3)';
        });

        button.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '';
        });
    });

    // تأثير النقر
    primaryButtons.forEach(button => {
        button.addEventListener('click', function() {
            this.style.transform = 'scale(0.98)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
        });
    });
});

// إضافة تأثيرات للزر العائم
document.addEventListener('DOMContentLoaded', function() {
    const fab = document.querySelector('.fab-create-quiz');

    // تأثير النبض للزر العائم
    setInterval(() => {
        if (!fab.matches(':hover')) {
            fab.style.animation = 'pulse 2s ease-in-out';
            setTimeout(() => {
                fab.style.animation = '';
            }, 2000);
        }
    }, 10000);

    // إخفاء/إظهار الزر العائم عند التمرير
    let lastScrollTop = 0;
    window.addEventListener('scroll', function() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

        if (scrollTop > lastScrollTop && scrollTop > 100) {
            // التمرير لأسفل
            fab.style.transform = 'translateY(100px)';
            fab.style.opacity = '0';
        } else {
            // التمرير لأعلى
            fab.style.transform = 'translateY(0)';
            fab.style.opacity = '1';
        }

        lastScrollTop = scrollTop;
    });
});

// تحسين تجربة النموذج
document.addEventListener('DOMContentLoaded', function() {
    const createQuizModal = document.getElementById('createQuizModal');

    createQuizModal.addEventListener('shown.bs.modal', function() {
        // التركيز على أول حقل
        const firstInput = this.querySelector('select[name="course_id"]');
        if (firstInput) {
            firstInput.focus();
        }

        // إضافة تأثير ترحيب
        const modalBody = this.querySelector('.modal-body');
        modalBody.style.opacity = '0';
        modalBody.style.transform = 'translateY(20px)';

        setTimeout(() => {
            modalBody.style.transition = 'all 0.3s ease';
            modalBody.style.opacity = '1';
            modalBody.style.transform = 'translateY(0)';
        }, 100);
    });

    // إعادة تعيين النموذج عند الإغلاق
    createQuizModal.addEventListener('hidden.bs.modal', function() {
        const form = this.querySelector('form');
        if (form) {
            form.reset();
            // إزالة رسائل الخطأ إن وجدت
            const errorMessages = form.querySelectorAll('.text-danger');
            errorMessages.forEach(msg => msg.remove());
        }
    });
});

// إضافة CSS للتأثيرات
const style = document.createElement('style');
style.textContent = `
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }

    .modal-body {
        transition: all 0.3s ease;
    }

    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    .btn-primary:focus {
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.5);
    }
`;
document.head.appendChild(style);

// دوال مساعدة
function previewQuiz(quizId) {
    window.open(`quiz-preview.php?id=${quizId}`, '_blank');
}

function duplicateQuiz(quizId) {
    if (confirm('هل تريد إنشاء نسخة من هذا الاختبار؟')) {
        window.location.href = `duplicate-quiz.php?id=${quizId}`;
    }
}

function exportQuizResults(quizId) {
    window.open(`export-quiz-results.php?quiz_id=${quizId}`, '_blank');
}
</script>

<style>
.table th {
    border-top: none;
    font-weight: 600;
    color: var(--bs-gray-700);
}

.btn-group .btn {
    border-radius: 0.375rem;
}

.btn-group .btn:not(:last-child) {
    margin-right: 0.25rem;
}

.badge {
    font-size: 0.75rem;
}

.card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1) !important;
}

/* تحسين عرض حالة الاختبارات */
.badge.bg-success {
    background-color: #198754 !important;
}

.badge.bg-info {
    background-color: #0dcaf0 !important;
}

.badge.bg-danger {
    background-color: #dc3545 !important;
}

/* تحسين عرض الإحصائيات */
.card-body .text-center strong {
    font-size: 1.1rem;
}

@media (max-width: 768px) {
    .btn-group {
        display: flex;
        flex-direction: column;
        width: 100%;
    }

    .btn-group .btn {
        margin-right: 0;
        margin-bottom: 0.25rem;
    }

    .table-responsive {
        font-size: 0.875rem;
    }

    .modal-dialog {
        margin: 0.5rem;
    }
}

/* تحسين عرض النماذج */
.form-check-label {
    cursor: pointer;
}

.form-text {
    font-size: 0.8rem;
}

/* تحسين عرض الأيقونات */
.fas {
    width: 1rem;
    text-align: center;
}
</style>

<?php include 'includes/footer.php'; ?>
