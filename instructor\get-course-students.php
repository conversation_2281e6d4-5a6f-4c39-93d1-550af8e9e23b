<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات المدرب
if (!isLoggedIn() || !isInstructor()) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit;
}

$course_id = $_GET['course_id'] ?? '';

if (!$course_id) {
    echo json_encode(['success' => false, 'message' => 'معرف الكورس مطلوب']);
    exit;
}

try {
    // التحقق من أن الكورس ينتمي للمدرب
    $stmt = $conn->prepare("SELECT id FROM courses WHERE id = ? AND instructor_id = ?");
    $stmt->execute([$course_id, $_SESSION['user_id']]);
    
    if (!$stmt->fetch()) {
        echo json_encode(['success' => false, 'message' => 'الكورس غير موجود أو لا تملك صلاحية للوصول إليه']);
        exit;
    }
    
    // جلب طلاب الكورس
    $stmt = $conn->prepare("
        SELECT DISTINCT u.id, u.name, u.email
        FROM users u
        INNER JOIN course_enrollments ce ON u.id = ce.student_id
        WHERE ce.course_id = ? AND ce.status = 'active'
        ORDER BY u.name
    ");
    $stmt->execute([$course_id]);
    $students = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'success' => true,
        'students' => $students,
        'count' => count($students)
    ]);
    
} catch (PDOException $e) {
    echo json_encode(['success' => false, 'message' => 'حدث خطأ أثناء جلب البيانات']);
}
?>
