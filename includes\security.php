<?php
require_once __DIR__ . '/session_config.php';

// بدء الجلسة إذا لم تكن نشطة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Security Headers
function setSecurityHeaders() {
    // Prevent Clickjacking
    header("X-Frame-Options: DENY");
    // XSS Protection
    header("X-XSS-Protection: 1; mode=block");
    // Prevent MIME-type sniffing
    header("X-Content-Type-Options: nosniff");
    // Referrer Policy
    header("Referrer-Policy: strict-origin-when-cross-origin");
    // Content Security Policy
    header("Content-Security-Policy: default-src 'self' https: data: 'unsafe-inline' 'unsafe-eval'; frame-ancestors 'none';");
    // HSTS (uncomment if you have HTTPS)
    // header("Strict-Transport-Security: max-age=31536000; includeSubDomains; preload");
}

// CSRF Protection
function generateCSRFToken() {
    if (empty($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

function validateCSRFToken($token) {
    if (!isset($_SESSION['csrf_token']) || !hash_equals($_SESSION['csrf_token'], $token)) {
        error_log("CSRF Token Validation Failed");
        die('انتهت صلاحية الجلسة. يرجى تحديث الصفحة والمحاولة مرة أخرى.');
    }
}

// Rate Limiting
function checkRateLimit($key, $limit = 5, $timeframe = 60) {
    $redis = new Redis();
    try {
        $redis->connect('127.0.0.1', 6379);
        $requests = $redis->incr($key);
        
        if ($requests === 1) {
            $redis->expire($key, $timeframe);
        }
        
        if ($requests > $limit) {
            error_log("Rate limit exceeded for key: $key");
            http_response_code(429);
            die('تم تجاوز عدد المحاولات المسموح بها. يرجى المحاولة لاحقاً.');
        }
    } catch (Exception $e) {
        error_log("Redis connection failed: " . $e->getMessage());
    }
}

// Input Validation and Sanitization
function sanitizeInput($input) {
    if (is_array($input)) {
        return array_map('sanitizeInput', $input);
    }
    $input = trim($input);
    $input = stripslashes($input);
    $input = htmlspecialchars($input, ENT_QUOTES, 'UTF-8');
    return $input;
}


// Password Validation
function validatePassword($password) {
    if (strlen($password) < 12) {
        return 'يجب أن تكون كلمة المرور 12 حرفاً على الأقل';
    }
    if (!preg_match('/[A-Z]/', $password)) {
        return 'يجب أن تحتوي كلمة المرور على حرف كبير واحد على الأقل';
    }
    if (!preg_match('/[a-z]/', $password)) {
        return 'يجب أن تحتوي كلمة المرور على حرف صغير واحد على الأقل';
    }
    if (!preg_match('/[0-9]/', $password)) {
        return 'يجب أن تحتوي كلمة المرور على رقم واحد على الأقل';
    }
    if (!preg_match('/[^A-Za-z0-9]/', $password)) {
        return 'يجب أن تحتوي كلمة المرور على رمز خاص واحد على الأقل';
    }
    return true;
}

// Session Security
function secureSession() {
    // تجديد معرف الجلسة بشكل دوري
    if (!isset($_SESSION['created'])) {
        $_SESSION['created'] = time();
    } else if (time() - $_SESSION['created'] > 3600) {
        session_regenerate_id(true);
        $_SESSION['created'] = time();
    }
}

// File Upload Security
function validateFile($file, $allowedTypes = ['image/jpeg', 'image/png'], $maxSize = 5242880) {
    if ($file['error'] !== UPLOAD_ERR_OK) {
        return 'حدث خطأ أثناء رفع الملف';
    }
    
    if (!in_array($file['type'], $allowedTypes)) {
        return 'نوع الملف غير مسموح به';
    }
    
    if ($file['size'] > $maxSize) {
        return 'حجم الملف كبير جداً';
    }
    
    $finfo = finfo_open(FILEINFO_MIME_TYPE);
    $mimeType = finfo_file($finfo, $file['tmp_name']);
    finfo_close($finfo);
    
    if (!in_array($mimeType, $allowedTypes)) {
        return 'نوع الملف غير صالح';
    }
    
    return true;
}

// IP Blocking
function checkIPBlacklist($ip) {
    try {
        global $conn;
        if (!$conn instanceof PDO) {
            error_log("Database connection error in checkIPBlacklist");
            return false;
        }

        $query = "SELECT COUNT(*) FROM blocked_ips WHERE ip = ? AND blocked_until > NOW()";
        $stmt = $conn->prepare($query);
        if (!$stmt) {
            error_log("Failed to prepare statement in checkIPBlacklist");
            return false;
        }

        $stmt->execute([$ip]);
        if ($stmt->fetchColumn() > 0) {
            error_log("Blocked IP attempt: $ip");
            die('تم حظر عنوان IP الخاص بك');
        }
        return true;
    } catch (PDOException $e) {
        error_log("Database error in checkIPBlacklist: " . $e->getMessage());
        return false;
    } catch (Exception $e) {
        error_log("General error in checkIPBlacklist: " . $e->getMessage());
        return false;
    }
}

// Login Attempt Tracking
function trackLoginAttempt($email, $success = false) {
    global $conn;
    $ip = $_SERVER['REMOTE_ADDR'];
    
    prepareAndExecute($conn, 
        "INSERT INTO login_attempts (email, ip_address, success, attempt_time) VALUES (?, ?, ?, NOW())",
        [$email, $ip, $success]
    );
    
    if (!$success) {
        $stmt = prepareAndExecute($conn, 
            "SELECT COUNT(*) FROM login_attempts WHERE email = ? AND success = 0 AND attempt_time > DATE_SUB(NOW(), INTERVAL 15 MINUTE)",
            [$email]
        );
        
        if ($stmt->fetchColumn() >= 5) {
            prepareAndExecute($conn,
                "INSERT INTO blocked_ips (ip, blocked_until) VALUES (?, DATE_ADD(NOW(), INTERVAL 1 HOUR))",
                [$ip]
            );
            die('تم تجاوز عدد محاولات تسجيل الدخول المسموح بها. يرجى المحاولة بعد ساعة.');
        }
    }
}

// URL Validation
function validateURL($url) {
    $pattern = '/^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/';
    return filter_var($url, FILTER_VALIDATE_URL) && preg_match($pattern, $url);
}

// Email Validation
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) && 
           preg_match('/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/', $email);
}

// Phone Number Validation
function validatePhone($phone) {
    return preg_match('/^[0-9]{10}$/', $phone);
}

// Secure File Download
function secureDownload($filePath, $fileName) {
    if (!file_exists($filePath)) {
        die('الملف غير موجود');
    }
    
    $mimeType = mime_content_type($filePath);
    header('Content-Type: ' . $mimeType);
    header('Content-Disposition: attachment; filename="' . basename($fileName) . '"');
    header('Content-Length: ' . filesize($filePath));
    header('X-Content-Type-Options: nosniff');
    
    readfile($filePath);
    exit;
}

// Activity Logging moved to activity_logger.php

// Initialize Security
setSecurityHeaders();
secureSession();
checkIPBlacklist($_SERVER['REMOTE_ADDR']);
