<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات الطالب
if (!isLoggedIn() || !isStudent()) {
    header('Location: ../login.php');
    exit;
}

$pageTitle = 'الجدول الزمني';
$breadcrumbs = [
    ['title' => 'الجدول الزمني']
];

$student_id = $_SESSION['user_id'];
$current_date = date('Y-m-d');
$view_type = $_GET['view'] ?? 'week'; // week, month, day
$selected_date = $_GET['date'] ?? $current_date;

// حساب تواريخ العرض حسب النوع
switch ($view_type) {
    case 'day':
        $start_date = $selected_date;
        $end_date = $selected_date;
        break;
    case 'month':
        $start_date = date('Y-m-01', strtotime($selected_date));
        $end_date = date('Y-m-t', strtotime($selected_date));
        break;
    default: // week
        $start_date = date('Y-m-d', strtotime('monday this week', strtotime($selected_date)));
        $end_date = date('Y-m-d', strtotime('sunday this week', strtotime($selected_date)));
}

try {
    // جلب الجلسات المجدولة
    $stmt = $conn->prepare("
        SELECT s.*, c.title as course_title, c.id as course_id,
               u.name as instructor_name,
               sa.join_time, sa.leave_time,
               CASE 
                   WHEN s.start_time < NOW() AND s.end_time > NOW() THEN 'live'
                   WHEN s.start_time > NOW() THEN 'upcoming'
                   WHEN s.end_time < NOW() THEN 'completed'
                   ELSE 'scheduled'
               END as session_status
        FROM sessions s
        JOIN courses c ON s.course_id = c.id
        JOIN course_enrollments ce ON c.id = ce.course_id
        JOIN users u ON c.instructor_id = u.id
        LEFT JOIN session_attendees sa ON s.id = sa.session_id AND sa.user_id = ?
        WHERE ce.student_id = ? 
        AND DATE(s.start_time) BETWEEN ? AND ?
        AND s.status != 'cancelled'
        ORDER BY s.start_time ASC
    ");
    $stmt->execute([$student_id, $student_id, $start_date, $end_date]);
    $sessions = $stmt->fetchAll();
    
    // جلب مواعيد الواجبات
    $stmt = $conn->prepare("
        SELECT a.*, c.title as course_title, c.id as course_id,
               asub.id as submission_id,
               CASE 
                   WHEN a.due_date < NOW() THEN 'expired'
                   WHEN asub.id IS NOT NULL THEN 'submitted'
                   ELSE 'pending'
               END as assignment_status
        FROM assignments a
        JOIN courses c ON a.course_id = c.id
        JOIN course_enrollments ce ON c.id = ce.course_id
        LEFT JOIN assignment_submissions asub ON a.id = asub.assignment_id AND asub.student_id = ?
        WHERE ce.student_id = ? 
        AND DATE(a.due_date) BETWEEN ? AND ?
        AND a.status = 'published'
        ORDER BY a.due_date ASC
    ");
    $stmt->execute([$student_id, $student_id, $start_date, $end_date]);
    $assignments = $stmt->fetchAll();
    
    // جلب مواعيد الاختبارات
    $stmt = $conn->prepare("
        SELECT q.*, c.title as course_title, c.id as course_id,
               (SELECT COUNT(*) FROM quiz_attempts WHERE quiz_id = q.id AND student_id = ?) as attempt_count,
               CASE 
                   WHEN q.end_date IS NOT NULL AND q.end_date < NOW() THEN 'expired'
                   WHEN q.start_date IS NOT NULL AND q.start_date > NOW() THEN 'upcoming'
                   ELSE 'available'
               END as quiz_status
        FROM quizzes q
        JOIN courses c ON q.course_id = c.id
        JOIN course_enrollments ce ON c.id = ce.course_id
        WHERE ce.student_id = ? 
        AND (
            (q.start_date IS NOT NULL AND DATE(q.start_date) BETWEEN ? AND ?)
            OR (q.end_date IS NOT NULL AND DATE(q.end_date) BETWEEN ? AND ?)
        )
        AND q.status = 'published'
        ORDER BY COALESCE(q.start_date, q.end_date) ASC
    ");
    $stmt->execute([$student_id, $student_id, $start_date, $end_date, $start_date, $end_date]);
    $quizzes = $stmt->fetchAll();
    
    // تجميع الأحداث حسب التاريخ
    $events_by_date = [];
    
    // إضافة الجلسات
    foreach ($sessions as $session) {
        $date = date('Y-m-d', strtotime($session['start_time']));
        if (!isset($events_by_date[$date])) {
            $events_by_date[$date] = [];
        }
        $events_by_date[$date][] = [
            'type' => 'session',
            'data' => $session,
            'time' => date('H:i', strtotime($session['start_time'])),
            'end_time' => date('H:i', strtotime($session['end_time']))
        ];
    }
    
    // إضافة الواجبات
    foreach ($assignments as $assignment) {
        $date = date('Y-m-d', strtotime($assignment['due_date']));
        if (!isset($events_by_date[$date])) {
            $events_by_date[$date] = [];
        }
        $events_by_date[$date][] = [
            'type' => 'assignment',
            'data' => $assignment,
            'time' => date('H:i', strtotime($assignment['due_date']))
        ];
    }
    
    // إضافة الاختبارات
    foreach ($quizzes as $quiz) {
        if ($quiz['start_date']) {
            $date = date('Y-m-d', strtotime($quiz['start_date']));
            if (!isset($events_by_date[$date])) {
                $events_by_date[$date] = [];
            }
            $events_by_date[$date][] = [
                'type' => 'quiz_start',
                'data' => $quiz,
                'time' => date('H:i', strtotime($quiz['start_date']))
            ];
        }
        
        if ($quiz['end_date']) {
            $date = date('Y-m-d', strtotime($quiz['end_date']));
            if (!isset($events_by_date[$date])) {
                $events_by_date[$date] = [];
            }
            $events_by_date[$date][] = [
                'type' => 'quiz_end',
                'data' => $quiz,
                'time' => date('H:i', strtotime($quiz['end_date']))
            ];
        }
    }
    
    // ترتيب الأحداث حسب الوقت
    foreach ($events_by_date as $date => &$events) {
        usort($events, function($a, $b) {
            return strcmp($a['time'], $b['time']);
        });
    }
    
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء جلب الجدول الزمني';
}

include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- العنوان والتحكم -->
        <div class="col-12 mb-4">
            <div class="d-flex justify-content-between align-items-center flex-wrap">
                <h2 class="text-primary">
                    <i class="fas fa-calendar me-2"></i>
                    الجدول الزمني
                </h2>
                
                <div class="d-flex gap-2 flex-wrap">
                    <!-- أزرار التنقل -->
                    <div class="btn-group">
                        <?php
                        $prev_date = '';
                        $next_date = '';
                        switch ($view_type) {
                            case 'day':
                                $prev_date = date('Y-m-d', strtotime($selected_date . ' -1 day'));
                                $next_date = date('Y-m-d', strtotime($selected_date . ' +1 day'));
                                break;
                            case 'month':
                                $prev_date = date('Y-m-d', strtotime($selected_date . ' -1 month'));
                                $next_date = date('Y-m-d', strtotime($selected_date . ' +1 month'));
                                break;
                            default: // week
                                $prev_date = date('Y-m-d', strtotime($selected_date . ' -1 week'));
                                $next_date = date('Y-m-d', strtotime($selected_date . ' +1 week'));
                        }
                        ?>
                        <a href="?view=<?php echo $view_type; ?>&date=<?php echo $prev_date; ?>" 
                           class="btn btn-outline-primary">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                        <a href="?view=<?php echo $view_type; ?>&date=<?php echo $current_date; ?>" 
                           class="btn btn-outline-primary">اليوم</a>
                        <a href="?view=<?php echo $view_type; ?>&date=<?php echo $next_date; ?>" 
                           class="btn btn-outline-primary">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    </div>
                    
                    <!-- أزرار العرض -->
                    <div class="btn-group">
                        <a href="?view=day&date=<?php echo $selected_date; ?>" 
                           class="btn <?php echo $view_type === 'day' ? 'btn-primary' : 'btn-outline-primary'; ?>">يوم</a>
                        <a href="?view=week&date=<?php echo $selected_date; ?>" 
                           class="btn <?php echo $view_type === 'week' ? 'btn-primary' : 'btn-outline-primary'; ?>">أسبوع</a>
                        <a href="?view=month&date=<?php echo $selected_date; ?>" 
                           class="btn <?php echo $view_type === 'month' ? 'btn-primary' : 'btn-outline-primary'; ?>">شهر</a>
                    </div>
                </div>
            </div>
            
            <!-- عرض الفترة الحالية -->
            <div class="mt-2">
                <h5 class="text-muted">
                    <?php
                    switch ($view_type) {
                        case 'day':
                            echo date('l, j F Y', strtotime($selected_date));
                            break;
                        case 'month':
                            echo date('F Y', strtotime($selected_date));
                            break;
                        default: // week
                            echo date('j F', strtotime($start_date)) . ' - ' . date('j F Y', strtotime($end_date));
                    }
                    ?>
                </h5>
            </div>
        </div>

        <!-- الإحصائيات السريعة -->
        <div class="col-12 mb-4">
            <div class="row">
                <div class="col-md-3 mb-3">
                    <div class="card-student text-center">
                        <div class="card-body">
                            <i class="fas fa-video fa-2x text-primary mb-2"></i>
                            <h4 class="text-primary"><?php echo count($sessions); ?></h4>
                            <p class="text-muted mb-0">جلسات</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card-student text-center">
                        <div class="card-body">
                            <i class="fas fa-tasks fa-2x text-warning mb-2"></i>
                            <h4 class="text-warning"><?php echo count($assignments); ?></h4>
                            <p class="text-muted mb-0">واجبات</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card-student text-center">
                        <div class="card-body">
                            <i class="fas fa-question-circle fa-2x text-info mb-2"></i>
                            <h4 class="text-info"><?php echo count($quizzes); ?></h4>
                            <p class="text-muted mb-0">اختبارات</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card-student text-center">
                        <div class="card-body">
                            <i class="fas fa-calendar-check fa-2x text-success mb-2"></i>
                            <h4 class="text-success"><?php echo count($events_by_date); ?></h4>
                            <p class="text-muted mb-0">أيام نشطة</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- عرض الجدول -->
        <div class="col-12">
            <?php if ($view_type === 'month'): ?>
            <!-- عرض الشهر -->
            <div class="card-student">
                <div class="card-body">
                    <div class="calendar-grid">
                        <?php
                        $first_day = date('Y-m-01', strtotime($selected_date));
                        $last_day = date('Y-m-t', strtotime($selected_date));
                        $start_calendar = date('Y-m-d', strtotime('monday this week', strtotime($first_day)));
                        $end_calendar = date('Y-m-d', strtotime('sunday this week', strtotime($last_day)));
                        
                        echo '<div class="row text-center mb-2">';
                        $days = ['الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت', 'الأحد'];
                        foreach ($days as $day) {
                            echo '<div class="col"><strong>' . $day . '</strong></div>';
                        }
                        echo '</div>';
                        
                        $current_week_start = $start_calendar;
                        while ($current_week_start <= $end_calendar) {
                            echo '<div class="row mb-2">';
                            for ($i = 0; $i < 7; $i++) {
                                $current_day = date('Y-m-d', strtotime($current_week_start . " +$i days"));
                                $is_current_month = date('m', strtotime($current_day)) === date('m', strtotime($selected_date));
                                $is_today = $current_day === $current_date;
                                $has_events = isset($events_by_date[$current_day]);
                                
                                echo '<div class="col">';
                                echo '<div class="calendar-day ' . 
                                     ($is_today ? 'today ' : '') . 
                                     ($is_current_month ? '' : 'other-month ') . 
                                     ($has_events ? 'has-events' : '') . '">';
                                echo '<div class="day-number">' . date('j', strtotime($current_day)) . '</div>';
                                
                                if ($has_events) {
                                    echo '<div class="events-indicator">';
                                    $event_count = count($events_by_date[$current_day]);
                                    echo '<small class="badge bg-primary">' . $event_count . '</small>';
                                    echo '</div>';
                                }
                                
                                echo '</div>';
                                echo '</div>';
                            }
                            echo '</div>';
                            $current_week_start = date('Y-m-d', strtotime($current_week_start . ' +1 week'));
                        }
                        ?>
                    </div>
                </div>
            </div>
            
            <?php else: ?>
            <!-- عرض الأسبوع أو اليوم -->
            <?php if (empty($events_by_date)): ?>
            <div class="card-student text-center py-5">
                <div class="card-body">
                    <i class="fas fa-calendar fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">لا توجد أحداث مجدولة</h4>
                    <p class="text-muted">لا توجد جلسات أو واجبات أو اختبارات في هذه الفترة</p>
                </div>
            </div>
            <?php else: ?>
            
            <?php
            // إنشاء قائمة بجميع الأيام في الفترة
            $period = new DatePeriod(
                new DateTime($start_date),
                new DateInterval('P1D'),
                new DateTime($end_date . ' +1 day')
            );
            ?>
            
            <div class="timeline-view">
                <?php foreach ($period as $date): ?>
                    <?php 
                    $date_str = $date->format('Y-m-d');
                    $is_today = $date_str === $current_date;
                    $has_events = isset($events_by_date[$date_str]);
                    ?>
                    
                    <div class="day-section mb-4">
                        <div class="day-header <?php echo $is_today ? 'today' : ''; ?>">
                            <h5 class="mb-0">
                                <?php echo $date->format('l, j F Y'); ?>
                                <?php if ($is_today): ?>
                                <span class="badge bg-primary ms-2">اليوم</span>
                                <?php endif; ?>
                            </h5>
                        </div>
                        
                        <?php if ($has_events): ?>
                        <div class="events-list">
                            <?php foreach ($events_by_date[$date_str] as $event): ?>
                            <div class="event-item <?php echo $event['type']; ?>">
                                <?php
                                switch ($event['type']) {
                                    case 'session':
                                        $session = $event['data'];
                                        $status_class = '';
                                        $status_icon = '';
                                        switch ($session['session_status']) {
                                            case 'live':
                                                $status_class = 'bg-danger';
                                                $status_icon = 'fas fa-circle';
                                                break;
                                            case 'upcoming':
                                                $status_class = 'bg-primary';
                                                $status_icon = 'fas fa-clock';
                                                break;
                                            case 'completed':
                                                $status_class = 'bg-success';
                                                $status_icon = 'fas fa-check';
                                                break;
                                        }
                                        ?>
                                        <div class="event-content">
                                            <div class="event-time">
                                                <i class="fas fa-video text-primary"></i>
                                                <?php echo $event['time'] . ' - ' . $event['end_time']; ?>
                                            </div>
                                            <div class="event-details">
                                                <h6 class="mb-1"><?php echo htmlspecialchars($session['title']); ?></h6>
                                                <p class="text-muted mb-1">
                                                    <strong><?php echo htmlspecialchars($session['course_title']); ?></strong>
                                                    - <?php echo htmlspecialchars($session['instructor_name']); ?>
                                                </p>
                                                <span class="badge <?php echo $status_class; ?>">
                                                    <i class="<?php echo $status_icon; ?> me-1"></i>
                                                    <?php
                                                    $status_text = [
                                                        'live' => 'مباشر الآن',
                                                        'upcoming' => 'قادم',
                                                        'completed' => 'مكتمل'
                                                    ];
                                                    echo $status_text[$session['session_status']];
                                                    ?>
                                                </span>
                                            </div>
                                            <div class="event-actions">
                                                <?php if ($session['session_status'] === 'live'): ?>
                                                <a href="join-session.php?id=<?php echo $session['id']; ?>" 
                                                   class="btn btn-sm btn-danger">
                                                    <i class="fas fa-play me-1"></i>انضم الآن
                                                </a>
                                                <?php elseif ($session['session_status'] === 'upcoming'): ?>
                                                <button class="btn btn-sm btn-outline-primary" disabled>
                                                    <i class="fas fa-clock me-1"></i>قريباً
                                                </button>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                        <?php
                                        break;
                                        
                                    case 'assignment':
                                        $assignment = $event['data'];
                                        $status_class = '';
                                        switch ($assignment['assignment_status']) {
                                            case 'submitted':
                                                $status_class = 'bg-success';
                                                break;
                                            case 'expired':
                                                $status_class = 'bg-danger';
                                                break;
                                            default:
                                                $status_class = 'bg-warning';
                                        }
                                        ?>
                                        <div class="event-content">
                                            <div class="event-time">
                                                <i class="fas fa-tasks text-warning"></i>
                                                موعد التسليم: <?php echo $event['time']; ?>
                                            </div>
                                            <div class="event-details">
                                                <h6 class="mb-1"><?php echo htmlspecialchars($assignment['title']); ?></h6>
                                                <p class="text-muted mb-1">
                                                    <strong><?php echo htmlspecialchars($assignment['course_title']); ?></strong>
                                                </p>
                                                <span class="badge <?php echo $status_class; ?>">
                                                    <?php
                                                    $status_text = [
                                                        'submitted' => 'مُسلم',
                                                        'expired' => 'منتهي',
                                                        'pending' => 'معلق'
                                                    ];
                                                    echo $status_text[$assignment['assignment_status']];
                                                    ?>
                                                </span>
                                            </div>
                                            <div class="event-actions">
                                                <a href="assignments.php" class="btn btn-sm btn-outline-warning">
                                                    <i class="fas fa-eye me-1"></i>عرض
                                                </a>
                                            </div>
                                        </div>
                                        <?php
                                        break;
                                        
                                    case 'quiz_start':
                                    case 'quiz_end':
                                        $quiz = $event['data'];
                                        $is_start = $event['type'] === 'quiz_start';
                                        ?>
                                        <div class="event-content">
                                            <div class="event-time">
                                                <i class="fas fa-question-circle text-info"></i>
                                                <?php echo $is_start ? 'بداية الاختبار: ' : 'نهاية الاختبار: '; ?>
                                                <?php echo $event['time']; ?>
                                            </div>
                                            <div class="event-details">
                                                <h6 class="mb-1"><?php echo htmlspecialchars($quiz['title']); ?></h6>
                                                <p class="text-muted mb-1">
                                                    <strong><?php echo htmlspecialchars($quiz['course_title']); ?></strong>
                                                </p>
                                                <span class="badge bg-info">
                                                    <?php echo $is_start ? 'بداية' : 'نهاية'; ?>
                                                </span>
                                            </div>
                                            <div class="event-actions">
                                                <a href="quizzes.php" class="btn btn-sm btn-outline-info">
                                                    <i class="fas fa-eye me-1"></i>عرض
                                                </a>
                                            </div>
                                        </div>
                                        <?php
                                        break;
                                }
                                ?>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        <?php else: ?>
                        <div class="no-events">
                            <p class="text-muted mb-0">لا توجد أحداث مجدولة</p>
                        </div>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            </div>
            <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
.calendar-day {
    border: 1px solid #e2e8f0;
    border-radius: 0.5rem;
    padding: 0.5rem;
    min-height: 80px;
    position: relative;
    cursor: pointer;
    transition: all 0.3s ease;
}

.calendar-day:hover {
    background-color: #f8fafc;
}

.calendar-day.today {
    background-color: var(--student-primary);
    color: white;
}

.calendar-day.other-month {
    opacity: 0.5;
}

.calendar-day.has-events {
    border-color: var(--student-primary);
}

.day-number {
    font-weight: bold;
    font-size: 1.1rem;
}

.events-indicator {
    position: absolute;
    top: 5px;
    right: 5px;
}

.day-header {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 0.75rem;
    padding: 1rem 1.5rem;
    margin-bottom: 1rem;
}

.day-header.today {
    background: linear-gradient(135deg, var(--student-primary) 0%, #1d4ed8 100%);
    color: white;
}

.event-item {
    background: white;
    border-radius: 0.75rem;
    padding: 1rem;
    margin-bottom: 1rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    border-left: 4px solid #e2e8f0;
    transition: all 0.3s ease;
}

.event-item:hover {
    transform: translateX(-5px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.event-item.session {
    border-left-color: var(--student-primary);
}

.event-item.assignment {
    border-left-color: var(--student-warning);
}

.event-item.quiz_start,
.event-item.quiz_end {
    border-left-color: var(--student-info);
}

.event-content {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.event-time {
    min-width: 150px;
    font-weight: 500;
    color: var(--student-secondary);
}

.event-details {
    flex: 1;
}

.event-actions {
    min-width: 100px;
}

.no-events {
    text-align: center;
    padding: 2rem;
    background: #f8fafc;
    border-radius: 0.75rem;
    border: 2px dashed #e2e8f0;
}

@media (max-width: 768px) {
    .event-content {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .event-time {
        min-width: auto;
    }
    
    .event-actions {
        min-width: auto;
        width: 100%;
    }
    
    .event-actions .btn {
        width: 100%;
    }
}
</style>

<?php include 'includes/footer.php'; ?>
