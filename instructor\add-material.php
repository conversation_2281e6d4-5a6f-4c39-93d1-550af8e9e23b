<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات المدرب
if (!isLoggedIn() || !isInstructor()) {
    header('Location: ../login.php');
    exit;
}

$pageTitle = 'إضافة مادة تعليمية';
$breadcrumbs = [
    ['title' => 'المواد التعليمية', 'url' => 'materials.php'],
    ['title' => 'إضافة مادة جديدة']
];

// معالجة إضافة المادة
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $course_id = $_POST['course_id'] ?? 0;
    $title = trim($_POST['title'] ?? '');
    $description = trim($_POST['description'] ?? '');
    $material_type = $_POST['material_type'] ?? 'file';
    $category = trim($_POST['category'] ?? '');
    $is_downloadable = isset($_POST['is_downloadable']) ? 1 : 0;
    $is_required = isset($_POST['is_required']) ? 1 : 0;
    $order_number = $_POST['order_number'] ?? 1;
    
    $errors = [];
    
    // التحقق من صحة البيانات
    if (empty($title)) {
        $errors[] = 'عنوان المادة مطلوب';
    }
    
    if (empty($course_id)) {
        $errors[] = 'يجب اختيار كورس';
    }
    
    // معالجة رفع الملف
    $file_path = '';
    $file_size = 0;
    $file_type = '';
    
    if ($material_type === 'file' && isset($_FILES['material_file']) && $_FILES['material_file']['error'] === UPLOAD_ERR_OK) {
        $upload_dir = '../uploads/materials/';
        if (!is_dir($upload_dir)) {
            mkdir($upload_dir, 0755, true);
        }
        
        $file_info = pathinfo($_FILES['material_file']['name']);
        $allowed_extensions = ['pdf', 'doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx', 'txt', 'zip', 'rar'];
        
        if (!in_array(strtolower($file_info['extension']), $allowed_extensions)) {
            $errors[] = 'نوع الملف غير مدعوم. الأنواع المدعومة: ' . implode(', ', $allowed_extensions);
        } else {
            $file_name = uniqid() . '_' . time() . '.' . $file_info['extension'];
            $file_path = $upload_dir . $file_name;
            $file_size = $_FILES['material_file']['size'];
            $file_type = $_FILES['material_file']['type'];
            
            if (!move_uploaded_file($_FILES['material_file']['tmp_name'], $file_path)) {
                $errors[] = 'فشل في رفع الملف';
            }
        }
    } elseif ($material_type === 'file') {
        $errors[] = 'يرجى اختيار ملف للرفع';
    }
    
    // معالجة الرابط
    $external_url = '';
    if ($material_type === 'link') {
        $external_url = trim($_POST['external_url'] ?? '');
        if (empty($external_url)) {
            $errors[] = 'رابط المادة مطلوب';
        } elseif (!filter_var($external_url, FILTER_VALIDATE_URL)) {
            $errors[] = 'رابط غير صحيح';
        }
    }
    
    // معالجة النص
    $content_text = '';
    if ($material_type === 'text') {
        $content_text = trim($_POST['content_text'] ?? '');
        if (empty($content_text)) {
            $errors[] = 'محتوى النص مطلوب';
        }
    }
    
    if (empty($errors)) {
        try {
            // التحقق من أن الكورس ينتمي للمدرب
            $stmt = $conn->prepare("SELECT id, title FROM courses WHERE id = ? AND instructor_id = ?");
            $stmt->execute([$course_id, $_SESSION['user_id']]);
            $course = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$course) {
                $errors[] = 'الكورس غير موجود أو لا تملك صلاحية للوصول إليه';
            } else {
                // إدراج المادة الجديدة
                $stmt = $conn->prepare("
                    INSERT INTO course_materials (
                        course_id, title, description, material_type, category,
                        file_path, file_size, file_type, external_url, content_text,
                        is_downloadable, is_required, order_number, uploaded_by, status
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'active')
                ");
                
                $final_file_path = $material_type === 'file' ? str_replace('../', '', $file_path) : null;
                
                $stmt->execute([
                    $course_id, $title, $description, $material_type, $category,
                    $final_file_path, $file_size, $file_type, $external_url, $content_text,
                    $is_downloadable, $is_required, $order_number, $_SESSION['user_id']
                ]);
                
                $material_id = $conn->lastInsertId();
                
                // إضافة سجل نشاط
                logActivity($conn, $_SESSION['user_id'], 'add_material', "تم إضافة مادة تعليمية: $title");
                
                $success_message = 'تم إضافة المادة التعليمية بنجاح';
                
                // إعادة توجيه بعد النجاح
                header("Location: materials.php?success=1");
                exit;
            }
        } catch (PDOException $e) {
            $errors[] = 'حدث خطأ أثناء حفظ المادة: ' . $e->getMessage();
        }
    }
}

// جلب الكورسات الخاصة بالمدرب
try {
    $stmt = $conn->prepare("
        SELECT id, title 
        FROM courses 
        WHERE instructor_id = ? AND status = 'active'
        ORDER BY title
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $instructor_courses = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $instructor_courses = [];
}

include 'includes/header.php';
?>

<!-- رأس الصفحة -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="h3 mb-2">
            <i class="fas fa-file-upload text-success me-2"></i>
            إضافة مادة تعليمية
        </h2>
        <p class="text-muted mb-0">إضافة ملفات ومواد تعليمية للكورس</p>
    </div>
    <div class="d-flex gap-2">
        <a href="materials.php" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>العودة للمواد
        </a>
        <a href="upload-video.php" class="btn btn-outline-primary">
            <i class="fas fa-video me-1"></i>رفع فيديو
        </a>
    </div>
</div>

<!-- رسائل الخطأ -->
<?php if (!empty($errors)): ?>
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <i class="fas fa-exclamation-triangle me-2"></i>
    <ul class="mb-0">
        <?php foreach ($errors as $error): ?>
        <li><?php echo htmlspecialchars($error); ?></li>
        <?php endforeach; ?>
    </ul>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<?php if (isset($success_message)): ?>
<div class="alert alert-success alert-dismissible fade show" role="alert">
    <i class="fas fa-check-circle me-2"></i>
    <?php echo $success_message; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<!-- نموذج إضافة المادة -->
<div class="row">
    <div class="col-lg-8">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-bottom">
                <h5 class="mb-0">
                    <i class="fas fa-plus-circle me-2"></i>
                    معلومات المادة التعليمية
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data" id="materialForm">
                    <!-- معلومات أساسية -->
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="course_id" class="form-label">الكورس <span class="text-danger">*</span></label>
                            <select name="course_id" id="course_id" class="form-select" required>
                                <option value="">-- اختر كورس --</option>
                                <?php foreach ($instructor_courses as $course): ?>
                                <option value="<?php echo $course['id']; ?>" 
                                        <?php echo (isset($_POST['course_id']) && $_POST['course_id'] == $course['id']) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($course['title']); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="title" class="form-label">عنوان المادة <span class="text-danger">*</span></label>
                            <input type="text" name="title" id="title" class="form-control" 
                                   value="<?php echo htmlspecialchars($_POST['title'] ?? ''); ?>" 
                                   placeholder="مثال: ملخص الدرس الأول" required>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="description" class="form-label">وصف المادة</label>
                            <textarea name="description" id="description" class="form-control" rows="3" 
                                      placeholder="وصف مختصر عن المادة التعليمية..."><?php echo htmlspecialchars($_POST['description'] ?? ''); ?></textarea>
                        </div>
                    </div>
                    
                    <!-- تصنيف المادة -->
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="category" class="form-label">التصنيف</label>
                            <select name="category" id="category" class="form-select">
                                <option value="">-- اختر تصنيف --</option>
                                <option value="lecture_notes" <?php echo (isset($_POST['category']) && $_POST['category'] == 'lecture_notes') ? 'selected' : ''; ?>>ملاحظات المحاضرة</option>
                                <option value="assignments" <?php echo (isset($_POST['category']) && $_POST['category'] == 'assignments') ? 'selected' : ''; ?>>الواجبات</option>
                                <option value="references" <?php echo (isset($_POST['category']) && $_POST['category'] == 'references') ? 'selected' : ''; ?>>المراجع</option>
                                <option value="exercises" <?php echo (isset($_POST['category']) && $_POST['category'] == 'exercises') ? 'selected' : ''; ?>>التمارين</option>
                                <option value="supplementary" <?php echo (isset($_POST['category']) && $_POST['category'] == 'supplementary') ? 'selected' : ''; ?>>مواد إضافية</option>
                                <option value="other" <?php echo (isset($_POST['category']) && $_POST['category'] == 'other') ? 'selected' : ''; ?>>أخرى</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="order_number" class="form-label">ترتيب المادة</label>
                            <input type="number" name="order_number" id="order_number" class="form-control" 
                                   value="<?php echo $_POST['order_number'] ?? '1'; ?>" 
                                   min="1" required>
                        </div>
                    </div>
                    
                    <!-- نوع المادة -->
                    <div class="mb-3">
                        <label class="form-label">نوع المادة</label>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="material_type" id="file_type" 
                                           value="file" <?php echo (!isset($_POST['material_type']) || $_POST['material_type'] === 'file') ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="file_type">
                                        <i class="fas fa-file text-primary me-2"></i>
                                        ملف
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="material_type" id="link_type" 
                                           value="link" <?php echo (isset($_POST['material_type']) && $_POST['material_type'] === 'link') ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="link_type">
                                        <i class="fas fa-link text-info me-2"></i>
                                        رابط خارجي
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="material_type" id="text_type" 
                                           value="text" <?php echo (isset($_POST['material_type']) && $_POST['material_type'] === 'text') ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="text_type">
                                        <i class="fas fa-align-left text-success me-2"></i>
                                        نص
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- رفع الملف -->
                    <div id="fileSection" class="mb-3">
                        <label for="material_file" class="form-label">الملف</label>
                        <div class="upload-area" id="uploadArea">
                            <input type="file" name="material_file" id="material_file" class="form-control" 
                                   accept=".pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx,.txt,.zip,.rar" style="display: none;">
                            <div class="upload-content text-center py-4">
                                <i class="fas fa-cloud-upload-alt text-primary" style="font-size: 3rem;"></i>
                                <h5 class="mt-3">اسحب الملف هنا أو انقر للاختيار</h5>
                                <p class="text-muted">الأنواع المدعومة: PDF, DOC, PPT, XLS, TXT, ZIP</p>
                                <p class="text-muted">الحد الأقصى: 50 ميجابايت</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- الرابط الخارجي -->
                    <div id="linkSection" class="mb-3" style="display: none;">
                        <label for="external_url" class="form-label">الرابط الخارجي</label>
                        <input type="url" name="external_url" id="external_url" class="form-control" 
                               value="<?php echo htmlspecialchars($_POST['external_url'] ?? ''); ?>" 
                               placeholder="https://example.com/resource">
                        <small class="form-text text-muted">
                            رابط لمورد خارجي مثل مقال أو موقع ويب
                        </small>
                    </div>
                    
                    <!-- محتوى النص -->
                    <div id="textSection" class="mb-3" style="display: none;">
                        <label for="content_text" class="form-label">محتوى النص</label>
                        <textarea name="content_text" id="content_text" class="form-control" rows="8" 
                                  placeholder="اكتب محتوى المادة التعليمية هنا..."><?php echo htmlspecialchars($_POST['content_text'] ?? ''); ?></textarea>
                    </div>
                    
                    <!-- إعدادات إضافية -->
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="is_downloadable" id="is_downloadable" 
                                       <?php echo (isset($_POST['is_downloadable']) && $_POST['is_downloadable']) ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="is_downloadable">
                                    <i class="fas fa-download text-success me-2"></i>
                                    قابل للتحميل
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="is_required" id="is_required" 
                                       <?php echo (isset($_POST['is_required']) && $_POST['is_required']) ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="is_required">
                                    <i class="fas fa-exclamation-circle text-warning me-2"></i>
                                    مادة مطلوبة
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <button type="button" class="btn btn-outline-secondary" onclick="resetForm()">
                            <i class="fas fa-undo me-1"></i>إعادة تعيين
                        </button>
                        <div class="d-flex gap-2">
                            <button type="submit" name="save_draft" class="btn btn-outline-primary">
                                <i class="fas fa-save me-1"></i>حفظ كمسودة
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i>إضافة المادة
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- الشريط الجانبي -->
    <div class="col-lg-4">
        <!-- أنواع المواد -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-light border-bottom">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle text-info me-2"></i>
                    أنواع المواد المدعومة
                </h6>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-12">
                        <h6 class="text-primary mb-2">المستندات:</h6>
                        <span class="badge bg-light text-dark me-1">PDF</span>
                        <span class="badge bg-light text-dark me-1">DOC</span>
                        <span class="badge bg-light text-dark">DOCX</span>
                    </div>
                    <div class="col-12">
                        <h6 class="text-success mb-2">العروض التقديمية:</h6>
                        <span class="badge bg-light text-dark me-1">PPT</span>
                        <span class="badge bg-light text-dark">PPTX</span>
                    </div>
                    <div class="col-12">
                        <h6 class="text-warning mb-2">جداول البيانات:</h6>
                        <span class="badge bg-light text-dark me-1">XLS</span>
                        <span class="badge bg-light text-dark">XLSX</span>
                    </div>
                    <div class="col-12">
                        <h6 class="text-info mb-2">أخرى:</h6>
                        <span class="badge bg-light text-dark me-1">TXT</span>
                        <span class="badge bg-light text-dark me-1">ZIP</span>
                        <span class="badge bg-light text-dark">RAR</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- نصائح -->
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-light border-bottom">
                <h6 class="mb-0">
                    <i class="fas fa-lightbulb text-warning me-2"></i>
                    نصائح مفيدة
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        استخدم أسماء واضحة للملفات
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        رتب المواد حسب الأهمية
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        أضف وصفاً مفيداً لكل مادة
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        حدد المواد المطلوبة
                    </li>
                    <li class="mb-0">
                        <i class="fas fa-check text-success me-2"></i>
                        تأكد من جودة المحتوى
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
// التبديل بين أنواع المواد
document.querySelectorAll('input[name="material_type"]').forEach(radio => {
    radio.addEventListener('change', function() {
        const fileSection = document.getElementById('fileSection');
        const linkSection = document.getElementById('linkSection');
        const textSection = document.getElementById('textSection');
        
        // إخفاء جميع الأقسام
        fileSection.style.display = 'none';
        linkSection.style.display = 'none';
        textSection.style.display = 'none';
        
        // إظهار القسم المناسب
        if (this.value === 'file') {
            fileSection.style.display = 'block';
        } else if (this.value === 'link') {
            linkSection.style.display = 'block';
        } else if (this.value === 'text') {
            textSection.style.display = 'block';
        }
    });
});

// منطقة السحب والإفلات
const uploadArea = document.getElementById('uploadArea');
const fileInput = document.getElementById('material_file');

uploadArea.addEventListener('click', () => fileInput.click());

uploadArea.addEventListener('dragover', (e) => {
    e.preventDefault();
    uploadArea.classList.add('drag-over');
});

uploadArea.addEventListener('dragleave', () => {
    uploadArea.classList.remove('drag-over');
});

uploadArea.addEventListener('drop', (e) => {
    e.preventDefault();
    uploadArea.classList.remove('drag-over');
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        fileInput.files = files;
        handleFileSelect(files[0]);
    }
});

fileInput.addEventListener('change', (e) => {
    if (e.target.files.length > 0) {
        handleFileSelect(e.target.files[0]);
    }
});

// معالجة اختيار الملف
function handleFileSelect(file) {
    const uploadContent = uploadArea.querySelector('.upload-content');
    const maxSize = 50 * 1024 * 1024; // 50 MB
    
    if (file.size > maxSize) {
        alert('حجم الملف كبير جداً. الحد الأقصى 50 ميجابايت');
        return;
    }
    
    const fileIcon = getFileIcon(file.name);
    
    uploadContent.innerHTML = `
        <i class="${fileIcon} text-success" style="font-size: 2rem;"></i>
        <h6 class="mt-2 mb-1">${file.name}</h6>
        <p class="text-muted mb-0">${(file.size / (1024 * 1024)).toFixed(2)} ميجابايت</p>
    `;
}

// الحصول على أيقونة الملف
function getFileIcon(fileName) {
    const extension = fileName.split('.').pop().toLowerCase();
    
    switch (extension) {
        case 'pdf': return 'fas fa-file-pdf';
        case 'doc':
        case 'docx': return 'fas fa-file-word';
        case 'ppt':
        case 'pptx': return 'fas fa-file-powerpoint';
        case 'xls':
        case 'xlsx': return 'fas fa-file-excel';
        case 'txt': return 'fas fa-file-alt';
        case 'zip':
        case 'rar': return 'fas fa-file-archive';
        default: return 'fas fa-file';
    }
}

// إعادة تعيين النموذج
function resetForm() {
    if (confirm('هل أنت متأكد من إعادة تعيين جميع البيانات؟')) {
        document.getElementById('materialForm').reset();
        
        // إعادة تعيين منطقة الرفع
        const uploadContent = uploadArea.querySelector('.upload-content');
        uploadContent.innerHTML = `
            <i class="fas fa-cloud-upload-alt text-primary" style="font-size: 3rem;"></i>
            <h5 class="mt-3">اسحب الملف هنا أو انقر للاختيار</h5>
            <p class="text-muted">الأنواع المدعومة: PDF, DOC, PPT, XLS, TXT, ZIP</p>
            <p class="text-muted">الحد الأقصى: 50 ميجابايت</p>
        `;
        
        // إظهار قسم الملف
        document.getElementById('fileSection').style.display = 'block';
        document.getElementById('linkSection').style.display = 'none';
        document.getElementById('textSection').style.display = 'none';
    }
}

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // إظهار القسم المناسب حسب النوع المحدد
    const selectedType = document.querySelector('input[name="material_type"]:checked').value;
    if (selectedType === 'link') {
        document.getElementById('fileSection').style.display = 'none';
        document.getElementById('linkSection').style.display = 'block';
    } else if (selectedType === 'text') {
        document.getElementById('fileSection').style.display = 'none';
        document.getElementById('textSection').style.display = 'block';
    }
});
</script>

<style>
.upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.upload-area:hover,
.upload-area.drag-over {
    border-color: var(--bs-primary);
    background-color: rgba(var(--bs-primary-rgb), 0.05);
}

.upload-content {
    padding: 2rem;
}

.form-check-label {
    cursor: pointer;
}

.badge {
    font-size: 0.75rem;
}
</style>

<?php include 'includes/footer.php'; ?>
