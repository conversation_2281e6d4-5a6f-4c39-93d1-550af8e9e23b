<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config/database.php';
require_once 'includes/functions.php';

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // التحقق من وجود البيانات المطلوبة
    if (!isset($_POST['name']) || !isset($_POST['email']) || !isset($_POST['phone']) || !isset($_POST['password']) || !isset($_POST['confirm_password'])) {
        $error = 'بيانات النموذج غير مكتملة';
    } else {
        $name = sanitize($_POST['name']);
        $email = filter_var($_POST['email'], FILTER_SANITIZE_EMAIL);
        $phone = sanitize($_POST['phone']);
        $password = $_POST['password'];
        $confirm_password = $_POST['confirm_password'];

        // التحقق من البيانات المطلوبة أولاً
        if (empty($name) || empty($email) || empty($phone) || empty($password)) {
            $error = 'جميع الحقول مطلوبة';
        }
        // التحقق من صحة البريد الإلكتروني
        elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $error = 'البريد الإلكتروني غير صالح';
        }
        // التحقق من تطابق كلمة المرور
        elseif ($password !== $confirm_password) {
            $error = 'كلمة المرور غير متطابقة';
        }
        // التحقق من قوة كلمة المرور
        elseif (strlen($password) < 8) {
            $error = 'يجب أن تكون كلمة المرور 8 أحرف على الأقل';
        }
        // التحقق من صحة رقم الهاتف
        elseif (!preg_match('/^[0-9]{10}$/', $phone)) {
            $error = 'رقم الهاتف غير صالح (يجب أن يكون 10 أرقام)';
        }
    }

    if (empty($error)) {
        try {
            // Check if email already exists
            $stmt = $conn->prepare("SELECT id FROM users WHERE email = ?");
            $stmt->execute([$email]);

            if ($stmt->rowCount() > 0) {
                $error = 'البريد الإلكتروني مسجل مسبقاً';
            } else {
                // Insert new user
                $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
                $username = explode('@', $email)[0];

                // التأكد من أن username فريد
                $counter = 1;
                $original_username = $username;
                while (true) {
                    $check_stmt = $conn->prepare("SELECT id FROM users WHERE username = ?");
                    $check_stmt->execute([$username]);
                    if ($check_stmt->rowCount() == 0) {
                        break;
                    }
                    $username = $original_username . $counter;
                    $counter++;
                }

                // إنشاء رمز التحقق
                $verification_token = bin2hex(random_bytes(32));

                $stmt = $conn->prepare("INSERT INTO users (name, email, phone, username, password, role, status, verification_token, created_at) VALUES (?, ?, ?, ?, ?, 'student', 'pending', ?, NOW())");
                $result = $stmt->execute([$name, $email, $phone, $username, $hashedPassword, $verification_token]);

                if ($result) {
                    $user_id = $conn->lastInsertId();

                    // إنشاء طلب انضمام عام
                    try {
                        $stmt = $conn->prepare("INSERT INTO join_requests (name, email, phone, status, created_at) VALUES (?, ?, ?, 'pending', NOW())");
                        $stmt->execute([$name, $email, $phone]);

                        // تسجيل النشاط
                        if (function_exists('logUserActivity')) {
                            logUserActivity('register', "تسجيل مستخدم جديد - {$name} ({$email})");
                        }

                    } catch (Exception $e) {
                        // إذا فشل إنشاء طلب الانضمام، لا نوقف العملية
                        error_log("Failed to create join request: " . $e->getMessage());
                    }

                    $success = 'تم التسجيل بنجاح! تم إرسال طلبك للمراجعة. ستتلقى إشعاراً عند الموافقة على حسابك.';

                    // إعادة التوجيه إذا كان هناك رابط محدد
                    $redirect = isset($_GET['redirect']) ? $_GET['redirect'] : '';
                    if (!empty($redirect)) {
                        header('refresh:3;url=login.php?redirect=' . urlencode($redirect));
                    }
                } else {
                    $error = 'فشل في إنشاء الحساب';
                }
            }
        } catch(Exception $e) {
            error_log("Registration error: " . $e->getMessage());
            $error = 'حدث خطأ في التسجيل: ' . $e->getMessage();
        }
    }
}
?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="إنشاء حساب جديد في منصة التعلم الإلكتروني">
    <title>تسجيل حساب جديد - منصة التعلم</title>

    <!-- الخطوط المحسنة -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Bootstrap و Font Awesome -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- AOS Animation -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

    <!-- ملفات CSS المخصصة -->
    <link href="assets/css/main.css" rel="stylesheet">

    <style>
        body {
            background: var(--gradient-primary);
            min-height: 100vh;
            font-family: var(--font-family-arabic);
            position: relative;
            overflow-x: hidden;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(255,255,255,0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255,255,255,0.1) 0%, transparent 50%);
            animation: particleFloat 8s ease-in-out infinite;
            z-index: -1;
        }

        .register-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: var(--spacing-8) var(--spacing-4);
        }

        .register-card {
            background: var(--white);
            border-radius: var(--border-radius-3xl);
            box-shadow: var(--shadow-2xl);
            overflow: hidden;
            max-width: 500px;
            width: 100%;
            position: relative;
            border: var(--border-width-1) solid rgba(255,255,255,0.2);
        }

        .register-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 6px;
            background: var(--gradient-primary);
        }

        .register-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: var(--white);
            padding: var(--spacing-8);
            text-align: center;
            position: relative;
        }

        .register-header::after {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: 20px solid transparent;
            border-right: 20px solid transparent;
            border-top: 15px solid var(--secondary-color);
        }

        .register-body {
            padding: var(--spacing-8);
        }

        .form-floating-enhanced {
            position: relative;
            margin-bottom: var(--spacing-6);
        }

        .form-floating-enhanced .form-control {
            border: var(--border-width-2) solid var(--gray-200);
            border-radius: var(--border-radius-xl);
            padding: var(--spacing-4) var(--spacing-4);
            height: auto;
            transition: var(--transition-normal);
            background: var(--gray-50);
        }

        .form-floating-enhanced .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            transform: translateY(-2px);
            background: var(--white);
        }

        .form-floating-enhanced .form-control:valid {
            border-color: var(--success-color);
            background: rgba(72, 187, 120, 0.05);
        }

        .form-floating-enhanced .form-control:invalid:not(:placeholder-shown) {
            border-color: var(--danger-color);
            background: rgba(245, 101, 101, 0.05);
        }

        .form-floating-enhanced label {
            padding: var(--spacing-4);
            color: var(--gray-600);
            font-weight: var(--font-weight-medium);
            transition: var(--transition-normal);
        }

        .form-floating-enhanced .form-control:focus + label,
        .form-floating-enhanced .form-control:not(:placeholder-shown) + label {
            color: var(--primary-color);
            transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
        }

        .password-strength {
            margin-top: var(--spacing-2);
            font-size: var(--font-size-sm);
        }

        .strength-bar {
            height: 4px;
            border-radius: var(--border-radius-full);
            background: var(--gray-200);
            margin-top: var(--spacing-1);
            overflow: hidden;
        }

        .strength-fill {
            height: 100%;
            transition: var(--transition-normal);
            border-radius: var(--border-radius-full);
        }

        .strength-weak { background: var(--danger-color); width: 25%; }
        .strength-fair { background: var(--warning-color); width: 50%; }
        .strength-good { background: var(--info-color); width: 75%; }
        .strength-strong { background: var(--success-color); width: 100%; }

        .btn-register {
            background: var(--gradient-primary);
            border: none;
            border-radius: var(--border-radius-full);
            padding: var(--spacing-4) var(--spacing-8);
            font-weight: var(--font-weight-semibold);
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: var(--transition-normal);
            position: relative;
            overflow: hidden;
            color: var(--white);
        }

        .btn-register:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-xl);
            color: var(--white);
        }

        .btn-register:active {
            transform: translateY(-1px);
        }

        .btn-register::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255,255,255,0.3);
            border-radius: var(--border-radius-full);
            transform: translate(-50%, -50%);
            transition: var(--transition-fast);
        }

        .btn-register:hover::before {
            width: 300px;
            height: 300px;
        }

        .alert-enhanced {
            border: none;
            border-radius: var(--border-radius-xl);
            padding: var(--spacing-4) var(--spacing-6);
            margin-bottom: var(--spacing-6);
            border-right: 4px solid;
            font-weight: var(--font-weight-medium);
        }

        .alert-danger-enhanced {
            background: rgba(245, 101, 101, 0.1);
            color: var(--danger-color);
            border-right-color: var(--danger-color);
        }

        .alert-success-enhanced {
            background: rgba(72, 187, 120, 0.1);
            color: var(--success-color);
            border-right-color: var(--success-color);
        }

        .input-group-enhanced .btn {
            border: var(--border-width-2) solid var(--gray-200);
            border-right: none;
            border-radius: 0 var(--border-radius-xl) var(--border-radius-xl) 0;
            background: var(--gray-100);
            color: var(--gray-600);
            transition: var(--transition-normal);
        }

        .input-group-enhanced .btn:hover {
            background: var(--primary-color);
            color: var(--white);
            border-color: var(--primary-color);
        }

        .floating-shapes {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .shape {
            position: absolute;
            opacity: 0.1;
            animation: float 6s ease-in-out infinite;
            color: var(--white);
        }

        .shape:nth-child(1) {
            top: 15%;
            left: 10%;
            animation-delay: 0s;
        }

        .shape:nth-child(2) {
            top: 70%;
            right: 15%;
            animation-delay: 2s;
        }

        .shape:nth-child(3) {
            bottom: 20%;
            left: 20%;
            animation-delay: 4s;
        }

        .shape:nth-child(4) {
            top: 40%;
            right: 30%;
            animation-delay: 1s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        @media (max-width: 768px) {
            .register-body {
                padding: var(--spacing-6);
            }

            .register-header {
                padding: var(--spacing-6);
            }

            .register-container {
                padding: var(--spacing-4);
            }
        }
    </style>
</head>
<body>
    <!-- الأشكال العائمة في الخلفية -->
    <div class="floating-shapes">
        <div class="shape">
            <i class="fas fa-graduation-cap fa-4x"></i>
        </div>
        <div class="shape">
            <i class="fas fa-book fa-3x"></i>
        </div>
        <div class="shape">
            <i class="fas fa-laptop fa-3x"></i>
        </div>
        <div class="shape">
            <i class="fas fa-user-graduate fa-2x"></i>
        </div>
    </div>

    <div class="register-container">
        <div class="register-card" data-aos="fade-up" data-aos-duration="800">
            <div class="register-header">
                <h2 class="mb-3">
                    <i class="fas fa-user-plus me-3"></i>
                    إنشاء حساب جديد
                </h2>
                <p class="mb-0 opacity-75">انضم إلى منصة التعلم وابدأ رحلتك التعليمية</p>
            </div>

            <div class="register-body">
                <?php if ($error): ?>
                    <div class="alert-enhanced alert-danger-enhanced" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?php echo htmlspecialchars($error, ENT_QUOTES, 'UTF-8'); ?>
                    </div>
                <?php endif; ?>
                <?php if ($success): ?>
                    <div class="alert-enhanced alert-success-enhanced" role="alert">
                        <i class="fas fa-check-circle me-2"></i>
                        <?php echo htmlspecialchars($success, ENT_QUOTES, 'UTF-8'); ?>
                    </div>
                <?php endif; ?>

                        <form method="POST" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" class="needs-validation" novalidate>
                            <!-- الاسم الكامل -->
                            <div class="form-floating-enhanced">
                                <input type="text" class="form-control" id="name" name="name"
                                       placeholder="الاسم الكامل" required minlength="2"
                                       value="<?php echo isset($_POST['name']) ? htmlspecialchars($_POST['name'], ENT_QUOTES, 'UTF-8') : ''; ?>">
                                <label for="name">
                                    <i class="fas fa-user me-2"></i>
                                    الاسم الكامل
                                </label>
                                <div class="invalid-feedback">
                                    يرجى إدخال اسم صحيح (حرفين على الأقل)
                                </div>
                            </div>

                            <!-- البريد الإلكتروني -->
                            <div class="form-floating-enhanced">
                                <input type="email" class="form-control" id="email" name="email"
                                       placeholder="البريد الإلكتروني" required
                                       value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email'], ENT_QUOTES, 'UTF-8') : ''; ?>">
                                <label for="email">
                                    <i class="fas fa-envelope me-2"></i>
                                    البريد الإلكتروني
                                </label>
                                <div class="invalid-feedback">
                                    يرجى إدخال بريد إلكتروني صحيح
                                </div>
                            </div>

                            <!-- رقم الهاتف -->
                            <div class="form-floating-enhanced">
                                <input type="tel" class="form-control" id="phone" name="phone"
                                       placeholder="رقم الهاتف" required pattern="[0-9]{10}"
                                       value="<?php echo isset($_POST['phone']) ? htmlspecialchars($_POST['phone'], ENT_QUOTES, 'UTF-8') : ''; ?>">
                                <label for="phone">
                                    <i class="fas fa-phone me-2"></i>
                                    رقم الهاتف (10 أرقام)
                                </label>
                                <div class="invalid-feedback">
                                    يرجى إدخال رقم هاتف صحيح (10 أرقام)
                                </div>
                            </div>

                            <!-- كلمة المرور -->
                            <div class="form-floating-enhanced">
                                <div class="input-group-enhanced">
                                    <input type="password" class="form-control" id="password" name="password"
                                           placeholder="كلمة المرور" required minlength="8">
                                    <button class="btn" type="button" id="togglePassword">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                <label for="password">
                                    <i class="fas fa-lock me-2"></i>
                                    كلمة المرور
                                </label>
                                <div class="password-strength">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">قوة كلمة المرور:</small>
                                        <small id="strengthText" class="fw-bold">ضعيفة</small>
                                    </div>
                                    <div class="strength-bar">
                                        <div id="strengthFill" class="strength-fill"></div>
                                    </div>
                                </div>
                                <div class="invalid-feedback">
                                    يجب أن تكون كلمة المرور 8 أحرف على الأقل
                                </div>
                            </div>

                            <!-- تأكيد كلمة المرور -->
                            <div class="form-floating-enhanced">
                                <div class="input-group-enhanced">
                                    <input type="password" class="form-control" id="confirm_password" name="confirm_password"
                                           placeholder="تأكيد كلمة المرور" required>
                                    <button class="btn" type="button" id="toggleConfirmPassword">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                <label for="confirm_password">
                                    <i class="fas fa-lock me-2"></i>
                                    تأكيد كلمة المرور
                                </label>
                                <div class="invalid-feedback">
                                    كلمة المرور غير متطابقة
                                </div>
                            </div>

                            <!-- شروط الاستخدام -->
                            <div class="form-check mb-4">
                                <input class="form-check-input" type="checkbox" id="terms" required>
                                <label class="form-check-label" for="terms">
                                    أوافق على <a href="#" class="text-primary">شروط الاستخدام</a> و <a href="#" class="text-primary">سياسة الخصوصية</a>
                                </label>
                                <div class="invalid-feedback">
                                    يجب الموافقة على الشروط والأحكام
                                </div>
                            </div>

                            <!-- زر التسجيل -->
                            <button type="submit" class="btn-register w-100 mb-4">
                                <i class="fas fa-user-plus me-2"></i>
                                <span>إنشاء الحساب</span>
                            </button>
                        </form>

                        <!-- روابط إضافية -->
                        <div class="text-center">
                            <p class="text-muted mb-2">لديك حساب بالفعل؟</p>
                            <div class="d-flex gap-2 justify-content-center flex-wrap">
                                <a href="login.php<?php echo isset($_GET['redirect']) ? '?redirect=' . urlencode($_GET['redirect']) : ''; ?>"
                                   class="btn btn-outline-primary">
                                    <i class="fas fa-sign-in-alt me-1"></i>
                                    تسجيل الدخول
                                </a>
                                <a href="registration-status.php" class="btn btn-outline-info">
                                    <i class="fas fa-search me-1"></i>
                                    تحقق من حالة التسجيل
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script src="assets/js/main.js"></script>
    <script src="assets/js/enhanced.js"></script>
    <script>
        // تهيئة AOS
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true
        });

        document.addEventListener('DOMContentLoaded', function() {
            // إظهار/إخفاء كلمة المرور
            document.getElementById('togglePassword').addEventListener('click', function() {
                const password = document.getElementById('password');
                const type = password.getAttribute('type') === 'password' ? 'text' : 'password';
                password.setAttribute('type', type);
                this.querySelector('i').classList.toggle('fa-eye');
                this.querySelector('i').classList.toggle('fa-eye-slash');
            });

            document.getElementById('toggleConfirmPassword').addEventListener('click', function() {
                const password = document.getElementById('confirm_password');
                const type = password.getAttribute('type') === 'password' ? 'text' : 'password';
                password.setAttribute('type', type);
                this.querySelector('i').classList.toggle('fa-eye');
                this.querySelector('i').classList.toggle('fa-eye-slash');
            });

            // فحص قوة كلمة المرور
            const passwordInput = document.getElementById('password');
            const strengthFill = document.getElementById('strengthFill');
            const strengthText = document.getElementById('strengthText');

            passwordInput.addEventListener('input', function() {
                const password = this.value;
                const strength = calculatePasswordStrength(password);
                updatePasswordStrength(strength);
            });

            function calculatePasswordStrength(password) {
                let score = 0;
                if (password.length >= 8) score++;
                if (/[a-z]/.test(password)) score++;
                if (/[A-Z]/.test(password)) score++;
                if (/[0-9]/.test(password)) score++;
                if (/[^A-Za-z0-9]/.test(password)) score++;
                return score;
            }

            function updatePasswordStrength(strength) {
                strengthFill.className = 'strength-fill';

                switch(strength) {
                    case 0:
                    case 1:
                        strengthFill.classList.add('strength-weak');
                        strengthText.textContent = 'ضعيفة';
                        strengthText.style.color = 'var(--danger-color)';
                        break;
                    case 2:
                        strengthFill.classList.add('strength-fair');
                        strengthText.textContent = 'متوسطة';
                        strengthText.style.color = 'var(--warning-color)';
                        break;
                    case 3:
                        strengthFill.classList.add('strength-good');
                        strengthText.textContent = 'جيدة';
                        strengthText.style.color = 'var(--info-color)';
                        break;
                    case 4:
                    case 5:
                        strengthFill.classList.add('strength-strong');
                        strengthText.textContent = 'قوية';
                        strengthText.style.color = 'var(--success-color)';
                        break;
                }
            }

            // التحقق من تطابق كلمة المرور
            const confirmPassword = document.getElementById('confirm_password');
            confirmPassword.addEventListener('input', function() {
                const password = passwordInput.value;
                const confirm = this.value;

                if (password !== confirm) {
                    this.setCustomValidity('كلمة المرور غير متطابقة');
                    this.classList.add('is-invalid');
                    this.classList.remove('is-valid');
                } else {
                    this.setCustomValidity('');
                    this.classList.remove('is-invalid');
                    this.classList.add('is-valid');
                }
            });

            // التحقق من صحة النموذج
            const form = document.querySelector('form');
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalBtnText = submitBtn.innerHTML;

            form.addEventListener('submit', function(e) {
                if (!form.checkValidity()) {
                    e.preventDefault();
                    e.stopPropagation();

                    // إظهار رسالة خطأ
                    showNotification('يرجى التأكد من صحة جميع البيانات المدخلة', 'error');
                } else {
                    // إظهار حالة التحميل
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الإنشاء...';
                    submitBtn.disabled = true;
                }

                form.classList.add('was-validated');
            });

            // التحقق المباشر من البريد الإلكتروني
            const emailInput = document.getElementById('email');
            let emailTimeout;

            emailInput.addEventListener('input', function() {
                clearTimeout(emailTimeout);
                emailTimeout = setTimeout(() => {
                    checkEmailAvailability(this.value);
                }, 500);
            });

            async function checkEmailAvailability(email) {
                if (!email || !isValidEmail(email)) return;

                try {
                    const response = await fetch('check-email.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ email: email })
                    });

                    const result = await response.json();

                    if (result.exists) {
                        emailInput.setCustomValidity('البريد الإلكتروني مسجل مسبقاً');
                        emailInput.classList.add('is-invalid');
                        emailInput.classList.remove('is-valid');
                    } else {
                        emailInput.setCustomValidity('');
                        emailInput.classList.remove('is-invalid');
                        emailInput.classList.add('is-valid');
                    }
                } catch (error) {
                    console.error('Error checking email:', error);
                }
            }

            function isValidEmail(email) {
                return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
            }

            // إضافة تأثيرات بصرية للحقول
            const inputs = document.querySelectorAll('.form-control');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.classList.add('focused');
                });

                input.addEventListener('blur', function() {
                    this.parentElement.classList.remove('focused');
                });
            });
        });

        // دالة إظهار الإشعارات
        function showNotification(message, type = 'info') {
            // يمكن استخدام مكتبة إشعارات أو إنشاء إشعار مخصص
            const alertClass = type === 'error' ? 'alert-danger-enhanced' : 'alert-success-enhanced';
            const icon = type === 'error' ? 'fas fa-exclamation-triangle' : 'fas fa-check-circle';

            const notification = document.createElement('div');
            notification.className = `alert-enhanced ${alertClass}`;
            notification.innerHTML = `<i class="${icon} me-2"></i>${message}`;

            const container = document.querySelector('.register-body');
            container.insertBefore(notification, container.firstChild);

            setTimeout(() => {
                notification.remove();
            }, 5000);
        }
    </script>
</body>
</html>
