<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات المدرب
if (!isLoggedIn() || !isInstructor()) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit;
}

$course_id = $_GET['course_id'] ?? '';

if (!$course_id) {
    echo json_encode(['success' => false, 'message' => 'معرف الكورس مطلوب']);
    exit;
}

try {
    // التحقق من أن الكورس ينتمي للمدرب
    $stmt = $conn->prepare("SELECT id, title FROM courses WHERE id = ? AND instructor_id = ?");
    $stmt->execute([$course_id, $_SESSION['user_id']]);
    $course = $stmt->fetch();
    
    if (!$course) {
        echo json_encode(['success' => false, 'message' => 'الكورس غير موجود أو لا تملك صلاحية للوصول إليه']);
        exit;
    }
    
    // إحصائيات عامة
    $stmt = $conn->prepare("
        SELECT 
            COUNT(DISTINCT ce.student_id) as total_students,
            COUNT(DISTINCT sg.assignment_name) as total_assignments,
            COUNT(sg.id) as total_grades,
            ROUND(AVG((sg.grade / sg.max_grade) * 100), 1) as average_grade,
            ROUND(MAX((sg.grade / sg.max_grade) * 100), 1) as highest_grade,
            ROUND(MIN((sg.grade / sg.max_grade) * 100), 1) as lowest_grade
        FROM course_enrollments ce
        LEFT JOIN student_grades sg ON ce.student_id = sg.student_id AND ce.course_id = sg.course_id
        WHERE ce.course_id = ? AND ce.status = 'active'
    ");
    $stmt->execute([$course_id]);
    $general_stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // إحصائيات الواجبات
    $stmt = $conn->prepare("
        SELECT 
            assignment_name,
            COUNT(*) as student_count,
            ROUND(AVG((grade / max_grade) * 100), 1) as avg_percentage,
            ROUND(MAX((grade / max_grade) * 100), 1) as max_percentage,
            ROUND(MIN((grade / max_grade) * 100), 1) as min_percentage,
            max_grade
        FROM student_grades 
        WHERE course_id = ?
        GROUP BY assignment_name, max_grade
        ORDER BY assignment_name
    ");
    $stmt->execute([$course_id]);
    $assignment_stats = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // إحصائيات الطلاب
    $stmt = $conn->prepare("
        SELECT 
            u.name as student_name,
            COUNT(sg.id) as total_assignments,
            ROUND(AVG((sg.grade / sg.max_grade) * 100), 1) as avg_percentage,
            ROUND(MAX((sg.grade / sg.max_grade) * 100), 1) as max_percentage,
            ROUND(MIN((sg.grade / sg.max_grade) * 100), 1) as min_percentage
        FROM users u
        INNER JOIN course_enrollments ce ON u.id = ce.student_id
        LEFT JOIN student_grades sg ON u.id = sg.student_id AND ce.course_id = sg.course_id
        WHERE ce.course_id = ? AND ce.status = 'active'
        GROUP BY u.id, u.name
        ORDER BY avg_percentage DESC
    ");
    $stmt->execute([$course_id]);
    $student_stats = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // توزيع الدرجات
    $stmt = $conn->prepare("
        SELECT 
            CASE 
                WHEN (grade / max_grade) * 100 >= 90 THEN 'A'
                WHEN (grade / max_grade) * 100 >= 80 THEN 'B'
                WHEN (grade / max_grade) * 100 >= 70 THEN 'C'
                WHEN (grade / max_grade) * 100 >= 60 THEN 'D'
                ELSE 'F'
            END as grade_letter,
            COUNT(*) as count
        FROM student_grades 
        WHERE course_id = ?
        GROUP BY grade_letter
        ORDER BY grade_letter
    ");
    $stmt->execute([$course_id]);
    $grade_distribution = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'success' => true,
        'statistics' => [
            'total_students' => $general_stats['total_students'] ?? 0,
            'total_assignments' => $general_stats['total_assignments'] ?? 0,
            'total_grades' => $general_stats['total_grades'] ?? 0,
            'average_grade' => $general_stats['average_grade'] ?? 0,
            'highest_grade' => $general_stats['highest_grade'] ?? 0,
            'lowest_grade' => $general_stats['lowest_grade'] ?? 0,
            'assignment_stats' => $assignment_stats,
            'student_stats' => $student_stats,
            'grade_distribution' => $grade_distribution
        ]
    ]);
    
} catch (PDOException $e) {
    echo json_encode(['success' => false, 'message' => 'حدث خطأ أثناء جلب الإحصائيات']);
}
?>
