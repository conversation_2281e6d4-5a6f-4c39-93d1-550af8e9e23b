<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات المدرب
if (!isLoggedIn() || !isInstructor()) {
    header('Location: ../login.php');
    exit;
}

$pageTitle = 'إدارة الطلاب';
$breadcrumbs = [
    ['title' => 'إدارة الطلاب']
];

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    if ($action === 'update_student_status') {
        $enrollment_id = $_POST['enrollment_id'] ?? 0;
        $status = $_POST['status'] ?? '';

        try {
            $stmt = $conn->prepare("
                UPDATE enrollments e
                INNER JOIN courses c ON e.course_id = c.id
                SET e.status = ?
                WHERE e.id = ? AND c.instructor_id = ?
            ");
            $stmt->execute([$status, $enrollment_id, $_SESSION['user_id']]);

            $success_message = 'تم تحديث حالة الطالب بنجاح';
        } catch (PDOException $e) {
            $error_message = 'حدث خطأ أثناء تحديث حالة الطالب';
        }
    }

    if ($action === 'send_message') {
        $student_ids = $_POST['student_ids'] ?? [];
        $subject = trim($_POST['subject'] ?? '');
        $message = trim($_POST['message'] ?? '');

        if (!empty($student_ids) && !empty($subject) && !empty($message)) {
            try {
                foreach ($student_ids as $student_id) {
                    $stmt = $conn->prepare("
                        INSERT INTO messages (sender_id, receiver_id, subject, message, created_at)
                        VALUES (?, ?, ?, ?, NOW())
                    ");
                    $stmt->execute([$_SESSION['user_id'], $student_id, $subject, $message]);
                }

                $success_message = 'تم إرسال الرسالة إلى ' . count($student_ids) . ' طالب';
            } catch (PDOException $e) {
                $error_message = 'حدث خطأ أثناء إرسال الرسالة';
            }
        }
    }

    if ($action === 'bulk_grade') {
        $student_ids = $_POST['student_ids'] ?? [];
        $assignment_id = $_POST['assignment_id'] ?? 0;
        $grade = $_POST['grade'] ?? 0;

        if (!empty($student_ids) && $assignment_id && $grade) {
            try {
                foreach ($student_ids as $student_id) {
                    $stmt = $conn->prepare("
                        INSERT INTO assignment_submissions (assignment_id, student_id, grade, graded_by, graded_at)
                        VALUES (?, ?, ?, ?, NOW())
                        ON DUPLICATE KEY UPDATE grade = VALUES(grade), graded_by = VALUES(graded_by), graded_at = VALUES(graded_at)
                    ");
                    $stmt->execute([$assignment_id, $student_id, $grade, $_SESSION['user_id']]);
                }

                $success_message = 'تم تحديث درجات ' . count($student_ids) . ' طالب';
            } catch (PDOException $e) {
                $error_message = 'حدث خطأ أثناء تحديث الدرجات';
            }
        }
    }
}

// فلاتر البحث
$course_filter = $_GET['course_id'] ?? '';
$status_filter = $_GET['status'] ?? '';
$search_query = $_GET['search'] ?? '';

// جلب الكورسات الخاصة بالمدرب
try {
    $stmt = $conn->prepare("
        SELECT id, title
        FROM courses
        WHERE instructor_id = ? AND status = 'active'
        ORDER BY title
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $instructor_courses = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $instructor_courses = [];
}

// جلب الطلاب
try {
    $where_conditions = ["c.instructor_id = ?"];
    $params = [$_SESSION['user_id']];

    if (!empty($course_filter)) {
        $where_conditions[] = "e.course_id = ?";
        $params[] = $course_filter;
    }

    if (!empty($status_filter)) {
        $where_conditions[] = "e.status = ?";
        $params[] = $status_filter;
    }

    if (!empty($search_query)) {
        $where_conditions[] = "(u.name LIKE ? OR u.email LIKE ?)";
        $params[] = "%$search_query%";
        $params[] = "%$search_query%";
    }

    $where_clause = implode(' AND ', $where_conditions);

    $stmt = $conn->prepare("
        SELECT
            e.*,
            u.name as student_name,
            u.email as student_email,
            u.phone as student_phone,
            u.profile_image,
            c.title as course_title,
            c.price as course_price,
            p.amount as payment_amount,
            p.status as payment_status,
            (SELECT AVG(grade) FROM assignment_submissions asub WHERE asub.student_id = u.id) as avg_grade,
            (SELECT COUNT(*) FROM course_progress cp WHERE cp.user_id = u.id AND cp.course_id = c.id) as progress_count,
            (SELECT COUNT(*) FROM course_videos cv WHERE cv.course_id = c.id) as total_videos,
            (SELECT COUNT(*) FROM video_watches vw
             INNER JOIN course_videos cv2 ON vw.video_id = cv2.id
             WHERE vw.user_id = u.id AND cv2.course_id = c.id) as watched_videos
        FROM enrollments e
        INNER JOIN users u ON e.student_id = u.id
        INNER JOIN courses c ON e.course_id = c.id
        LEFT JOIN payments p ON e.id = p.enrollment_id
        WHERE $where_clause
        ORDER BY e.created_at DESC
        LIMIT 50
    ");
    $stmt->execute($params);
    $students = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // إحصائيات الطلاب
    $stmt = $conn->prepare("
        SELECT
            COUNT(DISTINCT e.id) as total_students,
            COUNT(DISTINCT CASE WHEN e.status = 'active' THEN e.id END) as active_students,
            COUNT(DISTINCT CASE WHEN e.status = 'pending' THEN e.id END) as pending_students,
            COUNT(DISTINCT CASE WHEN e.status = 'completed' THEN e.id END) as completed_students,
            COUNT(DISTINCT CASE WHEN e.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN e.id END) as new_students
        FROM enrollments e
        INNER JOIN courses c ON e.course_id = c.id
        WHERE c.instructor_id = ?
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $stats = $stmt->fetch(PDO::FETCH_ASSOC);

    // جلب الواجبات للدرجات الجماعية
    $stmt = $conn->prepare("
        SELECT a.id, a.title
        FROM assignments a
        INNER JOIN courses c ON a.course_id = c.id
        WHERE c.instructor_id = ?
        ORDER BY a.created_at DESC
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $assignments = $stmt->fetchAll(PDO::FETCH_ASSOC);

} catch (PDOException $e) {
    $error_message = 'حدث خطأ أثناء جلب البيانات';
    $students = [];
    $stats = ['total_students' => 0, 'active_students' => 0, 'pending_students' => 0, 'completed_students' => 0, 'new_students' => 0];
    $assignments = [];
}

include 'includes/header.php';
?>

<!-- رأس الصفحة -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="h3 mb-2">
            <i class="fas fa-users text-primary me-2"></i>
            إدارة الطلاب
        </h2>
        <p class="text-muted mb-0">إدارة شاملة لطلاب الكورسات والتفاعل معهم</p>
    </div>
    <div class="d-flex gap-2">
        <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#messageModal">
            <i class="fas fa-envelope me-1"></i>إرسال رسالة
        </button>
        <button class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#gradeModal">
            <i class="fas fa-star me-1"></i>تقييم جماعي
        </button>
        <button class="btn btn-outline-primary" onclick="exportStudents()">
            <i class="fas fa-download me-1"></i>تصدير البيانات
        </button>
    </div>
</div>

<!-- رسائل النجاح والخطأ -->
<?php if (isset($success_message)): ?>
<div class="alert alert-success alert-dismissible fade show" role="alert">
    <i class="fas fa-check-circle me-2"></i>
    <?php echo $success_message; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<?php if (isset($error_message)): ?>
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <i class="fas fa-exclamation-triangle me-2"></i>
    <?php echo $error_message; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<!-- إحصائيات الطلاب -->
<div class="row g-4 mb-4">
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-primary bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-users text-primary fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h5 class="mb-1"><?php echo $stats['total_students']; ?></h5>
                        <p class="text-muted mb-0">إجمالي الطلاب</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-success bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-user-check text-success fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h5 class="mb-1"><?php echo $stats['active_students']; ?></h5>
                        <p class="text-muted mb-0">طلاب نشطون</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-warning bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-user-clock text-warning fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h5 class="mb-1"><?php echo $stats['pending_students']; ?></h5>
                        <p class="text-muted mb-0">في الانتظار</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-info bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-user-plus text-info fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h5 class="mb-1"><?php echo $stats['new_students']; ?></h5>
                        <p class="text-muted mb-0">جدد هذا الشهر</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- فلاتر البحث -->
<div class="card border-0 shadow-sm mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3 align-items-end">
            <div class="col-md-3">
                <label for="course_id" class="form-label">الكورس</label>
                <select name="course_id" id="course_id" class="form-select">
                    <option value="">جميع الكورسات</option>
                    <?php foreach ($instructor_courses as $course): ?>
                    <option value="<?php echo $course['id']; ?>"
                            <?php echo $course_filter == $course['id'] ? 'selected' : ''; ?>>
                        <?php echo htmlspecialchars($course['title']); ?>
                    </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-3">
                <label for="status" class="form-label">الحالة</label>
                <select name="status" id="status" class="form-select">
                    <option value="">جميع الحالات</option>
                    <option value="active" <?php echo $status_filter == 'active' ? 'selected' : ''; ?>>نشط</option>
                    <option value="pending" <?php echo $status_filter == 'pending' ? 'selected' : ''; ?>>في الانتظار</option>
                    <option value="completed" <?php echo $status_filter == 'completed' ? 'selected' : ''; ?>>مكتمل</option>
                    <option value="suspended" <?php echo $status_filter == 'suspended' ? 'selected' : ''; ?>>معلق</option>
                </select>
            </div>
            <div class="col-md-4">
                <label for="search" class="form-label">البحث</label>
                <input type="text" name="search" id="search" class="form-control"
                       value="<?php echo htmlspecialchars($search_query); ?>"
                       placeholder="اسم الطالب أو البريد الإلكتروني">
            </div>
            <div class="col-md-2">
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-search me-1"></i>بحث
                </button>
            </div>
        </form>
    </div>
</div>

<!-- جدول الطلاب -->
<div class="card border-0 shadow-sm">
    <div class="card-header bg-white border-bottom">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>
                قائمة الطلاب
            </h5>
            <div class="d-flex gap-2">
                <button class="btn btn-sm btn-outline-primary" onclick="selectAll()">
                    <i class="fas fa-check-square me-1"></i>تحديد الكل
                </button>
                <button class="btn btn-sm btn-outline-secondary" onclick="clearSelection()">
                    <i class="fas fa-square me-1"></i>إلغاء التحديد
                </button>
            </div>
        </div>
    </div>
    <div class="card-body p-0">
        <?php if (empty($students)): ?>
        <div class="text-center py-5">
            <i class="fas fa-users text-muted" style="font-size: 3rem;"></i>
            <h5 class="mt-3 text-muted">لا يوجد طلاب</h5>
            <p class="text-muted">لم يتم العثور على طلاب مطابقين للفلاتر المحددة</p>
        </div>
        <?php else: ?>
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th width="50">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="selectAllCheckbox" onchange="toggleSelectAll()">
                            </div>
                        </th>
                        <th>الطالب</th>
                        <th>الكورس</th>
                        <th>الحالة</th>
                        <th>التقدم</th>
                        <th>الدرجة</th>
                        <th>الدفع</th>
                        <th>تاريخ التسجيل</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($students as $student): ?>
                    <tr>
                        <td>
                            <div class="form-check">
                                <input class="form-check-input student-checkbox" type="checkbox"
                                       value="<?php echo $student['student_id']; ?>"
                                       data-enrollment="<?php echo $student['id']; ?>">
                            </div>
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0 me-3">
                                    <?php if ($student['profile_image']): ?>
                                    <img src="<?php echo htmlspecialchars($student['profile_image']); ?>"
                                         alt="صورة الطالب" class="rounded-circle" width="40" height="40">
                                    <?php else: ?>
                                    <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center"
                                         style="width: 40px; height: 40px;">
                                        <i class="fas fa-user text-white"></i>
                                    </div>
                                    <?php endif; ?>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1"><?php echo htmlspecialchars($student['student_name']); ?></h6>
                                    <small class="text-muted"><?php echo htmlspecialchars($student['student_email']); ?></small>
                                    <?php if ($student['student_phone']): ?>
                                    <br><small class="text-muted">
                                        <i class="fas fa-phone me-1"></i><?php echo htmlspecialchars($student['student_phone']); ?>
                                    </small>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </td>
                        <td>
                            <div>
                                <h6 class="mb-1"><?php echo htmlspecialchars($student['course_title']); ?></h6>
                                <?php if ($student['course_price'] > 0): ?>
                                <small class="text-success">$<?php echo number_format($student['course_price'], 2); ?></small>
                                <?php else: ?>
                                <small class="text-muted">مجاني</small>
                                <?php endif; ?>
                            </div>
                        </td>
                        <td>
                            <?php
                            $status_classes = [
                                'active' => 'bg-success',
                                'pending' => 'bg-warning',
                                'completed' => 'bg-info',
                                'suspended' => 'bg-danger'
                            ];
                            $status_labels = [
                                'active' => 'نشط',
                                'pending' => 'في الانتظار',
                                'completed' => 'مكتمل',
                                'suspended' => 'معلق'
                            ];
                            $status_class = $status_classes[$student['status']] ?? 'bg-secondary';
                            $status_label = $status_labels[$student['status']] ?? $student['status'];
                            ?>
                            <span class="badge <?php echo $status_class; ?>"><?php echo $status_label; ?></span>
                        </td>
                        <td>
                            <?php
                            $progress_percentage = $student['total_videos'] > 0 ?
                                round(($student['watched_videos'] / $student['total_videos']) * 100) : 0;
                            ?>
                            <div class="d-flex align-items-center">
                                <div class="progress flex-grow-1 me-2" style="height: 8px;">
                                    <div class="progress-bar bg-success" style="width: <?php echo $progress_percentage; ?>%"></div>
                                </div>
                                <small class="text-muted"><?php echo $progress_percentage; ?>%</small>
                            </div>
                            <small class="text-muted">
                                <?php echo $student['watched_videos']; ?>/<?php echo $student['total_videos']; ?> فيديو
                            </small>
                        </td>
                        <td>
                            <?php if ($student['avg_grade']): ?>
                            <div class="text-center">
                                <strong class="text-primary"><?php echo number_format($student['avg_grade'], 1); ?></strong>
                                <br><small class="text-muted">من 100</small>
                            </div>
                            <?php else: ?>
                            <span class="text-muted">-</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <?php if ($student['payment_amount']): ?>
                            <div class="text-center">
                                <strong class="text-success">$<?php echo number_format($student['payment_amount'], 2); ?></strong>
                                <br><span class="badge bg-<?php echo $student['payment_status'] === 'completed' ? 'success' : 'warning'; ?>">
                                    <?php echo $student['payment_status'] === 'completed' ? 'مدفوع' : 'معلق'; ?>
                                </span>
                            </div>
                            <?php else: ?>
                            <span class="text-muted">مجاني</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <small class="text-muted">
                                <?php echo date('Y-m-d', strtotime($student['created_at'])); ?>
                                <br><?php echo date('H:i', strtotime($student['created_at'])); ?>
                            </small>
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <button class="btn btn-sm btn-outline-primary"
                                        onclick="viewStudentDetails(<?php echo $student['student_id']; ?>)" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-success"
                                        onclick="sendMessage(<?php echo $student['student_id']; ?>)" title="إرسال رسالة">
                                    <i class="fas fa-envelope"></i>
                                </button>
                                <div class="btn-group" role="group">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle"
                                            data-bs-toggle="dropdown" title="المزيد">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#" onclick="changeStatus(<?php echo $student['id']; ?>, 'active')">
                                            <i class="fas fa-check text-success me-2"></i>تفعيل
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" onclick="changeStatus(<?php echo $student['id']; ?>, 'suspended')">
                                            <i class="fas fa-pause text-warning me-2"></i>تعليق
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" onclick="changeStatus(<?php echo $student['id']; ?>, 'completed')">
                                            <i class="fas fa-graduation-cap text-info me-2"></i>إكمال
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item text-danger" href="#" onclick="removeStudent(<?php echo $student['id']; ?>)">
                                            <i class="fas fa-trash me-2"></i>إزالة
                                        </a></li>
                                    </ul>
                                </div>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- Modal إرسال رسالة جماعية -->
<div class="modal fade" id="messageModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إرسال رسالة جماعية</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="send_message">

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        سيتم إرسال الرسالة إلى الطلاب المحددين فقط. إذا لم تحدد أي طالب، ستُرسل لجميع الطلاب النشطين.
                    </div>

                    <div class="mb-3">
                        <label for="subject" class="form-label">موضوع الرسالة <span class="text-danger">*</span></label>
                        <input type="text" name="subject" id="subject" class="form-control" required
                               placeholder="مثال: تحديث مهم حول الكورس">
                    </div>

                    <div class="mb-3">
                        <label for="message" class="form-label">محتوى الرسالة <span class="text-danger">*</span></label>
                        <textarea name="message" id="message" class="form-control" rows="6" required
                                  placeholder="اكتب رسالتك هنا..."></textarea>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">المستقبلون</label>
                        <div id="selectedStudents" class="border rounded p-3 bg-light">
                            <span class="text-muted">لم يتم تحديد طلاب. سيتم الإرسال لجميع الطلاب النشطين.</span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-success">إرسال الرسالة</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal تقييم جماعي -->
<div class="modal fade" id="gradeModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تقييم جماعي</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="bulk_grade">

                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        سيتم تطبيق الدرجة على جميع الطلاب المحددين للواجب المختار.
                    </div>

                    <div class="mb-3">
                        <label for="assignment_id" class="form-label">الواجب <span class="text-danger">*</span></label>
                        <select name="assignment_id" id="assignment_id" class="form-select" required>
                            <option value="">-- اختر واجب --</option>
                            <?php foreach ($assignments as $assignment): ?>
                            <option value="<?php echo $assignment['id']; ?>">
                                <?php echo htmlspecialchars($assignment['title']); ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="grade" class="form-label">الدرجة <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <input type="number" name="grade" id="grade" class="form-control"
                                   min="0" max="100" step="0.1" required placeholder="0">
                            <span class="input-group-text">من 100</span>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">الطلاب المحددون</label>
                        <div id="selectedStudentsGrade" class="border rounded p-3 bg-light">
                            <span class="text-muted">لم يتم تحديد طلاب</span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-warning">تطبيق الدرجات</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal تفاصيل الطالب -->
<div class="modal fade" id="studentDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل الطالب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="studentDetailsContent">
                    <div class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// متغيرات عامة
let selectedStudents = [];

// تحديد/إلغاء تحديد جميع الطلاب
function toggleSelectAll() {
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');
    const studentCheckboxes = document.querySelectorAll('.student-checkbox');

    studentCheckboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
    });

    updateSelectedStudents();
}

// تحديد الكل
function selectAll() {
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');
    const studentCheckboxes = document.querySelectorAll('.student-checkbox');

    selectAllCheckbox.checked = true;
    studentCheckboxes.forEach(checkbox => {
        checkbox.checked = true;
    });

    updateSelectedStudents();
}

// إلغاء التحديد
function clearSelection() {
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');
    const studentCheckboxes = document.querySelectorAll('.student-checkbox');

    selectAllCheckbox.checked = false;
    studentCheckboxes.forEach(checkbox => {
        checkbox.checked = false;
    });

    updateSelectedStudents();
}

// تحديث قائمة الطلاب المحددين
function updateSelectedStudents() {
    const checkedBoxes = document.querySelectorAll('.student-checkbox:checked');
    selectedStudents = Array.from(checkedBoxes).map(cb => ({
        id: cb.value,
        enrollment: cb.dataset.enrollment,
        name: cb.closest('tr').querySelector('h6').textContent
    }));

    // تحديث عرض الطلاب المحددين في النوافذ المنبثقة
    updateSelectedStudentsDisplay();
}

// تحديث عرض الطلاب المحددين
function updateSelectedStudentsDisplay() {
    const messageDisplay = document.getElementById('selectedStudents');
    const gradeDisplay = document.getElementById('selectedStudentsGrade');

    if (selectedStudents.length === 0) {
        messageDisplay.innerHTML = '<span class="text-muted">لم يتم تحديد طلاب. سيتم الإرسال لجميع الطلاب النشطين.</span>';
        gradeDisplay.innerHTML = '<span class="text-muted">لم يتم تحديد طلاب</span>';
    } else {
        const studentsList = selectedStudents.map(student =>
            `<span class="badge bg-primary me-1">${student.name}</span>`
        ).join('');

        messageDisplay.innerHTML = `<strong>الطلاب المحددون (${selectedStudents.length}):</strong><br>${studentsList}`;
        gradeDisplay.innerHTML = `<strong>الطلاب المحددون (${selectedStudents.length}):</strong><br>${studentsList}`;

        // إضافة حقول مخفية للطلاب المحددين
        const messageForm = document.querySelector('#messageModal form');
        const gradeForm = document.querySelector('#gradeModal form');

        // إزالة الحقول المخفية السابقة
        messageForm.querySelectorAll('input[name="student_ids[]"]').forEach(input => input.remove());
        gradeForm.querySelectorAll('input[name="student_ids[]"]').forEach(input => input.remove());

        // إضافة حقول جديدة
        selectedStudents.forEach(student => {
            const messageInput = document.createElement('input');
            messageInput.type = 'hidden';
            messageInput.name = 'student_ids[]';
            messageInput.value = student.id;
            messageForm.appendChild(messageInput);

            const gradeInput = document.createElement('input');
            gradeInput.type = 'hidden';
            gradeInput.name = 'student_ids[]';
            gradeInput.value = student.id;
            gradeForm.appendChild(gradeInput);
        });
    }
}

// عرض تفاصيل الطالب
function viewStudentDetails(studentId) {
    const modal = new bootstrap.Modal(document.getElementById('studentDetailsModal'));
    modal.show();

    // محاكاة تحميل البيانات
    setTimeout(() => {
        document.getElementById('studentDetailsContent').innerHTML = `
            <div class="row">
                <div class="col-md-4 text-center">
                    <div class="bg-secondary rounded-circle d-inline-flex align-items-center justify-content-center"
                         style="width: 80px; height: 80px;">
                        <i class="fas fa-user text-white fs-2"></i>
                    </div>
                    <h5 class="mt-3">اسم الطالب</h5>
                    <p class="text-muted"><EMAIL></p>
                </div>
                <div class="col-md-8">
                    <h6>معلومات التسجيل</h6>
                    <table class="table table-sm">
                        <tr><td>تاريخ التسجيل:</td><td>2024-01-15</td></tr>
                        <tr><td>الحالة:</td><td><span class="badge bg-success">نشط</span></td></tr>
                        <tr><td>التقدم:</td><td>75%</td></tr>
                        <tr><td>الدرجة:</td><td>85/100</td></tr>
                    </table>
                </div>
            </div>
        `;
    }, 1000);
}

// إرسال رسالة فردية
function sendMessage(studentId) {
    // فتح نافذة الرسالة وتحديد الطالب
    const checkbox = document.querySelector(`input[value="${studentId}"]`);
    if (checkbox) {
        checkbox.checked = true;
        updateSelectedStudents();
    }

    new bootstrap.Modal(document.getElementById('messageModal')).show();
}

// تغيير حالة الطالب
function changeStatus(enrollmentId, status) {
    if (confirm('هل أنت متأكد من تغيير حالة الطالب؟')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="update_student_status">
            <input type="hidden" name="enrollment_id" value="${enrollmentId}">
            <input type="hidden" name="status" value="${status}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

// إزالة طالب
function removeStudent(enrollmentId) {
    if (confirm('هل أنت متأكد من إزالة هذا الطالب؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        changeStatus(enrollmentId, 'removed');
    }
}

// تصدير بيانات الطلاب
function exportStudents() {
    const url = new URL('export-students.php', window.location.origin);

    // إضافة الفلاتر الحالية
    const courseFilter = document.getElementById('course_id').value;
    const statusFilter = document.getElementById('status').value;
    const searchQuery = document.getElementById('search').value;

    if (courseFilter) url.searchParams.set('course_id', courseFilter);
    if (statusFilter) url.searchParams.set('status', statusFilter);
    if (searchQuery) url.searchParams.set('search', searchQuery);

    window.open(url.toString(), '_blank');
}

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // ربط أحداث تحديد الطلاب
    const studentCheckboxes = document.querySelectorAll('.student-checkbox');
    studentCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateSelectedStudents);
    });

    // تحسين النماذج
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function() {
            const submitButton = this.querySelector('button[type="submit"]');
            if (submitButton) {
                submitButton.disabled = true;
                submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري المعالجة...';
            }
        });
    });

    // تحسين البحث المباشر
    const searchInput = document.getElementById('search');
    let searchTimeout;

    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            // يمكن إضافة بحث مباشر عبر AJAX هنا
        }, 500);
    });
});
</script>

<style>
.card {
    transition: all 0.3s ease;
    border-radius: 0.5rem;
}

.card:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1) !important;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: var(--bs-gray-700);
    background-color: var(--bs-gray-50);
}

.table tbody tr {
    transition: background-color 0.3s ease;
}

.table tbody tr:hover {
    background-color: rgba(0,123,255,0.05);
}

.btn-group .btn {
    border-radius: 0.375rem;
    margin-left: 0.25rem;
}

.badge {
    font-size: 0.75rem;
    padding: 0.5rem 0.75rem;
}

.progress {
    height: 8px;
    border-radius: 4px;
}

.progress-bar {
    transition: width 0.3s ease;
}

.form-check-input:checked {
    background-color: var(--bs-primary);
    border-color: var(--bs-primary);
}

.dropdown-menu {
    border-radius: 0.5rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.dropdown-item {
    transition: background-color 0.3s ease;
}

.dropdown-item:hover {
    background-color: rgba(0,123,255,0.1);
}

.modal-content {
    border-radius: 0.5rem;
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.modal-header {
    border-bottom: 1px solid rgba(0,0,0,0.1);
    padding: 1.5rem;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    border-top: 1px solid rgba(0,0,0,0.1);
    padding: 1.5rem;
}

.alert {
    border-radius: 0.5rem;
    border: none;
}

.alert-info {
    background-color: rgba(23, 162, 184, 0.1);
    color: #0c5460;
}

.alert-warning {
    background-color: rgba(255, 193, 7, 0.1);
    color: #856404;
}

/* تحسين عرض الصور الشخصية */
.rounded-circle {
    object-fit: cover;
}

/* تحسين العرض على الأجهزة المحمولة */
@media (max-width: 768px) {
    .btn-group {
        display: flex;
        flex-direction: column;
        width: 100%;
    }

    .btn-group .btn {
        margin-left: 0;
        margin-bottom: 0.25rem;
        border-radius: 0.375rem;
    }

    .table-responsive {
        font-size: 0.875rem;
    }

    .modal-dialog {
        margin: 0.5rem;
    }

    .d-flex.justify-content-between {
        flex-direction: column;
        align-items: flex-start !important;
    }

    .d-flex.gap-2 {
        width: 100%;
        margin-top: 1rem;
    }

    .d-flex.gap-2 .btn {
        flex: 1;
    }

    /* تحسين عرض معلومات الطالب */
    .d-flex.align-items-center {
        flex-direction: column;
        align-items: flex-start !important;
        text-align: center;
    }

    .flex-shrink-0 {
        margin-bottom: 0.5rem;
    }
}

/* تحسين الألوان */
.text-primary {
    color: #0d6efd !important;
}

.text-success {
    color: #198754 !important;
}

.text-warning {
    color: #ffc107 !important;
}

.text-danger {
    color: #dc3545 !important;
}

.text-info {
    color: #0dcaf0 !important;
}

.text-muted {
    color: #6c757d !important;
}

/* تحسين التمرير */
.table-responsive {
    scrollbar-width: thin;
    scrollbar-color: #dee2e6 transparent;
}

.table-responsive::-webkit-scrollbar {
    height: 6px;
}

.table-responsive::-webkit-scrollbar-track {
    background: transparent;
}

.table-responsive::-webkit-scrollbar-thumb {
    background-color: #dee2e6;
    border-radius: 3px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
    background-color: #adb5bd;
}

/* تحسين النماذج */
.form-control:focus,
.form-select:focus {
    border-color: var(--bs-primary);
    box-shadow: 0 0 0 0.2rem rgba(var(--bs-primary-rgb), 0.25);
}

.form-label {
    font-weight: 500;
    color: var(--bs-gray-700);
    margin-bottom: 0.5rem;
}

/* تحسين الإحصائيات */
.bg-opacity-10 {
    background-color: rgba(var(--bs-primary-rgb), 0.1) !important;
}

.fs-4 {
    font-size: 1.5rem !important;
}

/* تحسين التحديد */
.student-checkbox:checked + label {
    background-color: rgba(var(--bs-primary-rgb), 0.1);
}

/* تحسين شريط التقدم */
.progress {
    background-color: rgba(0,0,0,0.1);
}

.progress-bar {
    background: linear-gradient(90deg, #28a745 0%, #20c997 100%);
}

/* تحسين الأزرار */
.btn {
    transition: all 0.3s ease;
    border-radius: 0.375rem;
}

.btn:hover {
    transform: translateY(-1px);
}

.btn-outline-primary:hover {
    background-color: var(--bs-primary);
    border-color: var(--bs-primary);
}

.btn-outline-success:hover {
    background-color: var(--bs-success);
    border-color: var(--bs-success);
}

.btn-outline-warning:hover {
    background-color: var(--bs-warning);
    border-color: var(--bs-warning);
}

.btn-outline-danger:hover {
    background-color: var(--bs-danger);
    border-color: var(--bs-danger);
}
</style>

<?php include 'includes/footer.php'; ?>
