<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';
require_once '../includes/activity_logger.php';

// التحقق من صلاحيات المدير
if (!isLoggedIn() || !isAdmin()) {
    header('Location: ../login.php');
    exit;
}

$error = '';
$success = '';

// معالجة تحديث الإعدادات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $platform_commission = (float)$_POST['platform_commission'];
        
        // التحقق من صحة النسبة
        if ($platform_commission < 15 || $platform_commission > 50) {
            throw new Exception('نسبة العمولة يجب أن تكون بين 15% و 50%');
        }
        
        $instructor_commission = 100 - $platform_commission;
        
        // تحديث الإعدادات
        $stmt = $conn->prepare("UPDATE commission_settings SET setting_value = ? WHERE setting_name = 'platform_commission'");
        $stmt->execute([$platform_commission]);
        
        $stmt = $conn->prepare("UPDATE commission_settings SET setting_value = ? WHERE setting_name = 'instructor_commission'");
        $stmt->execute([$instructor_commission]);
        
        logUserActivity('تحديث إعدادات العمولة', "تم تحديث عمولة المنصة إلى {$platform_commission}%");
        $success = 'تم تحديث إعدادات العمولة بنجاح';
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// جلب الإعدادات الحالية
try {
    $stmt = $conn->query("SELECT * FROM commission_settings ORDER BY setting_name");
    $settings = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $platform_commission = 30; // افتراضي
    $instructor_commission = 70; // افتراضي
    
    foreach ($settings as $setting) {
        if ($setting['setting_name'] === 'platform_commission') {
            $platform_commission = $setting['setting_value'];
        } elseif ($setting['setting_name'] === 'instructor_commission') {
            $instructor_commission = $setting['setting_value'];
        }
    }
} catch (Exception $e) {
    $error = 'حدث خطأ في جلب الإعدادات: ' . $e->getMessage();
}

// جلب إحصائيات الكورسات المدفوعة
try {
    $stmt = $conn->query("
        SELECT 
            COUNT(*) as total_paid_courses,
            SUM(price) as total_value,
            AVG(price) as avg_price,
            MIN(price) as min_price,
            MAX(price) as max_price
        FROM courses 
        WHERE course_type = 'paid'
    ");
    $course_stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // جلب إحصائيات المدربين
    $stmt = $conn->query("
        SELECT 
            u.name as instructor_name,
            COUNT(c.id) as paid_courses_count,
            SUM(c.price) as total_course_value,
            SUM(c.price * ($instructor_commission / 100)) as instructor_earnings,
            SUM(c.price * ($platform_commission / 100)) as platform_earnings
        FROM users u
        LEFT JOIN courses c ON u.id = c.instructor_id AND c.course_type = 'paid'
        WHERE u.role = 'instructor'
        GROUP BY u.id, u.name
        HAVING paid_courses_count > 0
        ORDER BY total_course_value DESC
    ");
    $instructor_stats = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    $course_stats = ['total_paid_courses' => 0, 'total_value' => 0, 'avg_price' => 0, 'min_price' => 0, 'max_price' => 0];
    $instructor_stats = [];
}

$pageTitle = 'إعدادات العمولات';
require_once 'includes/header.php';
?>

<div class="container-fluid py-4">
    <!-- العنوان -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-warning text-dark d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="mb-0">⚙️ إعدادات العمولات</h5>
                        <small>إدارة نسب العمولات للكورسات المدفوعة</small>
                    </div>
                    <div>
                        <a href="dashboard.php" class="btn btn-dark btn-sm">
                            <i class="fas fa-arrow-left"></i> العودة
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php if ($success): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo $success; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- إعدادات العمولة -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">تحديث نسب العمولة</h6>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="form-group mb-3">
                            <label for="platform_commission">نسبة عمولة المنصة (%)</label>
                            <input type="number" class="form-control" id="platform_commission" name="platform_commission" 
                                   min="15" max="50" step="0.01" value="<?php echo $platform_commission; ?>" required>
                            <small class="form-text text-muted">
                                الحد الأدنى: 15% | الحد الأقصى: 50%
                            </small>
                        </div>
                        
                        <div class="form-group mb-3">
                            <label>نسبة المدرب (%)</label>
                            <input type="text" class="form-control" id="instructor_commission_display" 
                                   value="<?php echo $instructor_commission; ?>%" readonly>
                            <small class="form-text text-muted">
                                يتم حسابها تلقائياً (100% - نسبة المنصة)
                            </small>
                        </div>
                        
                        <div class="alert alert-info">
                            <h6>مثال على التوزيع:</h6>
                            <div id="commission_example">
                                <p>كورس بسعر 100 ريال:</p>
                                <ul>
                                    <li>المنصة: <span id="platform_amount"><?php echo $platform_commission; ?></span> ريال</li>
                                    <li>المدرب: <span id="instructor_amount"><?php echo $instructor_commission; ?></span> ريال</li>
                                </ul>
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-warning">
                            <i class="fas fa-save"></i> حفظ الإعدادات
                        </button>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">الإعدادات الحالية</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>الإعداد</th>
                                    <th>القيمة</th>
                                    <th>النطاق</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($settings as $setting): ?>
                                    <tr>
                                        <td>
                                            <?php 
                                            echo $setting['setting_name'] === 'platform_commission' ? 'عمولة المنصة' : 'نسبة المدرب';
                                            ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-primary"><?php echo $setting['setting_value']; ?>%</span>
                                        </td>
                                        <td>
                                            <small class="text-muted">
                                                <?php echo $setting['min_value']; ?>% - <?php echo $setting['max_value']; ?>%
                                            </small>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="mt-3">
                        <small class="text-muted">
                            آخر تحديث: <?php echo isset($settings[0]['updated_at']) ? date('Y-m-d H:i', strtotime($settings[0]['updated_at'])) : 'غير محدد'; ?>
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات الكورسات المدفوعة -->
    <div class="row mb-4">
        <div class="col-12">
            <h6 class="text-muted mb-3">إحصائيات الكورسات المدفوعة</h6>
        </div>
        <div class="col-md-2">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h4><?php echo $course_stats['total_paid_courses']; ?></h4>
                    <p class="mb-0">كورسات مدفوعة</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h4><?php echo number_format($course_stats['total_value'], 0); ?></h4>
                    <p class="mb-0">إجمالي القيمة (ريال)</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h4><?php echo number_format($course_stats['avg_price'], 0); ?></h4>
                    <p class="mb-0">متوسط السعر (ريال)</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h4><?php echo number_format($course_stats['total_value'] * ($platform_commission / 100), 0); ?></h4>
                    <p class="mb-0">أرباح المنصة المتوقعة (ريال)</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-secondary text-white">
                <div class="card-body text-center">
                    <h4><?php echo number_format($course_stats['total_value'] * ($instructor_commission / 100), 0); ?></h4>
                    <p class="mb-0">أرباح المدربين المتوقعة (ريال)</p>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات المدربين -->
    <?php if (!empty($instructor_stats)): ?>
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">إحصائيات المدربين</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>المدرب</th>
                                        <th>عدد الكورسات المدفوعة</th>
                                        <th>إجمالي قيمة الكورسات</th>
                                        <th>أرباح المدرب المتوقعة</th>
                                        <th>أرباح المنصة المتوقعة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($instructor_stats as $stat): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($stat['instructor_name']); ?></td>
                                            <td><span class="badge bg-primary"><?php echo $stat['paid_courses_count']; ?></span></td>
                                            <td><strong><?php echo number_format($stat['total_course_value'], 2); ?> ريال</strong></td>
                                            <td class="text-success">
                                                <strong><?php echo number_format($stat['instructor_earnings'], 2); ?> ريال</strong>
                                                <small class="d-block">(<?php echo $instructor_commission; ?>%)</small>
                                            </td>
                                            <td class="text-warning">
                                                <strong><?php echo number_format($stat['platform_earnings'], 2); ?> ريال</strong>
                                                <small class="d-block">(<?php echo $platform_commission; ?>%)</small>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<script>
// تحديث المثال عند تغيير نسبة العمولة
document.getElementById('platform_commission').addEventListener('input', function() {
    const platformCommission = parseFloat(this.value);
    const instructorCommission = 100 - platformCommission;
    
    // تحديث نسبة المدرب
    document.getElementById('instructor_commission_display').value = instructorCommission.toFixed(2) + '%';
    
    // تحديث المثال
    document.getElementById('platform_amount').textContent = platformCommission.toFixed(0);
    document.getElementById('instructor_amount').textContent = instructorCommission.toFixed(0);
});
</script>

<?php require_once '../includes/footer.php'; ?>
