<?php
/**
 * صفحة عرض حالة التسجيل
 * Registration Status Page
 */

require_once 'config/database.php';
require_once 'includes/functions.php';

$email = isset($_GET['email']) ? filter_var($_GET['email'], FILTER_SANITIZE_EMAIL) : '';
$status_info = null;
$error = '';

if (!empty($email)) {
    try {
        // البحث عن المستخدم
        $stmt = $conn->prepare("SELECT id, name, email, status, created_at FROM users WHERE email = ?");
        $stmt->execute([$email]);
        $user = $stmt->fetch();
        
        if ($user) {
            $status_info = $user;
            
            // البحث عن طلب الانضمام
            $stmt = $conn->prepare("SELECT status, created_at, processed_at FROM join_requests WHERE email = ? ORDER BY created_at DESC LIMIT 1");
            $stmt->execute([$email]);
            $join_request = $stmt->fetch();
            
            if ($join_request) {
                $status_info['join_request'] = $join_request;
            }
        } else {
            $error = 'لم يتم العثور على حساب بهذا البريد الإلكتروني';
        }
    } catch (Exception $e) {
        $error = 'حدث خطأ في البحث عن الحساب';
        error_log("Error in registration-status.php: " . $e->getMessage());
    }
}
?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>حالة التسجيل - منصة التعلم</title>
    
    <!-- الخطوط العربية -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Bootstrap و Font Awesome -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- ملفات CSS المخصصة -->
    <link href="assets/css/main.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Cairo', sans-serif;
        }
        
        .status-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem 1rem;
        }
        
        .status-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 600px;
            width: 100%;
        }
        
        .status-header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .status-body {
            padding: 2rem;
        }
        
        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.875rem;
            letter-spacing: 0.5px;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
            border: 2px solid #ffeaa7;
        }
        
        .status-active {
            background: #d4edda;
            color: #155724;
            border: 2px solid #00b894;
        }
        
        .status-rejected {
            background: #f8d7da;
            color: #721c24;
            border: 2px solid #e17055;
        }
        
        .timeline {
            position: relative;
            padding: 1rem 0;
        }
        
        .timeline-item {
            position: relative;
            padding: 1rem 0 1rem 3rem;
            border-right: 2px solid #e9ecef;
        }
        
        .timeline-item:last-child {
            border-right: none;
        }
        
        .timeline-icon {
            position: absolute;
            right: -10px;
            top: 1.5rem;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.75rem;
        }
        
        .timeline-icon.completed {
            background: #28a745;
            color: white;
        }
        
        .timeline-icon.pending {
            background: #ffc107;
            color: white;
        }
        
        .search-form {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
    </style>
</head>
<body>
    <div class="status-container">
        <div class="container">
            <?php if (empty($email) || empty($status_info)): ?>
                <!-- نموذج البحث -->
                <div class="search-form">
                    <h3 class="text-center mb-4">
                        <i class="fas fa-search me-2"></i>
                        تحقق من حالة تسجيلك
                    </h3>
                    
                    <form method="GET" action="">
                        <div class="input-group mb-3">
                            <input type="email" class="form-control form-control-lg" 
                                   name="email" placeholder="أدخل بريدك الإلكتروني"
                                   value="<?php echo htmlspecialchars($email); ?>" required>
                            <button class="btn btn-primary btn-lg" type="submit">
                                <i class="fas fa-search me-1"></i>
                                بحث
                            </button>
                        </div>
                    </form>
                    
                    <?php if ($error): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <?php echo htmlspecialchars($error); ?>
                        </div>
                    <?php endif; ?>
                    
                    <div class="text-center mt-4">
                        <a href="register.php" class="btn btn-outline-primary me-2">
                            <i class="fas fa-user-plus me-1"></i>
                            تسجيل حساب جديد
                        </a>
                        <a href="login.php" class="btn btn-outline-secondary">
                            <i class="fas fa-sign-in-alt me-1"></i>
                            تسجيل الدخول
                        </a>
                    </div>
                </div>
            <?php else: ?>
                <!-- عرض حالة التسجيل -->
                <div class="status-card">
                    <div class="status-header">
                        <h2>
                            <i class="fas fa-user-check me-2"></i>
                            حالة التسجيل
                        </h2>
                        <p class="mb-0">مرحباً <?php echo htmlspecialchars($status_info['name']); ?></p>
                    </div>
                    
                    <div class="status-body">
                        <div class="text-center mb-4">
                            <h5>حالة الحساب الحالية:</h5>
                            <?php
                            $status_class = '';
                            $status_text = '';
                            $status_icon = '';
                            
                            switch ($status_info['status']) {
                                case 'pending':
                                    $status_class = 'status-pending';
                                    $status_text = 'قيد المراجعة';
                                    $status_icon = 'fas fa-clock';
                                    break;
                                case 'active':
                                    $status_class = 'status-active';
                                    $status_text = 'مفعل';
                                    $status_icon = 'fas fa-check-circle';
                                    break;
                                case 'inactive':
                                    $status_class = 'status-rejected';
                                    $status_text = 'غير مفعل';
                                    $status_icon = 'fas fa-times-circle';
                                    break;
                            }
                            ?>
                            <span class="status-badge <?php echo $status_class; ?>">
                                <i class="<?php echo $status_icon; ?> me-1"></i>
                                <?php echo $status_text; ?>
                            </span>
                        </div>
                        
                        <!-- الجدول الزمني -->
                        <div class="timeline">
                            <div class="timeline-item">
                                <div class="timeline-icon completed">
                                    <i class="fas fa-check"></i>
                                </div>
                                <h6>تم إرسال طلب التسجيل</h6>
                                <small class="text-muted">
                                    <?php echo date('Y/m/d H:i', strtotime($status_info['created_at'])); ?>
                                </small>
                            </div>
                            
                            <?php if ($status_info['status'] === 'pending'): ?>
                                <div class="timeline-item">
                                    <div class="timeline-icon pending">
                                        <i class="fas fa-clock"></i>
                                    </div>
                                    <h6>قيد المراجعة من قبل الإدارة</h6>
                                    <small class="text-muted">سيتم إشعارك عند الموافقة</small>
                                </div>
                            <?php elseif ($status_info['status'] === 'active'): ?>
                                <div class="timeline-item">
                                    <div class="timeline-icon completed">
                                        <i class="fas fa-check"></i>
                                    </div>
                                    <h6>تم تفعيل الحساب</h6>
                                    <small class="text-muted">يمكنك الآن تسجيل الدخول</small>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <!-- الإجراءات -->
                        <div class="text-center mt-4">
                            <?php if ($status_info['status'] === 'active'): ?>
                                <a href="login.php" class="btn btn-success btn-lg">
                                    <i class="fas fa-sign-in-alt me-2"></i>
                                    تسجيل الدخول
                                </a>
                            <?php else: ?>
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    يرجى انتظار موافقة الإدارة على حسابك. ستتلقى إشعاراً عبر البريد الإلكتروني عند التفعيل.
                                </div>
                            <?php endif; ?>
                            
                            <div class="mt-3">
                                <a href="?email=" class="btn btn-outline-secondary me-2">
                                    <i class="fas fa-search me-1"></i>
                                    بحث عن حساب آخر
                                </a>
                                <a href="index.php" class="btn btn-outline-primary">
                                    <i class="fas fa-home me-1"></i>
                                    الصفحة الرئيسية
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
