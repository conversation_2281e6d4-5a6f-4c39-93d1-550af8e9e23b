<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات المدير
if (!isLoggedIn() || !isAdmin()) {
    header('Location: ../login.php');
    exit;
}

$success_message = '';
$error_message = '';

// معالجة تحديث حالة الدفع
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $action = $_POST['action'];
    $payment_id = (int)($_POST['payment_id'] ?? 0);
    
    if ($action === 'update_status' && $payment_id > 0) {
        $new_status = $_POST['status'] ?? '';
        $admin_notes = $_POST['admin_notes'] ?? '';
        
        try {
            $stmt = $conn->prepare("
                UPDATE payments 
                SET status = ?, admin_notes = ?, updated_at = NOW() 
                WHERE id = ?
            ");
            $stmt->execute([$new_status, $admin_notes, $payment_id]);
            
            if ($stmt->rowCount() > 0) {
                logUserActivity($_SESSION['user_id'], 'تحديث حالة دفع', "تم تحديث حالة الدفع رقم $payment_id إلى $new_status");
                $success_message = 'تم تحديث حالة الدفع بنجاح';
            } else {
                $error_message = 'لم يتم العثور على الدفعة';
            }
        } catch (Exception $e) {
            $error_message = 'حدث خطأ أثناء تحديث حالة الدفع: ' . $e->getMessage();
        }
    }
}

// جلب المدفوعات
try {
    $filter = $_GET['filter'] ?? 'all';
    $search = $_GET['search'] ?? '';
    
    $where_conditions = [];
    $params = [];
    
    if ($filter !== 'all') {
        $where_conditions[] = "p.status = ?";
        $params[] = $filter;
    }
    
    if (!empty($search)) {
        $where_conditions[] = "(u.name LIKE ? OR u.email LIKE ? OR c.title LIKE ?)";
        $search_param = "%$search%";
        $params[] = $search_param;
        $params[] = $search_param;
        $params[] = $search_param;
    }
    
    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
    
    $stmt = $conn->prepare("
        SELECT p.*, u.name as student_name, u.email as student_email, 
               c.title as course_title, c.price as course_price,
               i.name as instructor_name
        FROM payments p
        LEFT JOIN users u ON p.student_id = u.id
        LEFT JOIN courses c ON p.course_id = c.id
        LEFT JOIN users i ON c.instructor_id = i.id
        $where_clause
        ORDER BY p.created_at DESC
    ");
    $stmt->execute($params);
    $payments = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // إحصائيات المدفوعات
    $stats = [
        'total' => $conn->query("SELECT COUNT(*) FROM payments")->fetchColumn(),
        'pending' => $conn->query("SELECT COUNT(*) FROM payments WHERE status = 'pending'")->fetchColumn(),
        'completed' => $conn->query("SELECT COUNT(*) FROM payments WHERE status = 'completed'")->fetchColumn(),
        'failed' => $conn->query("SELECT COUNT(*) FROM payments WHERE status = 'failed'")->fetchColumn(),
        'total_amount' => $conn->query("SELECT COALESCE(SUM(amount), 0) FROM payments WHERE status = 'completed'")->fetchColumn()
    ];
    
} catch (Exception $e) {
    $error_message = 'حدث خطأ أثناء جلب المدفوعات: ' . $e->getMessage();
    $payments = [];
    $stats = ['total' => 0, 'pending' => 0, 'completed' => 0, 'failed' => 0, 'total_amount' => 0];
}

$pageTitle = 'إدارة المدفوعات';
$pageSubtitle = 'مراقبة ومتابعة جميع المدفوعات في النظام';
require_once 'includes/admin-header.php';
?>

<div class="container-fluid py-4">
    <!-- رسائل النجاح والخطأ -->
    <?php if ($success_message): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>
    
    <?php if ($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- الإحصائيات -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="admin-card">
                <div class="card-body text-center">
                    <div class="stat-icon bg-primary text-white mb-3">
                        <i class="fas fa-credit-card"></i>
                    </div>
                    <h3 class="mb-1"><?php echo number_format($stats['total']); ?></h3>
                    <p class="text-muted mb-0">إجمالي المدفوعات</p>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="admin-card">
                <div class="card-body text-center">
                    <div class="stat-icon bg-warning text-white mb-3">
                        <i class="fas fa-clock"></i>
                    </div>
                    <h3 class="mb-1"><?php echo number_format($stats['pending']); ?></h3>
                    <p class="text-muted mb-0">قيد المراجعة</p>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="admin-card">
                <div class="card-body text-center">
                    <div class="stat-icon bg-success text-white mb-3">
                        <i class="fas fa-check"></i>
                    </div>
                    <h3 class="mb-1"><?php echo number_format($stats['completed']); ?></h3>
                    <p class="text-muted mb-0">مكتملة</p>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="admin-card">
                <div class="card-body text-center">
                    <div class="stat-icon bg-info text-white mb-3">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <h3 class="mb-1"><?php echo number_format($stats['total_amount'], 2); ?> ر.س</h3>
                    <p class="text-muted mb-0">إجمالي الإيرادات</p>
                </div>
            </div>
        </div>
    </div>

    <!-- أدوات التصفية والبحث -->
    <div class="admin-card mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-4">
                    <label class="form-label">تصفية حسب الحالة</label>
                    <select name="filter" class="form-select">
                        <option value="all" <?php echo $filter === 'all' ? 'selected' : ''; ?>>جميع المدفوعات</option>
                        <option value="pending" <?php echo $filter === 'pending' ? 'selected' : ''; ?>>قيد المراجعة</option>
                        <option value="completed" <?php echo $filter === 'completed' ? 'selected' : ''; ?>>مكتملة</option>
                        <option value="failed" <?php echo $filter === 'failed' ? 'selected' : ''; ?>>فاشلة</option>
                    </select>
                </div>
                <div class="col-md-6">
                    <label class="form-label">البحث</label>
                    <input type="text" name="search" class="form-control" 
                           placeholder="ابحث بالطالب أو الدورة..."
                           value="<?php echo htmlspecialchars($search); ?>">
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <button type="submit" class="btn btn-primary d-block w-100">
                        <i class="fas fa-search me-1"></i>بحث
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- قائمة المدفوعات -->
    <div class="admin-card">
        <div class="card-header">
            <h6 class="mb-0"><i class="fas fa-list me-2"></i>المدفوعات (<?php echo count($payments); ?>)</h6>
        </div>
        <div class="card-body">
            <?php if (empty($payments)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-credit-card fa-4x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد مدفوعات</h5>
                    <p class="text-muted">لم يتم العثور على أي مدفوعات تطابق معايير البحث</p>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover" id="paymentsTable">
                        <thead>
                            <tr>
                                <th>الطالب</th>
                                <th>الدورة</th>
                                <th>المبلغ</th>
                                <th>طريقة الدفع</th>
                                <th>الحالة</th>
                                <th>التاريخ</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($payments as $payment): ?>
                                <tr>
                                    <td>
                                        <div>
                                            <strong><?php echo htmlspecialchars($payment['student_name'] ?? 'غير محدد'); ?></strong>
                                            <br>
                                            <small class="text-muted"><?php echo htmlspecialchars($payment['student_email'] ?? ''); ?></small>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <strong><?php echo htmlspecialchars($payment['course_title'] ?? 'غير محدد'); ?></strong>
                                            <br>
                                            <small class="text-muted">المدرب: <?php echo htmlspecialchars($payment['instructor_name'] ?? 'غير محدد'); ?></small>
                                        </div>
                                    </td>
                                    <td>
                                        <strong><?php echo number_format($payment['amount'], 2); ?> ر.س</strong>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">
                                            <?php echo htmlspecialchars($payment['payment_method'] ?? 'غير محدد'); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php 
                                            echo $payment['status'] === 'pending' ? 'warning' : 
                                                ($payment['status'] === 'completed' ? 'success' : 'danger'); 
                                        ?>">
                                            <?php
                                            switch($payment['status']) {
                                                case 'pending': echo 'قيد المراجعة'; break;
                                                case 'completed': echo 'مكتملة'; break;
                                                case 'failed': echo 'فاشلة'; break;
                                                default: echo $payment['status'];
                                            }
                                            ?>
                                        </span>
                                    </td>
                                    <td><?php echo date('Y-m-d H:i', strtotime($payment['created_at'])); ?></td>
                                    <td>
                                        <button class="btn btn-primary btn-sm" 
                                                onclick="updatePaymentStatus(<?php echo $payment['id']; ?>, '<?php echo $payment['status']; ?>')">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-info btn-sm" 
                                                onclick="viewPaymentDetails(<?php echo $payment['id']; ?>)">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Modal تحديث حالة الدفع -->
<div class="modal fade" id="updateStatusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تحديث حالة الدفع</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="updateStatusForm">
                <div class="modal-body">
                    <input type="hidden" name="action" value="update_status">
                    <input type="hidden" name="payment_id" id="updatePaymentId">
                    
                    <div class="mb-3">
                        <label class="form-label">الحالة الجديدة</label>
                        <select class="form-select" name="status" id="updateStatus" required>
                            <option value="pending">قيد المراجعة</option>
                            <option value="completed">مكتملة</option>
                            <option value="failed">فاشلة</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">ملاحظات الإدارة</label>
                        <textarea class="form-control" name="admin_notes" rows="3" 
                                  placeholder="أضف ملاحظات حول تحديث الحالة..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">تحديث الحالة</button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    font-size: 24px;
}
</style>

<script>
// تهيئة DataTable
$(document).ready(function() {
    $('#paymentsTable').DataTable({
        order: [[5, 'desc']],
        pageLength: 25,
        responsive: true,
        language: {
            url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
        }
    });
});

// تحديث حالة الدفع
function updatePaymentStatus(paymentId, currentStatus) {
    document.getElementById('updatePaymentId').value = paymentId;
    document.getElementById('updateStatus').value = currentStatus;
    new bootstrap.Modal(document.getElementById('updateStatusModal')).show();
}

// عرض تفاصيل الدفع
function viewPaymentDetails(paymentId) {
    showToast('سيتم تطوير هذه الميزة قريباً', 'info');
}
</script>

<?php require_once 'includes/admin-footer.php'; ?>
