<?php
require_once '../config/database.php';

echo "<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>إنشاء جدول الحضور</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css' rel='stylesheet'>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f8f9fa; }
        .container { max-width: 800px; margin: 30px auto; }
        .log-item { padding: 8px; margin: 3px 0; border-radius: 5px; font-size: 14px; }
        .log-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .log-error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .log-info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
<div class='container'>
    <div class='card'>
        <div class='card-header bg-primary text-white'>
            <h4 class='mb-0'><i class='fas fa-table me-2'></i>إنشاء جدول الحضور</h4>
        </div>
        <div class='card-body'>";

function logMessage($message, $type = 'info') {
    $icon = $type === 'success' ? 'check-circle' : ($type === 'error' ? 'exclamation-circle' : 'info-circle');
    echo "<div class='log-item log-$type'><i class='fas fa-$icon me-2'></i>$message</div>";
    flush();
    ob_flush();
}

try {
    logMessage('🔧 بدء إنشاء جدول الحضور...', 'info');
    
    // إنشاء جدول session_attendance
    $conn->exec("
        CREATE TABLE IF NOT EXISTS session_attendance (
            id INT AUTO_INCREMENT PRIMARY KEY,
            session_id INT NOT NULL,
            student_id INT NOT NULL,
            attended_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            attendance_status ENUM('present', 'absent', 'late') DEFAULT 'present',
            notes TEXT,
            FOREIGN KEY (session_id) REFERENCES sessions(id) ON DELETE CASCADE,
            FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
            UNIQUE KEY unique_session_student (session_id, student_id)
        )
    ");
    logMessage("✓ تم إنشاء جدول session_attendance", 'success');
    
    // إضافة بيانات حضور تجريبية
    logMessage('=== إضافة بيانات حضور تجريبية ===', 'info');
    
    // جلب الجلسات والطلاب
    $sessions = $conn->query("SELECT id FROM sessions LIMIT 10")->fetchAll(PDO::FETCH_COLUMN);
    $students = $conn->query("SELECT id FROM users WHERE role = 'student' LIMIT 20")->fetchAll(PDO::FETCH_COLUMN);
    
    $attendance_added = 0;
    foreach ($sessions as $session_id) {
        foreach ($students as $student_id) {
            if (rand(0, 2) == 0) { // 33% احتمال الحضور
                try {
                    $attendance_status = ['present', 'absent', 'late'][rand(0, 2)];
                    
                    $stmt = $conn->prepare("
                        INSERT IGNORE INTO session_attendance (session_id, student_id, attendance_status) 
                        VALUES (?, ?, ?)
                    ");
                    $stmt->execute([$session_id, $student_id, $attendance_status]);
                    $attendance_added++;
                } catch (Exception $e) {
                    // تجاهل الأخطاء (مثل التكرار)
                }
            }
        }
    }
    
    logMessage("✓ تم إضافة $attendance_added سجل حضور تجريبي", 'success');
    
    // عرض الإحصائيات
    logMessage('=== الإحصائيات النهائية ===', 'info');
    
    $stats = [
        'sessions' => $conn->query("SELECT COUNT(*) FROM sessions")->fetchColumn(),
        'students' => $conn->query("SELECT COUNT(*) FROM users WHERE role = 'student'")->fetchColumn(),
        'attendance_records' => $conn->query("SELECT COUNT(*) FROM session_attendance")->fetchColumn(),
        'present_count' => $conn->query("SELECT COUNT(*) FROM session_attendance WHERE attendance_status = 'present'")->fetchColumn(),
        'absent_count' => $conn->query("SELECT COUNT(*) FROM session_attendance WHERE attendance_status = 'absent'")->fetchColumn(),
        'late_count' => $conn->query("SELECT COUNT(*) FROM session_attendance WHERE attendance_status = 'late'")->fetchColumn()
    ];
    
    foreach ($stats as $key => $value) {
        logMessage("📊 $key: " . number_format($value), 'info');
    }
    
    logMessage('🎉 تم إنشاء جدول الحضور وإضافة البيانات التجريبية بنجاح!', 'success');
    
} catch (Exception $e) {
    logMessage('❌ خطأ: ' . $e->getMessage(), 'error');
}

echo "
        </div>
        <div class='mt-4 text-center'>
            <div class='alert alert-success'>
                <h5><i class='fas fa-check-circle me-2'></i>تم الإنشاء بنجاح!</h5>
                <p class='mb-0'>الآن يمكنك استخدام نظام إدارة الحضور</p>
            </div>
            <a href='manage-sessions.php?course_id=25' class='btn btn-primary btn-lg me-2'>
                <i class='fas fa-video me-2'></i>إدارة الجلسات
            </a>
            <a href='course-students.php?course_id=25' class='btn btn-success me-2'>
                <i class='fas fa-users me-2'></i>إدارة الطلاب
            </a>
            <a href='manage-courses.php' class='btn btn-info'>
                <i class='fas fa-book me-2'></i>إدارة الكورسات
            </a>
        </div>
    </div>
</div>
</body>
</html>";
?>
