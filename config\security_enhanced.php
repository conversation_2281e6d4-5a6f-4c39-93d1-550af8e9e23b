<?php
/**
 * إعدادات الأمان المحسنة
 * Enhanced Security Configuration
 */

// منع الوصول المباشر
if (!defined('SECURITY_CHECK')) {
    die('Access denied');
}

// ===== إعدادات الأمان العامة =====
class SecurityConfig {
    
    // إعدادات كلمة المرور
    const PASSWORD_MIN_LENGTH = 8;
    const PASSWORD_REQUIRE_UPPERCASE = true;
    const PASSWORD_REQUIRE_LOWERCASE = true;
    const PASSWORD_REQUIRE_NUMBERS = true;
    const PASSWORD_REQUIRE_SYMBOLS = false;
    
    // إعدادات تسجيل الدخول
    const MAX_LOGIN_ATTEMPTS = 5;
    const LOGIN_LOCKOUT_TIME = 900; // 15 دقيقة
    const SESSION_TIMEOUT = 1800; // 30 دقيقة
    const REMEMBER_ME_DURATION = 2592000; // 30 يوم
    
    // إعدادات CSRF
    const CSRF_TOKEN_LENGTH = 32;
    const CSRF_TOKEN_LIFETIME = 3600; // ساعة واحدة
    
    // إعدادات التشفير
    const ENCRYPTION_METHOD = 'AES-256-CBC';
    const HASH_ALGORITHM = 'sha256';
    
    // إعدادات الملفات
    const ALLOWED_FILE_TYPES = ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx'];
    const MAX_FILE_SIZE = 5242880; // 5MB
    const UPLOAD_PATH = 'uploads/';
    
    // إعدادات IP
    const ENABLE_IP_WHITELIST = false;
    const ALLOWED_IPS = ['127.0.0.1', '::1'];
    const ENABLE_IP_BLACKLIST = true;
    
    /**
     * تطبيق headers الأمان
     */
    public static function setSecurityHeaders() {
        // منع Clickjacking
        header("X-Frame-Options: DENY");
        
        // حماية XSS
        header("X-XSS-Protection: 1; mode=block");
        
        // منع MIME-type sniffing
        header("X-Content-Type-Options: nosniff");
        
        // Referrer Policy
        header("Referrer-Policy: strict-origin-when-cross-origin");
        
        // Content Security Policy
        $csp = "default-src 'self'; ";
        $csp .= "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://code.jquery.com https://cdn.datatables.net; ";
        $csp .= "style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://fonts.googleapis.com https://cdnjs.cloudflare.com; ";
        $csp .= "font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com; ";
        $csp .= "img-src 'self' data: https:; ";
        $csp .= "connect-src 'self'; ";
        $csp .= "frame-ancestors 'none';";
        header("Content-Security-Policy: $csp");
        
        // HSTS (فقط مع HTTPS)
        if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') {
            header("Strict-Transport-Security: max-age=31536000; includeSubDomains; preload");
        }
        
        // إزالة معلومات الخادم
        header_remove("X-Powered-By");
        header_remove("Server");
    }
    
    /**
     * التحقق من قوة كلمة المرور
     */
    public static function validatePassword($password) {
        $errors = [];
        
        if (strlen($password) < self::PASSWORD_MIN_LENGTH) {
            $errors[] = "كلمة المرور يجب أن تكون " . self::PASSWORD_MIN_LENGTH . " أحرف على الأقل";
        }
        
        if (self::PASSWORD_REQUIRE_UPPERCASE && !preg_match('/[A-Z]/', $password)) {
            $errors[] = "كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل";
        }
        
        if (self::PASSWORD_REQUIRE_LOWERCASE && !preg_match('/[a-z]/', $password)) {
            $errors[] = "كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل";
        }
        
        if (self::PASSWORD_REQUIRE_NUMBERS && !preg_match('/[0-9]/', $password)) {
            $errors[] = "كلمة المرور يجب أن تحتوي على رقم واحد على الأقل";
        }
        
        if (self::PASSWORD_REQUIRE_SYMBOLS && !preg_match('/[^A-Za-z0-9]/', $password)) {
            $errors[] = "كلمة المرور يجب أن تحتوي على رمز خاص واحد على الأقل";
        }
        
        return $errors;
    }
    
    /**
     * إنشاء CSRF token
     */
    public static function generateCSRFToken() {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        $token = bin2hex(random_bytes(self::CSRF_TOKEN_LENGTH));
        $_SESSION['csrf_token'] = $token;
        $_SESSION['csrf_token_time'] = time();
        
        return $token;
    }
    
    /**
     * التحقق من CSRF token
     */
    public static function validateCSRFToken($token) {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        if (!isset($_SESSION['csrf_token']) || !isset($_SESSION['csrf_token_time'])) {
            return false;
        }
        
        // التحقق من انتهاء صلاحية التوكن
        if (time() - $_SESSION['csrf_token_time'] > self::CSRF_TOKEN_LIFETIME) {
            unset($_SESSION['csrf_token'], $_SESSION['csrf_token_time']);
            return false;
        }
        
        return hash_equals($_SESSION['csrf_token'], $token);
    }
    
    /**
     * تنظيف البيانات المدخلة
     */
    public static function sanitizeInput($input, $type = 'string') {
        switch ($type) {
            case 'email':
                return filter_var(trim($input), FILTER_SANITIZE_EMAIL);
            case 'url':
                return filter_var(trim($input), FILTER_SANITIZE_URL);
            case 'int':
                return filter_var($input, FILTER_SANITIZE_NUMBER_INT);
            case 'float':
                return filter_var($input, FILTER_SANITIZE_NUMBER_FLOAT, FILTER_FLAG_ALLOW_FRACTION);
            case 'html':
                return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
            default:
                return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
        }
    }
    
    /**
     * التحقق من صحة الملف المرفوع
     */
    public static function validateUploadedFile($file) {
        $errors = [];
        
        // التحقق من وجود خطأ في الرفع
        if ($file['error'] !== UPLOAD_ERR_OK) {
            $errors[] = "حدث خطأ أثناء رفع الملف";
            return $errors;
        }
        
        // التحقق من حجم الملف
        if ($file['size'] > self::MAX_FILE_SIZE) {
            $errors[] = "حجم الملف كبير جداً. الحد الأقصى: " . (self::MAX_FILE_SIZE / 1024 / 1024) . "MB";
        }
        
        // التحقق من نوع الملف
        $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!in_array($fileExtension, self::ALLOWED_FILE_TYPES)) {
            $errors[] = "نوع الملف غير مسموح. الأنواع المسموحة: " . implode(', ', self::ALLOWED_FILE_TYPES);
        }
        
        // التحقق من MIME type
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);
        
        $allowedMimeTypes = [
            'image/jpeg', 'image/png', 'image/gif',
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        ];
        
        if (!in_array($mimeType, $allowedMimeTypes)) {
            $errors[] = "نوع الملف غير صحيح";
        }
        
        return $errors;
    }
    
    /**
     * التحقق من عنوان IP
     */
    public static function validateIP($ip = null) {
        if ($ip === null) {
            $ip = self::getRealIP();
        }
        
        // التحقق من القائمة البيضاء
        if (self::ENABLE_IP_WHITELIST && !in_array($ip, self::ALLOWED_IPS)) {
            return false;
        }
        
        // التحقق من القائمة السوداء
        if (self::ENABLE_IP_BLACKLIST) {
            // يمكن إضافة منطق للتحقق من قاعدة البيانات
            global $conn;
            try {
                $stmt = $conn->prepare("SELECT id FROM blocked_ips WHERE ip_address = ? AND blocked_until > NOW()");
                $stmt->execute([$ip]);
                if ($stmt->rowCount() > 0) {
                    return false;
                }
            } catch (PDOException $e) {
                error_log("Error checking blocked IPs: " . $e->getMessage());
            }
        }
        
        return true;
    }
    
    /**
     * الحصول على عنوان IP الحقيقي
     */
    public static function getRealIP() {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = explode(',', $ip)[0];
                }
                $ip = trim($ip);
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
    
    /**
     * تشفير البيانات
     */
    public static function encrypt($data, $key = null) {
        if ($key === null) {
            $key = self::getEncryptionKey();
        }
        
        $iv = random_bytes(16);
        $encrypted = openssl_encrypt($data, self::ENCRYPTION_METHOD, $key, 0, $iv);
        
        return base64_encode($iv . $encrypted);
    }
    
    /**
     * فك تشفير البيانات
     */
    public static function decrypt($data, $key = null) {
        if ($key === null) {
            $key = self::getEncryptionKey();
        }
        
        $data = base64_decode($data);
        $iv = substr($data, 0, 16);
        $encrypted = substr($data, 16);
        
        return openssl_decrypt($encrypted, self::ENCRYPTION_METHOD, $key, 0, $iv);
    }
    
    /**
     * الحصول على مفتاح التشفير
     */
    private static function getEncryptionKey() {
        // يجب تخزين هذا المفتاح في مكان آمن
        return hash('sha256', 'your-secret-encryption-key-here', true);
    }
    
    /**
     * تسجيل محاولة أمنية مشبوهة
     */
    public static function logSecurityEvent($event, $details = '', $userId = null) {
        global $conn;
        
        try {
            $ip = self::getRealIP();
            $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
            
            $stmt = $conn->prepare(
                "INSERT INTO activity_logs (user_id, action, details, ip_address, user_agent) 
                 VALUES (?, ?, ?, ?, ?)"
            );
            $stmt->execute([$userId, "SECURITY: $event", $details, $ip, $userAgent]);
        } catch (PDOException $e) {
            error_log("Error logging security event: " . $e->getMessage());
        }
    }
}

// تطبيق headers الأمان تلقائياً
SecurityConfig::setSecurityHeaders();

// تعريف ثابت للتحقق من الأمان
define('SECURITY_CHECK', true);
?>
