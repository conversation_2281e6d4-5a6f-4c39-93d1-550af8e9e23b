<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من تسجيل الدخول كطالب
if (!isLoggedIn() || !isStudent()) {
    header('Location: ../login.php');
    exit;
}

$student_id = $_SESSION['user_id'];
$session_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
$error = '';

// التحقق من صحة الجلسة وتسجيل الطالب في الكورس
try {
    $stmt = $conn->prepare("
        SELECT s.*, c.title as course_title, u.name as instructor_name,
               ce.student_id as is_enrolled
        FROM sessions s
        JOIN courses c ON s.course_id = c.id
        JOIN users u ON c.instructor_id = u.id
        LEFT JOIN course_enrollments ce ON c.id = ce.course_id AND ce.student_id = ? AND ce.status = 'active'
        WHERE s.id = ?
    ");
    $stmt->execute([$student_id, $session_id]);
    $session = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$session) {
        $error = 'الجلسة غير موجودة';
    } elseif (!$session['is_enrolled']) {
        $error = 'غير مسجل في هذا الكورس';
    } else {
        // التحقق من وقت الجلسة
        $current_date = date('Y-m-d');
        $current_time = date('H:i:s');
        
        if ($session['session_date'] > $current_date) {
            $error = 'الجلسة لم تبدأ بعد';
        } elseif ($session['session_date'] < $current_date) {
            $error = 'الجلسة انتهت';
        } elseif ($session['session_date'] == $current_date && $session['start_time'] > $current_time) {
            $error = 'الجلسة لم تبدأ بعد';
        }
    }
} catch (PDOException $e) {
    $error = 'حدث خطأ في التحقق من الجلسة';
}

$pageTitle = $session ? 'انضمام للجلسة: ' . $session['title'] : 'انضمام للجلسة';
require_once '../includes/header.php';
?>

<div class="container-fluid py-4">
    <?php if ($error): ?>
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-danger text-white text-center">
                        <h5 class="mb-0">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            خطأ في الوصول للجلسة
                        </h5>
                    </div>
                    <div class="card-body text-center">
                        <div class="alert alert-danger">
                            <i class="fas fa-times-circle fa-2x mb-3"></i>
                            <h6><?php echo htmlspecialchars($error); ?></h6>
                        </div>
                        <a href="dashboard.php" class="btn btn-primary">
                            <i class="fas fa-arrow-left me-1"></i>
                            العودة للوحة التحكم
                        </a>
                    </div>
                </div>
            </div>
        </div>
    <?php else: ?>
        <!-- معلومات الجلسة -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h5 class="mb-1">
                                    <i class="fas fa-video me-2"></i>
                                    <?php echo htmlspecialchars($session['title']); ?>
                                </h5>
                                <small>
                                    <i class="fas fa-book me-1"></i>
                                    الكورس: <?php echo htmlspecialchars($session['course_title']); ?>
                                    <span class="ms-3">
                                        <i class="fas fa-user me-1"></i>
                                        المدرب: <?php echo htmlspecialchars($session['instructor_name']); ?>
                                    </span>
                                </small>
                            </div>
                            <div class="col-md-4 text-end">
                                <span class="badge bg-light text-dark fs-6">
                                    <i class="fas fa-broadcast-tower me-1"></i>
                                    جلسة مباشرة
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if ($session['description']): ?>
                            <p class="mb-3"><?php echo htmlspecialchars($session['description']); ?></p>
                        <?php endif; ?>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <strong>تاريخ الجلسة:</strong>
                                <br>
                                <?php echo date('Y-m-d', strtotime($session['session_date'])); ?>
                            </div>
                            <div class="col-md-4">
                                <strong>وقت البداية:</strong>
                                <br>
                                <?php echo date('H:i', strtotime($session['start_time'])); ?>
                            </div>
                            <div class="col-md-4">
                                <strong>وقت النهاية:</strong>
                                <br>
                                <?php echo $session['end_time'] ? date('H:i', strtotime($session['end_time'])) : 'غير محدد'; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- منطقة الجلسة المباشرة -->
        <div class="row">
            <div class="col-lg-9">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-play-circle me-2"></i>
                            الجلسة المباشرة
                        </h6>
                    </div>
                    <div class="card-body">
                        <!-- منطقة الفيديو -->
                        <div class="ratio ratio-16x9 mb-3">
                            <div class="bg-dark d-flex align-items-center justify-content-center text-white">
                                <div class="text-center">
                                    <i class="fas fa-video fa-3x mb-3"></i>
                                    <h5>منطقة الفيديو</h5>
                                    <p class="mb-0">سيتم عرض الجلسة المباشرة هنا</p>
                                    <div class="mt-3">
                                        <?php if ($session['meeting_link']): ?>
                                            <a href="<?php echo htmlspecialchars($session['meeting_link']); ?>" 
                                               target="_blank" class="btn btn-success btn-lg">
                                                <i class="fas fa-external-link-alt me-2"></i>
                                                انضم للجلسة عبر Zoom
                                            </a>
                                        <?php else: ?>
                                            <button class="btn btn-success btn-lg" onclick="startLocalSession()">
                                                <i class="fas fa-play me-2"></i>
                                                بدء الجلسة
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- أدوات التحكم -->
                        <div class="d-flex justify-content-center gap-2">
                            <button class="btn btn-outline-primary" id="micBtn" onclick="toggleMic()">
                                <i class="fas fa-microphone"></i>
                            </button>
                            <button class="btn btn-outline-primary" id="cameraBtn" onclick="toggleCamera()">
                                <i class="fas fa-video"></i>
                            </button>
                            <button class="btn btn-outline-secondary" onclick="toggleChat()">
                                <i class="fas fa-comments"></i>
                            </button>
                            <button class="btn btn-outline-info" onclick="shareScreen()">
                                <i class="fas fa-desktop"></i>
                            </button>
                            <button class="btn btn-danger" onclick="leaveSession()">
                                <i class="fas fa-phone-slash"></i> مغادرة
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- الشريط الجانبي -->
            <div class="col-lg-3">
                <!-- المشاركون -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-users me-2"></i>
                            المشاركون
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="list-group list-group-flush">
                            <div class="list-group-item d-flex align-items-center">
                                <i class="fas fa-crown text-warning me-2"></i>
                                <div>
                                    <strong><?php echo htmlspecialchars($session['instructor_name']); ?></strong>
                                    <br>
                                    <small class="text-muted">المدرب</small>
                                </div>
                            </div>
                            <div class="list-group-item d-flex align-items-center">
                                <i class="fas fa-user text-primary me-2"></i>
                                <div>
                                    <strong><?php echo htmlspecialchars($_SESSION['user_name']); ?></strong>
                                    <br>
                                    <small class="text-muted">أنت</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الدردشة -->
                <div class="card" id="chatPanel" style="display: none;">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-comments me-2"></i>
                            الدردشة
                        </h6>
                    </div>
                    <div class="card-body">
                        <div id="chatMessages" style="height: 200px; overflow-y: auto;" class="mb-3">
                            <div class="text-muted text-center">
                                <small>مرحباً بك في الجلسة!</small>
                            </div>
                        </div>
                        <div class="input-group">
                            <input type="text" class="form-control" id="chatInput" placeholder="اكتب رسالة...">
                            <button class="btn btn-primary" onclick="sendMessage()">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- معلومات إضافية -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            معلومات الجلسة
                        </h6>
                    </div>
                    <div class="card-body">
                        <small class="text-muted">
                            <strong>نصائح للجلسة:</strong>
                            <ul class="mt-2">
                                <li>تأكد من اتصال إنترنت مستقر</li>
                                <li>استخدم سماعات لتجنب الصدى</li>
                                <li>اكتم الميكروفون عند عدم التحدث</li>
                                <li>شارك في الدردشة للأسئلة</li>
                            </ul>
                        </small>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<script>
let micEnabled = false;
let cameraEnabled = false;
let chatVisible = false;

function startLocalSession() {
    alert('سيتم بدء الجلسة المحلية قريباً. حالياً يمكنك استخدام رابط Zoom إذا كان متاحاً.');
}

function toggleMic() {
    micEnabled = !micEnabled;
    const btn = document.getElementById('micBtn');
    if (micEnabled) {
        btn.classList.remove('btn-outline-primary');
        btn.classList.add('btn-primary');
        btn.innerHTML = '<i class="fas fa-microphone"></i>';
    } else {
        btn.classList.remove('btn-primary');
        btn.classList.add('btn-outline-primary');
        btn.innerHTML = '<i class="fas fa-microphone-slash"></i>';
    }
}

function toggleCamera() {
    cameraEnabled = !cameraEnabled;
    const btn = document.getElementById('cameraBtn');
    if (cameraEnabled) {
        btn.classList.remove('btn-outline-primary');
        btn.classList.add('btn-primary');
        btn.innerHTML = '<i class="fas fa-video"></i>';
    } else {
        btn.classList.remove('btn-primary');
        btn.classList.add('btn-outline-primary');
        btn.innerHTML = '<i class="fas fa-video-slash"></i>';
    }
}

function toggleChat() {
    chatVisible = !chatVisible;
    const panel = document.getElementById('chatPanel');
    panel.style.display = chatVisible ? 'block' : 'none';
}

function shareScreen() {
    alert('مشاركة الشاشة ستكون متاحة قريباً');
}

function leaveSession() {
    if (confirm('هل أنت متأكد من مغادرة الجلسة؟')) {
        window.location.href = 'course-view.php?id=<?php echo $session['course_id'] ?? ''; ?>';
    }
}

function sendMessage() {
    const input = document.getElementById('chatInput');
    const messages = document.getElementById('chatMessages');
    
    if (input.value.trim()) {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'mb-2';
        messageDiv.innerHTML = `
            <strong><?php echo htmlspecialchars($_SESSION['user_name']); ?>:</strong>
            <br>
            <small>${input.value}</small>
        `;
        messages.appendChild(messageDiv);
        messages.scrollTop = messages.scrollHeight;
        input.value = '';
    }
}

// إرسال الرسالة بالضغط على Enter
document.getElementById('chatInput')?.addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        sendMessage();
    }
});
</script>

<?php require_once '../includes/footer.php'; ?>
