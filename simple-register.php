<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = sanitize($_POST['name']);
    $email = filter_var($_POST['email'], FILTER_SANITIZE_EMAIL);
    $phone = sanitize($_POST['phone']);
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];
    
    // التحقق من البيانات
    if (empty($name) || empty($email) || empty($phone) || empty($password)) {
        $error = 'جميع الحقول مطلوبة';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = 'البريد الإلكتروني غير صالح';
    } elseif ($password !== $confirm_password) {
        $error = 'كلمة المرور غير متطابقة';
    } elseif (strlen($password) < 8) {
        $error = 'يجب أن تكون كلمة المرور 8 أحرف على الأقل';
    } elseif (!preg_match('/^[0-9]{10}$/', $phone)) {
        $error = 'رقم الهاتف غير صالح (10 أرقام)';
    } else {
        try {
            // فحص وجود البريد الإلكتروني
            $stmt = $conn->prepare("SELECT id FROM users WHERE email = ?");
            $stmt->execute([$email]);
            
            if ($stmt->rowCount() > 0) {
                $error = 'البريد الإلكتروني مسجل مسبقاً';
            } else {
                // إنشاء المستخدم
                $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
                $username = explode('@', $email)[0]; // إنشاء اسم المستخدم من البريد الإلكتروني

                $stmt = $conn->prepare("INSERT INTO users (name, email, phone, username, password, role, status, created_at) VALUES (?, ?, ?, ?, ?, 'student', 'pending', NOW())");
                $result = $stmt->execute([$name, $email, $phone, $username, $hashedPassword]);
                
                if ($result) {
                    $user_id = $conn->lastInsertId();
                    
                    // إنشاء طلب انضمام
                    try {
                        $stmt = $conn->prepare("INSERT INTO join_requests (name, email, phone, status, created_at) VALUES (?, ?, ?, 'pending', NOW())");
                        $stmt->execute([$name, $email, $phone]);
                    } catch (Exception $e) {
                        // لا نوقف العملية إذا فشل طلب الانضمام
                    }
                    
                    $success = 'تم التسجيل بنجاح! يرجى انتظار موافقة المدير.';
                } else {
                    $error = 'فشل في إنشاء الحساب';
                }
            }
        } catch (Exception $e) {
            $error = 'حدث خطأ: ' . $e->getMessage();
        }
    }
}
?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل حساب جديد - نسخة مبسطة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Cairo', sans-serif;
        }
        .register-container {
            max-width: 500px;
            margin: 50px auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="register-container">
            <h2 class="text-center mb-4">إنشاء حساب جديد</h2>
            
            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo htmlspecialchars($success); ?>
                </div>
            <?php endif; ?>
            
            <form method="POST" action="">
                <div class="mb-3">
                    <label for="name" class="form-label">الاسم الكامل</label>
                    <input type="text" class="form-control" id="name" name="name" 
                           value="<?php echo isset($_POST['name']) ? htmlspecialchars($_POST['name']) : ''; ?>" 
                           required>
                </div>
                
                <div class="mb-3">
                    <label for="email" class="form-label">البريد الإلكتروني</label>
                    <input type="email" class="form-control" id="email" name="email" 
                           value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>" 
                           required>
                </div>
                
                <div class="mb-3">
                    <label for="phone" class="form-label">رقم الهاتف (10 أرقام)</label>
                    <input type="tel" class="form-control" id="phone" name="phone" 
                           value="<?php echo isset($_POST['phone']) ? htmlspecialchars($_POST['phone']) : ''; ?>" 
                           pattern="[0-9]{10}" placeholder="0501234567" required>
                </div>
                
                <div class="mb-3">
                    <label for="password" class="form-label">كلمة المرور (8 أحرف على الأقل)</label>
                    <input type="password" class="form-control" id="password" name="password" 
                           minlength="8" required>
                </div>
                
                <div class="mb-3">
                    <label for="confirm_password" class="form-label">تأكيد كلمة المرور</label>
                    <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                           required>
                </div>
                
                <div class="mb-3 form-check">
                    <input type="checkbox" class="form-check-input" id="terms" required>
                    <label class="form-check-label" for="terms">
                        أوافق على شروط الاستخدام وسياسة الخصوصية
                    </label>
                </div>
                
                <button type="submit" class="btn btn-primary w-100">إنشاء الحساب</button>
            </form>
            
            <div class="text-center mt-3">
                <p>لديك حساب بالفعل؟ <a href="login.php">تسجيل الدخول</a></p>
                <p><a href="index.php">العودة للرئيسية</a></p>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // التحقق من تطابق كلمة المرور
        document.getElementById('confirm_password').addEventListener('input', function() {
            const password = document.getElementById('password').value;
            const confirm = this.value;
            
            if (password !== confirm) {
                this.setCustomValidity('كلمة المرور غير متطابقة');
            } else {
                this.setCustomValidity('');
            }
        });
    </script>
</body>
</html>
