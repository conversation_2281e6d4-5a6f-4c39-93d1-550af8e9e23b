<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات المدرب
if (!isLoggedIn() || !isInstructor()) {
    header('Location: ../login.php');
    exit;
}

$pageTitle = 'الموارد التعليمية';
$breadcrumbs = [
    ['title' => 'الموارد التعليمية']
];

include 'includes/header.php';
?>

<!-- رأس الصفحة -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="h3 mb-2">
            <i class="fas fa-book-open text-success me-2"></i>
            الموارد التعليمية
        </h2>
        <p class="text-muted mb-0">مجموعة شاملة من الأدوات والموارد لتحسين التدريس</p>
    </div>
    <div class="d-flex gap-2">
        <button class="btn btn-primary" onclick="requestResource()">
            <i class="fas fa-plus me-1"></i>طلب مورد جديد
        </button>
        <button class="btn btn-outline-info" onclick="suggestResource()">
            <i class="fas fa-lightbulb me-1"></i>اقتراح مورد
        </button>
    </div>
</div>

<!-- فئات الموارد -->
<div class="row g-4 mb-4">
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm h-100 resource-category" data-category="templates">
            <div class="card-body text-center">
                <div class="bg-primary bg-opacity-10 rounded-3 p-3 mb-3 d-inline-block">
                    <i class="fas fa-file-alt text-primary fs-1"></i>
                </div>
                <h5 class="card-title">قوالب الدروس</h5>
                <p class="card-text text-muted">قوالب جاهزة لتصميم الدروس والمحاضرات</p>
                <span class="badge bg-primary">25 قالب</span>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm h-100 resource-category" data-category="tools">
            <div class="card-body text-center">
                <div class="bg-success bg-opacity-10 rounded-3 p-3 mb-3 d-inline-block">
                    <i class="fas fa-tools text-success fs-1"></i>
                </div>
                <h5 class="card-title">أدوات التدريس</h5>
                <p class="card-text text-muted">أدوات وتطبيقات مفيدة للتدريس الإلكتروني</p>
                <span class="badge bg-success">18 أداة</span>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm h-100 resource-category" data-category="guides">
            <div class="card-body text-center">
                <div class="bg-warning bg-opacity-10 rounded-3 p-3 mb-3 d-inline-block">
                    <i class="fas fa-book text-warning fs-1"></i>
                </div>
                <h5 class="card-title">أدلة التدريس</h5>
                <p class="card-text text-muted">أدلة شاملة لتحسين طرق التدريس</p>
                <span class="badge bg-warning">12 دليل</span>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm h-100 resource-category" data-category="media">
            <div class="card-body text-center">
                <div class="bg-info bg-opacity-10 rounded-3 p-3 mb-3 d-inline-block">
                    <i class="fas fa-photo-video text-info fs-1"></i>
                </div>
                <h5 class="card-title">مكتبة الوسائط</h5>
                <p class="card-text text-muted">صور وفيديوهات وملفات صوتية تعليمية</p>
                <span class="badge bg-info">150+ ملف</span>
            </div>
        </div>
    </div>
</div>

<!-- محتوى الموارد -->
<div id="resourcesContent">
    <!-- قوالب الدروس -->
    <div class="resource-section" id="templates" style="display: none;">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-bottom">
                <h5 class="mb-0">
                    <i class="fas fa-file-alt me-2"></i>
                    قوالب الدروس
                </h5>
            </div>
            <div class="card-body">
                <div class="row g-4">
                    <div class="col-lg-4 col-md-6">
                        <div class="card border">
                            <div class="card-body">
                                <div class="d-flex align-items-center mb-3">
                                    <i class="fas fa-file-powerpoint text-warning fs-3 me-3"></i>
                                    <div>
                                        <h6 class="mb-1">قالب عرض تقديمي</h6>
                                        <small class="text-muted">PowerPoint</small>
                                    </div>
                                </div>
                                <p class="text-muted small">قالب احترافي للعروض التقديمية مع تصميم عصري</p>
                                <div class="d-flex gap-2">
                                    <button class="btn btn-sm btn-primary flex-fill">تحميل</button>
                                    <button class="btn btn-sm btn-outline-secondary">معاينة</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-4 col-md-6">
                        <div class="card border">
                            <div class="card-body">
                                <div class="d-flex align-items-center mb-3">
                                    <i class="fas fa-file-word text-primary fs-3 me-3"></i>
                                    <div>
                                        <h6 class="mb-1">قالب خطة الدرس</h6>
                                        <small class="text-muted">Word</small>
                                    </div>
                                </div>
                                <p class="text-muted small">قالب منظم لكتابة خطط الدروس التفصيلية</p>
                                <div class="d-flex gap-2">
                                    <button class="btn btn-sm btn-primary flex-fill">تحميل</button>
                                    <button class="btn btn-sm btn-outline-secondary">معاينة</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-4 col-md-6">
                        <div class="card border">
                            <div class="card-body">
                                <div class="d-flex align-items-center mb-3">
                                    <i class="fas fa-file-excel text-success fs-3 me-3"></i>
                                    <div>
                                        <h6 class="mb-1">قالب تقييم الطلاب</h6>
                                        <small class="text-muted">Excel</small>
                                    </div>
                                </div>
                                <p class="text-muted small">جدول منظم لتسجيل درجات وتقييمات الطلاب</p>
                                <div class="d-flex gap-2">
                                    <button class="btn btn-sm btn-primary flex-fill">تحميل</button>
                                    <button class="btn btn-sm btn-outline-secondary">معاينة</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- أدوات التدريس -->
    <div class="resource-section" id="tools" style="display: none;">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-bottom">
                <h5 class="mb-0">
                    <i class="fas fa-tools me-2"></i>
                    أدوات التدريس
                </h5>
            </div>
            <div class="card-body">
                <div class="row g-4">
                    <div class="col-lg-6 col-md-12">
                        <div class="card border">
                            <div class="card-body">
                                <div class="d-flex align-items-center mb-3">
                                    <i class="fas fa-chalkboard-teacher text-primary fs-3 me-3"></i>
                                    <div>
                                        <h6 class="mb-1">Zoom للتدريس</h6>
                                        <small class="text-muted">منصة الفيديو</small>
                                    </div>
                                </div>
                                <p class="text-muted small">منصة متقدمة للفصول الافتراضية والتدريس المباشر</p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="badge bg-success">مجاني</span>
                                    <a href="https://zoom.us" target="_blank" class="btn btn-sm btn-outline-primary">زيارة الموقع</a>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-6 col-md-12">
                        <div class="card border">
                            <div class="card-body">
                                <div class="d-flex align-items-center mb-3">
                                    <i class="fas fa-poll text-warning fs-3 me-3"></i>
                                    <div>
                                        <h6 class="mb-1">Kahoot للاختبارات</h6>
                                        <small class="text-muted">اختبارات تفاعلية</small>
                                    </div>
                                </div>
                                <p class="text-muted small">إنشاء اختبارات تفاعلية ممتعة للطلاب</p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="badge bg-warning">مدفوع</span>
                                    <a href="https://kahoot.com" target="_blank" class="btn btn-sm btn-outline-primary">زيارة الموقع</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- أدلة التدريس -->
    <div class="resource-section" id="guides" style="display: none;">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-bottom">
                <h5 class="mb-0">
                    <i class="fas fa-book me-2"></i>
                    أدلة التدريس
                </h5>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-file-pdf text-danger fs-4 me-3"></i>
                            <div>
                                <h6 class="mb-1">دليل التدريس الإلكتروني الفعال</h6>
                                <small class="text-muted">استراتيجيات وطرق التدريس الحديثة</small>
                            </div>
                        </div>
                        <div class="d-flex gap-2">
                            <span class="badge bg-primary">PDF</span>
                            <button class="btn btn-sm btn-outline-primary">تحميل</button>
                        </div>
                    </div>
                    
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-file-pdf text-danger fs-4 me-3"></i>
                            <div>
                                <h6 class="mb-1">إدارة الفصل الافتراضي</h6>
                                <small class="text-muted">نصائح لإدارة الطلاب في البيئة الرقمية</small>
                            </div>
                        </div>
                        <div class="d-flex gap-2">
                            <span class="badge bg-primary">PDF</span>
                            <button class="btn btn-sm btn-outline-primary">تحميل</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- مكتبة الوسائط -->
    <div class="resource-section" id="media" style="display: none;">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-bottom">
                <h5 class="mb-0">
                    <i class="fas fa-photo-video me-2"></i>
                    مكتبة الوسائط
                </h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-lg-3 col-md-4 col-sm-6">
                        <div class="card border">
                            <img src="https://via.placeholder.com/200x150/007bff/ffffff?text=صورة+تعليمية" class="card-img-top" alt="صورة تعليمية">
                            <div class="card-body p-2">
                                <small class="text-muted">صورة تعليمية</small>
                                <div class="d-flex gap-1 mt-2">
                                    <button class="btn btn-sm btn-outline-primary flex-fill">تحميل</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-4 col-sm-6">
                        <div class="card border">
                            <div class="card-img-top bg-dark d-flex align-items-center justify-content-center" style="height: 150px;">
                                <i class="fas fa-play text-white fs-1"></i>
                            </div>
                            <div class="card-body p-2">
                                <small class="text-muted">فيديو تعليمي</small>
                                <div class="d-flex gap-1 mt-2">
                                    <button class="btn btn-sm btn-outline-primary flex-fill">مشاهدة</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// عرض فئة الموارد
function showResourceCategory(category) {
    // إخفاء جميع الأقسام
    const sections = document.querySelectorAll('.resource-section');
    sections.forEach(section => {
        section.style.display = 'none';
    });

    // إزالة التحديد من جميع البطاقات
    const cards = document.querySelectorAll('.resource-category');
    cards.forEach(card => {
        card.classList.remove('selected');
    });

    // عرض القسم المحدد
    const targetSection = document.getElementById(category);
    if (targetSection) {
        targetSection.style.display = 'block';
    }

    // تحديد البطاقة المختارة
    const selectedCard = document.querySelector(`[data-category="${category}"]`);
    if (selectedCard) {
        selectedCard.classList.add('selected');
    }
}

// طلب مورد جديد
function requestResource() {
    const modal = `
        <div class="modal fade" id="requestResourceModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">طلب مورد جديد</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form>
                            <div class="mb-3">
                                <label for="resourceType" class="form-label">نوع المورد</label>
                                <select class="form-select" id="resourceType" required>
                                    <option value="">-- اختر نوع المورد --</option>
                                    <option value="template">قالب</option>
                                    <option value="tool">أداة</option>
                                    <option value="guide">دليل</option>
                                    <option value="media">وسائط</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="resourceTitle" class="form-label">عنوان المورد</label>
                                <input type="text" class="form-control" id="resourceTitle" required>
                            </div>
                            <div class="mb-3">
                                <label for="resourceDescription" class="form-label">وصف المورد</label>
                                <textarea class="form-control" id="resourceDescription" rows="3" required></textarea>
                            </div>
                            <div class="mb-3">
                                <label for="resourcePriority" class="form-label">الأولوية</label>
                                <select class="form-select" id="resourcePriority">
                                    <option value="low">منخفضة</option>
                                    <option value="medium" selected>متوسطة</option>
                                    <option value="high">عالية</option>
                                </select>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-primary" onclick="submitResourceRequest()">إرسال الطلب</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modal);
    new bootstrap.Modal(document.getElementById('requestResourceModal')).show();
}

// اقتراح مورد
function suggestResource() {
    const modal = `
        <div class="modal fade" id="suggestResourceModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">اقتراح مورد</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form>
                            <div class="mb-3">
                                <label for="suggestionTitle" class="form-label">عنوان الاقتراح</label>
                                <input type="text" class="form-control" id="suggestionTitle" required>
                            </div>
                            <div class="mb-3">
                                <label for="suggestionDescription" class="form-label">وصف الاقتراح</label>
                                <textarea class="form-control" id="suggestionDescription" rows="4" required></textarea>
                            </div>
                            <div class="mb-3">
                                <label for="suggestionUrl" class="form-label">رابط المورد (إن وجد)</label>
                                <input type="url" class="form-control" id="suggestionUrl">
                            </div>
                            <div class="mb-3">
                                <label for="suggestionBenefit" class="form-label">الفائدة المتوقعة</label>
                                <textarea class="form-control" id="suggestionBenefit" rows="2"></textarea>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-success" onclick="submitSuggestion()">إرسال الاقتراح</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modal);
    new bootstrap.Modal(document.getElementById('suggestResourceModal')).show();
}

// إرسال طلب المورد
function submitResourceRequest() {
    const type = document.getElementById('resourceType').value;
    const title = document.getElementById('resourceTitle').value;
    const description = document.getElementById('resourceDescription').value;
    const priority = document.getElementById('resourcePriority').value;

    if (!type || !title || !description) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
    }

    // محاكاة إرسال الطلب
    alert('تم إرسال طلبك بنجاح! سيتم مراجعته والرد عليك قريباً.');
    bootstrap.Modal.getInstance(document.getElementById('requestResourceModal')).hide();
}

// إرسال الاقتراح
function submitSuggestion() {
    const title = document.getElementById('suggestionTitle').value;
    const description = document.getElementById('suggestionDescription').value;

    if (!title || !description) {
        alert('يرجى ملء الحقول المطلوبة');
        return;
    }

    // محاكاة إرسال الاقتراح
    alert('شكراً لك على اقتراحك! سيتم دراسته من قبل فريق المحتوى.');
    bootstrap.Modal.getInstance(document.getElementById('suggestResourceModal')).hide();
}

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // إضافة أحداث النقر على بطاقات الفئات
    const categoryCards = document.querySelectorAll('.resource-category');
    categoryCards.forEach(card => {
        card.addEventListener('click', function() {
            const category = this.dataset.category;
            showResourceCategory(category);
        });

        // تأثيرات بصرية
        card.addEventListener('mouseenter', function() {
            if (!this.classList.contains('selected')) {
                this.style.transform = 'translateY(-5px)';
                this.style.boxShadow = '0 8px 25px rgba(0,0,0,0.15)';
            }
        });

        card.addEventListener('mouseleave', function() {
            if (!this.classList.contains('selected')) {
                this.style.transform = '';
                this.style.boxShadow = '';
            }
        });
    });

    // عرض القوالب افتراضياً
    showResourceCategory('templates');

    // تحسين الأزرار
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(button => {
        button.addEventListener('click', function() {
            if (this.textContent.includes('تحميل')) {
                this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري التحميل...';
                this.disabled = true;

                setTimeout(() => {
                    this.innerHTML = '<i class="fas fa-check me-1"></i>تم التحميل';
                    this.classList.remove('btn-primary', 'btn-outline-primary');
                    this.classList.add('btn-success');

                    setTimeout(() => {
                        this.innerHTML = 'تحميل';
                        this.disabled = false;
                        this.classList.remove('btn-success');
                        this.classList.add('btn-outline-primary');
                    }, 2000);
                }, 1500);
            }
        });
    });
});

// دوال مساعدة
function searchResources() {
    const searchTerm = prompt('ابحث في الموارد:');
    if (searchTerm) {
        alert(`البحث عن: ${searchTerm}\nهذه الميزة قيد التطوير.`);
    }
}

function filterResources(filter) {
    alert(`تطبيق فلتر: ${filter}\nهذه الميزة قيد التطوير.`);
}

function shareResource(resourceId) {
    const url = `${window.location.origin}/resources/${resourceId}`;
    if (navigator.share) {
        navigator.share({
            title: 'مورد تعليمي مفيد',
            url: url
        });
    } else {
        navigator.clipboard.writeText(url).then(() => {
            alert('تم نسخ رابط المورد إلى الحافظة');
        });
    }
}

function rateResource(resourceId, rating) {
    alert(`تم تقييم المورد بـ ${rating} نجوم`);
}
</script>

<style>
.resource-category {
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.resource-category:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.resource-category.selected {
    border-color: var(--bs-primary);
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0,123,255,0.2);
}

.resource-category .card-body {
    padding: 2rem 1.5rem;
}

.resource-section {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.card {
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.list-group-item {
    transition: background-color 0.3s ease;
}

.list-group-item:hover {
    background-color: rgba(0,123,255,0.05);
}

.badge {
    font-size: 0.75rem;
    padding: 0.5rem 0.75rem;
}

.btn {
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
}

.fs-1 {
    font-size: 2.5rem !important;
}

.bg-opacity-10 {
    background-color: rgba(var(--bs-primary-rgb), 0.1) !important;
}

@media (max-width: 768px) {
    .resource-category .card-body {
        padding: 1.5rem 1rem;
    }

    .fs-1 {
        font-size: 2rem !important;
    }

    .d-flex.gap-2 {
        flex-direction: column;
    }

    .d-flex.gap-2 .btn {
        margin-bottom: 0.5rem;
    }
}

/* تحسين النوافذ المنبثقة */
.modal-content {
    border-radius: 0.5rem;
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.modal-header {
    border-bottom: 1px solid rgba(0,0,0,0.1);
    padding: 1.5rem;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    border-top: 1px solid rgba(0,0,0,0.1);
    padding: 1.5rem;
}

/* تحسين النماذج */
.form-control:focus,
.form-select:focus {
    border-color: var(--bs-primary);
    box-shadow: 0 0 0 0.2rem rgba(var(--bs-primary-rgb), 0.25);
}

.form-label {
    font-weight: 500;
    color: var(--bs-gray-700);
    margin-bottom: 0.5rem;
}

/* تحسين البطاقات */
.card-img-top {
    transition: transform 0.3s ease;
}

.card:hover .card-img-top {
    transform: scale(1.05);
}

/* تحسين الأيقونات */
.fas {
    transition: color 0.3s ease;
}

.card:hover .fas {
    color: var(--bs-primary) !important;
}
</style>

<?php include 'includes/footer.php'; ?>

<!-- الموارد الشائعة -->
<div class="card border-0 shadow-sm mt-4">
    <div class="card-header bg-white border-bottom">
        <h5 class="mb-0">
            <i class="fas fa-star me-2"></i>
            الموارد الأكثر استخداماً
        </h5>
    </div>
    <div class="card-body">
        <div class="row g-3">
            <div class="col-lg-4 col-md-6">
                <div class="d-flex align-items-center p-3 border rounded">
                    <i class="fas fa-download text-primary fs-4 me-3"></i>
                    <div class="flex-grow-1">
                        <h6 class="mb-1">قالب شهادة الإنجاز</h6>
                        <small class="text-muted">تم التحميل 1,234 مرة</small>
                    </div>
                    <button class="btn btn-sm btn-outline-primary">تحميل</button>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6">
                <div class="d-flex align-items-center p-3 border rounded">
                    <i class="fas fa-chart-bar text-success fs-4 me-3"></i>
                    <div class="flex-grow-1">
                        <h6 class="mb-1">أداة تحليل الأداء</h6>
                        <small class="text-muted">تم الاستخدام 856 مرة</small>
                    </div>
                    <button class="btn btn-sm btn-outline-primary">استخدام</button>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6">
                <div class="d-flex align-items-center p-3 border rounded">
                    <i class="fas fa-video text-danger fs-4 me-3"></i>
                    <div class="flex-grow-1">
                        <h6 class="mb-1">دليل تسجيل الفيديو</h6>
                        <small class="text-muted">تم المشاهدة 2,145 مرة</small>
                    </div>
                    <button class="btn btn-sm btn-outline-primary">مشاهدة</button>
                </div>
            </div>
        </div>
    </div>
</div>
