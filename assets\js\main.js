/**
 * نظام التعلم عن بعد - الوظائف الرئيسية
 * Learning Management System - Main JavaScript Functions
 */

// ===== المتغيرات العامة =====
const LMS = {
    config: {
        animationDuration: 300,
        toastDuration: 5000,
        tablePageLength: 10
    },
    
    // ===== التهيئة الأولية =====
    init: function() {
        this.initializeComponents();
        this.bindEvents();
        this.setupAnimations();
        this.initializeDataTables();
        this.setupFormValidation();
        console.log('نظام التعلم عن بعد تم تحميله بنجاح');
    },
    
    // ===== تهيئة المكونات =====
    initializeComponents: function() {
        // تهيئة Bootstrap tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
        
        // تهيئة Bootstrap popovers
        const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
        popoverTriggerList.map(function (popoverTriggerEl) {
            return new bootstrap.Popover(popoverTriggerEl);
        });
        
        // إضافة تأثيرات التحميل
        this.addLoadingEffects();
    },
    
    // ===== ربط الأحداث =====
    bindEvents: function() {
        // أحداث النقر على الأزرار
        document.addEventListener('click', this.handleButtonClicks.bind(this));
        
        // أحداث النماذج
        document.addEventListener('submit', this.handleFormSubmit.bind(this));
        
        // أحداث تغيير كلمة المرور
        this.setupPasswordToggle();
        
        // أحداث البحث المباشر
        this.setupLiveSearch();
        
        // أحداث التحديث التلقائي
        this.setupAutoRefresh();
    },
    
    // ===== معالجة النقر على الأزرار =====
    handleButtonClicks: function(e) {
        const target = e.target.closest('button, .btn');
        if (!target) return;
        
        // إضافة تأثير الموجة
        this.createRippleEffect(target, e);
        
        // معالجة أزرار الحذف
        if (target.classList.contains('btn-delete')) {
            e.preventDefault();
            this.confirmDelete(target);
        }
        
        // معالجة أزرار التحديث
        if (target.classList.contains('btn-refresh')) {
            e.preventDefault();
            this.refreshData(target);
        }
    },
    
    // ===== تأثير الموجة على الأزرار =====
    createRippleEffect: function(button, event) {
        const ripple = document.createElement('span');
        const rect = button.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = event.clientX - rect.left - size / 2;
        const y = event.clientY - rect.top - size / 2;
        
        ripple.style.width = ripple.style.height = size + 'px';
        ripple.style.left = x + 'px';
        ripple.style.top = y + 'px';
        ripple.classList.add('ripple');
        
        button.appendChild(ripple);
        
        setTimeout(() => {
            ripple.remove();
        }, 600);
    },
    
    // ===== تأكيد الحذف =====
    confirmDelete: function(button) {
        const itemName = button.dataset.itemName || 'هذا العنصر';
        const deleteUrl = button.dataset.deleteUrl;
        
        Swal.fire({
            title: 'هل أنت متأكد؟',
            text: `سيتم حذف ${itemName} نهائياً`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'نعم، احذف',
            cancelButtonText: 'إلغاء',
            reverseButtons: true
        }).then((result) => {
            if (result.isConfirmed) {
                this.performDelete(deleteUrl, itemName);
            }
        });
    },
    
    // ===== تنفيذ الحذف =====
    performDelete: function(url, itemName) {
        fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.showToast('تم الحذف بنجاح', 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                this.showToast(data.message || 'حدث خطأ أثناء الحذف', 'error');
            }
        })
        .catch(error => {
            this.showToast('حدث خطأ في الاتصال', 'error');
        });
    },
    
    // ===== إظهار الرسائل =====
    showToast: function(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `toast-notification toast-${type}`;
        toast.innerHTML = `
            <div class="toast-content">
                <i class="fas fa-${this.getToastIcon(type)}"></i>
                <span>${message}</span>
            </div>
            <button class="toast-close">&times;</button>
        `;
        
        document.body.appendChild(toast);
        
        // إظهار التوست
        setTimeout(() => toast.classList.add('show'), 100);
        
        // إخفاء التوست تلقائياً
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => toast.remove(), 300);
        }, this.config.toastDuration);
        
        // إغلاق عند النقر
        toast.querySelector('.toast-close').addEventListener('click', () => {
            toast.classList.remove('show');
            setTimeout(() => toast.remove(), 300);
        });
    },
    
    // ===== أيقونات التوست =====
    getToastIcon: function(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    },
    
    // ===== تبديل إظهار كلمة المرور =====
    setupPasswordToggle: function() {
        document.querySelectorAll('.password-toggle').forEach(toggle => {
            toggle.addEventListener('click', function() {
                const input = this.previousElementSibling;
                const icon = this.querySelector('i');
                
                if (input.type === 'password') {
                    input.type = 'text';
                    icon.classList.remove('fa-eye');
                    icon.classList.add('fa-eye-slash');
                } else {
                    input.type = 'password';
                    icon.classList.remove('fa-eye-slash');
                    icon.classList.add('fa-eye');
                }
            });
        });
    },
    
    // ===== البحث المباشر =====
    setupLiveSearch: function() {
        const searchInputs = document.querySelectorAll('.live-search');
        searchInputs.forEach(input => {
            let timeout;
            input.addEventListener('input', function() {
                clearTimeout(timeout);
                timeout = setTimeout(() => {
                    LMS.performLiveSearch(this);
                }, 300);
            });
        });
    },
    
    // ===== تنفيذ البحث المباشر =====
    performLiveSearch: function(input) {
        const query = input.value.trim();
        const targetTable = input.dataset.target;
        
        if (targetTable) {
            const table = document.querySelector(targetTable);
            const rows = table.querySelectorAll('tbody tr');
            
            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                const match = text.includes(query.toLowerCase());
                row.style.display = match ? '' : 'none';
            });
        }
    },
    
    // ===== تهيئة DataTables =====
    initializeDataTables: function() {
        if (typeof DataTable !== 'undefined') {
            document.querySelectorAll('.data-table').forEach(table => {
                new DataTable(table, {
                    pageLength: this.config.tablePageLength,
                    language: {
                        url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/ar.json'
                    },
                    responsive: true,
                    order: [[0, 'desc']],
                    columnDefs: [
                        { orderable: false, targets: 'no-sort' }
                    ]
                });
            });
        }
    },
    
    // ===== تهيئة الرسوم المتحركة =====
    setupAnimations: function() {
        // تحريك العناصر عند الظهور
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                }
            });
        }, observerOptions);
        
        document.querySelectorAll('.animate-on-scroll').forEach(el => {
            observer.observe(el);
        });
    },
    
    // ===== التحقق من صحة النماذج =====
    setupFormValidation: function() {
        document.querySelectorAll('form').forEach(form => {
            form.addEventListener('submit', function(e) {
                if (!form.checkValidity()) {
                    e.preventDefault();
                    e.stopPropagation();
                }
                form.classList.add('was-validated');
            });
        });
    },
    
    // ===== معالجة إرسال النماذج =====
    handleFormSubmit: function(e) {
        const form = e.target;
        if (form.classList.contains('ajax-form')) {
            e.preventDefault();
            this.submitFormAjax(form);
        }
    },
    
    // ===== إرسال النماذج عبر AJAX =====
    submitFormAjax: function(form) {
        const formData = new FormData(form);
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        
        // إظهار حالة التحميل
        submitBtn.innerHTML = '<span class="loading-spinner"></span> جاري الإرسال...';
        submitBtn.disabled = true;
        
        fetch(form.action, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.showToast(data.message || 'تم الحفظ بنجاح', 'success');
                if (data.redirect) {
                    setTimeout(() => window.location.href = data.redirect, 1000);
                }
            } else {
                this.showToast(data.message || 'حدث خطأ', 'error');
            }
        })
        .catch(error => {
            this.showToast('حدث خطأ في الاتصال', 'error');
        })
        .finally(() => {
            // إعادة تعيين الزر
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        });
    },
    
    // ===== التحديث التلقائي =====
    setupAutoRefresh: function() {
        const autoRefreshElements = document.querySelectorAll('[data-auto-refresh]');
        autoRefreshElements.forEach(element => {
            const interval = parseInt(element.dataset.autoRefresh) * 1000;
            setInterval(() => {
                this.refreshElement(element);
            }, interval);
        });
    },
    
    // ===== تحديث العنصر =====
    refreshElement: function(element) {
        const url = element.dataset.refreshUrl;
        if (url) {
            fetch(url)
            .then(response => response.text())
            .then(html => {
                element.innerHTML = html;
                this.initializeComponents();
            })
            .catch(error => {
                console.error('خطأ في التحديث:', error);
            });
        }
    },
    
    // ===== إضافة تأثيرات التحميل =====
    addLoadingEffects: function() {
        // إضافة تأثير التحميل للصفحة
        window.addEventListener('load', () => {
            document.body.classList.add('loaded');
        });
        
        // إضافة تأثير التحميل للروابط
        document.querySelectorAll('a[href]').forEach(link => {
            link.addEventListener('click', function(e) {
                if (this.href && !this.href.includes('#') && !this.target) {
                    document.body.classList.add('loading');
                }
            });
        });
    }
};

// ===== تهيئة النظام عند تحميل الصفحة =====
document.addEventListener('DOMContentLoaded', function() {
    LMS.init();
});

// ===== وظائف مساعدة عامة =====
window.LMS = LMS;
