<?php
require_once '../config/database.php';

echo "<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>إصلاح شامل للنظام</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css' rel='stylesheet'>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f8f9fa; }
        .container { max-width: 900px; margin: 30px auto; }
        .log-item { padding: 8px; margin: 3px 0; border-radius: 5px; font-size: 14px; }
        .log-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .log-error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .log-info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .log-warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        #logContainer { max-height: 400px; overflow-y: auto; }
    </style>
</head>
<body>
<div class='container'>
    <div class='card'>
        <div class='card-header bg-primary text-white'>
            <h4 class='mb-0'><i class='fas fa-tools me-2'></i>إصلاح شامل للنظام</h4>
        </div>
        <div class='card-body'>
            <div id='logContainer'>";

function logMessage($message, $type = 'info') {
    $icon = $type === 'success' ? 'check-circle' : ($type === 'error' ? 'exclamation-circle' : ($type === 'warning' ? 'exclamation-triangle' : 'info-circle'));
    echo "<div class='log-item log-$type'><i class='fas fa-$icon me-2'></i>$message</div>";
    flush();
    ob_flush();
}

try {
    logMessage('بدء الإصلاح الشامل للنظام...', 'info');
    
    // 1. إصلاح جدول المستخدمين
    logMessage('=== إصلاح جدول المستخدمين ===', 'info');
    
    // التحقق من الأعمدة الموجودة
    $stmt = $conn->query("DESCRIBE users");
    $existing_columns = [];
    while ($row = $stmt->fetch()) {
        $existing_columns[] = $row['Field'];
    }
    
    // إضافة الأعمدة المفقودة
    $user_columns = [
        'email_verified' => 'BOOLEAN DEFAULT TRUE',
        'bio' => 'TEXT DEFAULT NULL',
        'profile_image' => 'VARCHAR(255) DEFAULT NULL',
        'phone' => 'VARCHAR(20) DEFAULT NULL',
        'last_login' => 'TIMESTAMP NULL DEFAULT NULL',
        'status' => 'ENUM("active", "inactive", "pending", "suspended") DEFAULT "active"'
    ];
    
    foreach ($user_columns as $column => $definition) {
        if (!in_array($column, $existing_columns)) {
            try {
                $conn->exec("ALTER TABLE users ADD COLUMN $column $definition");
                logMessage("✓ تم إضافة العمود: $column", 'success');
            } catch (PDOException $e) {
                logMessage("تخطي العمود $column: " . $e->getMessage(), 'warning');
            }
        } else {
            logMessage("• العمود $column موجود", 'info');
        }
    }
    
    // 2. إصلاح جدول الكورسات
    logMessage('=== إصلاح جدول الكورسات ===', 'info');
    
    $stmt = $conn->query("DESCRIBE courses");
    $course_columns = [];
    while ($row = $stmt->fetch()) {
        $course_columns[] = $row['Field'];
    }
    
    $course_required_columns = [
        'course_type' => 'ENUM("free", "paid") DEFAULT "free"',
        'price' => 'DECIMAL(10,2) DEFAULT 0',
        'status' => 'ENUM("active", "inactive", "pending") DEFAULT "active"'
    ];
    
    foreach ($course_required_columns as $column => $definition) {
        if (!in_array($column, $course_columns)) {
            try {
                $conn->exec("ALTER TABLE courses ADD COLUMN $column $definition");
                logMessage("✓ تم إضافة العمود في courses: $column", 'success');
            } catch (PDOException $e) {
                logMessage("تخطي العمود $column في courses: " . $e->getMessage(), 'warning');
            }
        } else {
            logMessage("• العمود $column موجود في courses", 'info');
        }
    }
    
    // 3. إنشاء الجداول المفقودة
    logMessage('=== إنشاء الجداول المفقودة ===', 'info');
    
    // جدول course_enrollments
    try {
        $conn->exec("
            CREATE TABLE IF NOT EXISTS course_enrollments (
                id INT AUTO_INCREMENT PRIMARY KEY,
                student_id INT NOT NULL,
                course_id INT NOT NULL,
                payment_status ENUM('pending', 'completed', 'failed') DEFAULT 'completed',
                payment_amount DECIMAL(10,2) DEFAULT 0,
                enrolled_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
                UNIQUE KEY unique_enrollment (student_id, course_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        logMessage('✓ جدول course_enrollments جاهز', 'success');
    } catch (PDOException $e) {
        logMessage('تخطي course_enrollments: ' . $e->getMessage(), 'warning');
    }
    
    // جدول join_requests
    try {
        $conn->exec("
            CREATE TABLE IF NOT EXISTS join_requests (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                instructor_id INT NOT NULL,
                status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
                message TEXT DEFAULT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (instructor_id) REFERENCES users(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        logMessage('✓ جدول join_requests جاهز', 'success');
    } catch (PDOException $e) {
        logMessage('تخطي join_requests: ' . $e->getMessage(), 'warning');
    }
    
    // جدول course_categories
    try {
        $conn->exec("
            CREATE TABLE IF NOT EXISTS course_categories (
                id INT AUTO_INCREMENT PRIMARY KEY,
                course_id INT NOT NULL,
                category_id INT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
                FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE,
                UNIQUE KEY unique_course_category (course_id, category_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        logMessage('✓ جدول course_categories جاهز', 'success');
    } catch (PDOException $e) {
        logMessage('تخطي course_categories: ' . $e->getMessage(), 'warning');
    }
    
    // 4. إنشاء بيانات تجريبية
    logMessage('=== إنشاء بيانات تجريبية ===', 'info');
    
    // إنشاء مدربين
    $instructors = [
        ['أحمد محمد', '<EMAIL>', 'خبير في البرمجة'],
        ['فاطمة علي', '<EMAIL>', 'متخصصة في التصميم'],
        ['محمد سالم', '<EMAIL>', 'مدرب إدارة أعمال']
    ];
    
    $instructor_ids = [];
    foreach ($instructors as $instructor) {
        try {
            $password = password_hash('123456', PASSWORD_DEFAULT);
            $stmt = $conn->prepare("
                INSERT IGNORE INTO users (name, email, password, role, status, email_verified, bio) 
                VALUES (?, ?, ?, 'instructor', 'active', TRUE, ?)
            ");
            $stmt->execute([$instructor[0], $instructor[1], $password, $instructor[2]]);
            
            $stmt = $conn->prepare("SELECT id FROM users WHERE email = ?");
            $stmt->execute([$instructor[1]]);
            $id = $stmt->fetchColumn();
            if ($id) {
                $instructor_ids[] = $id;
                logMessage("✓ مدرب: {$instructor[0]}", 'success');
            }
        } catch (PDOException $e) {
            logMessage("تخطي المدرب {$instructor[0]}: موجود بالفعل", 'info');
        }
    }
    
    // إنشاء طلاب
    $students = [
        ['سارة محمد', '<EMAIL>'],
        ['عبدالله أحمد', '<EMAIL>'],
        ['مريم سالم', '<EMAIL>'],
        ['يوسف علي', '<EMAIL>']
    ];
    
    $student_ids = [];
    foreach ($students as $student) {
        try {
            $password = password_hash('123456', PASSWORD_DEFAULT);
            $stmt = $conn->prepare("
                INSERT IGNORE INTO users (name, email, password, role, status, email_verified) 
                VALUES (?, ?, ?, 'student', 'active', TRUE)
            ");
            $stmt->execute([$student[0], $student[1], $password]);
            
            $stmt = $conn->prepare("SELECT id FROM users WHERE email = ?");
            $stmt->execute([$student[1]]);
            $id = $stmt->fetchColumn();
            if ($id) {
                $student_ids[] = $id;
                logMessage("✓ طالب: {$student[0]}", 'success');
            }
        } catch (PDOException $e) {
            logMessage("تخطي الطالب {$student[0]}: موجود بالفعل", 'info');
        }
    }
    
    // إنشاء تصنيفات
    $categories = [
        ['البرمجة', 'programming', 'تعلم لغات البرمجة'],
        ['التصميم', 'design', 'التصميم الجرافيكي'],
        ['إدارة الأعمال', 'business', 'إدارة الأعمال والقيادة']
    ];
    
    $category_ids = [];
    foreach ($categories as $category) {
        try {
            $stmt = $conn->prepare("
                INSERT IGNORE INTO categories (name, slug, description) 
                VALUES (?, ?, ?)
            ");
            $stmt->execute($category);
            
            $stmt = $conn->prepare("SELECT id FROM categories WHERE slug = ?");
            $stmt->execute([$category[1]]);
            $id = $stmt->fetchColumn();
            if ($id) {
                $category_ids[] = $id;
                logMessage("✓ تصنيف: {$category[0]}", 'success');
            }
        } catch (PDOException $e) {
            logMessage("تخطي التصنيف {$category[0]}: موجود بالفعل", 'info');
        }
    }
    
    // إنشاء كورسات
    if (!empty($instructor_ids)) {
        $courses = [
            ['تعلم PHP من الصفر', 'مقدمة شاملة لتعلم PHP', 'paid', 299.99],
            ['أساسيات التصميم', 'تعلم التصميم الجرافيكي', 'paid', 199.99],
            ['إدارة المشاريع', 'كيفية إدارة المشاريع بفعالية', 'free', 0],
            ['التسويق الرقمي', 'استراتيجيات التسويق الحديثة', 'paid', 149.99],
            ['تطوير المواقع', 'تعلم HTML وCSS', 'free', 0]
        ];
        
        $course_ids = [];
        foreach ($courses as $index => $course) {
            try {
                $instructor_id = $instructor_ids[$index % count($instructor_ids)];
                $stmt = $conn->prepare("
                    INSERT INTO courses (title, description, course_type, price, instructor_id, status) 
                    VALUES (?, ?, ?, ?, ?, 'active')
                ");
                $stmt->execute([$course[0], $course[1], $course[2], $course[3], $instructor_id]);
                $course_id = $conn->lastInsertId();
                $course_ids[] = $course_id;
                
                // ربط بتصنيف
                if (!empty($category_ids)) {
                    $category_id = $category_ids[$index % count($category_ids)];
                    $stmt = $conn->prepare("
                        INSERT IGNORE INTO course_categories (course_id, category_id) 
                        VALUES (?, ?)
                    ");
                    $stmt->execute([$course_id, $category_id]);
                }
                
                logMessage("✓ كورس: {$course[0]}", 'success');
            } catch (PDOException $e) {
                logMessage("تخطي الكورس {$course[0]}: " . $e->getMessage(), 'warning');
            }
        }
        
        // إنشاء تسجيلات
        if (!empty($student_ids) && !empty($course_ids)) {
            $enrollment_count = 0;
            foreach ($course_ids as $course_id) {
                foreach (array_slice($student_ids, 0, 2) as $student_id) {
                    try {
                        // جلب معلومات الكورس
                        $stmt = $conn->prepare("SELECT course_type, price FROM courses WHERE id = ?");
                        $stmt->execute([$course_id]);
                        $course_info = $stmt->fetch();
                        
                        $payment_amount = $course_info['course_type'] === 'paid' ? $course_info['price'] : 0;
                        
                        $stmt = $conn->prepare("
                            INSERT IGNORE INTO course_enrollments (student_id, course_id, payment_status, payment_amount) 
                            VALUES (?, ?, 'completed', ?)
                        ");
                        $stmt->execute([$student_id, $course_id, $payment_amount]);
                        $enrollment_count++;
                    } catch (PDOException $e) {
                        // تجاهل الأخطاء المكررة
                    }
                }
            }
            logMessage("✓ تم إنشاء $enrollment_count تسجيل", 'success');
        }
        
        // إنشاء جلسات
        foreach (array_slice($course_ids, 0, 3) as $course_id) {
            try {
                $start_time = date('Y-m-d H:i:s', strtotime('+1 day +2 hours'));
                $end_time = date('Y-m-d H:i:s', strtotime('+1 day +4 hours'));
                
                $stmt = $conn->prepare("
                    INSERT INTO sessions (course_id, title, description, start_time, end_time, status) 
                    VALUES (?, 'جلسة تجريبية', 'وصف الجلسة التجريبية', ?, ?, 'scheduled')
                ");
                $stmt->execute([$course_id, $start_time, $end_time]);
                logMessage("✓ جلسة للكورس $course_id", 'success');
            } catch (PDOException $e) {
                logMessage("تخطي الجلسة: " . $e->getMessage(), 'warning');
            }
        }
        
        // إنشاء طلبات انضمام
        if (!empty($student_ids) && !empty($instructor_ids)) {
            for ($i = 0; $i < 3; $i++) {
                try {
                    $student_id = $student_ids[$i % count($student_ids)];
                    $instructor_id = $instructor_ids[$i % count($instructor_ids)];
                    
                    $stmt = $conn->prepare("
                        INSERT IGNORE INTO join_requests (user_id, instructor_id, status) 
                        VALUES (?, ?, 'pending')
                    ");
                    $stmt->execute([$student_id, $instructor_id]);
                } catch (PDOException $e) {
                    // تجاهل الأخطاء
                }
            }
            logMessage("✓ تم إنشاء طلبات انضمام", 'success');
        }
    }
    
    logMessage('تم الانتهاء من الإصلاح الشامل بنجاح!', 'success');
    
    // عرض ملخص
    logMessage('=== ملخص النظام ===', 'info');
    $tables = ['users', 'courses', 'categories', 'course_enrollments', 'sessions', 'join_requests'];
    foreach ($tables as $table) {
        try {
            $stmt = $conn->query("SELECT COUNT(*) FROM $table");
            $count = $stmt->fetchColumn();
            logMessage("• $table: $count سجل", 'info');
        } catch (Exception $e) {
            logMessage("• $table: غير متاح", 'warning');
        }
    }
    
} catch (Exception $e) {
    logMessage('خطأ عام: ' . $e->getMessage(), 'error');
}

echo "</div>
        <div class='mt-4 text-center'>
            <div class='alert alert-success'>
                <h5><i class='fas fa-check-circle me-2'></i>تم الإصلاح الشامل بنجاح!</h5>
                <p class='mb-0'>النظام جاهز للاستخدام مع بيانات تجريبية</p>
            </div>
            <a href='dashboard.php' class='btn btn-primary btn-lg me-2'>
                <i class='fas fa-tachometer-alt me-2'></i>لوحة التحكم
            </a>
            <a href='manage-users.php' class='btn btn-success'>
                <i class='fas fa-users me-2'></i>إدارة المستخدمين
            </a>
        </div>
    </div>
</div>
</body>
</html>";
?>
