<?php
require_once '../config/database.php';

echo "<h2>إعداد جداول التصنيفات</h2>";

try {
    // إنشاء جدول التصنيفات
    $conn->exec("CREATE TABLE IF NOT EXISTS categories (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL UNIQUE,
        slug VARCHAR(255) NOT NULL UNIQUE,
        description TEXT NULL,
        icon VARCHAR(100) NULL,
        color VARCHAR(7) NULL,
        sort_order INT DEFAULT 0,
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    
    echo "<p>✅ تم إنشاء جدول categories</p>";
    
    // إنشاء جدول ربط الكورسات بالتصنيفات
    $conn->exec("CREATE TABLE IF NOT EXISTS course_categories (
        id INT AUTO_INCREMENT PRIMARY KEY,
        course_id INT NOT NULL,
        category_id INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE KEY unique_course_category (course_id, category_id),
        FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
        FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    
    echo "<p>✅ تم إنشاء جدول course_categories</p>";
    
    // إضافة عمود category_id إلى جدول courses إذا لم يكن موجود
    try {
        $conn->exec("ALTER TABLE courses ADD COLUMN category_id INT NULL");
        echo "<p>✅ تم إضافة عمود category_id إلى جدول courses</p>";
    } catch (Exception $e) {
        echo "<p>⚠️ عمود category_id موجود مسبقاً في جدول courses</p>";
    }
    
    // إضافة عمود specialization إلى جدول courses إذا لم يكن موجود
    try {
        $conn->exec("ALTER TABLE courses ADD COLUMN specialization VARCHAR(255) NULL");
        echo "<p>✅ تم إضافة عمود specialization إلى جدول courses</p>";
    } catch (Exception $e) {
        echo "<p>⚠️ عمود specialization موجود مسبقاً في جدول courses</p>";
    }
    
    // إضافة عمود price إلى جدول courses إذا لم يكن موجود
    try {
        $conn->exec("ALTER TABLE courses ADD COLUMN price DECIMAL(10,2) DEFAULT 0.00");
        echo "<p>✅ تم إضافة عمود price إلى جدول courses</p>";
    } catch (Exception $e) {
        echo "<p>⚠️ عمود price موجود مسبقاً في جدول courses</p>";
    }
    
    // إضافة عمود level إلى جدول courses إذا لم يكن موجود
    try {
        $conn->exec("ALTER TABLE courses ADD COLUMN level ENUM('beginner', 'intermediate', 'advanced') DEFAULT 'beginner'");
        echo "<p>✅ تم إضافة عمود level إلى جدول courses</p>";
    } catch (Exception $e) {
        echo "<p>⚠️ عمود level موجود مسبقاً في جدول courses</p>";
    }
    
    // التحقق من وجود تصنيفات، وإضافة تصنيفات افتراضية إذا لم توجد
    $stmt = $conn->query("SELECT COUNT(*) FROM categories");
    $count = $stmt->fetchColumn();
    
    if ($count == 0) {
        echo "<h3>إضافة تصنيفات افتراضية:</h3>";
        
        $default_categories = [
            ['name' => 'البرمجة', 'slug' => 'programming', 'description' => 'دورات البرمجة وتطوير البرمجيات', 'icon' => 'fas fa-code', 'color' => '#007bff'],
            ['name' => 'التصميم', 'slug' => 'design', 'description' => 'دورات التصميم الجرافيكي وتصميم المواقع', 'icon' => 'fas fa-paint-brush', 'color' => '#28a745'],
            ['name' => 'التسويق', 'slug' => 'marketing', 'description' => 'دورات التسويق الرقمي والتسويق الإلكتروني', 'icon' => 'fas fa-bullhorn', 'color' => '#ffc107'],
            ['name' => 'إدارة الأعمال', 'slug' => 'business', 'description' => 'دورات إدارة الأعمال والريادة', 'icon' => 'fas fa-briefcase', 'color' => '#dc3545'],
            ['name' => 'اللغات', 'slug' => 'languages', 'description' => 'دورات تعلم اللغات المختلفة', 'icon' => 'fas fa-language', 'color' => '#6f42c1'],
            ['name' => 'العلوم', 'slug' => 'science', 'description' => 'دورات العلوم والرياضيات', 'icon' => 'fas fa-flask', 'color' => '#20c997'],
            ['name' => 'التكنولوجيا', 'slug' => 'technology', 'description' => 'دورات التكنولوجيا والذكاء الاصطناعي', 'icon' => 'fas fa-microchip', 'color' => '#fd7e14'],
            ['name' => 'الصحة واللياقة', 'slug' => 'health-fitness', 'description' => 'دورات الصحة واللياقة البدنية', 'icon' => 'fas fa-heartbeat', 'color' => '#e83e8c']
        ];
        
        $stmt = $conn->prepare("INSERT INTO categories (name, slug, description, icon, color, sort_order) VALUES (?, ?, ?, ?, ?, ?)");
        
        foreach ($default_categories as $index => $category) {
            $stmt->execute([
                $category['name'],
                $category['slug'],
                $category['description'],
                $category['icon'],
                $category['color'],
                $index + 1
            ]);
            echo "<p>✅ تم إضافة تصنيف: " . $category['name'] . "</p>";
        }
    } else {
        echo "<p>⚠️ يوجد $count تصنيف في قاعدة البيانات</p>";
    }
    
    // إنشاء جدول المدفوعات إذا لم يكن موجود
    $conn->exec("CREATE TABLE IF NOT EXISTS payments (
        id INT AUTO_INCREMENT PRIMARY KEY,
        student_id INT NOT NULL,
        course_id INT NOT NULL,
        amount DECIMAL(10,2) NOT NULL,
        payment_method VARCHAR(50) NOT NULL,
        payment_reference VARCHAR(255) NULL,
        status ENUM('pending', 'completed', 'failed', 'refunded') DEFAULT 'pending',
        admin_notes TEXT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    
    echo "<p>✅ تم إنشاء جدول payments</p>";
    
    // عرض بنية الجداول
    echo "<h3>بنية جدول categories:</h3>";
    $stmt = $conn->query("DESCRIBE categories");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>بنية جدول course_categories:</h3>";
    $stmt = $conn->query("DESCRIBE course_categories");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>✅ تم إعداد جميع الجداول بنجاح!</h3>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
}
?>

<p><a href="manage-categories.php">الذهاب إلى إدارة التصنيفات</a></p>
