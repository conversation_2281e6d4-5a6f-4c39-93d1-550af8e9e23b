<?php
require_once '../../includes/session_config.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// التحقق من صلاحيات المدرب
if (!isLoggedIn() || !isInstructor()) {
    echo '<div class="alert alert-danger">غير مصرح لك بالوصول</div>';
    exit;
}

$course_id = isset($_GET['course_id']) ? (int)$_GET['course_id'] : 0;
$instructor_id = $_SESSION['user_id'];

// التحقق من ملكية الكورس
try {
    $stmt = $conn->prepare("SELECT id FROM courses WHERE id = ? AND instructor_id = ?");
    $stmt->execute([$course_id, $instructor_id]);
    if (!$stmt->fetch()) {
        echo '<div class="alert alert-danger">غير مصرح لك بالوصول لهذا الكورس</div>';
        exit;
    }
} catch (PDOException $e) {
    echo '<div class="alert alert-danger">حدث خطأ في التحقق من الصلاحيات</div>';
    exit;
}

// جلب الطلاب المسجلين
try {
    $stmt = $conn->prepare("
        SELECT 
            ce.*,
            u.name as student_name,
            u.email as student_email,
            u.phone as student_phone,
            u.created_at as registration_date,
            (SELECT COUNT(*) FROM session_attendance sa 
             JOIN sessions s ON sa.session_id = s.id 
             WHERE s.course_id = ? AND sa.student_id = u.id) as total_attendance,
            (SELECT COUNT(*) FROM sessions WHERE course_id = ?) as total_sessions,
            (SELECT AVG(grade) FROM student_grades WHERE course_id = ? AND student_id = u.id) as avg_grade
        FROM course_enrollments ce
        JOIN users u ON ce.student_id = u.id
        WHERE ce.course_id = ?
        ORDER BY ce.enrollment_date DESC
    ");
    
    $stmt->execute([$course_id, $course_id, $course_id, $course_id]);
    $students = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($students)) {
        echo '<div class="alert alert-info text-center">';
        echo '<i class="fas fa-users fa-3x text-muted mb-3"></i>';
        echo '<h5>لا يوجد طلاب مسجلين</h5>';
        echo '<p>لم يتم تسجيل أي طلاب في هذا الكورس بعد</p>';
        echo '<a href="join-requests.php?course_id=' . $course_id . '" class="btn btn-primary">عرض طلبات الانضمام</a>';
        echo '</div>';
    } else {
        echo '<div class="d-flex justify-content-between align-items-center mb-3">';
        echo '<h6>الطلاب المسجلين (' . count($students) . ')</h6>';
        echo '<div>';
        echo '<button class="btn btn-sm btn-success" onclick="exportStudents()"><i class="fas fa-download"></i> تصدير</button>';
        echo '<button class="btn btn-sm btn-primary" onclick="addStudent()"><i class="fas fa-user-plus"></i> إضافة طالب</button>';
        echo '</div>';
        echo '</div>';
        
        echo '<div class="table-responsive">';
        echo '<table class="table table-hover">';
        echo '<thead class="table-light">';
        echo '<tr>';
        echo '<th>الطالب</th>';
        echo '<th>البريد الإلكتروني</th>';
        echo '<th>تاريخ التسجيل</th>';
        echo '<th>الحضور</th>';
        echo '<th>متوسط الدرجات</th>';
        echo '<th>الحالة</th>';
        echo '<th>الإجراءات</th>';
        echo '</tr>';
        echo '</thead>';
        echo '<tbody>';
        
        foreach ($students as $student) {
            $attendance_percentage = $student['total_sessions'] > 0 ? 
                round(($student['total_attendance'] / $student['total_sessions']) * 100, 1) : 0;
            
            $avg_grade = $student['avg_grade'] ? round($student['avg_grade'], 1) : 'لا توجد';
            
            // تحديد لون الحالة
            $status_class = '';
            $status_text = '';
            switch($student['status']) {
                case 'active':
                    $status_class = 'success';
                    $status_text = 'نشط';
                    break;
                case 'completed':
                    $status_class = 'info';
                    $status_text = 'مكتمل';
                    break;
                case 'dropped':
                    $status_class = 'warning';
                    $status_text = 'منسحب';
                    break;
                case 'suspended':
                    $status_class = 'danger';
                    $status_text = 'موقوف';
                    break;
            }
            
            echo '<tr>';
            echo '<td>';
            echo '<div class="d-flex align-items-center">';
            echo '<div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2">';
            echo strtoupper(substr($student['student_name'], 0, 1));
            echo '</div>';
            echo '<div>';
            echo '<strong>' . htmlspecialchars($student['student_name']) . '</strong>';
            if ($student['student_phone']) {
                echo '<br><small class="text-muted">' . htmlspecialchars($student['student_phone']) . '</small>';
            }
            echo '</div>';
            echo '</div>';
            echo '</td>';
            echo '<td>' . htmlspecialchars($student['student_email']) . '</td>';
            echo '<td>' . date('Y-m-d', strtotime($student['enrollment_date'])) . '</td>';
            echo '<td>';
            echo '<div class="progress" style="height: 20px;">';
            echo '<div class="progress-bar" role="progressbar" style="width: ' . $attendance_percentage . '%">';
            echo $attendance_percentage . '%';
            echo '</div>';
            echo '</div>';
            echo '<small class="text-muted">' . $student['total_attendance'] . '/' . $student['total_sessions'] . ' جلسة</small>';
            echo '</td>';
            echo '<td>';
            if ($avg_grade !== 'لا توجد') {
                $grade_class = $avg_grade >= 80 ? 'success' : ($avg_grade >= 60 ? 'warning' : 'danger');
                echo '<span class="badge bg-' . $grade_class . '">' . $avg_grade . '%</span>';
            } else {
                echo '<span class="text-muted">لا توجد درجات</span>';
            }
            echo '</td>';
            echo '<td><span class="badge bg-' . $status_class . '">' . $status_text . '</span></td>';
            echo '<td>';
            echo '<div class="btn-group btn-group-sm">';
            echo '<button class="btn btn-outline-primary" onclick="viewStudentDetails(' . $student['student_id'] . ')" title="عرض التفاصيل">';
            echo '<i class="fas fa-eye"></i>';
            echo '</button>';
            echo '<button class="btn btn-outline-success" onclick="addGrade(' . $student['student_id'] . ')" title="إضافة درجة">';
            echo '<i class="fas fa-star"></i>';
            echo '</button>';
            echo '<button class="btn btn-outline-warning" onclick="editStudent(' . $student['student_id'] . ')" title="تعديل">';
            echo '<i class="fas fa-edit"></i>';
            echo '</button>';
            echo '</div>';
            echo '</td>';
            echo '</tr>';
        }
        
        echo '</tbody>';
        echo '</table>';
        echo '</div>';
    }
    
} catch (PDOException $e) {
    echo '<div class="alert alert-danger">حدث خطأ في جلب بيانات الطلاب: ' . htmlspecialchars($e->getMessage()) . '</div>';
}
?>

<script>
function viewStudentDetails(studentId) {
    // فتح نافذة منبثقة لعرض تفاصيل الطالب
    window.open(`student-details.php?id=${studentId}&course_id=<?php echo $course_id; ?>`, 
                '_blank', 'width=800,height=600,scrollbars=yes');
}

function addGrade(studentId) {
    // فتح نافذة إضافة درجة
    const modal = new bootstrap.Modal(document.getElementById('addGradeModal'));
    document.getElementById('gradeStudentId').value = studentId;
    modal.show();
}

function editStudent(studentId) {
    // تعديل بيانات الطالب
    window.location.href = `edit-student.php?id=${studentId}&course_id=<?php echo $course_id; ?>`;
}

function exportStudents() {
    // تصدير قائمة الطلاب
    window.location.href = `export-students.php?course_id=<?php echo $course_id; ?>`;
}

function addStudent() {
    // إضافة طالب جديد
    window.location.href = `add-student.php?course_id=<?php echo $course_id; ?>`;
}
</script>

<!-- Modal إضافة درجة -->
<div class="modal fade" id="addGradeModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة درجة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addGradeForm">
                <div class="modal-body">
                    <input type="hidden" id="gradeStudentId" name="student_id">
                    <input type="hidden" name="course_id" value="<?php echo $course_id; ?>">
                    
                    <div class="mb-3">
                        <label for="assignmentName" class="form-label">اسم التقييم</label>
                        <input type="text" class="form-control" id="assignmentName" name="assignment_name" required>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <label for="grade" class="form-label">الدرجة</label>
                            <input type="number" class="form-control" id="grade" name="grade" min="0" max="100" required>
                        </div>
                        <div class="col-md-6">
                            <label for="maxGrade" class="form-label">الدرجة الكاملة</label>
                            <input type="number" class="form-control" id="maxGrade" name="max_grade" value="100" min="1" required>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="gradeNotes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="gradeNotes" name="notes" rows="2"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ الدرجة</button>
                </div>
            </form>
        </div>
    </div>
</div>
