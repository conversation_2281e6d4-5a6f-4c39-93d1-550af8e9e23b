<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من صلاحيات المدرب
if (!isLoggedIn() || !isInstructor()) {
    header('Location: ../login.php');
    exit;
}

$course_id = (int)($_GET['course_id'] ?? 0);
$error = '';
$success = '';

// التحقق من أن الكورس ينتمي للمدرب
try {
    $stmt = $conn->prepare("SELECT title, price FROM courses WHERE id = ? AND instructor_id = ?");
    $stmt->execute([$course_id, $_SESSION['user_id']]);
    $course = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$course) {
        header('Location: courses.php');
        exit;
    }
} catch (PDOException $e) {
    $error = 'خطأ في التحقق من الكورس';
}

// معالجة إضافة الطالب
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $method = $_POST['method'] ?? 'existing';
    
    if ($method === 'existing') {
        // إضافة طالب موجود
        $student_email = trim($_POST['student_email'] ?? '');
        
        if (empty($student_email)) {
            $error = 'البريد الإلكتروني مطلوب';
        } else {
            try {
                // البحث عن الطالب
                $stmt = $conn->prepare("SELECT id, name FROM users WHERE email = ? AND role = 'student'");
                $stmt->execute([$student_email]);
                $student = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if (!$student) {
                    $error = 'لم يتم العثور على طالب بهذا البريد الإلكتروني';
                } else {
                    // التحقق من عدم تسجيله مسبقاً
                    $stmt = $conn->prepare("SELECT id FROM enrollments WHERE student_id = ? AND course_id = ?");
                    $stmt->execute([$student['id'], $course_id]);
                    
                    if ($stmt->fetch()) {
                        $error = 'الطالب مسجل بالفعل في هذا الكورس';
                    } else {
                        // إضافة التسجيل
                        $stmt = $conn->prepare("
                            INSERT INTO enrollments (student_id, course_id, status, enrollment_date, notes) 
                            VALUES (?, ?, 'approved', NOW(), 'تم إضافته بواسطة المدرب')
                        ");
                        $stmt->execute([$student['id'], $course_id]);
                        
                        $success = 'تم إضافة الطالب ' . $student['name'] . ' بنجاح';
                    }
                }
            } catch (PDOException $e) {
                $error = 'خطأ في إضافة الطالب: ' . $e->getMessage();
            }
        }
        
    } elseif ($method === 'new') {
        // إنشاء طالب جديد
        $name = trim($_POST['name'] ?? '');
        $email = trim($_POST['email'] ?? '');
        $phone = trim($_POST['phone'] ?? '');
        $password = $_POST['password'] ?? '';
        $send_credentials = isset($_POST['send_credentials']);
        
        if (empty($name) || empty($email) || empty($password)) {
            $error = 'جميع الحقول مطلوبة';
        } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $error = 'البريد الإلكتروني غير صحيح';
        } elseif (strlen($password) < 6) {
            $error = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
        } else {
            try {
                $conn->beginTransaction();
                
                // التحقق من عدم وجود البريد الإلكتروني
                $stmt = $conn->prepare("SELECT id FROM users WHERE email = ?");
                $stmt->execute([$email]);
                
                if ($stmt->fetch()) {
                    $error = 'البريد الإلكتروني مستخدم بالفعل';
                } else {
                    // إنشاء المستخدم
                    $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                    $stmt = $conn->prepare("
                        INSERT INTO users (name, email, phone, password, role, created_at) 
                        VALUES (?, ?, ?, ?, 'student', NOW())
                    ");
                    $stmt->execute([$name, $email, $phone, $hashed_password]);
                    $student_id = $conn->lastInsertId();
                    
                    // تسجيل الطالب في الكورس
                    $stmt = $conn->prepare("
                        INSERT INTO enrollments (student_id, course_id, status, enrollment_date, notes) 
                        VALUES (?, ?, 'approved', NOW(), 'تم إنشاؤه وإضافته بواسطة المدرب')
                    ");
                    $stmt->execute([$student_id, $course_id]);
                    
                    $conn->commit();
                    
                    // إرسال بيانات الدخول بالبريد الإلكتروني (إذا تم تحديد الخيار)
                    if ($send_credentials && function_exists('sendEmail')) {
                        $subject = 'مرحباً بك في منصة التعلم';
                        $message = "
                            مرحباً $name،
                            
                            تم إنشاء حساب لك في منصة التعلم وتسجيلك في كورس: {$course['title']}
                            
                            بيانات الدخول:
                            البريد الإلكتروني: $email
                            كلمة المرور: $password
                            
                            يمكنك تسجيل الدخول من خلال الرابط التالي:
                            " . $_SERVER['HTTP_HOST'] . "/login.php
                            
                            مع تحيات فريق المنصة
                        ";
                        
                        sendEmail($email, $subject, $message);
                    }
                    
                    $success = 'تم إنشاء الطالب وإضافته للكورس بنجاح';
                }
                
            } catch (PDOException $e) {
                $conn->rollBack();
                $error = 'خطأ في إنشاء الطالب: ' . $e->getMessage();
            }
        }
        
    } elseif ($method === 'invite') {
        // إرسال دعوة
        $invite_email = trim($_POST['invite_email'] ?? '');
        $invite_message = trim($_POST['invite_message'] ?? '');
        
        if (empty($invite_email)) {
            $error = 'البريد الإلكتروني مطلوب';
        } elseif (!filter_var($invite_email, FILTER_VALIDATE_EMAIL)) {
            $error = 'البريد الإلكتروني غير صحيح';
        } else {
            try {
                // إنشاء رمز الدعوة
                $invite_code = bin2hex(random_bytes(16));
                
                $stmt = $conn->prepare("
                    INSERT INTO course_invitations (course_id, instructor_id, email, invite_code, message, created_at, expires_at) 
                    VALUES (?, ?, ?, ?, ?, NOW(), DATE_ADD(NOW(), INTERVAL 7 DAY))
                ");
                $stmt->execute([$course_id, $_SESSION['user_id'], $invite_email, $invite_code, $invite_message]);
                
                // إرسال الدعوة بالبريد الإلكتروني
                if (function_exists('sendEmail')) {
                    $invite_link = $_SERVER['HTTP_HOST'] . "/register.php?invite=" . $invite_code;
                    $subject = 'دعوة للانضمام لكورس: ' . $course['title'];
                    $message = "
                        مرحباً،
                        
                        تم دعوتك للانضمام لكورس: {$course['title']}
                        
                        " . ($invite_message ? "رسالة من المدرب: $invite_message" : "") . "
                        
                        للانضمام، يرجى النقر على الرابط التالي:
                        $invite_link
                        
                        هذه الدعوة صالحة لمدة 7 أيام.
                        
                        مع تحيات فريق المنصة
                    ";
                    
                    sendEmail($invite_email, $subject, $message);
                }
                
                $success = 'تم إرسال الدعوة بنجاح إلى ' . $invite_email;
                
            } catch (PDOException $e) {
                $error = 'خطأ في إرسال الدعوة: ' . $e->getMessage();
            }
        }
    }
}

$pageTitle = 'إضافة طالب جديد - ' . $course['title'];
$breadcrumbs = [
    ['title' => 'الكورسات', 'url' => 'courses.php'],
    ['title' => $course['title'], 'url' => 'course-details.php?id=' . $course_id],
    ['title' => 'الطلاب', 'url' => 'students.php?course_id=' . $course_id],
    ['title' => 'إضافة طالب']
];

include 'includes/header.php';
?>

<div class="row">
    <div class="col-md-8">
        <!-- طرق إضافة الطالب -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user-plus me-2"></i>
                    إضافة طالب للكورس
                </h5>
            </div>
            <div class="card-body">
                <?php if ($error): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endif; ?>

                <?php if ($success): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        <?php echo htmlspecialchars($success); ?>
                    </div>
                <?php endif; ?>

                <!-- تبويبات طرق الإضافة -->
                <ul class="nav nav-tabs" id="addStudentTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="existing-tab" data-bs-toggle="tab" data-bs-target="#existing" type="button">
                            <i class="fas fa-search me-2"></i>
                            طالب موجود
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="new-tab" data-bs-toggle="tab" data-bs-target="#new" type="button">
                            <i class="fas fa-user-plus me-2"></i>
                            إنشاء جديد
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="invite-tab" data-bs-toggle="tab" data-bs-target="#invite" type="button">
                            <i class="fas fa-envelope me-2"></i>
                            إرسال دعوة
                        </button>
                    </li>
                </ul>

                <div class="tab-content mt-4" id="addStudentTabsContent">
                    <!-- إضافة طالب موجود -->
                    <div class="tab-pane fade show active" id="existing" role="tabpanel">
                        <form method="POST" action="">
                            <input type="hidden" name="method" value="existing">
                            
                            <div class="mb-3">
                                <label for="student_email" class="form-label">البريد الإلكتروني للطالب</label>
                                <input type="email" class="form-control" id="student_email" name="student_email" 
                                       placeholder="<EMAIL>" required>
                                <div class="form-text">
                                    أدخل البريد الإلكتروني لطالب مسجل بالفعل في المنصة
                                </div>
                            </div>
                            
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-plus me-2"></i>
                                إضافة الطالب
                            </button>
                        </form>
                    </div>

                    <!-- إنشاء طالب جديد -->
                    <div class="tab-pane fade" id="new" role="tabpanel">
                        <form method="POST" action="">
                            <input type="hidden" name="method" value="new">
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="name" class="form-label">اسم الطالب</label>
                                        <input type="text" class="form-control" id="name" name="name" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="email" class="form-label">البريد الإلكتروني</label>
                                        <input type="email" class="form-control" id="email" name="email" required>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="phone" class="form-label">رقم الهاتف (اختياري)</label>
                                        <input type="tel" class="form-control" id="phone" name="phone">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="password" class="form-label">كلمة المرور</label>
                                        <input type="password" class="form-control" id="password" name="password" 
                                               minlength="6" required>
                                        <div class="form-text">6 أحرف على الأقل</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="send_credentials" name="send_credentials" checked>
                                    <label class="form-check-label" for="send_credentials">
                                        إرسال بيانات الدخول بالبريد الإلكتروني
                                    </label>
                                </div>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-user-plus me-2"></i>
                                إنشاء وإضافة الطالب
                            </button>
                        </form>
                    </div>

                    <!-- إرسال دعوة -->
                    <div class="tab-pane fade" id="invite" role="tabpanel">
                        <form method="POST" action="">
                            <input type="hidden" name="method" value="invite">
                            
                            <div class="mb-3">
                                <label for="invite_email" class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="invite_email" name="invite_email" 
                                       placeholder="<EMAIL>" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="invite_message" class="form-label">رسالة شخصية (اختياري)</label>
                                <textarea class="form-control" id="invite_message" name="invite_message" rows="3" 
                                          placeholder="رسالة ترحيبية أو تعريفية بالكورس..."></textarea>
                            </div>
                            
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                سيتم إرسال رابط دعوة صالح لمدة 7 أيام. يمكن للمدعو إنشاء حساب والانضمام للكورس مباشرة.
                            </div>
                            
                            <button type="submit" class="btn btn-info">
                                <i class="fas fa-envelope me-2"></i>
                                إرسال الدعوة
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <!-- معلومات الكورس -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-graduation-cap me-2"></i>
                    معلومات الكورس
                </h6>
            </div>
            <div class="card-body">
                <h6><?php echo htmlspecialchars($course['title']); ?></h6>
                
                <?php if ($course['price'] > 0): ?>
                    <div class="alert alert-warning">
                        <i class="fas fa-dollar-sign me-2"></i>
                        <strong>كورس مدفوع:</strong> <?php echo number_format($course['price'], 2); ?> ريال
                        <br>
                        <small>الطلاب المضافون بواسطة المدرب معفيون من الرسوم</small>
                    </div>
                <?php else: ?>
                    <div class="alert alert-success">
                        <i class="fas fa-gift me-2"></i>
                        <strong>كورس مجاني</strong>
                    </div>
                <?php endif; ?>
                
                <div class="d-grid gap-2">
                    <a href="students.php?course_id=<?php echo $course_id; ?>" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-users me-2"></i>
                        عرض جميع الطلاب
                    </a>
                    <a href="course-details.php?id=<?php echo $course_id; ?>" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للكورس
                    </a>
                </div>
            </div>
        </div>

        <!-- نصائح -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>
                    نصائح
                </h6>
            </div>
            <div class="card-body">
                <div class="small">
                    <div class="mb-3">
                        <strong>طالب موجود:</strong>
                        <p class="mb-0">للطلاب المسجلين بالفعل في المنصة</p>
                    </div>
                    
                    <div class="mb-3">
                        <strong>إنشاء جديد:</strong>
                        <p class="mb-0">لإنشاء حساب جديد وإضافته مباشرة</p>
                    </div>
                    
                    <div class="mb-0">
                        <strong>إرسال دعوة:</strong>
                        <p class="mb-0">للسماح للطالب بإنشاء حسابه بنفسه</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.card-header {
    background: var(--instructor-light);
    border-bottom: 1px solid var(--gray-200);
    border-radius: 12px 12px 0 0 !important;
}

.nav-tabs .nav-link {
    border: none;
    color: var(--gray-600);
    font-weight: 500;
}

.nav-tabs .nav-link.active {
    background: var(--instructor-primary);
    color: white;
    border-radius: 8px 8px 0 0;
}

.form-control:focus,
.form-check-input:focus {
    border-color: var(--instructor-primary);
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}
</style>

<!-- JavaScript -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // إنشاء كلمة مرور عشوائية
    const generatePasswordBtn = document.createElement('button');
    generatePasswordBtn.type = 'button';
    generatePasswordBtn.className = 'btn btn-outline-secondary btn-sm mt-2';
    generatePasswordBtn.innerHTML = '<i class="fas fa-random me-1"></i>إنشاء كلمة مرور';
    
    const passwordField = document.getElementById('password');
    passwordField.parentNode.appendChild(generatePasswordBtn);
    
    generatePasswordBtn.addEventListener('click', function() {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let password = '';
        for (let i = 0; i < 8; i++) {
            password += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        passwordField.value = password;
    });
});
</script>

</body>
</html>
