<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>اختبار النموذج</h2>";

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo "<h3>البيانات المرسلة:</h3>";
    echo "<pre>";
    print_r($_POST);
    echo "</pre>";
    
    if (isset($_POST['name'])) {
        echo "<p>الاسم: " . htmlspecialchars($_POST['name']) . "</p>";
    }
    if (isset($_POST['email'])) {
        echo "<p>البريد: " . htmlspecialchars($_POST['email']) . "</p>";
    }
    if (isset($_POST['phone'])) {
        echo "<p>الهاتف: " . htmlspecialchars($_POST['phone']) . "</p>";
    }
    if (isset($_POST['password'])) {
        echo "<p>كلمة المرور: " . (strlen($_POST['password']) > 0 ? "تم إدخالها" : "فارغة") . "</p>";
    }
    
    echo "<p style='color: green;'>✅ تم استلام البيانات بنجاح!</p>";
}
?>

<form method="POST" action="">
    <h3>نموذج اختبار:</h3>
    <table border="1" style="border-collapse: collapse;">
        <tr>
            <td>الاسم:</td>
            <td><input type="text" name="name" value="أحمد محمد" required></td>
        </tr>
        <tr>
            <td>البريد:</td>
            <td><input type="email" name="email" value="<EMAIL>" required></td>
        </tr>
        <tr>
            <td>الهاتف:</td>
            <td><input type="text" name="phone" value="0501234567" required></td>
        </tr>
        <tr>
            <td>كلمة المرور:</td>
            <td><input type="password" name="password" value="12345678" required></td>
        </tr>
        <tr>
            <td>تأكيد كلمة المرور:</td>
            <td><input type="password" name="confirm_password" value="12345678" required></td>
        </tr>
        <tr>
            <td colspan="2">
                <input type="submit" value="إرسال" style="padding: 10px 20px;">
            </td>
        </tr>
    </table>
</form>
