<?php
require_once '../../includes/session_config.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';
require_once '../../includes/security.php';

// التحقق من صلاحيات المدير
if (!isLoggedIn() || !isAdmin()) {
    echo json_encode(['success' => false, 'message' => 'غير مصرح لك بهذا الإجراء']);
    exit;
}

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'طريقة طلب غير صحيحة']);
    exit;
}

$session_id = $_POST['session_id'] ?? 0;
$status = $_POST['status'] ?? '';

if (!$session_id) {
    echo json_encode(['success' => false, 'message' => 'معرف الجلسة مطلوب']);
    exit;
}

$allowed_statuses = ['scheduled', 'live', 'completed', 'cancelled'];
if (!in_array($status, $allowed_statuses)) {
    echo json_encode(['success' => false, 'message' => 'حالة غير صحيحة']);
    exit;
}

try {
    // جلب معلومات الجلسة
    $stmt = $conn->prepare("SELECT title, status FROM sessions WHERE id = ?");
    $stmt->execute([$session_id]);
    $session = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$session) {
        throw new Exception('الجلسة غير موجودة');
    }
    
    // تحديث حالة الجلسة
    $stmt = $conn->prepare("UPDATE sessions SET status = ? WHERE id = ?");
    $stmt->execute([$status, $session_id]);
    
    // تسجيل النشاط
    $status_text = [
        'scheduled' => 'مجدولة',
        'live' => 'مباشرة',
        'completed' => 'مكتملة',
        'cancelled' => 'ملغية'
    ];
    
    logUserActivity($_SESSION['user_id'], 'تحديث حالة جلسة', "تم تغيير حالة الجلسة '{$session['title']}' إلى: " . $status_text[$status]);
    
    echo json_encode([
        'success' => true, 
        'message' => 'تم تحديث حالة الجلسة بنجاح'
    ]);
    
} catch (Exception $e) {
    error_log("Error updating session status: " . $e->getMessage());
    echo json_encode([
        'success' => false, 
        'message' => 'حدث خطأ أثناء تحديث حالة الجلسة: ' . $e->getMessage()
    ]);
}
?>
