<?php
/**
 * اختبار إضافة كورس مع تشخيص مفصل
 * Test course addition with detailed diagnostics
 */

require_once 'includes/session_config.php';
require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'includes/security.php';
require_once 'includes/activity_logger.php';

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>اختبار إضافة كورس</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "</head>";
echo "<body>";
echo "<div class='container mt-4'>";
echo "<h2>🧪 اختبار إضافة كورس</h2>";

// التحقق من الجلسة
if (!isLoggedIn()) {
    echo "<div class='alert alert-warning'>يجب تسجيل الدخول أولاً. <a href='login.php'>تسجيل الدخول</a></div>";
} else {
    echo "<div class='alert alert-info'>مسجل دخول كـ: " . ($_SESSION['name'] ?? 'غير محدد') . " (" . ($_SESSION['role'] ?? 'غير محدد') . ")</div>";
}

// فحص هيكل قاعدة البيانات
echo "<h4>🔍 فحص هيكل قاعدة البيانات</h4>";

try {
    // فحص جدول courses
    echo "<h5>📚 جدول courses:</h5>";
    $stmt = $conn->query("DESCRIBE courses");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<div class='table-responsive'>";
    echo "<table class='table table-sm table-bordered'>";
    echo "<thead><tr><th>العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th></tr></thead>";
    echo "<tbody>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . ($column['Default'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</tbody></table>";
    echo "</div>";
    
    // التحقق من الأعمدة المطلوبة
    $required_columns = ['image_path', 'category_id'];
    $missing_columns = [];
    
    $existing_columns = array_column($columns, 'Field');
    foreach ($required_columns as $col) {
        if (!in_array($col, $existing_columns)) {
            $missing_columns[] = $col;
        }
    }
    
    if (!empty($missing_columns)) {
        echo "<div class='alert alert-warning'>أعمدة مفقودة: " . implode(', ', $missing_columns) . "</div>";
        
        // إضافة الأعمدة المفقودة
        foreach ($missing_columns as $col) {
            try {
                if ($col === 'image_path') {
                    $conn->exec("ALTER TABLE courses ADD COLUMN image_path VARCHAR(255) DEFAULT NULL");
                    echo "<div class='alert alert-success'>✅ تم إضافة عمود image_path</div>";
                } elseif ($col === 'category_id') {
                    $conn->exec("ALTER TABLE courses ADD COLUMN category_id INT DEFAULT NULL");
                    echo "<div class='alert alert-success'>✅ تم إضافة عمود category_id</div>";
                }
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>❌ فشل في إضافة عمود $col: " . $e->getMessage() . "</div>";
            }
        }
    } else {
        echo "<div class='alert alert-success'>✅ جميع الأعمدة المطلوبة موجودة</div>";
    }
    
    // فحص جدول categories
    echo "<h5>📋 جدول categories:</h5>";
    try {
        $stmt = $conn->query("SELECT COUNT(*) FROM categories");
        $categories_count = $stmt->fetchColumn();
        echo "<div class='alert alert-info'>عدد التصنيفات: $categories_count</div>";
        
        if ($categories_count == 0) {
            echo "<div class='alert alert-warning'>لا توجد تصنيفات. <a href='fix_database_structure.php'>إنشاء تصنيفات</a></div>";
        } else {
            $stmt = $conn->query("SELECT * FROM categories LIMIT 5");
            $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<div class='table-responsive'>";
            echo "<table class='table table-sm'>";
            echo "<thead><tr><th>ID</th><th>الاسم</th><th>الحالة</th></tr></thead>";
            echo "<tbody>";
            foreach ($categories as $cat) {
                echo "<tr>";
                echo "<td>" . $cat['id'] . "</td>";
                echo "<td>" . htmlspecialchars($cat['name']) . "</td>";
                echo "<td>" . $cat['status'] . "</td>";
                echo "</tr>";
            }
            echo "</tbody></table>";
            echo "</div>";
        }
    } catch (Exception $e) {
        echo "<div class='alert alert-danger'>❌ خطأ في جدول categories: " . $e->getMessage() . "</div>";
    }
    
    // فحص مجلد uploads
    echo "<h5>📁 مجلد uploads:</h5>";
    $upload_dir = 'uploads/courses';
    if (!file_exists($upload_dir)) {
        if (mkdir($upload_dir, 0777, true)) {
            echo "<div class='alert alert-success'>✅ تم إنشاء مجلد uploads/courses</div>";
        } else {
            echo "<div class='alert alert-danger'>❌ فشل في إنشاء مجلد uploads/courses</div>";
        }
    } else {
        echo "<div class='alert alert-success'>✅ مجلد uploads/courses موجود</div>";
        
        // التحقق من صلاحيات الكتابة
        if (is_writable($upload_dir)) {
            echo "<div class='alert alert-success'>✅ مجلد uploads/courses قابل للكتابة</div>";
        } else {
            echo "<div class='alert alert-danger'>❌ مجلد uploads/courses غير قابل للكتابة</div>";
        }
    }
    
    // اختبار دوال تسجيل الأنشطة
    echo "<h5>📝 اختبار دوال تسجيل الأنشطة:</h5>";
    try {
        $test_result = logUserActivity('اختبار النظام', 'اختبار دالة تسجيل الأنشطة');
        if ($test_result) {
            echo "<div class='alert alert-success'>✅ دالة logUserActivity تعمل بشكل صحيح</div>";
        } else {
            echo "<div class='alert alert-danger'>❌ دالة logUserActivity لا تعمل</div>";
        }
    } catch (Exception $e) {
        echo "<div class='alert alert-danger'>❌ خطأ في دالة logUserActivity: " . $e->getMessage() . "</div>";
    }
    
    // اختبار إضافة كورس تجريبي
    echo "<h4>🎯 اختبار إضافة كورس تجريبي</h4>";
    
    if (isLoggedIn() && (isInstructor() || isAdmin())) {
        echo "<form method='POST' class='mt-3'>";
        echo "<div class='mb-3'>";
        echo "<label class='form-label'>عنوان الكورس التجريبي:</label>";
        echo "<input type='text' class='form-control' name='test_title' value='كورس تجريبي - " . date('Y-m-d H:i') . "' required>";
        echo "</div>";
        echo "<div class='mb-3'>";
        echo "<label class='form-label'>وصف الكورس:</label>";
        echo "<textarea class='form-control' name='test_description' required>هذا كورس تجريبي لاختبار النظام</textarea>";
        echo "</div>";
        echo "<div class='mb-3'>";
        echo "<label class='form-label'>التصنيف:</label>";
        echo "<select class='form-control' name='test_category' required>";
        echo "<option value=''>اختر تصنيف...</option>";
        
        try {
            $stmt = $conn->query("SELECT * FROM categories");
            $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
            foreach ($categories as $cat) {
                echo "<option value='" . $cat['id'] . "'>" . htmlspecialchars($cat['name']) . "</option>";
            }
        } catch (Exception $e) {
            echo "<option value=''>خطأ في جلب التصنيفات</option>";
        }
        
        echo "</select>";
        echo "</div>";
        echo "<button type='submit' name='test_add_course' class='btn btn-primary'>إضافة كورس تجريبي</button>";
        echo "</form>";
        
        // معالجة إضافة الكورس التجريبي
        if (isset($_POST['test_add_course'])) {
            try {
                $title = trim($_POST['test_title']);
                $description = trim($_POST['test_description']);
                $category_id = (int)$_POST['test_category'];
                $instructor_id = $_SESSION['user_id'];
                
                $stmt = $conn->prepare("
                    INSERT INTO courses (title, description, start_date, end_date, max_students, status, instructor_id, category_id, created_at) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
                ");
                
                $stmt->execute([
                    $title,
                    $description,
                    date('Y-m-d', strtotime('+1 week')),
                    date('Y-m-d', strtotime('+2 months')),
                    30,
                    'active',
                    $instructor_id,
                    $category_id
                ]);
                
                $course_id = $conn->lastInsertId();
                
                // تسجيل النشاط
                logUserActivity('إضافة كورس تجريبي', "تم إضافة كورس تجريبي: $title");
                
                echo "<div class='alert alert-success'>✅ تم إضافة الكورس التجريبي بنجاح! ID: $course_id</div>";
                
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>❌ فشل في إضافة الكورس التجريبي: " . $e->getMessage() . "</div>";
            }
        }
    } else {
        echo "<div class='alert alert-warning'>يجب تسجيل الدخول كمدرب أو مدير لاختبار إضافة الكورس</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>❌ خطأ عام: " . $e->getMessage() . "</div>";
}

echo "<div class='mt-4'>";
echo "<a href='instructor/add-course.php' class='btn btn-primary btn-lg me-2'>➕ إضافة كورس حقيقي</a>";
echo "<a href='fix_database_structure.php' class='btn btn-warning btn-lg me-2'>🔧 إصلاح قاعدة البيانات</a>";
echo "<a href='admin/dashboard.php' class='btn btn-success btn-lg'>📊 لوحة التحكم</a>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
