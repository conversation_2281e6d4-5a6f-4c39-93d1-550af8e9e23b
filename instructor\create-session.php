<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات المدرب
if (!isLoggedIn() || !isInstructor()) {
    header('Location: ../login.php');
    exit;
}

$pageTitle = 'إنشاء جلسة جديدة';
$breadcrumbs = [
    ['title' => 'الجلسات', 'url' => 'sessions.php'],
    ['title' => 'إنشاء جلسة جديدة']
];

// معالجة إنشاء الجلسة
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $course_id = $_POST['course_id'] ?? 0;
    $title = trim($_POST['title'] ?? '');
    $description = trim($_POST['description'] ?? '');
    $session_date = $_POST['session_date'] ?? '';
    $start_time = $_POST['start_time'] ?? '';
    $end_time = $_POST['end_time'] ?? '';
    $meeting_type = $_POST['meeting_type'] ?? 'zoom';
    $zoom_meeting_id = trim($_POST['zoom_meeting_id'] ?? '');
    $zoom_password = trim($_POST['zoom_password'] ?? '');
    $zoom_join_url = trim($_POST['zoom_join_url'] ?? '');
    $max_attendees = $_POST['max_attendees'] ?? null;
    $materials = trim($_POST['materials'] ?? '');
    $homework = trim($_POST['homework'] ?? '');
    
    $errors = [];
    
    // التحقق من صحة البيانات
    if (empty($title)) {
        $errors[] = 'عنوان الجلسة مطلوب';
    }
    
    if (empty($course_id)) {
        $errors[] = 'يجب اختيار كورس';
    }
    
    if (empty($session_date)) {
        $errors[] = 'تاريخ الجلسة مطلوب';
    }
    
    if (empty($start_time)) {
        $errors[] = 'وقت البداية مطلوب';
    }
    
    if (empty($end_time)) {
        $errors[] = 'وقت النهاية مطلوب';
    }
    
    if ($start_time && $end_time && $start_time >= $end_time) {
        $errors[] = 'وقت النهاية يجب أن يكون بعد وقت البداية';
    }
    
    // التحقق من أن التاريخ في المستقبل
    if ($session_date && $session_date < date('Y-m-d')) {
        $errors[] = 'لا يمكن إنشاء جلسة في الماضي';
    }
    
    if (empty($errors)) {
        try {
            // التحقق من أن الكورس ينتمي للمدرب
            $stmt = $conn->prepare("SELECT id, title FROM courses WHERE id = ? AND instructor_id = ?");
            $stmt->execute([$course_id, $_SESSION['user_id']]);
            $course = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$course) {
                $errors[] = 'الكورس غير موجود أو لا تملك صلاحية للوصول إليه';
            } else {
                // حساب مدة الجلسة بالدقائق
                $start_datetime = new DateTime($session_date . ' ' . $start_time);
                $end_datetime = new DateTime($session_date . ' ' . $end_time);
                $duration_minutes = $end_datetime->diff($start_datetime)->h * 60 + $end_datetime->diff($start_datetime)->i;
                
                // إدراج الجلسة الجديدة
                $stmt = $conn->prepare("
                    INSERT INTO sessions (
                        course_id, title, description, session_date, start_time, end_time, 
                        duration_minutes, meeting_type, zoom_meeting_id, zoom_meeting_password, 
                        zoom_join_url, max_attendees, materials, homework, status
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'scheduled')
                ");
                
                $stmt->execute([
                    $course_id, $title, $description, $session_date, $start_time, $end_time,
                    $duration_minutes, $meeting_type, $zoom_meeting_id, $zoom_password,
                    $zoom_join_url, $max_attendees ?: null, $materials, $homework
                ]);
                
                $session_id = $conn->lastInsertId();
                
                // إضافة سجل نشاط
                logActivity($conn, $_SESSION['user_id'], 'create_session', "تم إنشاء جلسة جديدة: $title");
                
                $success_message = 'تم إنشاء الجلسة بنجاح';
                
                // إعادة توجيه بعد النجاح
                header("Location: sessions.php?success=1");
                exit;
            }
        } catch (PDOException $e) {
            $errors[] = 'حدث خطأ أثناء إنشاء الجلسة: ' . $e->getMessage();
        }
    }
}

// جلب الكورسات الخاصة بالمدرب
try {
    $stmt = $conn->prepare("
        SELECT id, title, max_students 
        FROM courses 
        WHERE instructor_id = ? AND status = 'active'
        ORDER BY title
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $instructor_courses = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $instructor_courses = [];
}

include 'includes/header.php';
?>

<!-- رأس الصفحة -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="h3 mb-2">
            <i class="fas fa-video text-primary me-2"></i>
            إنشاء جلسة جديدة
        </h2>
        <p class="text-muted mb-0">إنشاء جلسة مباشرة جديدة للطلاب</p>
    </div>
    <div class="d-flex gap-2">
        <a href="sessions.php" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>العودة للجلسات
        </a>
        <a href="schedule-session.php" class="btn btn-outline-primary">
            <i class="fas fa-calendar-plus me-1"></i>جدولة متقدمة
        </a>
    </div>
</div>

<!-- رسائل الخطأ -->
<?php if (!empty($errors)): ?>
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <i class="fas fa-exclamation-triangle me-2"></i>
    <ul class="mb-0">
        <?php foreach ($errors as $error): ?>
        <li><?php echo htmlspecialchars($error); ?></li>
        <?php endforeach; ?>
    </ul>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<?php if (isset($success_message)): ?>
<div class="alert alert-success alert-dismissible fade show" role="alert">
    <i class="fas fa-check-circle me-2"></i>
    <?php echo $success_message; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<!-- نموذج إنشاء الجلسة -->
<div class="row">
    <div class="col-lg-8">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-bottom">
                <h5 class="mb-0">
                    <i class="fas fa-plus-circle me-2"></i>
                    معلومات الجلسة
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" id="sessionForm">
                    <!-- معلومات أساسية -->
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="course_id" class="form-label">الكورس <span class="text-danger">*</span></label>
                            <select name="course_id" id="course_id" class="form-select" required>
                                <option value="">-- اختر كورس --</option>
                                <?php foreach ($instructor_courses as $course): ?>
                                <option value="<?php echo $course['id']; ?>" 
                                        <?php echo (isset($_POST['course_id']) && $_POST['course_id'] == $course['id']) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($course['title']); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="title" class="form-label">عنوان الجلسة <span class="text-danger">*</span></label>
                            <input type="text" name="title" id="title" class="form-control" 
                                   value="<?php echo htmlspecialchars($_POST['title'] ?? ''); ?>" 
                                   placeholder="مثال: الدرس الأول - مقدمة في البرمجة" required>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="description" class="form-label">وصف الجلسة</label>
                            <textarea name="description" id="description" class="form-control" rows="4" 
                                      placeholder="وصف مختصر عن محتوى الجلسة وما سيتم تغطيته..."><?php echo htmlspecialchars($_POST['description'] ?? ''); ?></textarea>
                        </div>
                    </div>
                    
                    <!-- التوقيت -->
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="session_date" class="form-label">تاريخ الجلسة <span class="text-danger">*</span></label>
                            <input type="date" name="session_date" id="session_date" class="form-control" 
                                   value="<?php echo $_POST['session_date'] ?? ''; ?>" 
                                   min="<?php echo date('Y-m-d'); ?>" required>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="start_time" class="form-label">وقت البداية <span class="text-danger">*</span></label>
                            <input type="time" name="start_time" id="start_time" class="form-control" 
                                   value="<?php echo $_POST['start_time'] ?? ''; ?>" required>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="end_time" class="form-label">وقت النهاية <span class="text-danger">*</span></label>
                            <input type="time" name="end_time" id="end_time" class="form-control" 
                                   value="<?php echo $_POST['end_time'] ?? ''; ?>" required>
                        </div>
                    </div>
                    
                    <!-- إعدادات الاجتماع -->
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="meeting_type" class="form-label">نوع الاجتماع</label>
                            <select name="meeting_type" id="meeting_type" class="form-select">
                                <option value="zoom" <?php echo (isset($_POST['meeting_type']) && $_POST['meeting_type'] == 'zoom') ? 'selected' : ''; ?>>Zoom</option>
                                <option value="teams" <?php echo (isset($_POST['meeting_type']) && $_POST['meeting_type'] == 'teams') ? 'selected' : ''; ?>>Microsoft Teams</option>
                                <option value="meet" <?php echo (isset($_POST['meeting_type']) && $_POST['meeting_type'] == 'meet') ? 'selected' : ''; ?>>Google Meet</option>
                                <option value="other" <?php echo (isset($_POST['meeting_type']) && $_POST['meeting_type'] == 'other') ? 'selected' : ''; ?>>أخرى</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="max_attendees" class="form-label">الحد الأقصى للحضور</label>
                            <input type="number" name="max_attendees" id="max_attendees" class="form-control" 
                                   value="<?php echo $_POST['max_attendees'] ?? ''; ?>" 
                                   placeholder="اتركه فارغاً للسماح لجميع الطلاب" min="1">
                        </div>
                    </div>
                    
                    <!-- معلومات Zoom -->
                    <div id="zoomSettings" class="zoom-settings">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="zoom_meeting_id" class="form-label">معرف اجتماع Zoom</label>
                                <input type="text" name="zoom_meeting_id" id="zoom_meeting_id" class="form-control" 
                                       value="<?php echo htmlspecialchars($_POST['zoom_meeting_id'] ?? ''); ?>" 
                                       placeholder="123-456-7890">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="zoom_password" class="form-label">كلمة مرور Zoom</label>
                                <input type="text" name="zoom_password" id="zoom_password" class="form-control" 
                                       value="<?php echo htmlspecialchars($_POST['zoom_password'] ?? ''); ?>" 
                                       placeholder="كلمة المرور">
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <label for="zoom_join_url" class="form-label">رابط الانضمام</label>
                                <input type="url" name="zoom_join_url" id="zoom_join_url" class="form-control" 
                                       value="<?php echo htmlspecialchars($_POST['zoom_join_url'] ?? ''); ?>" 
                                       placeholder="https://zoom.us/j/123456789">
                            </div>
                        </div>
                    </div>
                    
                    <!-- المواد والواجبات -->
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="materials" class="form-label">المواد المطلوبة</label>
                            <textarea name="materials" id="materials" class="form-control" rows="3" 
                                      placeholder="قائمة بالمواد أو الأدوات المطلوبة للجلسة..."><?php echo htmlspecialchars($_POST['materials'] ?? ''); ?></textarea>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="homework" class="form-label">الواجب المنزلي</label>
                            <textarea name="homework" id="homework" class="form-control" rows="3" 
                                      placeholder="الواجب المطلوب إنجازه بعد الجلسة..."><?php echo htmlspecialchars($_POST['homework'] ?? ''); ?></textarea>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <button type="button" class="btn btn-outline-secondary" onclick="resetForm()">
                            <i class="fas fa-undo me-1"></i>إعادة تعيين
                        </button>
                        <div class="d-flex gap-2">
                            <button type="submit" name="save_draft" class="btn btn-outline-primary">
                                <i class="fas fa-save me-1"></i>حفظ كمسودة
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i>إنشاء الجلسة
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- الشريط الجانبي -->
    <div class="col-lg-4">
        <!-- نصائح -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-light border-bottom">
                <h6 class="mb-0">
                    <i class="fas fa-lightbulb text-warning me-2"></i>
                    نصائح لجلسة ناجحة
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        اختر عنواناً واضحاً ومفهوماً
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        حدد أهداف الجلسة مسبقاً
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        تأكد من جودة الاتصال
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        جهز المواد التعليمية مسبقاً
                    </li>
                    <li class="mb-0">
                        <i class="fas fa-check text-success me-2"></i>
                        اترك وقتاً للأسئلة والمناقشة
                    </li>
                </ul>
            </div>
        </div>
        
        <!-- معاينة سريعة -->
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-light border-bottom">
                <h6 class="mb-0">
                    <i class="fas fa-eye text-info me-2"></i>
                    معاينة سريعة
                </h6>
            </div>
            <div class="card-body">
                <div id="sessionPreview">
                    <p class="text-muted text-center">
                        <i class="fas fa-info-circle me-1"></i>
                        املأ النموذج لرؤية معاينة الجلسة
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// إظهار/إخفاء إعدادات Zoom
document.getElementById('meeting_type').addEventListener('change', function() {
    const zoomSettings = document.getElementById('zoomSettings');
    if (this.value === 'zoom') {
        zoomSettings.style.display = 'block';
    } else {
        zoomSettings.style.display = 'none';
    }
});

// إعادة تعيين النموذج
function resetForm() {
    if (confirm('هل أنت متأكد من إعادة تعيين جميع البيانات؟')) {
        document.getElementById('sessionForm').reset();
        updatePreview();
    }
}

// تحديث المعاينة
function updatePreview() {
    const title = document.getElementById('title').value;
    const date = document.getElementById('session_date').value;
    const startTime = document.getElementById('start_time').value;
    const endTime = document.getElementById('end_time').value;
    const courseSelect = document.getElementById('course_id');
    const courseName = courseSelect.options[courseSelect.selectedIndex].text;
    
    const preview = document.getElementById('sessionPreview');
    
    if (title && date && startTime && endTime && courseSelect.value) {
        preview.innerHTML = `
            <div class="session-preview">
                <h6 class="text-primary">${title}</h6>
                <p class="mb-1"><strong>الكورس:</strong> ${courseName}</p>
                <p class="mb-1"><strong>التاريخ:</strong> ${date}</p>
                <p class="mb-1"><strong>الوقت:</strong> ${startTime} - ${endTime}</p>
                <p class="mb-0 text-muted">
                    <i class="fas fa-clock me-1"></i>
                    المدة: ${calculateDuration(startTime, endTime)} دقيقة
                </p>
            </div>
        `;
    } else {
        preview.innerHTML = `
            <p class="text-muted text-center">
                <i class="fas fa-info-circle me-1"></i>
                املأ النموذج لرؤية معاينة الجلسة
            </p>
        `;
    }
}

// حساب مدة الجلسة
function calculateDuration(startTime, endTime) {
    if (!startTime || !endTime) return 0;
    
    const start = new Date(`2000-01-01 ${startTime}`);
    const end = new Date(`2000-01-01 ${endTime}`);
    const diff = end - start;
    
    return Math.round(diff / (1000 * 60));
}

// تحديث المعاينة عند تغيير البيانات
document.addEventListener('DOMContentLoaded', function() {
    const inputs = ['title', 'course_id', 'session_date', 'start_time', 'end_time'];
    inputs.forEach(id => {
        document.getElementById(id).addEventListener('input', updatePreview);
        document.getElementById(id).addEventListener('change', updatePreview);
    });
    
    // إظهار إعدادات Zoom إذا كان محدداً
    const meetingType = document.getElementById('meeting_type');
    if (meetingType.value === 'zoom') {
        document.getElementById('zoomSettings').style.display = 'block';
    }
    
    updatePreview();
});

// التحقق من صحة النموذج قبل الإرسال
document.getElementById('sessionForm').addEventListener('submit', function(e) {
    const startTime = document.getElementById('start_time').value;
    const endTime = document.getElementById('end_time').value;
    
    if (startTime && endTime && startTime >= endTime) {
        e.preventDefault();
        alert('وقت النهاية يجب أن يكون بعد وقت البداية');
        return false;
    }
    
    const sessionDate = document.getElementById('session_date').value;
    const today = new Date().toISOString().split('T')[0];
    
    if (sessionDate < today) {
        e.preventDefault();
        alert('لا يمكن إنشاء جلسة في الماضي');
        return false;
    }
});
</script>

<style>
.zoom-settings {
    display: none;
    border: 1px solid #e9ecef;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-bottom: 1rem;
    background-color: #f8f9fa;
}

.session-preview {
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 0.375rem;
    border-left: 4px solid var(--bs-primary);
}

.avatar-sm {
    width: 32px;
    height: 32px;
}
</style>

<?php include 'includes/footer.php'; ?>
