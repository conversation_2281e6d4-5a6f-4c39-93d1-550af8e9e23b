        </div> <!-- End admin-content -->
    </div> <!-- End admin-main -->

    <!-- Footer -->
    <footer class="admin-footer">
        <div class="footer-content">
            <div class="footer-left">
                <p>&copy; <?php echo date('Y'); ?> منصة التعلم الإلكتروني. جميع الحقوق محفوظة.</p>
            </div>
            <div class="footer-right">
                <span>الإصدار 2.0.1</span>
                <span class="mx-2">|</span>
                <a href="#" onclick="showSupport()">الدعم التقني</a>
                <span class="mx-2">|</span>
                <a href="#" onclick="showDocumentation()">التوثيق</a>
            </div>
        </div>
    </footer>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="loading-spinner">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <p class="mt-3">جاري التحميل...</p>
        </div>
    </div>

    <style>
        .admin-footer {
            background: white;
            border-top: 1px solid #e2e8f0;
            padding: 20px 30px;
            margin-top: auto;
        }

        .footer-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: var(--secondary-color);
            font-size: 14px;
        }

        .footer-right a {
            color: var(--primary-color);
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .footer-right a:hover {
            color: var(--info-color);
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }

        .loading-spinner {
            text-align: center;
            color: white;
        }

        /* Quick Actions Floating Button */
        .quick-actions {
            position: fixed;
            bottom: 30px;
            left: 30px;
            z-index: 1000;
        }

        .quick-actions-btn {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(45deg, var(--primary-color), var(--info-color));
            color: white;
            border: none;
            font-size: 24px;
            box-shadow: 0 4px 20px rgba(37,99,235,0.3);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .quick-actions-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 25px rgba(37,99,235,0.4);
        }

        .quick-actions-menu {
            position: absolute;
            bottom: 70px;
            left: 0;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 15px;
            min-width: 200px;
            transform: scale(0);
            transition: all 0.3s ease;
            transform-origin: bottom left;
        }

        .quick-actions-menu.show {
            transform: scale(1);
        }

        .quick-action-item {
            display: block;
            padding: 10px 15px;
            color: var(--dark-color);
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
            margin-bottom: 5px;
        }

        .quick-action-item:hover {
            background: var(--light-color);
            color: var(--primary-color);
        }

        .quick-action-item i {
            width: 20px;
            margin-left: 10px;
        }

        @media (max-width: 768px) {
            .footer-content {
                flex-direction: column;
                gap: 10px;
                text-align: center;
            }

            .quick-actions {
                bottom: 20px;
                left: 20px;
            }

            .quick-actions-btn {
                width: 50px;
                height: 50px;
                font-size: 20px;
            }
        }
    </style>

    <!-- Quick Actions -->
    <div class="quick-actions">
        <div class="quick-actions-menu" id="quickActionsMenu">
            <a href="add-instructor.php" class="quick-action-item">
                <i class="fas fa-user-plus"></i>
                إضافة مدرب
            </a>
            <a href="add-course.php" class="quick-action-item">
                <i class="fas fa-plus"></i>
                إضافة كورس
            </a>
            <a href="join-requests.php" class="quick-action-item">
                <i class="fas fa-user-check"></i>
                طلبات الانضمام
            </a>
            <a href="activity-logs.php" class="quick-action-item">
                <i class="fas fa-history"></i>
                سجل الأنشطة
            </a>
            <a href="backup.php" class="quick-action-item">
                <i class="fas fa-download"></i>
                نسخ احتياطي
            </a>
        </div>
        <button class="quick-actions-btn" onclick="toggleQuickActions()">
            <i class="fas fa-plus" id="quickActionsIcon"></i>
        </button>
    </div>

    <script>
        // Quick Actions
        function toggleQuickActions() {
            const menu = document.getElementById('quickActionsMenu');
            const icon = document.getElementById('quickActionsIcon');
            
            menu.classList.toggle('show');
            
            if (menu.classList.contains('show')) {
                icon.className = 'fas fa-times';
                icon.style.transform = 'rotate(180deg)';
            } else {
                icon.className = 'fas fa-plus';
                icon.style.transform = 'rotate(0deg)';
            }
        }

        // Close quick actions when clicking outside
        document.addEventListener('click', function(e) {
            const quickActions = document.querySelector('.quick-actions');
            if (!quickActions.contains(e.target)) {
                const menu = document.getElementById('quickActionsMenu');
                const icon = document.getElementById('quickActionsIcon');
                menu.classList.remove('show');
                icon.className = 'fas fa-plus';
                icon.style.transform = 'rotate(0deg)';
            }
        });

        // Support Modal
        function showSupport() {
            Swal.fire({
                title: 'الدعم التقني',
                html: `
                    <div class="text-start">
                        <div class="mb-3">
                            <h6><i class="fas fa-phone text-success"></i> الهاتف</h6>
                            <p class="mb-0">+966 50 123 4567</p>
                        </div>
                        <div class="mb-3">
                            <h6><i class="fas fa-envelope text-primary"></i> البريد الإلكتروني</h6>
                            <p class="mb-0"><EMAIL></p>
                        </div>
                        <div class="mb-3">
                            <h6><i class="fab fa-whatsapp text-success"></i> واتساب</h6>
                            <p class="mb-0">+966 50 123 4567</p>
                        </div>
                        <div class="mb-3">
                            <h6><i class="fas fa-clock text-warning"></i> ساعات العمل</h6>
                            <p class="mb-0">الأحد - الخميس: 9:00 ص - 6:00 م</p>
                        </div>
                    </div>
                `,
                showConfirmButton: false,
                showCloseButton: true,
                width: 400
            });
        }

        // Documentation Modal
        function showDocumentation() {
            Swal.fire({
                title: 'التوثيق والمساعدة',
                html: `
                    <div class="text-start">
                        <div class="list-group">
                            <a href="#" class="list-group-item list-group-item-action">
                                <i class="fas fa-book text-primary me-2"></i>
                                دليل المستخدم
                            </a>
                            <a href="#" class="list-group-item list-group-item-action">
                                <i class="fas fa-video text-danger me-2"></i>
                                فيديوهات تعليمية
                            </a>
                            <a href="#" class="list-group-item list-group-item-action">
                                <i class="fas fa-question-circle text-info me-2"></i>
                                الأسئلة الشائعة
                            </a>
                            <a href="#" class="list-group-item list-group-item-action">
                                <i class="fas fa-download text-success me-2"></i>
                                تحميل الدليل PDF
                            </a>
                        </div>
                    </div>
                `,
                showConfirmButton: false,
                showCloseButton: true,
                width: 400
            });
        }

        // Loading Functions
        function showLoading(message = 'جاري التحميل...') {
            const overlay = document.getElementById('loadingOverlay');
            const text = overlay.querySelector('p');
            text.textContent = message;
            overlay.style.display = 'flex';
        }

        function hideLoading() {
            document.getElementById('loadingOverlay').style.display = 'none';
        }

        // Global AJAX Setup
        $(document).ajaxStart(function() {
            showLoading();
        }).ajaxStop(function() {
            hideLoading();
        });

        // Auto-save functionality
        let autoSaveTimer;
        function enableAutoSave(formSelector, saveUrl) {
            $(formSelector + ' input, ' + formSelector + ' textarea, ' + formSelector + ' select').on('input change', function() {
                clearTimeout(autoSaveTimer);
                autoSaveTimer = setTimeout(function() {
                    const formData = $(formSelector).serialize();
                    $.post(saveUrl, formData + '&auto_save=1')
                        .done(function() {
                            showToast('تم الحفظ التلقائي', 'success');
                        });
                }, 2000);
            });
        }

        // Toast Notifications
        function showToast(message, type = 'info', duration = 3000) {
            const toast = $(`
                <div class="toast-notification toast-${type}">
                    <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'info'}-circle"></i>
                    <span>${message}</span>
                </div>
            `);

            $('body').append(toast);
            
            setTimeout(() => {
                toast.addClass('show');
            }, 100);

            setTimeout(() => {
                toast.removeClass('show');
                setTimeout(() => toast.remove(), 300);
            }, duration);
        }

        // Keyboard Shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl/Cmd + S for save
            if ((e.ctrlKey || e.metaKey) && e.key === 's') {
                e.preventDefault();
                const saveBtn = document.querySelector('[type="submit"], .btn-save');
                if (saveBtn) saveBtn.click();
            }
            
            // Ctrl/Cmd + N for new
            if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
                e.preventDefault();
                const newBtn = document.querySelector('.btn-new, [href*="add-"]');
                if (newBtn) newBtn.click();
            }
        });

        // Initialize tooltips
        $(function () {
            $('[data-bs-toggle="tooltip"]').tooltip();
        });

        // Print functionality
        function printPage() {
            window.print();
        }

        // Export functionality
        function exportData(format, url) {
            showLoading('جاري تصدير البيانات...');
            window.location.href = url + '?format=' + format;
            setTimeout(hideLoading, 2000);
        }
    </script>

    <style>
        .toast-notification {
            position: fixed;
            top: 20px;
            left: 20px;
            background: white;
            padding: 15px 20px;
            border-radius: 10px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.2);
            display: flex;
            align-items: center;
            gap: 10px;
            transform: translateX(-100%);
            transition: transform 0.3s ease;
            z-index: 10000;
        }

        .toast-notification.show {
            transform: translateX(0);
        }

        .toast-success {
            border-left: 4px solid var(--success-color);
            color: var(--success-color);
        }

        .toast-error {
            border-left: 4px solid var(--danger-color);
            color: var(--danger-color);
        }

        .toast-info {
            border-left: 4px solid var(--info-color);
            color: var(--info-color);
        }
    </style>

</body>
</html>
