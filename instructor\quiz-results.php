<?php
/**
 * نتائج الاختبارات للمدرب
 * Quiz Results for Instructor
 */

require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات المدرب
if (!isLoggedIn() || !isInstructor()) {
    header('Location: ../login.php');
    exit;
}

$quiz_id = $_GET['quiz_id'] ?? 0;

// التحقق من وجود الاختبار وأنه ينتمي للمدرب
try {
    $stmt = $conn->prepare("
        SELECT q.*, c.title as course_title
        FROM quizzes q
        INNER JOIN courses c ON q.course_id = c.id
        WHERE q.id = ? AND c.instructor_id = ?
    ");
    $stmt->execute([$quiz_id, $_SESSION['user_id']]);
    $quiz = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$quiz) {
        header('Location: quizzes.php');
        exit;
    }
} catch (PDOException $e) {
    header('Location: quizzes.php');
    exit;
}

// جلب نتائج الاختبار
try {
    $stmt = $conn->prepare("
        SELECT 
            qa.*,
            u.name as student_name,
            u.email as student_email,
            CASE 
                WHEN qa.score >= ? THEN 'نجح'
                WHEN qa.status = 'completed' THEN 'راسب'
                ELSE 'لم يكمل'
            END as result_status,
            CASE 
                WHEN qa.score >= ? THEN 'success'
                WHEN qa.status = 'completed' THEN 'danger'
                ELSE 'warning'
            END as result_class
        FROM quiz_attempts qa
        INNER JOIN users u ON qa.student_id = u.id
        WHERE qa.quiz_id = ?
        ORDER BY qa.score DESC, qa.completed_at DESC
    ");
    $stmt->execute([$quiz['passing_grade'], $quiz['passing_grade'], $quiz_id]);
    $attempts = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // إحصائيات
    $total_attempts = count($attempts);
    $completed_attempts = count(array_filter($attempts, fn($a) => $a['status'] === 'completed'));
    $passed_attempts = count(array_filter($attempts, fn($a) => $a['score'] >= $quiz['passing_grade']));
    $avg_score = $completed_attempts > 0 ? 
        array_sum(array_column(array_filter($attempts, fn($a) => $a['status'] === 'completed'), 'score')) / $completed_attempts : 0;
    
} catch (PDOException $e) {
    $attempts = [];
    $total_attempts = $completed_attempts = $passed_attempts = $avg_score = 0;
}

$pageTitle = 'نتائج الاختبار - ' . $quiz['title'];
$breadcrumbs = [
    ['title' => 'الاختبارات', 'url' => 'quizzes.php'],
    ['title' => 'النتائج']
];

include 'includes/header.php';
?>

<!-- رأس الصفحة -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="h3 mb-2">
            <i class="fas fa-chart-bar text-success me-2"></i>
            نتائج الاختبار
        </h2>
        <p class="text-muted mb-0">
            <strong><?php echo htmlspecialchars($quiz['title']); ?></strong> - 
            <?php echo htmlspecialchars($quiz['course_title']); ?>
        </p>
    </div>
    <div class="d-flex gap-2">
        <a href="quizzes.php" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>العودة للاختبارات
        </a>
        <a href="quiz-questions.php?quiz_id=<?php echo $quiz_id; ?>" class="btn btn-outline-primary">
            <i class="fas fa-list me-1"></i>إدارة الأسئلة
        </a>
        <?php if (!empty($attempts)): ?>
        <button class="btn btn-success" onclick="exportResults()">
            <i class="fas fa-download me-1"></i>تصدير النتائج
        </button>
        <?php endif; ?>
    </div>
</div>

<!-- بطاقات الإحصائيات -->
<div class="row g-4 mb-4">
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-primary bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-users text-primary fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h5 class="mb-1"><?php echo $total_attempts; ?></h5>
                        <p class="text-muted mb-0">إجمالي المحاولات</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-info bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-check-circle text-info fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h5 class="mb-1"><?php echo $completed_attempts; ?></h5>
                        <p class="text-muted mb-0">محاولات مكتملة</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-success bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-trophy text-success fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h5 class="mb-1"><?php echo $passed_attempts; ?></h5>
                        <p class="text-muted mb-0">ناجحون</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-warning bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-star text-warning fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h5 class="mb-1"><?php echo number_format($avg_score, 1); ?>%</h5>
                        <p class="text-muted mb-0">متوسط الدرجات</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- معلومات الاختبار -->
<div class="card border-0 shadow-sm mb-4">
    <div class="card-header bg-info text-white">
        <h6 class="mb-0">
            <i class="fas fa-info-circle me-2"></i>
            معلومات الاختبار
        </h6>
    </div>
    <div class="card-body">
        <div class="row g-3">
            <div class="col-md-3">
                <div class="text-center">
                    <h6 class="text-primary mb-1"><?php echo $quiz['passing_grade']; ?>%</h6>
                    <small class="text-muted">درجة النجاح</small>
                </div>
            </div>
            <div class="col-md-3">
                <div class="text-center">
                    <h6 class="text-warning mb-1"><?php echo $quiz['time_limit'] ?: 'غير محدود'; ?></h6>
                    <small class="text-muted">المدة (دقيقة)</small>
                </div>
            </div>
            <div class="col-md-3">
                <div class="text-center">
                    <h6 class="text-info mb-1"><?php echo $quiz['max_attempts']; ?></h6>
                    <small class="text-muted">عدد المحاولات المسموحة</small>
                </div>
            </div>
            <div class="col-md-3">
                <div class="text-center">
                    <h6 class="text-success mb-1"><?php echo $quiz['total_marks']; ?></h6>
                    <small class="text-muted">إجمالي النقاط</small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- جدول النتائج -->
<div class="card border-0 shadow-sm">
    <div class="card-header bg-white border-bottom">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>
                تفاصيل النتائج
            </h5>
            <div class="d-flex gap-2">
                <input type="text" class="form-control form-control-sm" placeholder="البحث في الأسماء..." 
                       id="searchInput" style="width: 200px;">
                <select class="form-select form-select-sm" id="statusFilter" style="width: 150px;">
                    <option value="">جميع الحالات</option>
                    <option value="نجح">ناجح</option>
                    <option value="راسب">راسب</option>
                    <option value="لم يكمل">لم يكمل</option>
                </select>
            </div>
        </div>
    </div>
    <div class="card-body p-0">
        <?php if (empty($attempts)): ?>
        <div class="text-center py-5">
            <i class="fas fa-chart-bar text-muted" style="font-size: 3rem;"></i>
            <h5 class="mt-3 text-muted">لا توجد محاولات</h5>
            <p class="text-muted">لم يقم أي طالب بحل هذا الاختبار بعد</p>
        </div>
        <?php else: ?>
        <div class="table-responsive">
            <table class="table table-hover mb-0" id="resultsTable">
                <thead class="table-light">
                    <tr>
                        <th>الطالب</th>
                        <th>المحاولة</th>
                        <th>النتيجة</th>
                        <th>الوقت المستغرق</th>
                        <th>تاريخ الإكمال</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($attempts as $attempt): ?>
                    <tr>
                        <td>
                            <div>
                                <h6 class="mb-1"><?php echo htmlspecialchars($attempt['student_name']); ?></h6>
                                <small class="text-muted"><?php echo htmlspecialchars($attempt['student_email']); ?></small>
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-primary">#<?php echo $attempt['attempt_number']; ?></span>
                        </td>
                        <td>
                            <?php if ($attempt['status'] === 'completed'): ?>
                            <div class="text-center">
                                <h6 class="mb-1"><?php echo number_format($attempt['score'], 1); ?>%</h6>
                                <small class="text-muted"><?php echo $attempt['score']; ?> / <?php echo $attempt['total_marks']; ?></small>
                            </div>
                            <?php else: ?>
                            <span class="text-muted">لم يكمل</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <?php if ($attempt['time_taken']): ?>
                            <span class="text-info">
                                <i class="fas fa-clock me-1"></i>
                                <?php echo gmdate('H:i:s', $attempt['time_taken']); ?>
                            </span>
                            <?php else: ?>
                            <span class="text-muted">-</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <?php if ($attempt['completed_at']): ?>
                            <small class="text-muted">
                                <?php echo date('Y/m/d H:i', strtotime($attempt['completed_at'])); ?>
                            </small>
                            <?php else: ?>
                            <small class="text-muted">
                                بدأ: <?php echo date('Y/m/d H:i', strtotime($attempt['started_at'])); ?>
                            </small>
                            <?php endif; ?>
                        </td>
                        <td>
                            <span class="badge bg-<?php echo $attempt['result_class']; ?>">
                                <?php echo $attempt['result_status']; ?>
                            </span>
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <?php if ($attempt['status'] === 'completed'): ?>
                                <a href="quiz-attempt-details.php?attempt_id=<?php echo $attempt['id']; ?>" 
                                   class="btn btn-sm btn-outline-primary" title="تفاصيل المحاولة">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <button class="btn btn-sm btn-outline-success" 
                                        onclick="downloadCertificate(<?php echo $attempt['id']; ?>)" 
                                        title="تحميل الشهادة"
                                        <?php echo $attempt['score'] < $quiz['passing_grade'] ? 'disabled' : ''; ?>>
                                    <i class="fas fa-certificate"></i>
                                </button>
                                <?php endif; ?>
                                <button class="btn btn-sm btn-outline-danger" 
                                        onclick="deleteAttempt(<?php echo $attempt['id']; ?>)" title="حذف المحاولة">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php endif; ?>
    </div>
</div>

<script>
// البحث والفلترة
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('searchInput');
    const statusFilter = document.getElementById('statusFilter');
    const table = document.getElementById('resultsTable');
    
    function filterTable() {
        const searchTerm = searchInput.value.toLowerCase();
        const statusTerm = statusFilter.value;
        const rows = table.querySelectorAll('tbody tr');
        
        rows.forEach(row => {
            const studentName = row.cells[0].textContent.toLowerCase();
            const status = row.cells[5].textContent.trim();
            
            const matchesSearch = studentName.includes(searchTerm);
            const matchesStatus = !statusTerm || status.includes(statusTerm);
            
            row.style.display = matchesSearch && matchesStatus ? '' : 'none';
        });
    }
    
    searchInput.addEventListener('input', filterTable);
    statusFilter.addEventListener('change', filterTable);
});

// تصدير النتائج
function exportResults() {
    window.open(`export-quiz-results.php?quiz_id=<?php echo $quiz_id; ?>`, '_blank');
}

// تحميل الشهادة
function downloadCertificate(attemptId) {
    window.open(`generate-certificate.php?attempt_id=${attemptId}`, '_blank');
}

// حذف محاولة
function deleteAttempt(attemptId) {
    if (confirm('هل أنت متأكد من حذف هذه المحاولة؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        // إرسال طلب حذف عبر AJAX
        fetch('delete-attempt.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                attempt_id: attemptId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ أثناء الحذف');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء الحذف');
        });
    }
}

// رسم بياني للنتائج
function showChart() {
    // يمكن إضافة Chart.js هنا لعرض الرسوم البيانية
    console.log('عرض الرسم البياني');
}
</script>

<style>
.table th {
    border-top: none;
    font-weight: 600;
    color: var(--bs-gray-700);
}

.btn-group .btn {
    border-radius: 0.375rem;
}

.btn-group .btn:not(:last-child) {
    margin-right: 0.25rem;
}

.badge {
    font-size: 0.75rem;
}

.card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1) !important;
}

@media (max-width: 768px) {
    .btn-group {
        display: flex;
        flex-direction: column;
        width: 100%;
    }

    .btn-group .btn {
        margin-right: 0;
        margin-bottom: 0.25rem;
    }

    .table-responsive {
        font-size: 0.875rem;
    }
}
</style>

<?php include 'includes/footer.php'; ?>
