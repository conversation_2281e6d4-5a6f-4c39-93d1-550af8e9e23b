<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات المدرب
if (!isLoggedIn() || !isInstructor()) {
    header('Location: ../login.php');
    exit;
}

$pageTitle = 'إدارة الدرجات';
$breadcrumbs = [
    ['title' => 'إدارة الدرجات']
];

// معالجة إضافة/تحديث الدرجات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'add_grade') {
        $course_id = $_POST['course_id'] ?? 0;
        $student_id = $_POST['student_id'] ?? 0;
        $assignment_name = $_POST['assignment_name'] ?? '';
        $grade = $_POST['grade'] ?? 0;
        $max_grade = $_POST['max_grade'] ?? 100;
        $notes = $_POST['notes'] ?? '';
        
        try {
            // التحقق من أن الكورس ينتمي للمدرب
            $stmt = $conn->prepare("SELECT id FROM courses WHERE id = ? AND instructor_id = ?");
            $stmt->execute([$course_id, $_SESSION['user_id']]);
            
            if ($stmt->fetch()) {
                $stmt = $conn->prepare("
                    INSERT INTO student_grades (course_id, student_id, assignment_name, grade, max_grade, notes, grade_date)
                    VALUES (?, ?, ?, ?, ?, ?, NOW())
                ");
                $stmt->execute([$course_id, $student_id, $assignment_name, $grade, $max_grade, $notes]);
                
                $success_message = 'تم إضافة الدرجة بنجاح';
            }
        } catch (PDOException $e) {
            $error_message = 'حدث خطأ أثناء إضافة الدرجة';
        }
    }
    
    if ($action === 'update_grade') {
        $grade_id = $_POST['grade_id'] ?? 0;
        $grade = $_POST['grade'] ?? 0;
        $notes = $_POST['notes'] ?? '';

        try {
            $stmt = $conn->prepare("
                UPDATE student_grades sg
                INNER JOIN courses c ON sg.course_id = c.id
                SET sg.grade = ?, sg.notes = ?, sg.updated_at = NOW()
                WHERE sg.id = ? AND c.instructor_id = ?
            ");
            $stmt->execute([$grade, $notes, $grade_id, $_SESSION['user_id']]);

            $success_message = 'تم تحديث الدرجة بنجاح';
        } catch (PDOException $e) {
            $error_message = 'حدث خطأ أثناء تحديث الدرجة';
        }
    }

    if ($action === 'delete_grade') {
        $grade_id = $_POST['grade_id'] ?? 0;

        try {
            $stmt = $conn->prepare("
                DELETE sg FROM student_grades sg
                INNER JOIN courses c ON sg.course_id = c.id
                WHERE sg.id = ? AND c.instructor_id = ?
            ");
            $stmt->execute([$grade_id, $_SESSION['user_id']]);

            $success_message = 'تم حذف الدرجة بنجاح';
        } catch (PDOException $e) {
            $error_message = 'حدث خطأ أثناء حذف الدرجة';
        }
    }

    if ($action === 'bulk_add_grades') {
        $course_id = $_POST['course_id'] ?? 0;
        $assignment_name = $_POST['assignment_name'] ?? '';
        $max_grade = $_POST['max_grade'] ?? 100;
        $student_ids = $_POST['bulk_student_ids'] ?? [];
        $grades = $_POST['bulk_grades'] ?? [];
        $notes = $_POST['bulk_notes'] ?? [];

        try {
            // التحقق من أن الكورس ينتمي للمدرب
            $stmt = $conn->prepare("SELECT id FROM courses WHERE id = ? AND instructor_id = ?");
            $stmt->execute([$course_id, $_SESSION['user_id']]);

            if ($stmt->fetch()) {
                $conn->beginTransaction();

                $stmt = $conn->prepare("
                    INSERT INTO student_grades (course_id, student_id, assignment_name, grade, max_grade, notes, grade_date)
                    VALUES (?, ?, ?, ?, ?, ?, NOW())
                ");

                $added_count = 0;
                for ($i = 0; $i < count($student_ids); $i++) {
                    if (isset($grades[$i]) && $grades[$i] !== '') {
                        $stmt->execute([
                            $course_id,
                            $student_ids[$i],
                            $assignment_name,
                            $grades[$i],
                            $max_grade,
                            $notes[$i] ?? ''
                        ]);
                        $added_count++;
                    }
                }

                $conn->commit();
                $success_message = "تم إضافة {$added_count} درجة بنجاح";
            } else {
                $error_message = 'الكورس غير موجود أو لا تملك صلاحية للوصول إليه';
            }
        } catch (PDOException $e) {
            $conn->rollBack();
            $error_message = 'حدث خطأ أثناء إضافة الدرجات: ' . $e->getMessage();
        }
    }
}

// جلب الكورسات الخاصة بالمدرب
try {
    $stmt = $conn->prepare("
        SELECT id, title 
        FROM courses 
        WHERE instructor_id = ? AND status = 'active'
        ORDER BY title
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $instructor_courses = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $instructor_courses = [];
}

// جلب الدرجات
$selected_course = $_GET['course_id'] ?? '';
$grades = [];
$course_students = [];

if ($selected_course) {
    try {
        // جلب طلاب الكورس
        $stmt = $conn->prepare("
            SELECT DISTINCT u.id, u.name, u.email
            FROM users u
            INNER JOIN course_enrollments ce ON u.id = ce.student_id
            INNER JOIN courses c ON ce.course_id = c.id
            WHERE c.id = ? AND c.instructor_id = ? AND ce.status = 'active'
            ORDER BY u.name
        ");
        $stmt->execute([$selected_course, $_SESSION['user_id']]);
        $course_students = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // جلب الدرجات
        $stmt = $conn->prepare("
            SELECT 
                sg.*,
                u.name as student_name,
                u.email as student_email,
                c.title as course_title
            FROM student_grades sg
            INNER JOIN users u ON sg.student_id = u.id
            INNER JOIN courses c ON sg.course_id = c.id
            WHERE c.id = ? AND c.instructor_id = ?
            ORDER BY u.name, sg.assignment_name, sg.grade_date DESC
        ");
        $stmt->execute([$selected_course, $_SESSION['user_id']]);
        $grades = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
    } catch (PDOException $e) {
        $error_message = 'حدث خطأ أثناء جلب البيانات';
    }
}

include 'includes/header.php';
?>

<!-- رأس الصفحة -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="h3 mb-2">
            <i class="fas fa-star text-warning me-2"></i>
            إدارة الدرجات
        </h2>
        <p class="text-muted mb-0">إضافة وإدارة درجات الطلاب في الكورسات</p>
    </div>
    <div class="d-flex gap-2">
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addGradeModal">
            <i class="fas fa-plus me-1"></i>إضافة درجة
        </button>
        <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#bulkGradeModal">
            <i class="fas fa-users me-1"></i>إضافة درجات متعددة
        </button>
        <button class="btn btn-outline-info" onclick="showStatistics()">
            <i class="fas fa-chart-bar me-1"></i>إحصائيات
        </button>
        <button class="btn btn-outline-success" onclick="exportGrades()">
            <i class="fas fa-download me-1"></i>تصدير
        </button>
    </div>
</div>

<!-- رسائل النجاح والخطأ -->
<?php if (isset($success_message)): ?>
<div class="alert alert-success alert-dismissible fade show" role="alert">
    <i class="fas fa-check-circle me-2"></i>
    <?php echo $success_message; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<?php if (isset($error_message)): ?>
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <i class="fas fa-exclamation-triangle me-2"></i>
    <?php echo $error_message; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<!-- فلتر الكورسات -->
<div class="card border-0 shadow-sm mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3 align-items-end">
            <div class="col-md-6">
                <label for="course_id" class="form-label">اختر الكورس</label>
                <select name="course_id" id="course_id" class="form-select" onchange="this.form.submit()">
                    <option value="">-- اختر كورس --</option>
                    <?php foreach ($instructor_courses as $course): ?>
                    <option value="<?php echo $course['id']; ?>" 
                            <?php echo $selected_course == $course['id'] ? 'selected' : ''; ?>>
                        <?php echo htmlspecialchars($course['title']); ?>
                    </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-6">
                <div class="d-flex gap-2">
                    <input type="text" class="form-control" placeholder="البحث في الدرجات..." id="searchGrades">
                    <select class="form-select" id="assignmentFilter" style="max-width: 200px;">
                        <option value="">جميع الواجبات</option>
                    </select>
                </div>
            </div>
        </form>
    </div>
</div>

<?php if ($selected_course && !empty($grades)): ?>
<!-- جدول الدرجات -->
<div class="card border-0 shadow-sm">
    <div class="card-header bg-white border-bottom">
        <h5 class="mb-0">
            <i class="fas fa-table me-2"></i>
            درجات الطلاب
        </h5>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0" id="gradesTable">
                <thead class="table-light">
                    <tr>
                        <th>الطالب</th>
                        <th>الواجب/الاختبار</th>
                        <th>الدرجة</th>
                        <th>النسبة المئوية</th>
                        <th>تاريخ التقييم</th>
                        <th>ملاحظات</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($grades as $grade): ?>
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar-sm bg-primary bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center me-2">
                                    <i class="fas fa-user text-primary"></i>
                                </div>
                                <div>
                                    <h6 class="mb-0"><?php echo htmlspecialchars($grade['student_name']); ?></h6>
                                    <small class="text-muted"><?php echo htmlspecialchars($grade['student_email']); ?></small>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="fw-medium"><?php echo htmlspecialchars($grade['assignment_name']); ?></span>
                        </td>
                        <td>
                            <span class="badge bg-primary fs-6">
                                <?php echo $grade['grade']; ?>/<?php echo $grade['max_grade']; ?>
                            </span>
                        </td>
                        <td>
                            <?php 
                            $percentage = ($grade['grade'] / $grade['max_grade']) * 100;
                            $badge_class = $percentage >= 90 ? 'success' : ($percentage >= 70 ? 'warning' : ($percentage >= 50 ? 'info' : 'danger'));
                            ?>
                            <span class="badge bg-<?php echo $badge_class; ?>">
                                <?php echo number_format($percentage, 1); ?>%
                            </span>
                        </td>
                        <td>
                            <small class="text-muted">
                                <?php echo date('Y-m-d H:i', strtotime($grade['grade_date'])); ?>
                            </small>
                        </td>
                        <td>
                            <?php if ($grade['notes']): ?>
                            <span class="text-muted" title="<?php echo htmlspecialchars($grade['notes']); ?>">
                                <?php echo mb_substr(htmlspecialchars($grade['notes']), 0, 30) . (mb_strlen($grade['notes']) > 30 ? '...' : ''); ?>
                            </span>
                            <?php else: ?>
                            <span class="text-muted">-</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <div class="d-flex gap-1">
                                <button class="btn btn-sm btn-outline-primary" 
                                        onclick="editGrade(<?php echo $grade['id']; ?>, <?php echo $grade['grade']; ?>, '<?php echo htmlspecialchars($grade['notes'], ENT_QUOTES); ?>')"
                                        title="تعديل الدرجة">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-danger" 
                                        onclick="deleteGrade(<?php echo $grade['id']; ?>)"
                                        title="حذف الدرجة">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<?php elseif ($selected_course): ?>
<div class="card border-0 shadow-sm">
    <div class="card-body text-center py-5">
        <i class="fas fa-chart-bar text-muted" style="font-size: 3rem;"></i>
        <h5 class="mt-3 text-muted">لا توجد درجات مسجلة</h5>
        <p class="text-muted">ابدأ بإضافة درجات للطلاب في هذا الكورس</p>
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addGradeModal">
            <i class="fas fa-plus me-1"></i>إضافة أول درجة
        </button>
    </div>
</div>

<?php else: ?>
<div class="card border-0 shadow-sm">
    <div class="card-body text-center py-5">
        <i class="fas fa-graduation-cap text-muted" style="font-size: 3rem;"></i>
        <h5 class="mt-3 text-muted">اختر كورساً لعرض الدرجات</h5>
        <p class="text-muted">حدد كورساً من القائمة أعلاه لبدء إدارة درجات الطلاب</p>
    </div>
</div>
<?php endif; ?>

<!-- Modal إضافة درجة -->
<div class="modal fade" id="addGradeModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة درجة جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="add_grade">
                    
                    <div class="mb-3">
                        <label for="modal_course_id" class="form-label">الكورس</label>
                        <select name="course_id" id="modal_course_id" class="form-select" required onchange="loadCourseStudents(this.value)">
                            <option value="">-- اختر كورس --</option>
                            <?php foreach ($instructor_courses as $course): ?>
                            <option value="<?php echo $course['id']; ?>" 
                                    <?php echo $selected_course == $course['id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($course['title']); ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="student_id" class="form-label">الطالب</label>
                        <select name="student_id" id="student_id" class="form-select" required>
                            <option value="">-- اختر طالب --</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="assignment_name" class="form-label">اسم الواجب/الاختبار</label>
                        <input type="text" name="assignment_name" id="assignment_name" class="form-control" required>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="grade" class="form-label">الدرجة المحصلة</label>
                                <input type="number" name="grade" id="grade" class="form-control" min="0" step="0.01" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="max_grade" class="form-label">الدرجة الكاملة</label>
                                <input type="number" name="max_grade" id="max_grade" class="form-control" min="1" step="0.01" value="100" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات (اختياري)</label>
                        <textarea name="notes" id="notes" class="form-control" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">إضافة الدرجة</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal تعديل درجة -->
<div class="modal fade" id="editGradeModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تعديل الدرجة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="update_grade">
                    <input type="hidden" name="grade_id" id="edit_grade_id">
                    
                    <div class="mb-3">
                        <label for="edit_grade" class="form-label">الدرجة</label>
                        <input type="number" name="grade" id="edit_grade" class="form-control" min="0" step="0.01" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="edit_notes" class="form-label">ملاحظات</label>
                        <textarea name="notes" id="edit_notes" class="form-control" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal إضافة درجات متعددة -->
<div class="modal fade" id="bulkGradeModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة درجات لجميع الطلاب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="bulkGradeForm">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="bulk_course_id" class="form-label">الكورس</label>
                            <select id="bulk_course_id" class="form-select" required onchange="loadBulkStudents(this.value)">
                                <option value="">-- اختر كورس --</option>
                                <?php foreach ($instructor_courses as $course): ?>
                                <option value="<?php echo $course['id']; ?>"
                                        <?php echo $selected_course == $course['id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($course['title']); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="bulk_assignment_name" class="form-label">اسم الواجب/الاختبار</label>
                            <input type="text" id="bulk_assignment_name" class="form-control" required>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="bulk_max_grade" class="form-label">الدرجة الكاملة</label>
                            <input type="number" id="bulk_max_grade" class="form-control" min="1" step="0.01" value="100" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">إجراءات سريعة</label>
                            <div class="d-flex gap-2">
                                <button type="button" class="btn btn-sm btn-outline-success" onclick="fillAllGrades('full')">
                                    الدرجة الكاملة
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-warning" onclick="fillAllGrades('half')">
                                    نصف الدرجة
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-danger" onclick="fillAllGrades('zero')">
                                    صفر
                                </button>
                            </div>
                        </div>
                    </div>

                    <div id="studentsGradesList">
                        <p class="text-muted text-center">اختر كورساً لعرض الطلاب</p>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveBulkGrades()">
                    <i class="fas fa-save me-1"></i>حفظ جميع الدرجات
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal الإحصائيات -->
<div class="modal fade" id="statisticsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إحصائيات الدرجات</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="statisticsContent">
                    <p class="text-center">جاري تحميل الإحصائيات...</p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// تحميل طلاب الكورس
function loadCourseStudents(courseId) {
    const studentSelect = document.getElementById('student_id');
    studentSelect.innerHTML = '<option value="">جاري التحميل...</option>';

    if (!courseId) {
        studentSelect.innerHTML = '<option value="">-- اختر طالب --</option>';
        return;
    }

    // استخدام AJAX لجلب طلاب الكورس
    fetch(`get-course-students.php?course_id=${courseId}`)
        .then(response => response.json())
        .then(data => {
            studentSelect.innerHTML = '<option value="">-- اختر طالب --</option>';

            if (data.success && data.students) {
                data.students.forEach(student => {
                    studentSelect.innerHTML += `<option value="${student.id}">${student.name}</option>`;
                });
            } else {
                studentSelect.innerHTML += '<option value="">لا توجد طلاب مسجلون</option>';
            }
        })
        .catch(error => {
            console.error('خطأ في تحميل الطلاب:', error);
            studentSelect.innerHTML = '<option value="">خطأ في التحميل</option>';
        });
}

// تعديل درجة
function editGrade(gradeId, currentGrade, currentNotes) {
    document.getElementById('edit_grade_id').value = gradeId;
    document.getElementById('edit_grade').value = currentGrade;
    document.getElementById('edit_notes').value = currentNotes;
    
    new bootstrap.Modal(document.getElementById('editGradeModal')).show();
}

// حذف درجة
function deleteGrade(gradeId) {
    if (confirm('هل أنت متأكد من حذف هذه الدرجة؟\n\nلا يمكن التراجع عن هذا الإجراء.')) {
        // إنشاء نموذج مخفي لإرسال طلب الحذف
        const form = document.createElement('form');
        form.method = 'POST';
        form.style.display = 'none';

        const actionInput = document.createElement('input');
        actionInput.type = 'hidden';
        actionInput.name = 'action';
        actionInput.value = 'delete_grade';

        const gradeIdInput = document.createElement('input');
        gradeIdInput.type = 'hidden';
        gradeIdInput.name = 'grade_id';
        gradeIdInput.value = gradeId;

        form.appendChild(actionInput);
        form.appendChild(gradeIdInput);
        document.body.appendChild(form);

        form.submit();
    }
}

// تصدير الدرجات
function exportGrades() {
    const courseId = document.getElementById('course_id').value;
    if (!courseId) {
        alert('يرجى اختيار كورس أولاً');
        return;
    }

    // إظهار خيارات التصدير
    const options = [
        { text: 'تصدير CSV', url: `export-grades.php?course_id=${courseId}&format=csv&download=1` },
        { text: 'تصدير Excel', url: `export-grades.php?course_id=${courseId}&format=excel&download=1` },
        { text: 'عرض تقرير مفصل', url: `export-grades.php?course_id=${courseId}&format=pdf` }
    ];

    let optionsHtml = 'اختر تنسيق التصدير:\n\n';
    options.forEach((option, index) => {
        optionsHtml += `${index + 1}. ${option.text}\n`;
    });

    const choice = prompt(optionsHtml + '\nأدخل رقم الخيار (1-3):');

    if (choice && choice >= 1 && choice <= 3) {
        window.open(options[choice - 1].url, '_blank');
    }
}

// البحث في الدرجات
document.getElementById('searchGrades').addEventListener('input', function() {
    const searchTerm = this.value.toLowerCase();
    const rows = document.querySelectorAll('#gradesTable tbody tr');
    
    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        row.style.display = text.includes(searchTerm) ? '' : 'none';
    });
});

// تحميل طلاب الكورس للدرجات المتعددة
function loadBulkStudents(courseId) {
    const container = document.getElementById('studentsGradesList');

    if (!courseId) {
        container.innerHTML = '<p class="text-muted text-center">اختر كورساً لعرض الطلاب</p>';
        return;
    }

    container.innerHTML = '<p class="text-center">جاري تحميل الطلاب...</p>';

    fetch(`get-course-students.php?course_id=${courseId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.students && data.students.length > 0) {
                let html = '<div class="table-responsive"><table class="table table-sm"><thead><tr><th>الطالب</th><th>الدرجة</th><th>ملاحظات</th></tr></thead><tbody>';

                data.students.forEach(student => {
                    html += `
                        <tr>
                            <td>
                                <input type="hidden" name="student_ids[]" value="${student.id}">
                                <strong>${student.name}</strong><br>
                                <small class="text-muted">${student.email}</small>
                            </td>
                            <td>
                                <input type="number" name="grades[]" class="form-control form-control-sm grade-input"
                                       min="0" step="0.01" placeholder="الدرجة">
                            </td>
                            <td>
                                <input type="text" name="notes[]" class="form-control form-control-sm"
                                       placeholder="ملاحظات (اختياري)">
                            </td>
                        </tr>
                    `;
                });

                html += '</tbody></table></div>';
                container.innerHTML = html;
            } else {
                container.innerHTML = '<p class="text-muted text-center">لا توجد طلاب مسجلون في هذا الكورس</p>';
            }
        })
        .catch(error => {
            console.error('خطأ في تحميل الطلاب:', error);
            container.innerHTML = '<p class="text-danger text-center">خطأ في تحميل الطلاب</p>';
        });
}

// ملء جميع الدرجات
function fillAllGrades(type) {
    const maxGrade = parseFloat(document.getElementById('bulk_max_grade').value) || 100;
    const gradeInputs = document.querySelectorAll('.grade-input');

    let value = 0;
    switch(type) {
        case 'full':
            value = maxGrade;
            break;
        case 'half':
            value = maxGrade / 2;
            break;
        case 'zero':
            value = 0;
            break;
    }

    gradeInputs.forEach(input => {
        input.value = value;
    });
}

// حفظ الدرجات المتعددة
function saveBulkGrades() {
    const courseId = document.getElementById('bulk_course_id').value;
    const assignmentName = document.getElementById('bulk_assignment_name').value;
    const maxGrade = document.getElementById('bulk_max_grade').value;

    if (!courseId || !assignmentName || !maxGrade) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
    }

    const studentIds = Array.from(document.querySelectorAll('input[name="student_ids[]"]')).map(input => input.value);
    const grades = Array.from(document.querySelectorAll('input[name="grades[]"]')).map(input => input.value);
    const notes = Array.from(document.querySelectorAll('input[name="notes[]"]')).map(input => input.value);

    // التحقق من وجود درجات
    const validGrades = grades.filter(grade => grade && grade.trim() !== '');
    if (validGrades.length === 0) {
        alert('يرجى إدخال درجة واحدة على الأقل');
        return;
    }

    // إرسال البيانات
    const formData = new FormData();
    formData.append('action', 'bulk_add_grades');
    formData.append('course_id', courseId);
    formData.append('assignment_name', assignmentName);
    formData.append('max_grade', maxGrade);

    studentIds.forEach((id, index) => {
        if (grades[index] && grades[index].trim() !== '') {
            formData.append('bulk_student_ids[]', id);
            formData.append('bulk_grades[]', grades[index]);
            formData.append('bulk_notes[]', notes[index] || '');
        }
    });

    fetch(window.location.href, {
        method: 'POST',
        body: formData
    })
    .then(response => response.text())
    .then(data => {
        // إعادة تحميل الصفحة لعرض النتائج
        window.location.reload();
    })
    .catch(error => {
        console.error('خطأ في حفظ الدرجات:', error);
        alert('حدث خطأ أثناء حفظ الدرجات');
    });
}

// عرض الإحصائيات
function showStatistics() {
    const courseId = document.getElementById('course_id').value;
    if (!courseId) {
        alert('يرجى اختيار كورس أولاً');
        return;
    }

    const modal = new bootstrap.Modal(document.getElementById('statisticsModal'));
    modal.show();

    // جلب الإحصائيات
    fetch(`get-grade-statistics.php?course_id=${courseId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayStatistics(data.statistics);
            } else {
                document.getElementById('statisticsContent').innerHTML = '<p class="text-danger">خطأ في تحميل الإحصائيات</p>';
            }
        })
        .catch(error => {
            console.error('خطأ في تحميل الإحصائيات:', error);
            document.getElementById('statisticsContent').innerHTML = '<p class="text-danger">خطأ في تحميل الإحصائيات</p>';
        });
}

// عرض الإحصائيات
function displayStatistics(stats) {
    const content = document.getElementById('statisticsContent');
    content.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <div class="card border-0 bg-light">
                    <div class="card-body text-center">
                        <h5 class="text-primary">${stats.total_students}</h5>
                        <small>إجمالي الطلاب</small>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card border-0 bg-light">
                    <div class="card-body text-center">
                        <h5 class="text-success">${stats.total_assignments}</h5>
                        <small>إجمالي الواجبات</small>
                    </div>
                </div>
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-md-4">
                <div class="card border-0 bg-light">
                    <div class="card-body text-center">
                        <h5 class="text-info">${stats.average_grade}%</h5>
                        <small>متوسط الدرجات</small>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card border-0 bg-light">
                    <div class="card-body text-center">
                        <h5 class="text-warning">${stats.highest_grade}%</h5>
                        <small>أعلى درجة</small>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card border-0 bg-light">
                    <div class="card-body text-center">
                        <h5 class="text-danger">${stats.lowest_grade}%</h5>
                        <small>أقل درجة</small>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// تحميل طلاب الكورس المحدد عند فتح الصفحة
document.addEventListener('DOMContentLoaded', function() {
    const courseSelect = document.getElementById('modal_course_id');
    if (courseSelect && courseSelect.value) {
        loadCourseStudents(courseSelect.value);
    }

    const bulkCourseSelect = document.getElementById('bulk_course_id');
    if (bulkCourseSelect && bulkCourseSelect.value) {
        loadBulkStudents(bulkCourseSelect.value);
    }
});
</script>

<?php include 'includes/footer.php'; ?>
