<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات المدرب
if (!isLoggedIn() || !isInstructor()) {
    header('Location: ../login.php');
    exit;
}

$course_id = isset($_GET['course_id']) ? (int)$_GET['course_id'] : 0;
$error = '';
$success = '';

// التحقق من ملكية الكورس
try {
    $stmt = $conn->prepare("SELECT * FROM courses WHERE id = ? AND instructor_id = ?");
    $stmt->execute([$course_id, $_SESSION['user_id']]);
    $course = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$course) {
        header('Location: dashboard.php');
        exit;
    }
} catch (PDOException $e) {
    $error = 'حدث خطأ في التحقق من الكورس';
}

// معالجة إضافة فصل جديد
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_chapter'])) {
    $title = trim($_POST['chapter_title']);
    $description = trim($_POST['chapter_description']);
    
    if (empty($title)) {
        $error = 'يرجى إدخال عنوان الفصل';
    } else {
        try {
            // جلب آخر ترتيب
            $stmt = $conn->prepare("SELECT MAX(chapter_order) FROM course_chapters WHERE course_id = ?");
            $stmt->execute([$course_id]);
            $max_order = $stmt->fetchColumn() ?: 0;
            
            $stmt = $conn->prepare("
                INSERT INTO course_chapters (course_id, title, description, chapter_order, created_at)
                VALUES (?, ?, ?, ?, NOW())
            ");
            $stmt->execute([$course_id, $title, $description, $max_order + 1]);
            $success = 'تم إضافة الفصل بنجاح';
        } catch (PDOException $e) {
            $error = 'حدث خطأ أثناء إضافة الفصل';
        }
    }
}

// معالجة إضافة درس جديد
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_lesson'])) {
    $chapter_id = (int)$_POST['chapter_id'];
    $title = trim($_POST['lesson_title']);
    $description = trim($_POST['lesson_description']);
    $is_free = isset($_POST['is_free']) ? 1 : 0;
    
    if (empty($title)) {
        $error = 'يرجى إدخال عنوان الدرس';
    } else {
        try {
            // جلب آخر ترتيب في الفصل
            $stmt = $conn->prepare("SELECT MAX(lesson_order) FROM course_lessons WHERE chapter_id = ?");
            $stmt->execute([$chapter_id]);
            $max_order = $stmt->fetchColumn() ?: 0;
            
            $stmt = $conn->prepare("
                INSERT INTO course_lessons (course_id, chapter_id, title, description, lesson_order, is_free, created_at)
                VALUES (?, ?, ?, ?, ?, ?, NOW())
            ");
            $stmt->execute([$course_id, $chapter_id, $title, $description, $max_order + 1, $is_free]);
            $success = 'تم إضافة الدرس بنجاح';
        } catch (PDOException $e) {
            $error = 'حدث خطأ أثناء إضافة الدرس';
        }
    }
}

// معالجة رفع فيديو
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['upload_video'])) {
    $lesson_id = (int)$_POST['lesson_id'];
    
    if (isset($_FILES['video_file']) && $_FILES['video_file']['error'] === UPLOAD_ERR_OK) {
        $upload_dir = '../uploads/videos/';
        $file_name = time() . '_' . basename($_FILES['video_file']['name']);
        $target_path = $upload_dir . $file_name;
        
        // التحقق من نوع الملف
        $allowed_types = ['video/mp4', 'video/avi', 'video/mov', 'video/wmv'];
        if (in_array($_FILES['video_file']['type'], $allowed_types)) {
            if (move_uploaded_file($_FILES['video_file']['tmp_name'], $target_path)) {
                try {
                    $stmt = $conn->prepare("
                        UPDATE course_lessons 
                        SET video_file_path = ?, video_size = ?, updated_at = NOW()
                        WHERE id = ? AND course_id = ?
                    ");
                    $stmt->execute([$target_path, $_FILES['video_file']['size'], $lesson_id, $course_id]);
                    $success = 'تم رفع الفيديو بنجاح';
                } catch (PDOException $e) {
                    $error = 'حدث خطأ أثناء حفظ معلومات الفيديو';
                }
            } else {
                $error = 'فشل في رفع الفيديو';
            }
        } else {
            $error = 'نوع الملف غير مدعوم. يرجى رفع ملف فيديو صالح';
        }
    } else {
        $error = 'يرجى اختيار ملف فيديو';
    }
}

// جلب فصول الكورس
try {
    $stmt = $conn->prepare("
        SELECT ch.*, 
               (SELECT COUNT(*) FROM course_lessons WHERE chapter_id = ch.id) as lessons_count
        FROM course_chapters ch
        WHERE ch.course_id = ?
        ORDER BY ch.chapter_order
    ");
    $stmt->execute([$course_id]);
    $chapters = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $chapters = [];
}

$pageTitle = 'إدارة محتوى الكورس';
include 'includes/header.php';

?>

<style>
.chapter-card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.lesson-item {
    border: 1px solid #e9ecef;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 10px;
    transition: all 0.3s ease;
}

.lesson-item:hover {
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
}

.video-upload-area {
    border: 2px dashed #007bff;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    background: #f8f9fa;
    transition: all 0.3s ease;
}

.video-upload-area:hover {
    background: #e9ecef;
}

.progress-bar-custom {
    height: 25px;
    border-radius: 15px;
}

.btn-floating {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    font-size: 24px;
    z-index: 1000;
}
</style>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2>إدارة محتوى الكورس</h2>
                    <p class="text-muted"><?php echo htmlspecialchars($course['title']); ?></p>
                </div>
                <div>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addChapterModal">
                        <i class="fas fa-plus me-2"></i>
                        إضافة فصل جديد
                    </button>
                </div>
            </div>

            <?php if ($error): ?>
                <div class="alert alert-danger"><?php echo $error; ?></div>
            <?php endif; ?>
            <?php if ($success): ?>
                <div class="alert alert-success"><?php echo $success; ?></div>
            <?php endif; ?>

            <!-- عرض الفصول والدروس -->
            <?php if (empty($chapters)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-book-open fa-5x text-muted mb-3"></i>
                    <h4>لا توجد فصول بعد</h4>
                    <p class="text-muted">ابدأ بإضافة فصل جديد لتنظيم محتوى الكورس</p>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addChapterModal">
                        إضافة أول فصل
                    </button>
                </div>
            <?php else: ?>
                <?php foreach ($chapters as $chapter): ?>
                    <div class="chapter-card card">
                        <div class="card-header bg-primary text-white">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">
                                    <i class="fas fa-book me-2"></i>
                                    <?php echo htmlspecialchars($chapter['title']); ?>
                                    <span class="badge bg-light text-dark ms-2"><?php echo $chapter['lessons_count']; ?> دروس</span>
                                </h5>
                                <button class="btn btn-light btn-sm" onclick="addLesson(<?php echo $chapter['id']; ?>)">
                                    <i class="fas fa-plus me-1"></i>
                                    إضافة درس
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <p class="text-muted"><?php echo htmlspecialchars($chapter['description']); ?></p>
                            
                            <!-- دروس الفصل -->
                            <?php
                            try {
                                $stmt = $conn->prepare("
                                    SELECT * FROM course_lessons 
                                    WHERE chapter_id = ? 
                                    ORDER BY lesson_order
                                ");
                                $stmt->execute([$chapter['id']]);
                                $lessons = $stmt->fetchAll(PDO::FETCH_ASSOC);
                            } catch (PDOException $e) {
                                $lessons = [];
                            }
                            ?>
                            
                            <?php if (empty($lessons)): ?>
                                <div class="text-center py-3">
                                    <i class="fas fa-video fa-2x text-muted mb-2"></i>
                                    <p class="text-muted">لا توجد دروس في هذا الفصل</p>
                                    <button class="btn btn-outline-primary btn-sm" onclick="addLesson(<?php echo $chapter['id']; ?>)">
                                        إضافة أول درس
                                    </button>
                                </div>
                            <?php else: ?>
                                <?php foreach ($lessons as $lesson): ?>
                                    <div class="lesson-item">
                                        <div class="row align-items-center">
                                            <div class="col-md-6">
                                                <h6 class="mb-1">
                                                    <i class="fas fa-play-circle me-2 text-primary"></i>
                                                    <?php echo htmlspecialchars($lesson['title']); ?>
                                                    <?php if ($lesson['is_free']): ?>
                                                        <span class="badge bg-success">مجاني</span>
                                                    <?php endif; ?>
                                                </h6>
                                                <p class="text-muted mb-0"><?php echo htmlspecialchars($lesson['description']); ?></p>
                                            </div>
                                            <div class="col-md-3">
                                                <?php if ($lesson['video_file_path']): ?>
                                                    <span class="badge bg-success">
                                                        <i class="fas fa-check me-1"></i>
                                                        فيديو مرفوع
                                                    </span>
                                                    <br>
                                                    <small class="text-muted">
                                                        الحجم: <?php echo formatFileSize($lesson['video_size']); ?>
                                                    </small>
                                                <?php else: ?>
                                                    <span class="badge bg-warning">
                                                        <i class="fas fa-exclamation me-1"></i>
                                                        بحاجة لفيديو
                                                    </span>
                                                <?php endif; ?>
                                            </div>
                                            <div class="col-md-3 text-end">
                                                <?php if (!$lesson['video_file_path']): ?>
                                                    <button class="btn btn-primary btn-sm" onclick="uploadVideo(<?php echo $lesson['id']; ?>)">
                                                        <i class="fas fa-upload me-1"></i>
                                                        رفع فيديو
                                                    </button>
                                                <?php else: ?>
                                                    <button class="btn btn-success btn-sm" onclick="viewVideo(<?php echo $lesson['id']; ?>)">
                                                        <i class="fas fa-eye me-1"></i>
                                                        عرض
                                                    </button>
                                                    <button class="btn btn-warning btn-sm" onclick="uploadVideo(<?php echo $lesson['id']; ?>)">
                                                        <i class="fas fa-edit me-1"></i>
                                                        تغيير
                                                    </button>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Modal إضافة فصل -->
<div class="modal fade" id="addChapterModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة فصل جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="chapter_title" class="form-label">عنوان الفصل</label>
                        <input type="text" class="form-control" id="chapter_title" name="chapter_title" required>
                    </div>
                    <div class="mb-3">
                        <label for="chapter_description" class="form-label">وصف الفصل</label>
                        <textarea class="form-control" id="chapter_description" name="chapter_description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" name="add_chapter" class="btn btn-primary">إضافة الفصل</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal إضافة درس -->
<div class="modal fade" id="addLessonModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة درس جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" id="lesson_chapter_id" name="chapter_id">
                    <div class="mb-3">
                        <label for="lesson_title" class="form-label">عنوان الدرس</label>
                        <input type="text" class="form-control" id="lesson_title" name="lesson_title" required>
                    </div>
                    <div class="mb-3">
                        <label for="lesson_description" class="form-label">وصف الدرس</label>
                        <textarea class="form-control" id="lesson_description" name="lesson_description" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_free" name="is_free">
                            <label class="form-check-label" for="is_free">
                                درس مجاني (يمكن للجميع مشاهدته)
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" name="add_lesson" class="btn btn-primary">إضافة الدرس</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal رفع فيديو -->
<div class="modal fade" id="uploadVideoModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">رفع فيديو الدرس</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" enctype="multipart/form-data">
                <div class="modal-body">
                    <input type="hidden" id="video_lesson_id" name="lesson_id">
                    
                    <div class="video-upload-area">
                        <i class="fas fa-cloud-upload-alt fa-3x text-primary mb-3"></i>
                        <h5>اختر ملف الفيديو</h5>
                        <p class="text-muted">الأنواع المدعومة: MP4, AVI, MOV, WMV</p>
                        <input type="file" class="form-control" name="video_file" accept="video/*" required>
                    </div>
                    
                    <div class="mt-3">
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            الحد الأقصى لحجم الملف: 500 ميجابايت
                        </small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" name="upload_video" class="btn btn-primary">
                        <i class="fas fa-upload me-1"></i>
                        رفع الفيديو
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function addLesson(chapterId) {
    document.getElementById('lesson_chapter_id').value = chapterId;
    new bootstrap.Modal(document.getElementById('addLessonModal')).show();
}

function uploadVideo(lessonId) {
    document.getElementById('video_lesson_id').value = lessonId;
    new bootstrap.Modal(document.getElementById('uploadVideoModal')).show();
}

function viewVideo(lessonId) {
    window.open('view-lesson.php?lesson_id=' + lessonId, '_blank');
}

// تحديث شريط التقدم أثناء رفع الفيديو
document.querySelector('form[enctype="multipart/form-data"]').addEventListener('submit', function() {
    const submitBtn = this.querySelector('button[type="submit"]');
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> جاري الرفع...';
    submitBtn.disabled = true;
});
</script>

<?php
function formatFileSize($bytes) {
    if ($bytes >= 1073741824) {
        return number_format($bytes / 1073741824, 2) . ' GB';
    } elseif ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' bytes';
    }
}

require_once '../includes/footer.php';
?>
