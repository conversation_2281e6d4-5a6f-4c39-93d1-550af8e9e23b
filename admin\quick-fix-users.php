<?php
require_once '../config/database.php';

echo "<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>إصلاح سريع للمستخدمين</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css' rel='stylesheet'>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f8f9fa; }
        .container { max-width: 800px; margin: 30px auto; }
        .log-item { padding: 8px; margin: 3px 0; border-radius: 5px; font-size: 14px; }
        .log-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .log-error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .log-info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
<div class='container'>
    <div class='card'>
        <div class='card-header bg-warning text-dark'>
            <h4 class='mb-0'><i class='fas fa-tools me-2'></i>إصلاح سريع للمستخدمين</h4>
        </div>
        <div class='card-body'>";

function logMessage($message, $type = 'info') {
    $icon = $type === 'success' ? 'check-circle' : ($type === 'error' ? 'exclamation-circle' : 'info-circle');
    echo "<div class='log-item log-$type'><i class='fas fa-$icon me-2'></i>$message</div>";
    flush();
    ob_flush();
}

try {
    logMessage('🔧 بدء الإصلاح السريع...', 'info');
    
    // 1. التحقق من وجود المستخدمين
    $user_count = $conn->query("SELECT COUNT(*) FROM users WHERE role != 'admin'")->fetchColumn();
    logMessage("عدد المستخدمين الحالي: $user_count", 'info');
    
    if ($user_count < 10) {
        logMessage('إضافة مستخدمين جدد...', 'info');
        
        // إضافة مدربين
        $instructors = [
            ['د. أحمد محمد علي', '<EMAIL>', '0501234567'],
            ['أ. فاطمة سالم', '<EMAIL>', '0509876543'],
            ['م. محمد حسن', '<EMAIL>', '0512345678']
        ];
        
        foreach ($instructors as $instructor) {
            // التحقق من عدم وجود البريد الإلكتروني
            $stmt = $conn->prepare("SELECT COUNT(*) FROM users WHERE email = ?");
            $stmt->execute([$instructor[1]]);
            
            if ($stmt->fetchColumn() == 0) {
                $password = password_hash('instructor123', PASSWORD_DEFAULT);
                $stmt = $conn->prepare("
                    INSERT INTO users (name, email, password, role, phone, status, email_verified, created_at, last_login) 
                    VALUES (?, ?, ?, 'instructor', ?, 'active', TRUE, NOW() - INTERVAL FLOOR(RAND() * 30) DAY, NOW() - INTERVAL FLOOR(RAND() * 7) DAY)
                ");
                $stmt->execute([$instructor[0], $instructor[1], $password, $instructor[2]]);
                logMessage("✓ تم إضافة المدرب: {$instructor[0]}", 'success');
            }
        }
        
        // إضافة طلاب
        $students = [
            ['سارة محمد أحمد', '<EMAIL>', '0501111111'],
            ['عبدالله سالم علي', '<EMAIL>', '0502222222'],
            ['مريم حسن محمد', '<EMAIL>', '0503333333'],
            ['يوسف علي أحمد', '<EMAIL>', '0504444444'],
            ['هند خالد سالم', '<EMAIL>', '0505555555'],
            ['عمر محمود حسن', '<EMAIL>', '0506666666'],
            ['ليلى أحمد علي', '<EMAIL>', '0507777777'],
            ['كريم سعد محمد', '<EMAIL>', '0508888888'],
            ['رنا عبدالله أحمد', '<EMAIL>', '0509999999'],
            ['طارق محمد سالم', '<EMAIL>', '0501010101']
        ];
        
        foreach ($students as $student) {
            // التحقق من عدم وجود البريد الإلكتروني
            $stmt = $conn->prepare("SELECT COUNT(*) FROM users WHERE email = ?");
            $stmt->execute([$student[1]]);
            
            if ($stmt->fetchColumn() == 0) {
                $password = password_hash('student123', PASSWORD_DEFAULT);
                $stmt = $conn->prepare("
                    INSERT INTO users (name, email, password, role, phone, status, email_verified, created_at, last_login) 
                    VALUES (?, ?, ?, 'student', ?, 'active', TRUE, NOW() - INTERVAL FLOOR(RAND() * 60) DAY, NOW() - INTERVAL FLOOR(RAND() * 7) DAY)
                ");
                $stmt->execute([$student[0], $student[1], $password, $student[2]]);
                logMessage("✓ تم إضافة الطالب: {$student[0]}", 'success');
            }
        }
    }
    
    // 2. التحقق من وجود كورسات
    $course_count = $conn->query("SELECT COUNT(*) FROM courses")->fetchColumn();
    logMessage("عدد الكورسات الحالي: $course_count", 'info');
    
    if ($course_count < 5) {
        logMessage('إضافة كورسات جديدة...', 'info');
        
        // جلب المدربين
        $instructors = $conn->query("SELECT id FROM users WHERE role = 'instructor' LIMIT 3")->fetchAll(PDO::FETCH_COLUMN);
        
        if (!empty($instructors)) {
            $courses = [
                ['تعلم PHP من الصفر إلى الاحتراف', 'دورة شاملة لتعلم لغة PHP وتطوير المواقع الديناميكية', 'paid', 499.99],
                ['أساسيات التصميم الجرافيكي', 'تعلم أساسيات التصميم باستخدام Photoshop و Illustrator', 'paid', 299.99],
                ['إدارة المشاريع الناجحة', 'كيفية إدارة المشاريع بفعالية وتحقيق الأهداف', 'free', 0],
                ['التسويق عبر وسائل التواصل الاجتماعي', 'استراتيجيات التسويق الرقمي الحديثة', 'paid', 399.99],
                ['تطوير المواقع بـ HTML و CSS', 'تعلم تطوير المواقع من البداية', 'free', 0]
            ];
            
            foreach ($courses as $index => $course) {
                $instructor_id = $instructors[$index % count($instructors)];
                
                $stmt = $conn->prepare("
                    INSERT INTO courses (title, description, course_type, price, instructor_id, status, created_at) 
                    VALUES (?, ?, ?, ?, ?, 'active', NOW() - INTERVAL FLOOR(RAND() * 60) DAY)
                ");
                $stmt->execute([$course[0], $course[1], $course[2], $course[3], $instructor_id]);
                logMessage("✓ تم إضافة الكورس: {$course[0]}", 'success');
            }
        }
    }
    
    // 3. إضافة تسجيلات في الكورسات
    $enrollment_count = $conn->query("SELECT COUNT(*) FROM course_enrollments")->fetchColumn();
    logMessage("عدد التسجيلات الحالي: $enrollment_count", 'info');
    
    if ($enrollment_count < 10) {
        logMessage('إضافة تسجيلات جديدة...', 'info');
        
        $students = $conn->query("SELECT id FROM users WHERE role = 'student'")->fetchAll(PDO::FETCH_COLUMN);
        $courses = $conn->query("SELECT id, course_type, price FROM courses")->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($students) && !empty($courses)) {
            $added_enrollments = 0;
            foreach ($students as $student_id) {
                // كل طالب يسجل في 1-3 كورسات
                $num_courses = rand(1, 3);
                $selected_courses = array_rand($courses, min($num_courses, count($courses)));
                if (!is_array($selected_courses)) {
                    $selected_courses = [$selected_courses];
                }
                
                foreach ($selected_courses as $course_index) {
                    $course = $courses[$course_index];
                    
                    // التحقق من عدم وجود تسجيل مسبق
                    $stmt = $conn->prepare("SELECT COUNT(*) FROM course_enrollments WHERE student_id = ? AND course_id = ?");
                    $stmt->execute([$student_id, $course['id']]);
                    
                    if ($stmt->fetchColumn() == 0) {
                        $payment_amount = $course['course_type'] === 'paid' ? $course['price'] : 0;
                        $stmt = $conn->prepare("
                            INSERT INTO course_enrollments (student_id, course_id, payment_status, payment_amount, enrolled_at) 
                            VALUES (?, ?, 'completed', ?, NOW() - INTERVAL FLOOR(RAND() * 30) DAY)
                        ");
                        $stmt->execute([$student_id, $course['id'], $payment_amount]);
                        $added_enrollments++;
                    }
                }
            }
            logMessage("✓ تم إضافة $added_enrollments تسجيل جديد", 'success');
        }
    }
    
    // 4. عرض الإحصائيات النهائية
    logMessage('=== الإحصائيات النهائية ===', 'info');
    
    $final_stats = $conn->query("
        SELECT 
            COUNT(*) as total_users,
            COUNT(CASE WHEN role = 'student' THEN 1 END) as total_students,
            COUNT(CASE WHEN role = 'instructor' THEN 1 END) as total_instructors,
            COUNT(CASE WHEN status = 'active' THEN 1 END) as active_users
        FROM users WHERE role != 'admin'
    ")->fetch(PDO::FETCH_ASSOC);
    
    logMessage("👥 إجمالي المستخدمين: {$final_stats['total_users']}", 'info');
    logMessage("🎓 الطلاب: {$final_stats['total_students']}", 'info');
    logMessage("👨‍🏫 المدربين: {$final_stats['total_instructors']}", 'info');
    logMessage("✅ المستخدمين النشطين: {$final_stats['active_users']}", 'info');
    
    $course_stats = $conn->query("SELECT COUNT(*) as total_courses FROM courses")->fetch(PDO::FETCH_ASSOC);
    logMessage("📚 إجمالي الكورسات: {$course_stats['total_courses']}", 'info');
    
    $enrollment_stats = $conn->query("SELECT COUNT(*) as total_enrollments FROM course_enrollments")->fetch(PDO::FETCH_ASSOC);
    logMessage("📝 إجمالي التسجيلات: {$enrollment_stats['total_enrollments']}", 'info');
    
    logMessage('🎉 تم الإصلاح السريع بنجاح!', 'success');
    
} catch (Exception $e) {
    logMessage('❌ خطأ: ' . $e->getMessage(), 'error');
}

echo "
        </div>
        <div class='mt-4 text-center'>
            <div class='alert alert-success'>
                <h5><i class='fas fa-check-circle me-2'></i>تم الإصلاح السريع بنجاح!</h5>
                <p class='mb-0'>الآن يمكنك الوصول إلى صفحة إدارة المستخدمين</p>
            </div>
            <a href='manage-users.php' class='btn btn-primary btn-lg me-2'>
                <i class='fas fa-users me-2'></i>إدارة المستخدمين
            </a>
            <a href='dashboard.php' class='btn btn-success me-2'>
                <i class='fas fa-tachometer-alt me-2'></i>لوحة التحكم
            </a>
            <a href='manage-courses.php' class='btn btn-info'>
                <i class='fas fa-book me-2'></i>إدارة الكورسات
            </a>
        </div>
    </div>
</div>
</body>
</html>";
?>
