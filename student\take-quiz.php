<?php
/**
 * صفحة حل الاختبار للطلاب
 * Take Quiz Page for Students
 */

require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات الطالب
if (!isLoggedIn() || !isStudent()) {
    header('Location: ../login.php');
    exit;
}

$student_id = $_SESSION['user_id'];
$attempt_id = $_GET['attempt_id'] ?? 0;
$success = '';
$error = '';

// التحقق من وجود المحاولة وأنها تخص الطالب
try {
    $stmt = $conn->prepare("
        SELECT qa.*, q.*, c.title as course_title, c.instructor_id,
               TIMESTAMPDIFF(SECOND, qa.started_at, NOW()) as elapsed_seconds
        FROM quiz_attempts qa
        JOIN quizzes q ON qa.quiz_id = q.id
        JOIN courses c ON q.course_id = c.id
        JOIN course_enrollments ce ON c.id = ce.course_id
        WHERE qa.id = ? AND qa.student_id = ? AND ce.student_id = ? AND qa.status = 'in_progress'
    ");
    $stmt->execute([$attempt_id, $student_id, $student_id]);
    $attempt = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$attempt) {
        header('Location: quizzes.php');
        exit;
    }

    // التحقق من انتهاء الوقت
    if ($attempt['time_limit'] && $attempt['elapsed_seconds'] >= ($attempt['time_limit'] * 60)) {
        // انتهى الوقت - إنهاء الاختبار تلقائياً
        $stmt = $conn->prepare("
            UPDATE quiz_attempts
            SET status = 'completed', completed_at = NOW(), time_taken = ?
            WHERE id = ?
        ");
        $stmt->execute([$attempt['time_limit'] * 60, $attempt_id]);

        // حساب النتيجة
        calculateQuizScore($conn, $attempt_id);

        header('Location: quiz-results.php?quiz_id=' . $attempt['quiz_id']);
        exit;
    }

} catch (PDOException $e) {
    header('Location: quizzes.php');
    exit;
}

// جلب أسئلة الاختبار
try {
    $stmt = $conn->prepare("
        SELECT qq.*, qqo.id as option_id, qqo.option_text, qqo.option_order,
               qa_existing.answer_text, qa_existing.selected_option_id
        FROM quiz_questions qq
        LEFT JOIN quiz_question_options qqo ON qq.id = qqo.question_id
        LEFT JOIN quiz_answers qa_existing ON qq.id = qa_existing.question_id AND qa_existing.attempt_id = ?
        WHERE qq.quiz_id = ?
        ORDER BY qq.question_order ASC, qq.id ASC, qqo.option_order ASC
    ");
    $stmt->execute([$attempt_id, $attempt['quiz_id']]);
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // تنظيم البيانات
    $questions = [];
    foreach ($results as $row) {
        $question_id = $row['id'];

        if (!isset($questions[$question_id])) {
            $questions[$question_id] = [
                'id' => $row['id'],
                'question_text' => $row['question_text'],
                'question_type' => $row['question_type'],
                'points' => $row['points'],
                'options' => [],
                'saved_answer' => $row['answer_text'],
                'saved_option' => $row['selected_option_id']
            ];
        }

        if ($row['option_id']) {
            $questions[$question_id]['options'][] = [
                'id' => $row['option_id'],
                'text' => $row['option_text'],
                'order' => $row['option_order']
            ];
        }
    }

} catch (PDOException $e) {
    $questions = [];
}

// معالجة حفظ الإجابات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    if ($action === 'save_answer') {
        $question_id = (int)$_POST['question_id'];
        $answer_text = $_POST['answer_text'] ?? null;
        $selected_option_id = $_POST['selected_option_id'] ?? null;

        try {
            // حفظ أو تحديث الإجابة
            $stmt = $conn->prepare("
                INSERT INTO quiz_answers (attempt_id, question_id, answer_text, selected_option_id)
                VALUES (?, ?, ?, ?)
                ON DUPLICATE KEY UPDATE
                answer_text = VALUES(answer_text),
                selected_option_id = VALUES(selected_option_id),
                answered_at = NOW()
            ");
            $stmt->execute([$attempt_id, $question_id, $answer_text, $selected_option_id]);

            echo json_encode(['success' => true, 'message' => 'تم حفظ الإجابة']);
            exit;

        } catch (PDOException $e) {
            echo json_encode(['success' => false, 'message' => 'حدث خطأ في حفظ الإجابة']);
            exit;
        }
    }

    if ($action === 'submit_quiz') {
        try {
            // حفظ جميع الإجابات المرسلة
            foreach ($_POST as $key => $value) {
                if (strpos($key, 'question_') === 0) {
                    $question_id = str_replace('question_', '', $key);

                    if (is_array($value)) {
                        // أسئلة متعددة الاختيارات
                        $selected_option_id = $value[0] ?? null;
                        $answer_text = null;
                    } else {
                        // أسئلة نصية
                        $answer_text = $value;
                        $selected_option_id = null;
                    }

                    $stmt = $conn->prepare("
                        INSERT INTO quiz_answers (attempt_id, question_id, answer_text, selected_option_id)
                        VALUES (?, ?, ?, ?)
                        ON DUPLICATE KEY UPDATE
                        answer_text = VALUES(answer_text),
                        selected_option_id = VALUES(selected_option_id),
                        answered_at = NOW()
                    ");
                    $stmt->execute([$attempt_id, $question_id, $answer_text, $selected_option_id]);
                }
            }

            // إنهاء المحاولة
            $time_taken = $attempt['elapsed_seconds'];
            $stmt = $conn->prepare("
                UPDATE quiz_attempts
                SET status = 'completed', completed_at = NOW(), time_taken = ?
                WHERE id = ?
            ");
            $stmt->execute([$time_taken, $attempt_id]);

            // حساب النتيجة
            calculateQuizScore($conn, $attempt_id);

            header('Location: quiz-results.php?quiz_id=' . $attempt['quiz_id']);
            exit;

        } catch (PDOException $e) {
            $error = 'حدث خطأ أثناء إرسال الاختبار';
        }
    }
}

// حساب الوقت المتبقي
$remaining_seconds = null;
if ($attempt['time_limit']) {
    $remaining_seconds = ($attempt['time_limit'] * 60) - $attempt['elapsed_seconds'];
    if ($remaining_seconds <= 0) {
        $remaining_seconds = 0;
    }
}

$pageTitle = 'حل الاختبار - ' . $attempt['title'];

// دالة حساب النتيجة
function calculateQuizScore($conn, $attempt_id) {
    try {
        // جلب الإجابات والأسئلة
        $stmt = $conn->prepare("
            SELECT qa.*, qq.points, qq.question_type, qqo.is_correct
            FROM quiz_answers qa
            JOIN quiz_questions qq ON qa.question_id = qq.id
            LEFT JOIN quiz_question_options qqo ON qa.selected_option_id = qqo.id
            WHERE qa.attempt_id = ?
        ");
        $stmt->execute([$attempt_id]);
        $answers = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $total_points = 0;
        $earned_points = 0;

        foreach ($answers as $answer) {
            $total_points += $answer['points'];

            // تحديد النقاط المكتسبة حسب نوع السؤال
            $points_earned = 0;

            if ($answer['question_type'] === 'multiple_choice') {
                if ($answer['is_correct']) {
                    $points_earned = $answer['points'];
                }
            } elseif ($answer['question_type'] === 'true_false') {
                // يحتاج تقييم يدوي أو منطق إضافي
                $points_earned = 0; // مؤقت
            } else {
                // أسئلة نصية تحتاج تقييم يدوي
                $points_earned = 0; // مؤقت
            }

            $earned_points += $points_earned;

            // تحديث النقاط المكتسبة للإجابة
            $stmt = $conn->prepare("
                UPDATE quiz_answers
                SET points_earned = ?, is_correct = ?
                WHERE id = ?
            ");
            $stmt->execute([$points_earned, $points_earned > 0 ? 1 : 0, $answer['id']]);
        }

        // حساب النسبة المئوية
        $score_percentage = $total_points > 0 ? ($earned_points / $total_points) * 100 : 0;

        // تحديث نتيجة المحاولة
        $stmt = $conn->prepare("
            UPDATE quiz_attempts
            SET score = ?, total_marks = ?
            WHERE id = ?
        ");
        $stmt->execute([$score_percentage, $total_points, $attempt_id]);

    } catch (PDOException $e) {
        error_log("Error calculating quiz score: " . $e->getMessage());
    }
}
?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($pageTitle); ?></title>

    <!-- الخطوط العربية -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <!-- Bootstrap و Font Awesome -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- ملفات CSS المخصصة -->
    <link href="../assets/css/main.css" rel="stylesheet">

    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }

        .quiz-container {
            max-width: 1000px;
            margin: 2rem auto;
            padding: 0 1rem;
        }

        .quiz-header {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            padding: 2rem;
            margin-bottom: 2rem;
            position: sticky;
            top: 20px;
            z-index: 100;
        }

        .timer-display {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 1rem;
            border-radius: 10px;
            text-align: center;
            font-size: 1.2rem;
            font-weight: 600;
        }

        .question-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            overflow: hidden;
            transition: transform 0.3s ease;
        }

        .question-card:hover {
            transform: translateY(-2px);
        }

        .question-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 1.5rem;
        }

        .question-body {
            padding: 2rem;
        }

        .option-item {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 0.75rem;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .option-item:hover {
            border-color: #007bff;
            background: #e3f2fd;
        }

        .option-item.selected {
            border-color: #28a745;
            background: #d4edda;
        }

        .progress-bar-container {
            background: #e9ecef;
            border-radius: 10px;
            height: 8px;
            overflow: hidden;
        }

        .progress-bar {
            background: linear-gradient(90deg, #28a745, #20c997);
            height: 100%;
            transition: width 0.3s ease;
        }

        .submit-section {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            padding: 2rem;
            text-align: center;
            position: sticky;
            bottom: 20px;
        }

        .auto-save-indicator {
            position: fixed;
            top: 50%;
            left: 20px;
            transform: translateY(-50%);
            background: #28a745;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-size: 0.875rem;
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 1000;
        }

        .auto-save-indicator.show {
            opacity: 1;
        }

        @media (max-width: 768px) {
            .quiz-container {
                margin: 1rem auto;
                padding: 0 0.5rem;
            }

            .quiz-header {
                padding: 1.5rem;
            }

            .question-body {
                padding: 1.5rem;
            }

            .timer-display {
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- مؤشر الحفظ التلقائي -->
    <div class="auto-save-indicator" id="autoSaveIndicator">
        <i class="fas fa-check me-1"></i>
        تم الحفظ تلقائياً
    </div>

    <div class="quiz-container">
        <!-- رأس الاختبار -->
        <div class="quiz-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h2 class="mb-2"><?php echo htmlspecialchars($attempt['title']); ?></h2>
                    <p class="text-muted mb-0">
                        <strong><?php echo htmlspecialchars($attempt['course_title']); ?></strong>
                        - المحاولة رقم <?php echo $attempt['attempt_number']; ?>
                    </p>
                </div>
                <div class="col-md-4">
                    <?php if ($remaining_seconds !== null): ?>
                        <div class="timer-display" id="timerDisplay">
                            <i class="fas fa-clock me-2"></i>
                            <span id="timeRemaining"><?php echo gmdate('H:i:s', $remaining_seconds); ?></span>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-info mb-0">
                            <i class="fas fa-infinity me-2"></i>
                            وقت غير محدود
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- شريط التقدم -->
            <div class="mt-3">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span class="text-muted">التقدم:</span>
                    <span class="text-muted" id="progressText">0 / <?php echo count($questions); ?></span>
                </div>
                <div class="progress-bar-container">
                    <div class="progress-bar" id="progressBar" style="width: 0%"></div>
                </div>
            </div>
        </div>

        <!-- رسائل الخطأ والنجاح -->
        <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?php echo htmlspecialchars($error); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- نموذج الاختبار -->
        <form id="quizForm" method="POST">
            <input type="hidden" name="action" value="submit_quiz">

            <!-- الأسئلة -->
            <?php if (empty($questions)): ?>
                <div class="question-card">
                    <div class="question-body text-center">
                        <i class="fas fa-question-circle text-muted" style="font-size: 3rem;"></i>
                        <h5 class="mt-3 text-muted">لا توجد أسئلة</h5>
                        <p class="text-muted">لم يتم إضافة أسئلة لهذا الاختبار</p>
                    </div>
                </div>
            <?php else: ?>
                <?php foreach ($questions as $index => $question): ?>
                    <div class="question-card" data-question-id="<?php echo $question['id']; ?>">
                        <div class="question-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h6 class="mb-0">السؤال <?php echo $index + 1; ?></h6>
                                <span class="badge bg-light text-dark">
                                    <?php echo $question['points']; ?> نقطة
                                </span>
                            </div>
                        </div>

                        <div class="question-body">
                            <h6 class="mb-4"><?php echo nl2br(htmlspecialchars($question['question_text'])); ?></h6>

                            <?php if ($question['question_type'] === 'multiple_choice' && !empty($question['options'])): ?>
                                <div class="options-list">
                                    <?php foreach ($question['options'] as $option): ?>
                                        <div class="option-item" onclick="selectOption(this, <?php echo $question['id']; ?>, <?php echo $option['id']; ?>)">
                                            <div class="d-flex align-items-center">
                                                <div class="form-check me-3">
                                                    <input class="form-check-input" type="radio"
                                                           name="question_<?php echo $question['id']; ?>[]"
                                                           value="<?php echo $option['id']; ?>"
                                                           id="option_<?php echo $option['id']; ?>"
                                                           <?php echo $question['saved_option'] == $option['id'] ? 'checked' : ''; ?>>
                                                </div>
                                                <label class="form-check-label flex-grow-1"
                                                       for="option_<?php echo $option['id']; ?>">
                                                    <?php echo htmlspecialchars($option['text']); ?>
                                                </label>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>

                            <?php elseif ($question['question_type'] === 'true_false'): ?>
                                <div class="options-list">
                                    <div class="option-item" onclick="selectOption(this, <?php echo $question['id']; ?>, 'true')">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio"
                                                   name="question_<?php echo $question['id']; ?>"
                                                   value="true" id="true_<?php echo $question['id']; ?>"
                                                   <?php echo $question['saved_answer'] === 'true' ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="true_<?php echo $question['id']; ?>">
                                                صحيح
                                            </label>
                                        </div>
                                    </div>
                                    <div class="option-item" onclick="selectOption(this, <?php echo $question['id']; ?>, 'false')">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio"
                                                   name="question_<?php echo $question['id']; ?>"
                                                   value="false" id="false_<?php echo $question['id']; ?>"
                                                   <?php echo $question['saved_answer'] === 'false' ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="false_<?php echo $question['id']; ?>">
                                                خطأ
                                            </label>
                                        </div>
                                    </div>
                                </div>

                            <?php elseif ($question['question_type'] === 'short_answer'): ?>
                                <div class="mb-3">
                                    <input type="text" class="form-control form-control-lg"
                                           name="question_<?php echo $question['id']; ?>"
                                           placeholder="اكتب إجابتك هنا..."
                                           value="<?php echo htmlspecialchars($question['saved_answer'] ?? ''); ?>"
                                           onchange="autoSaveAnswer(<?php echo $question['id']; ?>, this.value)">
                                </div>

                            <?php elseif ($question['question_type'] === 'essay'): ?>
                                <div class="mb-3">
                                    <textarea class="form-control" rows="6"
                                              name="question_<?php echo $question['id']; ?>"
                                              placeholder="اكتب إجابتك المفصلة هنا..."
                                              onchange="autoSaveAnswer(<?php echo $question['id']; ?>, this.value)"><?php echo htmlspecialchars($question['saved_answer'] ?? ''); ?></textarea>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>

            <!-- قسم الإرسال -->
            <div class="submit-section">
                <h5 class="mb-3">هل أنت مستعد لإرسال الاختبار؟</h5>
                <p class="text-muted mb-4">
                    تأكد من مراجعة جميع إجاباتك قبل الإرسال. لن تتمكن من تعديل الإجابات بعد الإرسال.
                </p>

                <div class="d-flex gap-3 justify-content-center">
                    <button type="button" class="btn btn-outline-secondary" onclick="reviewAnswers()">
                        <i class="fas fa-eye me-1"></i>
                        مراجعة الإجابات
                    </button>
                    <button type="submit" class="btn btn-success btn-lg" onclick="return confirmSubmit()">
                        <i class="fas fa-paper-plane me-1"></i>
                        إرسال الاختبار
                    </button>
                    <a href="quizzes.php" class="btn btn-outline-danger">
                        <i class="fas fa-times me-1"></i>
                        إلغاء
                    </a>
                </div>
            </div>
        </form>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // متغيرات عامة
        let timeRemaining = <?php echo $remaining_seconds ?? 'null'; ?>;
        let timerInterval;
        let autoSaveTimeout;

        // بدء العداد التنازلي
        if (timeRemaining !== null) {
            timerInterval = setInterval(updateTimer, 1000);
        }

        // تحديث العداد
        function updateTimer() {
            if (timeRemaining <= 0) {
                clearInterval(timerInterval);
                alert('انتهى الوقت المحدد للاختبار. سيتم إرسال إجاباتك تلقائياً.');
                document.getElementById('quizForm').submit();
                return;
            }

            const hours = Math.floor(timeRemaining / 3600);
            const minutes = Math.floor((timeRemaining % 3600) / 60);
            const seconds = timeRemaining % 60;

            const timeString =
                String(hours).padStart(2, '0') + ':' +
                String(minutes).padStart(2, '0') + ':' +
                String(seconds).padStart(2, '0');

            document.getElementById('timeRemaining').textContent = timeString;

            // تغيير لون العداد عند اقتراب انتهاء الوقت
            const timerDisplay = document.getElementById('timerDisplay');
            if (timeRemaining <= 300) { // 5 دقائق
                timerDisplay.style.background = 'linear-gradient(135deg, #e74c3c, #c0392b)';
            } else if (timeRemaining <= 600) { // 10 دقائق
                timerDisplay.style.background = 'linear-gradient(135deg, #f39c12, #e67e22)';
            }

            timeRemaining--;
        }

        // اختيار خيار
        function selectOption(element, questionId, value) {
            // إزالة التحديد من جميع الخيارات في نفس السؤال
            const questionCard = element.closest('.question-card');
            questionCard.querySelectorAll('.option-item').forEach(item => {
                item.classList.remove('selected');
            });

            // تحديد الخيار المختار
            element.classList.add('selected');

            // تحديد الراديو بوتن
            const radio = element.querySelector('input[type="radio"]');
            if (radio) {
                radio.checked = true;
            }

            // حفظ تلقائي
            if (radio && radio.name.includes('[]')) {
                // أسئلة متعددة الاختيارات
                autoSaveAnswer(questionId, null, value);
            } else {
                // أسئلة صح/خطأ
                autoSaveAnswer(questionId, value);
            }

            updateProgress();
        }

        // حفظ تلقائي للإجابة
        function autoSaveAnswer(questionId, answerText, selectedOptionId = null) {
            clearTimeout(autoSaveTimeout);

            autoSaveTimeout = setTimeout(() => {
                const formData = new FormData();
                formData.append('action', 'save_answer');
                formData.append('question_id', questionId);
                formData.append('answer_text', answerText);
                formData.append('selected_option_id', selectedOptionId);

                fetch('take-quiz.php?attempt_id=<?php echo $attempt_id; ?>', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showAutoSaveIndicator();
                    }
                })
                .catch(error => {
                    console.error('خطأ في الحفظ التلقائي:', error);
                });
            }, 1000);
        }

        // إظهار مؤشر الحفظ التلقائي
        function showAutoSaveIndicator() {
            const indicator = document.getElementById('autoSaveIndicator');
            indicator.classList.add('show');
            setTimeout(() => {
                indicator.classList.remove('show');
            }, 2000);
        }

        // تحديث شريط التقدم
        function updateProgress() {
            const totalQuestions = <?php echo count($questions); ?>;
            let answeredQuestions = 0;

            // عد الأسئلة المجابة
            document.querySelectorAll('.question-card').forEach(card => {
                const checkedInputs = card.querySelectorAll('input:checked');
                const filledInputs = card.querySelectorAll('input[type="text"]:not(:placeholder-shown), textarea:not(:placeholder-shown)');

                if (checkedInputs.length > 0 || filledInputs.length > 0) {
                    answeredQuestions++;
                }
            });

            const progressPercentage = (answeredQuestions / totalQuestions) * 100;
            document.getElementById('progressBar').style.width = progressPercentage + '%';
            document.getElementById('progressText').textContent = answeredQuestions + ' / ' + totalQuestions;
        }

        // مراجعة الإجابات
        function reviewAnswers() {
            const unansweredQuestions = [];

            document.querySelectorAll('.question-card').forEach((card, index) => {
                const questionNumber = index + 1;
                const checkedInputs = card.querySelectorAll('input:checked');
                const textInputs = card.querySelectorAll('input[type="text"], textarea');
                let hasAnswer = checkedInputs.length > 0;

                // فحص الحقول النصية
                textInputs.forEach(input => {
                    if (input.value.trim() !== '') {
                        hasAnswer = true;
                    }
                });

                if (!hasAnswer) {
                    unansweredQuestions.push(questionNumber);
                }
            });

            if (unansweredQuestions.length > 0) {
                alert('الأسئلة غير المجابة: ' + unansweredQuestions.join(', '));
            } else {
                alert('تم الإجابة على جميع الأسئلة!');
            }
        }

        // تأكيد الإرسال
        function confirmSubmit() {
            return confirm('هل أنت متأكد من إرسال الاختبار؟ لن تتمكن من تعديل الإجابات بعد الإرسال.');
        }

        // تحديث التقدم عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateProgress();

            // إضافة مستمعات للحقول النصية
            document.querySelectorAll('input[type="text"], textarea').forEach(input => {
                input.addEventListener('input', updateProgress);
            });

            // منع إرسال النموذج بالخطأ
            document.getElementById('quizForm').addEventListener('submit', function(e) {
                if (!confirmSubmit()) {
                    e.preventDefault();
                }
            });
        });

        // تحذير عند مغادرة الصفحة
        window.addEventListener('beforeunload', function(e) {
            e.preventDefault();
            e.returnValue = 'هل أنت متأكد من مغادرة الصفحة؟ قد تفقد إجاباتك غير المحفوظة.';
        });
    </script>
</body>
</html>