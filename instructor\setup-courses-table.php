<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من صلاحيات المدرب
if (!isLoggedIn() || !isInstructor()) {
    header('Location: ../login.php');
    exit();
}

echo "<h2>إعداد جدول الكورسات</h2>";

try {
    // إنشاء جدول الكورسات مع جميع الأعمدة المطلوبة
    $conn->exec("CREATE TABLE IF NOT EXISTS courses (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        description TEXT NOT NULL,
        course_type ENUM('free', 'paid') DEFAULT 'free',
        price DECIMAL(10,2) DEFAULT 0.00,
        currency VARCHAR(3) DEFAULT 'SAR',
        start_date DATE NOT NULL,
        end_date DATE NOT NULL,
        max_students INT DEFAULT 30,
        status ENUM('active', 'inactive', 'draft', 'completed') DEFAULT 'active',
        instructor_id INT NOT NULL,
        image_path VARCHAR(255) DEFAULT NULL,
        category_id INT DEFAULT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        INDEX idx_instructor_id (instructor_id),
        INDEX idx_category_id (category_id),
        INDEX idx_status (status),
        INDEX idx_course_type (course_type),
        INDEX idx_start_date (start_date),
        
        FOREIGN KEY (instructor_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    
    echo "<p>✅ تم إنشاء/تحديث جدول courses</p>";
    
    // التحقق من وجود جدول التصنيفات وإنشاؤه إذا لم يكن موجود
    $conn->exec("CREATE TABLE IF NOT EXISTS categories (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        icon VARCHAR(100) DEFAULT NULL,
        color VARCHAR(7) DEFAULT '#007bff',
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        INDEX idx_name (name),
        INDEX idx_status (status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    
    echo "<p>✅ تم إنشاء/تحديث جدول categories</p>";
    
    // إضافة تصنيفات أساسية إذا لم توجد
    $stmt = $conn->query("SELECT COUNT(*) FROM categories");
    $count = $stmt->fetchColumn();
    
    if ($count == 0) {
        $categories = [
            ['البرمجة وتطوير المواقع', 'تعلم لغات البرمجة وتطوير المواقع والتطبيقات', 'fas fa-code', '#007bff'],
            ['التصميم الجرافيكي', 'تعلم التصميم الجرافيكي والإبداع البصري', 'fas fa-paint-brush', '#28a745'],
            ['التسويق الرقمي', 'تعلم استراتيجيات التسويق الرقمي والإعلان', 'fas fa-bullhorn', '#ffc107'],
            ['إدارة الأعمال', 'تطوير مهارات إدارة الأعمال والقيادة', 'fas fa-briefcase', '#dc3545'],
            ['اللغات', 'تعلم اللغات المختلفة وتطوير مهارات التواصل', 'fas fa-language', '#6f42c1'],
            ['العلوم والرياضيات', 'دورات في العلوم والرياضيات والفيزياء', 'fas fa-calculator', '#20c997'],
            ['الفنون والموسيقى', 'تعلم الفنون والموسيقى والإبداع', 'fas fa-music', '#fd7e14'],
            ['الصحة واللياقة', 'دورات الصحة واللياقة البدنية والتغذية', 'fas fa-heartbeat', '#e83e8c']
        ];
        
        $stmt = $conn->prepare("INSERT INTO categories (name, description, icon, color) VALUES (?, ?, ?, ?)");
        foreach ($categories as $category) {
            $stmt->execute($category);
        }
        
        echo "<p>✅ تم إضافة " . count($categories) . " تصنيف أساسي</p>";
    }
    
    // إنشاء مجلد رفع الصور
    $upload_dir = '../uploads/courses';
    if (!is_dir($upload_dir)) {
        if (mkdir($upload_dir, 0755, true)) {
            echo "<p>✅ تم إنشاء مجلد رفع الصور: $upload_dir</p>";
        } else {
            echo "<p>❌ فشل في إنشاء مجلد رفع الصور</p>";
        }
    } else {
        echo "<p>⚠️ مجلد رفع الصور موجود مسبقاً</p>";
    }
    
    // إنشاء ملف .htaccess لحماية مجلد الرفع
    $htaccess_content = "Options -Indexes\n";
    $htaccess_content .= "AddType image/jpeg .jpg .jpeg\n";
    $htaccess_content .= "AddType image/png .png\n";
    $htaccess_content .= "AddType image/gif .gif\n";
    file_put_contents($upload_dir . '/.htaccess', $htaccess_content);
    echo "<p>✅ تم إنشاء ملف الحماية .htaccess</p>";
    
    // عرض بنية الجداول
    echo "<h3>بنية جدول courses:</h3>";
    $stmt = $conn->query("DESCRIBE courses");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>بنية جدول categories:</h3>";
    $stmt = $conn->query("DESCRIBE categories");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // عرض التصنيفات المتاحة
    echo "<h3>التصنيفات المتاحة:</h3>";
    $stmt = $conn->query("SELECT * FROM categories ORDER BY name");
    $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($categories)) {
        echo "<ul>";
        foreach ($categories as $category) {
            echo "<li>";
            echo "<i class='" . $category['icon'] . "' style='color: " . $category['color'] . ";'></i> ";
            echo "<strong>" . htmlspecialchars($category['name']) . "</strong>";
            if ($category['description']) {
                echo " - " . htmlspecialchars($category['description']);
            }
            echo "</li>";
        }
        echo "</ul>";
    }
    
    // عرض الكورسات الموجودة
    echo "<h3>الكورسات الموجودة:</h3>";
    $stmt = $conn->query("SELECT COUNT(*) FROM courses");
    $course_count = $stmt->fetchColumn();
    echo "<p>عدد الكورسات: " . $course_count . "</p>";
    
    if ($course_count > 0) {
        $stmt = $conn->query("
            SELECT c.*, cat.name as category_name, u.name as instructor_name 
            FROM courses c 
            LEFT JOIN categories cat ON c.category_id = cat.id 
            LEFT JOIN users u ON c.instructor_id = u.id 
            ORDER BY c.created_at DESC 
            LIMIT 10
        ");
        $courses = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>العنوان</th><th>المدرب</th><th>التصنيف</th><th>النوع</th><th>السعر</th><th>الحالة</th></tr>";
        foreach ($courses as $course) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($course['title']) . "</td>";
            echo "<td>" . htmlspecialchars($course['instructor_name'] ?? 'غير محدد') . "</td>";
            echo "<td>" . htmlspecialchars($course['category_name'] ?? 'غير محدد') . "</td>";
            echo "<td>" . ($course['course_type'] === 'paid' ? 'مدفوع' : 'مجاني') . "</td>";
            echo "<td>" . ($course['course_type'] === 'paid' ? $course['price'] . ' ' . $course['currency'] : 'مجاني') . "</td>";
            echo "<td>" . $course['status'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h3>✅ تم إعداد نظام الكورسات بنجاح!</h3>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
}
?>

<p><a href="add-course.php">إضافة كورس جديد</a></p>
<p><a href="courses.php">عرض الكورسات</a></p>
<p><a href="dashboard.php">لوحة التحكم</a></p>
