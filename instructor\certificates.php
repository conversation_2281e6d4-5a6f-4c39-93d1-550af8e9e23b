<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات المدرب
if (!isLoggedIn() || !isInstructor()) {
    header('Location: ../login.php');
    exit;
}

$pageTitle = 'إدارة الشهادات';
$breadcrumbs = [
    ['title' => 'إدارة الشهادات']
];

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'create_certificate_template') {
        $course_id = $_POST['course_id'] ?? 0;
        $template_name = trim($_POST['template_name'] ?? '');
        $certificate_text = trim($_POST['certificate_text'] ?? '');
        $background_color = $_POST['background_color'] ?? '#ffffff';
        $text_color = $_POST['text_color'] ?? '#000000';
        $border_style = $_POST['border_style'] ?? 'simple';
        $font_size = $_POST['font_size'] ?? 'medium';
        $is_active = isset($_POST['is_active']) ? 1 : 0;
        
        $errors = [];
        
        if (empty($template_name)) {
            $errors[] = 'اسم القالب مطلوب';
        }
        
        if (empty($course_id)) {
            $errors[] = 'يجب اختيار كورس';
        }
        
        if (empty($certificate_text)) {
            $errors[] = 'نص الشهادة مطلوب';
        }
        
        if (empty($errors)) {
            try {
                // التحقق من أن الكورس ينتمي للمدرب
                $stmt = $conn->prepare("SELECT id FROM courses WHERE id = ? AND instructor_id = ?");
                $stmt->execute([$course_id, $_SESSION['user_id']]);
                
                if ($stmt->fetch()) {
                    $stmt = $conn->prepare("
                        INSERT INTO certificate_templates 
                        (course_id, template_name, certificate_text, background_color, text_color, 
                         border_style, font_size, is_active, created_by) 
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ");
                    $stmt->execute([
                        $course_id, $template_name, $certificate_text, $background_color, 
                        $text_color, $border_style, $font_size, $is_active, $_SESSION['user_id']
                    ]);
                    
                    $success_message = 'تم إنشاء قالب الشهادة بنجاح';
                } else {
                    $error_message = 'الكورس غير موجود أو لا تملك صلاحية للوصول إليه';
                }
            } catch (PDOException $e) {
                $error_message = 'حدث خطأ أثناء إنشاء قالب الشهادة';
            }
        }
    }
    
    if ($action === 'issue_certificate') {
        $student_id = $_POST['student_id'] ?? 0;
        $course_id = $_POST['course_id'] ?? 0;
        $template_id = $_POST['template_id'] ?? 0;
        $completion_date = $_POST['completion_date'] ?? date('Y-m-d');
        $grade = $_POST['grade'] ?? '';
        $notes = trim($_POST['notes'] ?? '');
        
        $errors = [];
        
        if (empty($student_id) || empty($course_id) || empty($template_id)) {
            $errors[] = 'جميع الحقول مطلوبة';
        }
        
        if (empty($errors)) {
            try {
                // التحقق من أن الطالب مسجل في الكورس
                $stmt = $conn->prepare("
                    SELECT e.id 
                    FROM enrollments e
                    INNER JOIN courses c ON e.course_id = c.id
                    WHERE e.student_id = ? AND e.course_id = ? AND c.instructor_id = ?
                ");
                $stmt->execute([$student_id, $course_id, $_SESSION['user_id']]);
                
                if ($stmt->fetch()) {
                    // إنشاء رقم شهادة فريد
                    $certificate_number = 'CERT-' . date('Y') . '-' . str_pad(rand(1, 99999), 5, '0', STR_PAD_LEFT);
                    
                    $stmt = $conn->prepare("
                        INSERT INTO certificates 
                        (student_id, course_id, template_id, certificate_number, completion_date, 
                         grade, notes, issued_by, issued_at) 
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
                    ");
                    $stmt->execute([
                        $student_id, $course_id, $template_id, $certificate_number, 
                        $completion_date, $grade, $notes, $_SESSION['user_id']
                    ]);
                    
                    $success_message = 'تم إصدار الشهادة بنجاح - رقم الشهادة: ' . $certificate_number;
                } else {
                    $error_message = 'الطالب غير مسجل في هذا الكورس';
                }
            } catch (PDOException $e) {
                $error_message = 'حدث خطأ أثناء إصدار الشهادة';
            }
        }
    }
    
    if ($action === 'delete_certificate') {
        $certificate_id = $_POST['certificate_id'] ?? 0;
        
        try {
            $stmt = $conn->prepare("
                DELETE cert FROM certificates cert
                INNER JOIN courses c ON cert.course_id = c.id
                WHERE cert.id = ? AND c.instructor_id = ?
            ");
            $stmt->execute([$certificate_id, $_SESSION['user_id']]);
            
            $success_message = 'تم حذف الشهادة بنجاح';
        } catch (PDOException $e) {
            $error_message = 'حدث خطأ أثناء حذف الشهادة';
        }
    }
}

// فلاتر البحث
$course_filter = $_GET['course_id'] ?? '';
$status_filter = $_GET['status'] ?? '';

// جلب الكورسات الخاصة بالمدرب
try {
    $stmt = $conn->prepare("
        SELECT id, title 
        FROM courses 
        WHERE instructor_id = ? AND status = 'active'
        ORDER BY title
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $instructor_courses = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $instructor_courses = [];
}

// جلب قوالب الشهادات
try {
    $where_conditions = ["c.instructor_id = ?"];
    $params = [$_SESSION['user_id']];
    
    if (!empty($course_filter)) {
        $where_conditions[] = "ct.course_id = ?";
        $params[] = $course_filter;
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    $stmt = $conn->prepare("
        SELECT 
            ct.*,
            c.title as course_title,
            COUNT(DISTINCT cert.id) as certificates_issued
        FROM certificate_templates ct
        INNER JOIN courses c ON ct.course_id = c.id
        LEFT JOIN certificates cert ON ct.id = cert.template_id
        WHERE $where_clause
        GROUP BY ct.id
        ORDER BY ct.created_at DESC
    ");
    $stmt->execute($params);
    $templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (PDOException $e) {
    $templates = [];
}

// جلب الشهادات المُصدرة
try {
    $where_conditions = ["c.instructor_id = ?"];
    $params = [$_SESSION['user_id']];
    
    if (!empty($course_filter)) {
        $where_conditions[] = "cert.course_id = ?";
        $params[] = $course_filter;
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    $stmt = $conn->prepare("
        SELECT 
            cert.*,
            u.name as student_name,
            u.email as student_email,
            c.title as course_title,
            ct.template_name
        FROM certificates cert
        INNER JOIN users u ON cert.student_id = u.id
        INNER JOIN courses c ON cert.course_id = c.id
        INNER JOIN certificate_templates ct ON cert.template_id = ct.id
        WHERE $where_clause
        ORDER BY cert.issued_at DESC
        LIMIT 20
    ");
    $stmt->execute($params);
    $certificates = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // إحصائيات الشهادات
    $stmt = $conn->prepare("
        SELECT 
            COUNT(DISTINCT cert.id) as total_certificates,
            COUNT(DISTINCT ct.id) as total_templates,
            COUNT(DISTINCT CASE WHEN cert.issued_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN cert.id END) as monthly_certificates,
            COUNT(DISTINCT cert.course_id) as courses_with_certificates
        FROM certificates cert
        INNER JOIN courses c ON cert.course_id = c.id
        LEFT JOIN certificate_templates ct ON cert.template_id = ct.id
        WHERE c.instructor_id = ?
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
} catch (PDOException $e) {
    $certificates = [];
    $stats = ['total_certificates' => 0, 'total_templates' => 0, 'monthly_certificates' => 0, 'courses_with_certificates' => 0];
}

include 'includes/header.php';
?>

<!-- رأس الصفحة -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="h3 mb-2">
            <i class="fas fa-certificate text-warning me-2"></i>
            إدارة الشهادات
        </h2>
        <p class="text-muted mb-0">إنشاء وإدارة شهادات إتمام الكورسات</p>
    </div>
    <div class="d-flex gap-2">
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createTemplateModal">
            <i class="fas fa-plus me-1"></i>إنشاء قالب شهادة
        </button>
        <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#issueCertificateModal">
            <i class="fas fa-award me-1"></i>إصدار شهادة
        </button>
    </div>
</div>

<!-- رسائل النجاح والخطأ -->
<?php if (isset($success_message)): ?>
<div class="alert alert-success alert-dismissible fade show" role="alert">
    <i class="fas fa-check-circle me-2"></i>
    <?php echo $success_message; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<?php if (isset($error_message)): ?>
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <i class="fas fa-exclamation-triangle me-2"></i>
    <?php echo $error_message; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<?php if (!empty($errors)): ?>
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <i class="fas fa-exclamation-triangle me-2"></i>
    <ul class="mb-0">
        <?php foreach ($errors as $error): ?>
        <li><?php echo htmlspecialchars($error); ?></li>
        <?php endforeach; ?>
    </ul>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<!-- إحصائيات الشهادات -->
<div class="row g-4 mb-4">
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-warning bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-certificate text-warning fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h5 class="mb-1"><?php echo $stats['total_certificates']; ?></h5>
                        <p class="text-muted mb-0">إجمالي الشهادات</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-primary bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-file-alt text-primary fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h5 class="mb-1"><?php echo $stats['total_templates']; ?></h5>
                        <p class="text-muted mb-0">قوالب الشهادات</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-success bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-calendar-month text-success fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h5 class="mb-1"><?php echo $stats['monthly_certificates']; ?></h5>
                        <p class="text-muted mb-0">هذا الشهر</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-info bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-graduation-cap text-info fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h5 class="mb-1"><?php echo $stats['courses_with_certificates']; ?></h5>
                        <p class="text-muted mb-0">كورسات بشهادات</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- فلاتر البحث -->
<div class="card border-0 shadow-sm mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3 align-items-end">
            <div class="col-md-6">
                <label for="course_id" class="form-label">الكورس</label>
                <select name="course_id" id="course_id" class="form-select">
                    <option value="">جميع الكورسات</option>
                    <?php foreach ($instructor_courses as $course): ?>
                    <option value="<?php echo $course['id']; ?>" 
                            <?php echo $course_filter == $course['id'] ? 'selected' : ''; ?>>
                        <?php echo htmlspecialchars($course['title']); ?>
                    </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-3">
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-search me-1"></i>بحث
                </button>
            </div>
            <div class="col-md-3">
                <a href="?" class="btn btn-outline-secondary w-100">
                    <i class="fas fa-undo me-1"></i>إعادة تعيين
                </a>
            </div>
        </form>
    </div>
</div>

<!-- تبويبات إدارة الشهادات -->
<div class="row">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-bottom">
                <ul class="nav nav-tabs card-header-tabs" id="certificateTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="templates-tab" data-bs-toggle="tab"
                                data-bs-target="#templates" type="button" role="tab">
                            <i class="fas fa-file-alt me-2"></i>قوالب الشهادات
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="certificates-tab" data-bs-toggle="tab"
                                data-bs-target="#certificates" type="button" role="tab">
                            <i class="fas fa-certificate me-2"></i>الشهادات المُصدرة
                        </button>
                    </li>
                </ul>
            </div>
            <div class="card-body">
                <div class="tab-content" id="certificateTabsContent">
                    <!-- تبويب قوالب الشهادات -->
                    <div class="tab-pane fade show active" id="templates" role="tabpanel">
                        <?php if (empty($templates)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-file-alt text-muted" style="font-size: 3rem;"></i>
                            <h5 class="mt-3 text-muted">لا توجد قوالب شهادات</h5>
                            <p class="text-muted">ابدأ بإنشاء قالب شهادة للكورسات</p>
                            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createTemplateModal">
                                <i class="fas fa-plus me-1"></i>إنشاء قالب جديد
                            </button>
                        </div>
                        <?php else: ?>
                        <div class="row g-4">
                            <?php foreach ($templates as $template): ?>
                            <div class="col-lg-6 col-md-12">
                                <div class="card border h-100">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h6 class="mb-0"><?php echo htmlspecialchars($template['template_name']); ?></h6>
                                        <div class="d-flex gap-1">
                                            <?php if ($template['is_active']): ?>
                                            <span class="badge bg-success">نشط</span>
                                            <?php else: ?>
                                            <span class="badge bg-secondary">غير نشط</span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <div class="certificate-preview mb-3"
                                             style="background-color: <?php echo $template['background_color']; ?>;
                                                    color: <?php echo $template['text_color']; ?>;
                                                    border: 2px <?php echo $template['border_style'] === 'decorative' ? 'double' : 'solid'; ?> #ddd;
                                                    padding: 20px; text-align: center; min-height: 150px;
                                                    font-size: <?php echo $template['font_size'] === 'large' ? '16px' : ($template['font_size'] === 'small' ? '12px' : '14px'); ?>;">
                                            <?php echo nl2br(htmlspecialchars($template['certificate_text'])); ?>
                                        </div>

                                        <div class="row text-center mb-3">
                                            <div class="col-6">
                                                <small class="text-muted">الكورس</small>
                                                <div class="fw-bold"><?php echo htmlspecialchars($template['course_title']); ?></div>
                                            </div>
                                            <div class="col-6">
                                                <small class="text-muted">الشهادات المُصدرة</small>
                                                <div class="fw-bold text-primary"><?php echo $template['certificates_issued']; ?></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="card-footer bg-transparent">
                                        <div class="d-flex gap-2">
                                            <button class="btn btn-sm btn-outline-primary flex-fill"
                                                    onclick="previewTemplate(<?php echo $template['id']; ?>)">
                                                <i class="fas fa-eye me-1"></i>معاينة
                                            </button>
                                            <button class="btn btn-sm btn-outline-warning flex-fill"
                                                    onclick="editTemplate(<?php echo $template['id']; ?>)">
                                                <i class="fas fa-edit me-1"></i>تعديل
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger"
                                                    onclick="deleteTemplate(<?php echo $template['id']; ?>)">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        <?php endif; ?>
                    </div>

                    <!-- تبويب الشهادات المُصدرة -->
                    <div class="tab-pane fade" id="certificates" role="tabpanel">
                        <?php if (empty($certificates)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-certificate text-muted" style="font-size: 3rem;"></i>
                            <h5 class="mt-3 text-muted">لا توجد شهادات مُصدرة</h5>
                            <p class="text-muted">ابدأ بإصدار شهادات للطلاب المتميزين</p>
                            <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#issueCertificateModal">
                                <i class="fas fa-award me-1"></i>إصدار شهادة
                            </button>
                        </div>
                        <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>رقم الشهادة</th>
                                        <th>الطالب</th>
                                        <th>الكورس</th>
                                        <th>القالب</th>
                                        <th>تاريخ الإتمام</th>
                                        <th>الدرجة</th>
                                        <th>تاريخ الإصدار</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($certificates as $certificate): ?>
                                    <tr>
                                        <td>
                                            <span class="badge bg-primary"><?php echo htmlspecialchars($certificate['certificate_number']); ?></span>
                                        </td>
                                        <td>
                                            <div>
                                                <div class="fw-bold"><?php echo htmlspecialchars($certificate['student_name']); ?></div>
                                                <small class="text-muted"><?php echo htmlspecialchars($certificate['student_email']); ?></small>
                                            </div>
                                        </td>
                                        <td><?php echo htmlspecialchars($certificate['course_title']); ?></td>
                                        <td><?php echo htmlspecialchars($certificate['template_name']); ?></td>
                                        <td><?php echo date('Y-m-d', strtotime($certificate['completion_date'])); ?></td>
                                        <td>
                                            <?php if ($certificate['grade']): ?>
                                            <span class="badge bg-success"><?php echo htmlspecialchars($certificate['grade']); ?></span>
                                            <?php else: ?>
                                            <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo date('Y-m-d H:i', strtotime($certificate['issued_at'])); ?></td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button class="btn btn-sm btn-outline-primary"
                                                        onclick="viewCertificate(<?php echo $certificate['id']; ?>)" title="عرض">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-success"
                                                        onclick="downloadCertificate(<?php echo $certificate['id']; ?>)" title="تحميل">
                                                    <i class="fas fa-download"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-info"
                                                        onclick="sendCertificate(<?php echo $certificate['id']; ?>)" title="إرسال">
                                                    <i class="fas fa-paper-plane"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-danger"
                                                        onclick="deleteCertificate(<?php echo $certificate['id']; ?>)" title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal إنشاء قالب شهادة -->
<div class="modal fade" id="createTemplateModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إنشاء قالب شهادة جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="create_certificate_template">

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="course_id" class="form-label">الكورس <span class="text-danger">*</span></label>
                            <select name="course_id" id="course_id" class="form-select" required>
                                <option value="">-- اختر كورس --</option>
                                <?php foreach ($instructor_courses as $course): ?>
                                <option value="<?php echo $course['id']; ?>">
                                    <?php echo htmlspecialchars($course['title']); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="template_name" class="form-label">اسم القالب <span class="text-danger">*</span></label>
                            <input type="text" name="template_name" id="template_name" class="form-control"
                                   placeholder="مثال: شهادة إتمام الكورس الأساسي" required>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="certificate_text" class="form-label">نص الشهادة <span class="text-danger">*</span></label>
                        <textarea name="certificate_text" id="certificate_text" class="form-control" rows="6"
                                  placeholder="مثال: هذا يشهد أن {student_name} قد أتم بنجاح كورس {course_title} بتاريخ {completion_date}" required></textarea>
                        <div class="form-text">
                            يمكنك استخدام المتغيرات التالية: {student_name}, {course_title}, {completion_date}, {grade}, {instructor_name}
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="background_color" class="form-label">لون الخلفية</label>
                            <input type="color" name="background_color" id="background_color" class="form-control form-control-color"
                                   value="#ffffff" title="اختر لون الخلفية">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="text_color" class="form-label">لون النص</label>
                            <input type="color" name="text_color" id="text_color" class="form-control form-control-color"
                                   value="#000000" title="اختر لون النص">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="font_size" class="form-label">حجم الخط</label>
                            <select name="font_size" id="font_size" class="form-select">
                                <option value="small">صغير</option>
                                <option value="medium" selected>متوسط</option>
                                <option value="large">كبير</option>
                            </select>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="border_style" class="form-label">نمط الحدود</label>
                            <select name="border_style" id="border_style" class="form-select">
                                <option value="simple">بسيط</option>
                                <option value="decorative">زخرفي</option>
                                <option value="none">بدون حدود</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="form-check form-switch mt-4">
                                <input class="form-check-input" type="checkbox" name="is_active" id="is_active" checked>
                                <label class="form-check-label" for="is_active">
                                    قالب نشط
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- معاينة القالب -->
                    <div class="mb-3">
                        <label class="form-label">معاينة القالب</label>
                        <div id="templatePreview" class="border p-4 text-center"
                             style="background-color: #ffffff; color: #000000; min-height: 200px; border: 2px solid #ddd;">
                            <div class="preview-text">سيتم عرض معاينة القالب هنا...</div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">إنشاء القالب</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal إصدار شهادة -->
<div class="modal fade" id="issueCertificateModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إصدار شهادة جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="issue_certificate">

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="issue_course_id" class="form-label">الكورس <span class="text-danger">*</span></label>
                            <select name="course_id" id="issue_course_id" class="form-select" required onchange="loadCourseStudents()">
                                <option value="">-- اختر كورس --</option>
                                <?php foreach ($instructor_courses as $course): ?>
                                <option value="<?php echo $course['id']; ?>">
                                    <?php echo htmlspecialchars($course['title']); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="student_id" class="form-label">الطالب <span class="text-danger">*</span></label>
                            <select name="student_id" id="student_id" class="form-select" required disabled>
                                <option value="">-- اختر الكورس أولاً --</option>
                            </select>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="template_id" class="form-label">قالب الشهادة <span class="text-danger">*</span></label>
                            <select name="template_id" id="template_id" class="form-select" required disabled>
                                <option value="">-- اختر الكورس أولاً --</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="completion_date" class="form-label">تاريخ الإتمام <span class="text-danger">*</span></label>
                            <input type="date" name="completion_date" id="completion_date" class="form-control"
                                   value="<?php echo date('Y-m-d'); ?>" required>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="grade" class="form-label">الدرجة (اختياري)</label>
                            <input type="text" name="grade" id="grade" class="form-control"
                                   placeholder="مثال: ممتاز، 95%، A+">
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات (اختياري)</label>
                        <textarea name="notes" id="notes" class="form-control" rows="3"
                                  placeholder="ملاحظات إضافية حول الشهادة..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-success">إصدار الشهادة</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal حذف شهادة -->
<div class="modal fade" id="deleteCertificateModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title text-danger">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="delete_certificate">
                    <input type="hidden" name="certificate_id" id="deleteCertificateId">

                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        هل أنت متأكد من حذف هذه الشهادة؟
                    </div>

                    <p class="text-muted">
                        <strong>تحذير:</strong> سيتم حذف الشهادة نهائياً ولن يتمكن الطالب من الوصول إليها.
                        هذا الإجراء لا يمكن التراجع عنه.
                    </p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-danger">تأكيد الحذف</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// تحديث معاينة القالب
function updateTemplatePreview() {
    const certificateText = document.getElementById('certificate_text').value;
    const backgroundColor = document.getElementById('background_color').value;
    const textColor = document.getElementById('text_color').value;
    const fontSize = document.getElementById('font_size').value;
    const borderStyle = document.getElementById('border_style').value;

    const preview = document.getElementById('templatePreview');
    const previewText = preview.querySelector('.preview-text');

    // تحديث النص
    let displayText = certificateText || 'سيتم عرض معاينة القالب هنا...';
    displayText = displayText.replace('{student_name}', 'أحمد محمد');
    displayText = displayText.replace('{course_title}', 'كورس تجريبي');
    displayText = displayText.replace('{completion_date}', new Date().toLocaleDateString('ar-SA'));
    displayText = displayText.replace('{grade}', 'ممتاز');
    displayText = displayText.replace('{instructor_name}', 'المدرب');

    previewText.innerHTML = displayText.replace(/\n/g, '<br>');

    // تحديث التنسيق
    preview.style.backgroundColor = backgroundColor;
    preview.style.color = textColor;

    let fontSizeValue = '14px';
    if (fontSize === 'small') fontSizeValue = '12px';
    else if (fontSize === 'large') fontSizeValue = '16px';
    preview.style.fontSize = fontSizeValue;

    let borderStyleValue = '2px solid #ddd';
    if (borderStyle === 'decorative') borderStyleValue = '2px double #ddd';
    else if (borderStyle === 'none') borderStyleValue = 'none';
    preview.style.border = borderStyleValue;
}

// تحميل طلاب الكورس
function loadCourseStudents() {
    const courseId = document.getElementById('issue_course_id').value;
    const studentSelect = document.getElementById('student_id');
    const templateSelect = document.getElementById('template_id');

    if (!courseId) {
        studentSelect.disabled = true;
        templateSelect.disabled = true;
        studentSelect.innerHTML = '<option value="">-- اختر الكورس أولاً --</option>';
        templateSelect.innerHTML = '<option value="">-- اختر الكورس أولاً --</option>';
        return;
    }

    // تحميل الطلاب (يمكن تنفيذ هذا عبر AJAX)
    studentSelect.disabled = false;
    templateSelect.disabled = false;
    studentSelect.innerHTML = '<option value="">-- جاري التحميل... --</option>';
    templateSelect.innerHTML = '<option value="">-- جاري التحميل... --</option>';

    // محاكاة تحميل البيانات
    setTimeout(() => {
        studentSelect.innerHTML = `
            <option value="">-- اختر طالب --</option>
            <option value="1">أحمد محمد</option>
            <option value="2">فاطمة علي</option>
            <option value="3">محمد أحمد</option>
        `;

        templateSelect.innerHTML = `
            <option value="">-- اختر قالب --</option>
            <option value="1">قالب الشهادة الأساسي</option>
            <option value="2">قالب الشهادة المتقدم</option>
        `;
    }, 1000);
}

// معاينة قالب
function previewTemplate(templateId) {
    window.open(`certificate-preview.php?template_id=${templateId}`, '_blank', 'width=800,height=600');
}

// تعديل قالب
function editTemplate(templateId) {
    window.location.href = `edit-certificate-template.php?id=${templateId}`;
}

// حذف قالب
function deleteTemplate(templateId) {
    if (confirm('هل أنت متأكد من حذف هذا القالب؟ سيتم حذف جميع الشهادات المرتبطة به.')) {
        // يمكن تنفيذ هذا عبر AJAX أو form
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="delete_template">
            <input type="hidden" name="template_id" value="${templateId}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

// عرض شهادة
function viewCertificate(certificateId) {
    window.open(`certificate-view.php?id=${certificateId}`, '_blank', 'width=800,height=600');
}

// تحميل شهادة
function downloadCertificate(certificateId) {
    window.open(`certificate-download.php?id=${certificateId}`, '_blank');
}

// إرسال شهادة
function sendCertificate(certificateId) {
    if (confirm('هل تريد إرسال الشهادة إلى الطالب عبر البريد الإلكتروني؟')) {
        // يمكن تنفيذ هذا عبر AJAX
        alert('سيتم إرسال الشهادة إلى الطالب قريباً');
    }
}

// حذف شهادة
function deleteCertificate(certificateId) {
    document.getElementById('deleteCertificateId').value = certificateId;
    new bootstrap.Modal(document.getElementById('deleteCertificateModal')).show();
}

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // ربط أحداث تحديث المعاينة
    const previewInputs = ['certificate_text', 'background_color', 'text_color', 'font_size', 'border_style'];
    previewInputs.forEach(inputId => {
        const input = document.getElementById(inputId);
        if (input) {
            input.addEventListener('input', updateTemplatePreview);
            input.addEventListener('change', updateTemplatePreview);
        }
    });

    // تحديث المعاينة الأولية
    updateTemplatePreview();

    // تحسين النماذج
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function() {
            const submitButton = this.querySelector('button[type="submit"]');
            if (submitButton) {
                submitButton.disabled = true;
                submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري المعالجة...';
            }
        });
    });

    // تحسين التبويبات
    const tabButtons = document.querySelectorAll('#certificateTabs .nav-link');
    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            // حفظ التبويب النشط في localStorage
            localStorage.setItem('activeCertificateTab', this.id);
        });
    });

    // استعادة التبويب النشط
    const activeTab = localStorage.getItem('activeCertificateTab');
    if (activeTab) {
        const tabButton = document.getElementById(activeTab);
        if (tabButton) {
            tabButton.click();
        }
    }

    // إضافة تأثيرات بصرية للبطاقات
    const cards = document.querySelectorAll('.card');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
            this.style.boxShadow = '0 4px 15px rgba(0,0,0,0.1)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = '';
            this.style.boxShadow = '';
        });
    });
});

// دوال مساعدة
function exportCertificates() {
    window.open('export-certificates.php', '_blank');
}

function bulkIssueCertificates() {
    window.location.href = 'bulk-issue-certificates.php';
}

function certificateStatistics() {
    window.location.href = 'certificate-statistics.php';
}
</script>

<style>
.certificate-preview {
    border-radius: 0.5rem;
    transition: all 0.3s ease;
}

.certificate-preview:hover {
    transform: scale(1.02);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.nav-tabs .nav-link {
    border-radius: 0.5rem 0.5rem 0 0;
    border: none;
    color: var(--bs-gray-600);
    font-weight: 500;
}

.nav-tabs .nav-link.active {
    background-color: var(--bs-primary);
    color: white;
    border-color: var(--bs-primary);
}

.nav-tabs .nav-link:hover {
    background-color: rgba(var(--bs-primary-rgb), 0.1);
    border-color: transparent;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: var(--bs-gray-700);
    background-color: var(--bs-gray-50);
}

.btn-group .btn {
    border-radius: 0.375rem;
    margin-left: 0.25rem;
}

.form-control-color {
    width: 100%;
    height: 38px;
}

.badge {
    font-size: 0.75rem;
}

.card {
    transition: all 0.3s ease;
    border-radius: 0.5rem;
}

.card:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1) !important;
}

.modal-dialog {
    max-width: 800px;
}

@media (max-width: 768px) {
    .btn-group {
        display: flex;
        flex-direction: column;
        width: 100%;
    }

    .btn-group .btn {
        margin-left: 0;
        margin-bottom: 0.25rem;
        border-radius: 0.375rem;
    }

    .table-responsive {
        font-size: 0.875rem;
    }

    .modal-dialog {
        margin: 0.5rem;
        max-width: none;
    }

    .certificate-preview {
        font-size: 12px !important;
        padding: 15px !important;
        min-height: 120px !important;
    }
}

/* تحسين عرض الألوان */
.form-control-color::-webkit-color-swatch {
    border-radius: 0.25rem;
    border: none;
}

.form-control-color::-moz-color-swatch {
    border-radius: 0.25rem;
    border: none;
}

/* تحسين المعاينة */
#templatePreview {
    border-radius: 0.5rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

#templatePreview::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 49%, rgba(255,255,255,0.1) 50%, transparent 51%);
    pointer-events: none;
}

.preview-text {
    position: relative;
    z-index: 1;
    line-height: 1.6;
}

/* تحسين التبويبات */
.tab-content {
    padding-top: 1rem;
}

.tab-pane {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}
</style>

<?php include 'includes/footer.php'; ?>
