<?php
require_once '../config/database.php';

echo "<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>إنشاء بيانات تجريبية</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css' rel='stylesheet'>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f8f9fa; }
        .container { max-width: 800px; margin: 50px auto; }
        .log-item { padding: 10px; margin: 5px 0; border-radius: 5px; }
        .log-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .log-error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .log-info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
<div class='container'>
    <div class='card'>
        <div class='card-header bg-success text-white'>
            <h4 class='mb-0'><i class='fas fa-database me-2'></i>إنشاء بيانات تجريبية</h4>
        </div>
        <div class='card-body'>
            <div id='logContainer'>";

function logMessage($message, $type = 'info') {
    $icon = $type === 'success' ? 'check-circle' : ($type === 'error' ? 'exclamation-circle' : 'info-circle');
    echo "<div class='log-item log-$type'><i class='fas fa-$icon me-2'></i>$message</div>";
    flush();
    ob_flush();
}

try {
    $conn->beginTransaction();
    
    // إنشاء مدربين تجريبيين
    logMessage('إنشاء مدربين تجريبيين...', 'info');
    $instructors = [
        ['أحمد محمد', '<EMAIL>', 'خبير في البرمجة والتطوير'],
        ['فاطمة علي', '<EMAIL>', 'متخصصة في التصميم الجرافيكي'],
        ['محمد سالم', '<EMAIL>', 'مدرب في إدارة الأعمال'],
        ['نورا حسن', '<EMAIL>', 'خبيرة في التسويق الرقمي'],
        ['خالد أحمد', '<EMAIL>', 'متخصص في الذكاء الاصطناعي']
    ];
    
    $instructor_ids = [];
    foreach ($instructors as $instructor) {
        $password = password_hash('123456', PASSWORD_DEFAULT);
        $stmt = $conn->prepare("
            INSERT IGNORE INTO users (name, email, password, role, status, email_verified, bio, created_at) 
            VALUES (?, ?, ?, 'instructor', 'active', TRUE, ?, NOW())
        ");
        $stmt->execute([$instructor[0], $instructor[1], $password, $instructor[2]]);
        
        // جلب ID المدرب
        $stmt = $conn->prepare("SELECT id FROM users WHERE email = ?");
        $stmt->execute([$instructor[1]]);
        $instructor_ids[] = $stmt->fetchColumn();
    }
    logMessage('✓ تم إنشاء ' . count($instructors) . ' مدرب', 'success');
    
    // إنشاء طلاب تجريبيين
    logMessage('إنشاء طلاب تجريبيين...', 'info');
    $students = [
        ['سارة محمد', '<EMAIL>'],
        ['عبدالله أحمد', '<EMAIL>'],
        ['مريم سالم', '<EMAIL>'],
        ['يوسف علي', '<EMAIL>'],
        ['هند خالد', '<EMAIL>'],
        ['عمر حسن', '<EMAIL>'],
        ['ليلى محمود', '<EMAIL>'],
        ['كريم سعد', '<EMAIL>']
    ];
    
    $student_ids = [];
    foreach ($students as $student) {
        $password = password_hash('123456', PASSWORD_DEFAULT);
        $stmt = $conn->prepare("
            INSERT IGNORE INTO users (name, email, password, role, status, email_verified, created_at) 
            VALUES (?, ?, ?, 'student', 'active', TRUE, NOW())
        ");
        $stmt->execute([$student[0], $student[1], $password]);
        
        // جلب ID الطالب
        $stmt = $conn->prepare("SELECT id FROM users WHERE email = ?");
        $stmt->execute([$student[1]]);
        $student_ids[] = $stmt->fetchColumn();
    }
    logMessage('✓ تم إنشاء ' . count($students) . ' طالب', 'success');
    
    // إنشاء تصنيفات
    logMessage('إنشاء تصنيفات...', 'info');
    $categories = [
        ['البرمجة', 'programming', 'تعلم لغات البرمجة المختلفة'],
        ['التصميم', 'design', 'التصميم الجرافيكي وتصميم المواقع'],
        ['إدارة الأعمال', 'business', 'إدارة الأعمال والقيادة'],
        ['التسويق', 'marketing', 'التسويق الرقمي والتقليدي'],
        ['التكنولوجيا', 'technology', 'أحدث التقنيات والابتكارات']
    ];
    
    $category_ids = [];
    foreach ($categories as $category) {
        $stmt = $conn->prepare("
            INSERT IGNORE INTO categories (name, slug, description, created_at) 
            VALUES (?, ?, ?, NOW())
        ");
        $stmt->execute($category);
        
        // جلب ID التصنيف
        $stmt = $conn->prepare("SELECT id FROM categories WHERE slug = ?");
        $stmt->execute([$category[1]]);
        $category_ids[] = $stmt->fetchColumn();
    }
    logMessage('✓ تم إنشاء ' . count($categories) . ' تصنيف', 'success');
    
    // إنشاء كورسات تجريبية
    logMessage('إنشاء كورسات تجريبية...', 'info');
    $courses = [
        ['تعلم PHP من الصفر', 'مقدمة شاملة لتعلم لغة PHP', 'paid', 299.99, $instructor_ids[0]],
        ['أساسيات التصميم الجرافيكي', 'تعلم أساسيات التصميم باستخدام Photoshop', 'paid', 199.99, $instructor_ids[1]],
        ['إدارة المشاريع الناجحة', 'كيفية إدارة المشاريع بفعالية', 'free', 0, $instructor_ids[2]],
        ['التسويق عبر وسائل التواصل', 'استراتيجيات التسويق الرقمي', 'paid', 149.99, $instructor_ids[3]],
        ['مقدمة في الذكاء الاصطناعي', 'أساسيات الذكاء الاصطناعي والتعلم الآلي', 'paid', 399.99, $instructor_ids[4]],
        ['تطوير المواقع بـ HTML/CSS', 'تعلم تطوير المواقع من البداية', 'free', 0, $instructor_ids[0]],
        ['التصوير الفوتوغرافي', 'أساسيات التصوير الاحترافي', 'paid', 179.99, $instructor_ids[1]],
        ['ريادة الأعمال', 'كيف تبدأ مشروعك الخاص', 'free', 0, $instructor_ids[2]],
        ['إعلانات جوجل', 'إتقان إعلانات Google Ads', 'paid', 249.99, $instructor_ids[3]],
        ['تطوير تطبيقات الجوال', 'تطوير التطبيقات باستخدام React Native', 'paid', 349.99, $instructor_ids[4]]
    ];
    
    $course_ids = [];
    foreach ($courses as $index => $course) {
        $stmt = $conn->prepare("
            INSERT INTO courses (title, description, course_type, price, instructor_id, status, created_at) 
            VALUES (?, ?, ?, ?, ?, 'active', NOW())
        ");
        $stmt->execute([$course[0], $course[1], $course[2], $course[3], $course[4]]);
        $course_id = $conn->lastInsertId();
        $course_ids[] = $course_id;
        
        // ربط الكورس بتصنيف
        $category_id = $category_ids[$index % count($category_ids)];
        $stmt = $conn->prepare("
            INSERT IGNORE INTO course_categories (course_id, category_id) 
            VALUES (?, ?)
        ");
        $stmt->execute([$course_id, $category_id]);
    }
    logMessage('✓ تم إنشاء ' . count($courses) . ' كورس', 'success');
    
    // إنشاء تسجيلات في الكورسات
    logMessage('إنشاء تسجيلات في الكورسات...', 'info');
    $enrollment_count = 0;
    foreach ($course_ids as $course_id) {
        // تسجيل عشوائي للطلاب في الكورسات
        $enrolled_students = array_rand($student_ids, rand(2, 5));
        if (!is_array($enrolled_students)) {
            $enrolled_students = [$enrolled_students];
        }
        
        foreach ($enrolled_students as $student_index) {
            $student_id = $student_ids[$student_index];
            
            // جلب معلومات الكورس
            $stmt = $conn->prepare("SELECT course_type, price FROM courses WHERE id = ?");
            $stmt->execute([$course_id]);
            $course_info = $stmt->fetch();
            
            $payment_status = 'completed';
            $payment_amount = $course_info['course_type'] === 'paid' ? $course_info['price'] : 0;
            
            $stmt = $conn->prepare("
                INSERT IGNORE INTO course_enrollments (student_id, course_id, payment_status, payment_amount, enrolled_at) 
                VALUES (?, ?, ?, ?, NOW())
            ");
            $stmt->execute([$student_id, $course_id, $payment_status, $payment_amount]);
            $enrollment_count++;
        }
    }
    logMessage('✓ تم إنشاء ' . $enrollment_count . ' تسجيل في الكورسات', 'success');
    
    // إنشاء جلسات تجريبية
    logMessage('إنشاء جلسات تجريبية...', 'info');
    $session_count = 0;
    foreach (array_slice($course_ids, 0, 5) as $course_id) {
        for ($i = 1; $i <= 3; $i++) {
            $start_time = date('Y-m-d H:i:s', strtotime("+$i days +2 hours"));
            $end_time = date('Y-m-d H:i:s', strtotime("+$i days +4 hours"));
            
            $stmt = $conn->prepare("
                INSERT INTO sessions (course_id, title, description, start_time, end_time, status, created_at) 
                VALUES (?, ?, ?, ?, ?, 'scheduled', NOW())
            ");
            $stmt->execute([
                $course_id, 
                "الجلسة رقم $i", 
                "وصف الجلسة رقم $i للكورس", 
                $start_time, 
                $end_time
            ]);
            $session_count++;
        }
    }
    logMessage('✓ تم إنشاء ' . $session_count . ' جلسة', 'success');
    
    // إنشاء طلبات انضمام تجريبية
    logMessage('إنشاء طلبات انضمام...', 'info');
    $join_requests_count = 0;
    for ($i = 0; $i < 5; $i++) {
        $student_id = $student_ids[array_rand($student_ids)];
        $instructor_id = $instructor_ids[array_rand($instructor_ids)];
        
        $stmt = $conn->prepare("
            INSERT IGNORE INTO join_requests (user_id, instructor_id, status, created_at) 
            VALUES (?, ?, 'pending', NOW())
        ");
        $stmt->execute([$student_id, $instructor_id]);
        $join_requests_count++;
    }
    logMessage('✓ تم إنشاء ' . $join_requests_count . ' طلب انضمام', 'success');
    
    $conn->commit();
    
    logMessage('تم إنشاء جميع البيانات التجريبية بنجاح!', 'success');
    
    // عرض ملخص البيانات
    logMessage('ملخص البيانات المنشأة:', 'info');
    logMessage("• " . count($instructors) . " مدرب", 'info');
    logMessage("• " . count($students) . " طالب", 'info');
    logMessage("• " . count($categories) . " تصنيف", 'info');
    logMessage("• " . count($courses) . " كورس", 'info');
    logMessage("• $enrollment_count تسجيل في الكورسات", 'info');
    logMessage("• $session_count جلسة", 'info');
    logMessage("• $join_requests_count طلب انضمام", 'info');
    
} catch (Exception $e) {
    $conn->rollBack();
    logMessage('خطأ: ' . $e->getMessage(), 'error');
}

echo "</div>
        <div class='mt-4 text-center'>
            <div class='alert alert-success'>
                <h5><i class='fas fa-check-circle me-2'></i>تم إنشاء البيانات التجريبية بنجاح!</h5>
                <p class='mb-0'>يمكنك الآن مشاهدة البيانات في لوحة التحكم</p>
            </div>
            <a href='dashboard.php' class='btn btn-primary me-2'>
                <i class='fas fa-tachometer-alt me-2'></i>لوحة التحكم
            </a>
            <a href='quick-test.php' class='btn btn-info'>
                <i class='fas fa-vial me-2'></i>اختبار البيانات
            </a>
        </div>
    </div>
</div>
</body>
</html>";
?>
