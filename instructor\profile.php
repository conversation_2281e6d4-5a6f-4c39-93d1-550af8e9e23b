<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات المدرب
if (!isLoggedIn() || !isInstructor()) {
    header('Location: ../login.php');
    exit;
}

$pageTitle = 'الملف الشخصي';
$breadcrumbs = [
    ['title' => 'الملف الشخصي']
];

// جلب بيانات المدرب
try {
    $stmt = $conn->prepare("
        SELECT u.*,
               COUNT(DISTINCT c.id) as total_courses,
               COUNT(DISTINCT e.id) as total_students,
               AVG(cr.rating) as avg_rating,
               COUNT(DISTINCT cr.id) as total_reviews
        FROM users u
        LEFT JOIN courses c ON u.id = c.instructor_id AND c.status = 'active'
        LEFT JOIN course_enrollments e ON c.id = e.course_id AND e.status = 'approved'
        LEFT JOIN course_reviews cr ON c.id = cr.course_id
        WHERE u.id = ?
        GROUP BY u.id
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $instructor = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$instructor) {
        header('Location: ../login.php');
        exit;
    }

    // التأكد من وجود قيم افتراضية للحقول
    $instructor['avg_rating'] = $instructor['avg_rating'] ?: 0;
    $instructor['total_reviews'] = $instructor['total_reviews'] ?: 0;
    $instructor['total_courses'] = $instructor['total_courses'] ?: 0;
    $instructor['total_students'] = $instructor['total_students'] ?: 0;

    // جلب الكورسات الأخيرة
    $stmt = $conn->prepare("
        SELECT c.*, COUNT(e.id) as student_count
        FROM courses c
        LEFT JOIN course_enrollments e ON c.id = e.course_id AND e.status = 'approved'
        WHERE c.instructor_id = ?
        GROUP BY c.id
        ORDER BY c.created_at DESC
        LIMIT 5
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $recent_courses = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // جلب التقييمات الأخيرة
    $stmt = $conn->prepare("
        SELECT cr.*, u.name as student_name, c.title as course_title
        FROM course_reviews cr
        INNER JOIN users u ON cr.user_id = u.id
        INNER JOIN courses c ON cr.course_id = c.id
        WHERE c.instructor_id = ?
        ORDER BY cr.created_at DESC
        LIMIT 5
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $recent_reviews = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // جلب الإنجازات
    $stmt = $conn->prepare("
        SELECT
            COALESCE(SUM(CASE WHEN c.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 ELSE 0 END), 0) as courses_this_month,
            COALESCE(SUM(CASE WHEN e.enrolled_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 ELSE 0 END), 0) as enrollments_this_month,
            COALESCE(SUM(cv.duration), 0) as total_video_hours
        FROM courses c
        LEFT JOIN course_enrollments e ON c.id = e.course_id
        LEFT JOIN course_videos cv ON c.id = cv.course_id
        WHERE c.instructor_id = ?
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $achievements = $stmt->fetch(PDO::FETCH_ASSOC);

    // التأكد من وجود قيم افتراضية
    if (!$achievements) {
        $achievements = [
            'courses_this_month' => 0,
            'enrollments_this_month' => 0,
            'total_video_hours' => 0
        ];
    }

} catch (PDOException $e) {
    $error_message = 'حدث خطأ أثناء جلب البيانات';
    // قيم افتراضية في حالة الخطأ
    $achievements = [
        'courses_this_month' => 0,
        'enrollments_this_month' => 0,
        'total_video_hours' => 0
    ];
}

include 'includes/header.php';
?>

<!-- رأس الصفحة -->
<div class="row">
    <div class="col-lg-8">
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <div class="avatar-lg">
                            <?php if ($instructor['profile_image']): ?>
                            <img src="<?php echo htmlspecialchars($instructor['profile_image']); ?>" 
                                 alt="صورة المدرب" class="rounded-circle" width="80" height="80">
                            <?php else: ?>
                            <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center" 
                                 style="width: 80px; height: 80px;">
                                <i class="fas fa-user text-white fs-2"></i>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="col">
                        <h3 class="mb-1"><?php echo htmlspecialchars($instructor['name']); ?></h3>
                        <p class="text-muted mb-2"><?php echo htmlspecialchars($instructor['specialization'] ?: 'مدرب'); ?></p>
                        <div class="d-flex align-items-center gap-3">
                            <div class="d-flex align-items-center">
                                <div class="text-warning me-1">
                                    <?php for ($i = 1; $i <= 5; $i++): ?>
                                    <i class="fas fa-star<?php echo $i <= round($instructor['avg_rating']) ? '' : '-o'; ?>"></i>
                                    <?php endfor; ?>
                                </div>
                                <span class="text-muted small">
                                    (<?php echo number_format($instructor['avg_rating'], 1); ?> - <?php echo $instructor['total_reviews']; ?> تقييم)
                                </span>
                            </div>
                            <span class="badge bg-success"><?php echo $instructor['total_courses']; ?> كورس</span>
                            <span class="badge bg-primary"><?php echo $instructor['total_students']; ?> طالب</span>
                        </div>
                    </div>
                    <div class="col-auto">
                        <a href="settings.php" class="btn btn-outline-primary">
                            <i class="fas fa-edit me-1"></i>تعديل الملف
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white border-bottom">
                <h6 class="mb-0">
                    <i class="fas fa-trophy text-warning me-2"></i>
                    الإنجازات
                </h6>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span class="text-muted">كورسات هذا الشهر</span>
                    <span class="badge bg-primary"><?php echo $achievements['courses_this_month']; ?></span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span class="text-muted">تسجيلات جديدة</span>
                    <span class="badge bg-success"><?php echo $achievements['enrollments_this_month']; ?></span>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                    <span class="text-muted">ساعات الفيديو</span>
                    <span class="badge bg-info"><?php echo number_format($achievements['total_video_hours'], 1); ?></span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- معلومات المدرب -->
<div class="row">
    <div class="col-lg-8">
        <!-- النبذة الشخصية -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white border-bottom">
                <h6 class="mb-0">
                    <i class="fas fa-user me-2"></i>
                    النبذة الشخصية
                </h6>
            </div>
            <div class="card-body">
                <?php if ($instructor['bio']): ?>
                <p class="mb-0"><?php echo nl2br(htmlspecialchars($instructor['bio'])); ?></p>
                <?php else: ?>
                <p class="text-muted mb-0">لم يتم إضافة نبذة شخصية بعد.</p>
                <a href="settings.php" class="btn btn-sm btn-outline-primary mt-2">إضافة نبذة</a>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- الخبرة والتعليم -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white border-bottom">
                <h6 class="mb-0">
                    <i class="fas fa-graduation-cap me-2"></i>
                    الخبرة والتعليم
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-muted mb-2">سنوات الخبرة</h6>
                        <p class="mb-3"><?php echo $instructor['experience_years'] ?: 'غير محدد'; ?> سنة</p>
                        
                        <h6 class="text-muted mb-2">المؤهل العلمي</h6>
                        <p class="mb-0"><?php echo htmlspecialchars($instructor['education'] ?: 'غير محدد'); ?></p>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-muted mb-2">التخصص</h6>
                        <p class="mb-3"><?php echo htmlspecialchars($instructor['specialization'] ?: 'غير محدد'); ?></p>
                        
                        <h6 class="text-muted mb-2">الشهادات</h6>
                        <?php if ($instructor['certifications']): ?>
                        <p class="mb-0"><?php echo nl2br(htmlspecialchars($instructor['certifications'])); ?></p>
                        <?php else: ?>
                        <p class="text-muted mb-0">لا توجد شهادات مضافة</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- الكورسات الأخيرة -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white border-bottom d-flex justify-content-between align-items-center">
                <h6 class="mb-0">
                    <i class="fas fa-book me-2"></i>
                    الكورسات الأخيرة
                </h6>
                <a href="courses.php" class="btn btn-sm btn-outline-primary">عرض الكل</a>
            </div>
            <div class="card-body">
                <?php if (empty($recent_courses)): ?>
                <p class="text-muted mb-0">لا توجد كورسات بعد.</p>
                <a href="add-course.php" class="btn btn-sm btn-primary mt-2">إنشاء كورس جديد</a>
                <?php else: ?>
                <div class="list-group list-group-flush">
                    <?php foreach ($recent_courses as $course): ?>
                    <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                        <div>
                            <h6 class="mb-1"><?php echo htmlspecialchars($course['title']); ?></h6>
                            <small class="text-muted">
                                <?php echo $course['student_count']; ?> طالب • 
                                <?php echo date('Y-m-d', strtotime($course['created_at'])); ?>
                            </small>
                        </div>
                        <div>
                            <span class="badge bg-<?php echo $course['status'] === 'active' ? 'success' : 'secondary'; ?>">
                                <?php echo $course['status'] === 'active' ? 'نشط' : 'غير نشط'; ?>
                            </span>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- معلومات الاتصال -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white border-bottom">
                <h6 class="mb-0">
                    <i class="fas fa-address-card me-2"></i>
                    معلومات الاتصال
                </h6>
            </div>
            <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                    <i class="fas fa-envelope text-muted me-3"></i>
                    <div>
                        <small class="text-muted d-block">البريد الإلكتروني</small>
                        <span><?php echo htmlspecialchars($instructor['email']); ?></span>
                    </div>
                </div>
                
                <?php if ($instructor['phone']): ?>
                <div class="d-flex align-items-center mb-3">
                    <i class="fas fa-phone text-muted me-3"></i>
                    <div>
                        <small class="text-muted d-block">رقم الهاتف</small>
                        <span><?php echo htmlspecialchars($instructor['phone']); ?></span>
                    </div>
                </div>
                <?php endif; ?>
                
                <?php if ($instructor['website_url']): ?>
                <div class="d-flex align-items-center mb-3">
                    <i class="fas fa-globe text-muted me-3"></i>
                    <div>
                        <small class="text-muted d-block">الموقع الشخصي</small>
                        <a href="<?php echo htmlspecialchars($instructor['website_url']); ?>" target="_blank" class="text-decoration-none">
                            زيارة الموقع
                        </a>
                    </div>
                </div>
                <?php endif; ?>
                
                <?php if ($instructor['linkedin_url']): ?>
                <div class="d-flex align-items-center">
                    <i class="fab fa-linkedin text-muted me-3"></i>
                    <div>
                        <small class="text-muted d-block">LinkedIn</small>
                        <a href="<?php echo htmlspecialchars($instructor['linkedin_url']); ?>" target="_blank" class="text-decoration-none">
                            عرض الملف
                        </a>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- التقييمات الأخيرة -->
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-bottom d-flex justify-content-between align-items-center">
                <h6 class="mb-0">
                    <i class="fas fa-star me-2"></i>
                    التقييمات الأخيرة
                </h6>
                <a href="reviews.php" class="btn btn-sm btn-outline-primary">عرض الكل</a>
            </div>
            <div class="card-body">
                <?php if (empty($recent_reviews)): ?>
                <p class="text-muted mb-0">لا توجد تقييمات بعد.</p>
                <?php else: ?>
                <?php foreach ($recent_reviews as $review): ?>
                <div class="mb-3 pb-3 <?php echo !end($recent_reviews) === $review ? 'border-bottom' : ''; ?>">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <div>
                            <h6 class="mb-1"><?php echo htmlspecialchars($review['student_name']); ?></h6>
                            <small class="text-muted"><?php echo htmlspecialchars($review['course_title']); ?></small>
                        </div>
                        <div class="text-warning">
                            <?php for ($i = 1; $i <= 5; $i++): ?>
                            <i class="fas fa-star<?php echo $i <= $review['rating'] ? '' : '-o'; ?>"></i>
                            <?php endfor; ?>
                        </div>
                    </div>
                    <?php if ($review['comment']): ?>
                    <p class="text-muted small mb-0"><?php echo htmlspecialchars($review['comment']); ?></p>
                    <?php endif; ?>
                </div>
                <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<script>
// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // إضافة تأثيرات بصرية للبطاقات
    const cards = document.querySelectorAll('.card');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
            this.style.boxShadow = '0 4px 15px rgba(0,0,0,0.1)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = '';
            this.style.boxShadow = '';
        });
    });

    // تحسين عرض النجوم
    const starRatings = document.querySelectorAll('.text-warning');
    starRatings.forEach(rating => {
        const stars = rating.querySelectorAll('.fas');
        stars.forEach((star, index) => {
            star.addEventListener('mouseenter', function() {
                for (let i = 0; i <= index; i++) {
                    stars[i].style.color = '#ffc107';
                    stars[i].style.transform = 'scale(1.1)';
                }
            });

            star.addEventListener('mouseleave', function() {
                stars.forEach(s => {
                    s.style.color = '';
                    s.style.transform = '';
                });
            });
        });
    });
});

// دوال مساعدة
function shareProfile() {
    const url = window.location.href;
    if (navigator.share) {
        navigator.share({
            title: 'ملف المدرب - <?php echo htmlspecialchars($instructor['name']); ?>',
            url: url
        });
    } else {
        navigator.clipboard.writeText(url).then(() => {
            alert('تم نسخ رابط الملف الشخصي');
        });
    }
}

function printProfile() {
    window.print();
}
</script>

<style>
.avatar-lg {
    position: relative;
}

.avatar-lg img,
.avatar-lg > div {
    transition: all 0.3s ease;
}

.avatar-lg:hover img,
.avatar-lg:hover > div {
    transform: scale(1.05);
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.card {
    transition: all 0.3s ease;
    border-radius: 0.5rem;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1) !important;
}

.card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 0.5rem 0.5rem 0 0 !important;
}

.badge {
    font-size: 0.75rem;
    padding: 0.5rem 0.75rem;
    border-radius: 0.375rem;
}

.list-group-item {
    transition: background-color 0.3s ease;
    border: none;
    padding: 1rem 0;
}

.list-group-item:hover {
    background-color: rgba(0,123,255,0.05);
}

.text-warning .fas {
    transition: all 0.3s ease;
}

.btn {
    transition: all 0.3s ease;
    border-radius: 0.375rem;
}

.btn:hover {
    transform: translateY(-1px);
}

@media (max-width: 768px) {
    .avatar-lg {
        text-align: center;
        margin-bottom: 1rem;
    }

    .d-flex.align-items-center.gap-3 {
        flex-direction: column;
        align-items: flex-start !important;
        gap: 0.5rem !important;
    }
}
</style>

<?php include 'includes/footer.php'; ?>
