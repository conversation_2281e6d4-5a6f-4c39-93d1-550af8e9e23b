<?php
/**
 * إلغاء قفل الحسابات وإعادة تعيين نظام الأمان
 * Unlock accounts and reset security system
 */

require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>إلغاء قفل الحسابات</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>";
echo "</head>";
echo "<body>";
echo "<div class='container mt-4'>";
echo "<h2>🔓 إلغاء قفل الحسابات وإعادة تعيين الأمان</h2>";

try {
    // 1. فحص جدول محاولات تسجيل الدخول
    echo "<h4>🔍 فحص جدول محاولات تسجيل الدخول</h4>";
    
    $stmt = $conn->query("SHOW TABLES LIKE 'login_attempts'");
    if ($stmt->rowCount() > 0) {
        echo "<div class='alert alert-info'>ℹ️ جدول login_attempts موجود</div>";
        
        // عرض محاولات تسجيل الدخول الحالية
        $stmt = $conn->query("SELECT * FROM login_attempts ORDER BY attempt_time DESC LIMIT 10");
        $attempts = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($attempts)) {
            echo "<div class='table-responsive'>";
            echo "<table class='table table-striped'>";
            echo "<thead><tr><th>البريد الإلكتروني</th><th>IP</th><th>وقت المحاولة</th><th>نجحت</th></tr></thead>";
            echo "<tbody>";
            foreach ($attempts as $attempt) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($attempt['email']) . "</td>";
                echo "<td>" . htmlspecialchars($attempt['ip_address']) . "</td>";
                echo "<td>" . $attempt['attempt_time'] . "</td>";
                echo "<td>" . ($attempt['successful'] ? '<span class="badge bg-success">نعم</span>' : '<span class="badge bg-danger">لا</span>') . "</td>";
                echo "</tr>";
            }
            echo "</tbody></table>";
            echo "</div>";
        }
        
        // حذف جميع محاولات تسجيل الدخول الفاشلة
        $stmt = $conn->exec("DELETE FROM login_attempts WHERE successful = 0");
        echo "<div class='alert alert-success'>✅ تم حذف $stmt محاولة تسجيل دخول فاشلة</div>";
        
    } else {
        echo "<div class='alert alert-warning'>⚠️ جدول login_attempts غير موجود</div>";
    }

    // 2. فحص وإلغاء قفل المستخدمين
    echo "<h4>👥 فحص وإلغاء قفل المستخدمين</h4>";
    
    // التحقق من وجود عمود locked_until في جدول users
    $stmt = $conn->query("SHOW COLUMNS FROM users LIKE 'locked_until'");
    if ($stmt->rowCount() > 0) {
        echo "<div class='alert alert-info'>ℹ️ عمود locked_until موجود</div>";
        
        // عرض المستخدمين المقفلين
        $stmt = $conn->query("SELECT id, name, email, locked_until FROM users WHERE locked_until > NOW()");
        $locked_users = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($locked_users)) {
            echo "<div class='alert alert-warning'>⚠️ يوجد " . count($locked_users) . " مستخدم مقفل:</div>";
            echo "<div class='table-responsive'>";
            echo "<table class='table table-striped'>";
            echo "<thead><tr><th>الاسم</th><th>البريد الإلكتروني</th><th>مقفل حتى</th></tr></thead>";
            echo "<tbody>";
            foreach ($locked_users as $user) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($user['name']) . "</td>";
                echo "<td>" . htmlspecialchars($user['email']) . "</td>";
                echo "<td>" . $user['locked_until'] . "</td>";
                echo "</tr>";
            }
            echo "</tbody></table>";
            echo "</div>";
        }
        
        // إلغاء قفل جميع المستخدمين
        $stmt = $conn->exec("UPDATE users SET locked_until = NULL WHERE locked_until IS NOT NULL");
        echo "<div class='alert alert-success'>✅ تم إلغاء قفل جميع المستخدمين</div>";
        
    } else {
        echo "<div class='alert alert-info'>ℹ️ عمود locked_until غير موجود - لا توجد آلية قفل</div>";
    }

    // 3. فحص وإعادة تعيين عدد محاولات تسجيل الدخول
    $stmt = $conn->query("SHOW COLUMNS FROM users LIKE 'failed_login_attempts'");
    if ($stmt->rowCount() > 0) {
        $stmt = $conn->exec("UPDATE users SET failed_login_attempts = 0");
        echo "<div class='alert alert-success'>✅ تم إعادة تعيين عدد محاولات تسجيل الدخول الفاشلة لجميع المستخدمين</div>";
    }

    // 4. تنظيف جلسات PHP
    echo "<h4>🧹 تنظيف الجلسات</h4>";
    
    // حذف ملفات الجلسات القديمة
    $session_path = session_save_path();
    if (empty($session_path)) {
        $session_path = sys_get_temp_dir();
    }
    
    echo "<div class='alert alert-info'>ℹ️ مسار الجلسات: $session_path</div>";
    
    // بدء جلسة جديدة وتدميرها لتنظيف البيانات
    session_start();
    session_destroy();
    echo "<div class='alert alert-success'>✅ تم تنظيف بيانات الجلسة</div>";

    // 5. إعادة تعيين كلمات المرور للحسابات الافتراضية
    echo "<h4>🔑 إعادة تعيين كلمات المرور</h4>";
    
    $default_accounts = [
        ['<EMAIL>', 'admin123', 'مدير النظام'],
        ['<EMAIL>', 'instructor123', 'أحمد محمد - مدرب'],
        ['<EMAIL>', 'student123', 'سارة أحمد - طالبة']
    ];
    
    foreach ($default_accounts as $account) {
        $email = $account[0];
        $password = $account[1];
        $name = $account[2];
        $hashed_password = password_hash($password, PASSWORD_DEFAULT);
        
        $stmt = $conn->prepare("UPDATE users SET password = ?, name = ?, status = 'active', locked_until = NULL, failed_login_attempts = 0 WHERE email = ?");
        $stmt->execute([$hashed_password, $name, $email]);
        
        if ($stmt->rowCount() > 0) {
            echo "<div class='alert alert-success'>✅ تم إعادة تعيين كلمة المرور لـ: $email</div>";
        } else {
            echo "<div class='alert alert-warning'>⚠️ لم يتم العثور على المستخدم: $email</div>";
        }
    }

    // 6. اختبار تسجيل الدخول
    echo "<h4>🧪 اختبار تسجيل الدخول</h4>";
    
    foreach ($default_accounts as $account) {
        $email = $account[0];
        $password = $account[1];
        
        $stmt = $conn->prepare("SELECT id, name, password, role, status FROM users WHERE email = ?");
        $stmt->execute([$email]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($user) {
            if ($user['status'] === 'active') {
                if (password_verify($password, $user['password'])) {
                    echo "<div class='alert alert-success'>";
                    echo "✅ <strong>$email</strong> - كلمة المرور: <code>$password</code> ✓ جاهز للاستخدام";
                    echo "</div>";
                } else {
                    echo "<div class='alert alert-danger'>";
                    echo "❌ <strong>$email</strong> - كلمة المرور غير صحيحة";
                    echo "</div>";
                }
            } else {
                echo "<div class='alert alert-warning'>";
                echo "⚠️ <strong>$email</strong> - الحساب غير نشط";
                echo "</div>";
            }
        } else {
            echo "<div class='alert alert-danger'>";
            echo "❌ <strong>$email</strong> - المستخدم غير موجود";
            echo "</div>";
        }
    }

    // 7. فحص ملفات الأمان
    echo "<h4>📄 فحص ملفات الأمان</h4>";
    
    $security_files = [
        'includes/security.php' => 'ملف الأمان الرئيسي',
        'includes/session_config.php' => 'إعدادات الجلسة',
        'login.php' => 'صفحة تسجيل الدخول'
    ];
    
    foreach ($security_files as $file => $description) {
        if (file_exists($file)) {
            echo "<div class='alert alert-success'>✅ $description موجود</div>";
        } else {
            echo "<div class='alert alert-danger'>❌ $description غير موجود</div>";
        }
    }

    // 8. إعادة تعيين إعدادات الأمان في قاعدة البيانات
    echo "<h4>⚙️ إعادة تعيين إعدادات الأمان</h4>";
    
    // إنشاء/تحديث جدول إعدادات النظام
    $stmt = $conn->query("SHOW TABLES LIKE 'system_settings'");
    if ($stmt->rowCount() == 0) {
        $conn->exec("
            CREATE TABLE system_settings (
                id INT AUTO_INCREMENT PRIMARY KEY,
                setting_key VARCHAR(255) UNIQUE NOT NULL,
                setting_value TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        echo "<div class='alert alert-success'>✅ تم إنشاء جدول إعدادات النظام</div>";
    }
    
    // إعادة تعيين إعدادات الأمان
    $security_settings = [
        ['max_login_attempts', '5'],
        ['lockout_duration', '15'],
        ['session_timeout', '3600'],
        ['password_min_length', '6'],
        ['enable_account_lockout', '0'] // تعطيل قفل الحسابات مؤقتاً
    ];
    
    foreach ($security_settings as $setting) {
        $stmt = $conn->prepare("INSERT INTO system_settings (setting_key, setting_value) VALUES (?, ?) ON DUPLICATE KEY UPDATE setting_value = ?");
        $stmt->execute([$setting[0], $setting[1], $setting[1]]);
    }
    
    echo "<div class='alert alert-success'>✅ تم إعادة تعيين إعدادات الأمان (قفل الحسابات معطل مؤقتاً)</div>";

    // 9. عرض بيانات تسجيل الدخول النهائية
    echo "<h4>🔐 بيانات تسجيل الدخول الجاهزة</h4>";
    
    echo "<div class='row'>";
    
    foreach ($default_accounts as $account) {
        $email = $account[0];
        $password = $account[1];
        $name = $account[2];
        
        $role_info = '';
        $card_class = '';
        $icon = '';
        
        if (strpos($email, 'admin') !== false) {
            $role_info = 'إدارة النظام بالكامل';
            $card_class = 'border-danger';
            $icon = 'fas fa-user-shield';
        } elseif (strpos($email, 'instructor') !== false) {
            $role_info = 'إدارة الكورسات والطلاب';
            $card_class = 'border-primary';
            $icon = 'fas fa-chalkboard-teacher';
        } else {
            $role_info = 'الوصول للكورسات والجلسات';
            $card_class = 'border-success';
            $icon = 'fas fa-user-graduate';
        }
        
        echo "<div class='col-md-4 mb-3'>";
        echo "<div class='card $card_class'>";
        echo "<div class='card-header'>";
        echo "<h6 class='mb-0'><i class='$icon'></i> $name</h6>";
        echo "</div>";
        echo "<div class='card-body'>";
        echo "<p><strong>البريد:</strong> <code>$email</code></p>";
        echo "<p><strong>كلمة المرور:</strong> <code>$password</code></p>";
        echo "<p><strong>الصلاحيات:</strong> $role_info</p>";
        echo "<div class='text-center'>";
        echo "<span class='badge bg-success'>✅ جاهز للاستخدام</span>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
    }
    
    echo "</div>";

    echo "<div class='alert alert-success mt-4'>";
    echo "<h5>🎉 تم إلغاء قفل جميع الحسابات بنجاح!</h5>";
    echo "<ul class='mb-0'>";
    echo "<li>تم حذف جميع محاولات تسجيل الدخول الفاشلة</li>";
    echo "<li>تم إلغاء قفل جميع المستخدمين</li>";
    echo "<li>تم إعادة تعيين كلمات المرور</li>";
    echo "<li>تم تعطيل نظام قفل الحسابات مؤقتاً</li>";
    echo "<li>تم تنظيف بيانات الجلسات</li>";
    echo "</ul>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div class='alert alert-danger'>❌ خطأ: " . htmlspecialchars($e->getMessage()) . "</div>";
}

echo "<div class='mt-4'>";
echo "<a href='login.php' class='btn btn-primary btn-lg me-2'>🔐 تسجيل الدخول الآن</a>";
echo "<a href='fix_login_credentials.php' class='btn btn-info btn-lg me-2'>🔧 إصلاح بيانات الدخول</a>";
echo "<a href='system_health_check.php' class='btn btn-warning btn-lg me-2'>🔍 فحص النظام</a>";
echo "<a href='index.php' class='btn btn-success btn-lg'>🏠 الصفحة الرئيسية</a>";
echo "</div>";

echo "<div class='mt-4'>";
echo "<div class='alert alert-info'>";
echo "<h6>💡 نصائح مهمة:</h6>";
echo "<ul class='mb-0'>";
echo "<li>استخدم البيانات المعروضة أعلاه بالضبط</li>";
echo "<li>تأكد من اختيار نوع الحساب الصحيح (مدرب)</li>";
echo "<li>إذا واجهت مشاكل، شغل فحص النظام أولاً</li>";
echo "<li>نظام قفل الحسابات معطل مؤقتاً لتجنب المشاكل</li>";
echo "</ul>";
echo "</div>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
