<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات المدرب
if (!isLoggedIn() || !isInstructor()) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit;
}

$submission_id = $_GET['id'] ?? 0;

if (!$submission_id) {
    echo json_encode(['success' => false, 'message' => 'معرف التسليم مطلوب']);
    exit;
}

try {
    // جلب تفاصيل التسليم مع التحقق من صلاحيات المدرب
    $stmt = $conn->prepare("
        SELECT 
            asub.*,
            u.name as student_name,
            u.email as student_email,
            a.title as assignment_title,
            a.max_grade,
            a.due_date,
            c.title as course_title,
            grader.name as grader_name,
            CASE 
                WHEN asub.submission_date > a.due_date THEN 1 
                ELSE 0 
            END as is_late
        FROM assignment_submissions asub
        INNER JOIN users u ON asub.student_id = u.id
        INNER JOIN assignments a ON asub.assignment_id = a.id
        INNER JOIN courses c ON a.course_id = c.id
        LEFT JOIN users grader ON asub.graded_by = grader.id
        WHERE asub.id = ? AND c.instructor_id = ?
    ");
    $stmt->execute([$submission_id, $_SESSION['user_id']]);
    $submission = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$submission) {
        echo json_encode(['success' => false, 'message' => 'التسليم غير موجود أو لا تملك صلاحية للوصول إليه']);
        exit;
    }
    
    // تنسيق التواريخ
    $submission['submission_date'] = date('Y-m-d H:i', strtotime($submission['submission_date']));
    $submission['due_date'] = date('Y-m-d H:i', strtotime($submission['due_date']));
    
    if ($submission['graded_at']) {
        $submission['graded_at'] = date('Y-m-d H:i', strtotime($submission['graded_at']));
    }
    
    // تنسيق حالة التسليم
    $status_labels = [
        'draft' => 'مسودة',
        'submitted' => 'تم التسليم',
        'graded' => 'تم التقييم',
        'returned' => 'تم الإرجاع'
    ];
    
    $submission['status_label'] = $status_labels[$submission['status']] ?? $submission['status'];
    
    // معلومات الواجب
    $assignment = [
        'title' => $submission['assignment_title'],
        'max_grade' => $submission['max_grade'],
        'due_date' => $submission['due_date'],
        'course_title' => $submission['course_title']
    ];
    
    echo json_encode([
        'success' => true,
        'submission' => $submission,
        'assignment' => $assignment
    ]);
    
} catch (PDOException $e) {
    echo json_encode(['success' => false, 'message' => 'حدث خطأ أثناء جلب تفاصيل التسليم']);
}
?>
