<?php
require_once 'config/database.php';

echo "<h2>اختبار التسجيل</h2>";

// اختبار الاتصال بقاعدة البيانات
try {
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM users");
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "<p>✅ الاتصال بقاعدة البيانات ناجح</p>";
    echo "<p>عدد المستخدمين الحاليين: " . $result['count'] . "</p>";
} catch (Exception $e) {
    echo "<p>❌ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</p>";
}

// اختبار إنشاء مستخدم جديد
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = $_POST['name'];
    $email = $_POST['email'];
    $phone = $_POST['phone'];
    $password = password_hash($_POST['password'], PASSWORD_DEFAULT);
    $username = explode('@', $email)[0];
    
    try {
        $stmt = $conn->prepare("INSERT INTO users (name, email, phone, username, password, role, status, created_at) VALUES (?, ?, ?, ?, ?, 'student', 'pending', NOW())");
        $result = $stmt->execute([$name, $email, $phone, $username, $password]);
        
        if ($result) {
            echo "<p>✅ تم إنشاء المستخدم بنجاح!</p>";
            echo "<p>ID: " . $conn->lastInsertId() . "</p>";
        } else {
            echo "<p>❌ فشل في إنشاء المستخدم</p>";
        }
    } catch (Exception $e) {
        echo "<p>❌ خطأ في إنشاء المستخدم: " . $e->getMessage() . "</p>";
    }
}
?>

<form method="POST">
    <p>
        <label>الاسم:</label><br>
        <input type="text" name="name" required>
    </p>
    <p>
        <label>البريد الإلكتروني:</label><br>
        <input type="email" name="email" required>
    </p>
    <p>
        <label>الهاتف:</label><br>
        <input type="text" name="phone" required>
    </p>
    <p>
        <label>كلمة المرور:</label><br>
        <input type="password" name="password" required>
    </p>
    <p>
        <input type="submit" value="تسجيل">
    </p>
</form>
