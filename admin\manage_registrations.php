<?php
/**
 * إدارة طلبات التسجيل
 * Manage Registration Requests
 */

session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من صلاحيات المدير
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../login.php');
    exit();
}

$success = '';
$error = '';

// معالجة الطلبات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $user_id = (int)($_POST['user_id'] ?? 0);
    
    try {
        if ($action === 'approve' && $user_id > 0) {
            // الموافقة على الطلب
            $stmt = $conn->prepare("UPDATE users SET status = 'active' WHERE id = ? AND status = 'pending'");
            $result = $stmt->execute([$user_id]);
            
            if ($result && $stmt->rowCount() > 0) {
                // تحديث طلب الانضمام
                $stmt = $conn->prepare("UPDATE join_requests SET status = 'approved', processed_at = NOW(), processed_by = ? WHERE email = (SELECT email FROM users WHERE id = ?)");
                $stmt->execute([$_SESSION['user_id'], $user_id]);
                
                $success = 'تم قبول الطلب وتفعيل الحساب بنجاح';
                
                // تسجيل النشاط
                logUserActivity('approve_registration', "تم قبول طلب تسجيل المستخدم ID: {$user_id}");
            } else {
                $error = 'فشل في قبول الطلب';
            }
            
        } elseif ($action === 'reject' && $user_id > 0) {
            // رفض الطلب
            $rejection_reason = $_POST['rejection_reason'] ?? 'لم يتم تحديد السبب';
            
            $stmt = $conn->prepare("UPDATE users SET status = 'inactive' WHERE id = ? AND status = 'pending'");
            $result = $stmt->execute([$user_id]);
            
            if ($result && $stmt->rowCount() > 0) {
                // تحديث طلب الانضمام
                $stmt = $conn->prepare("UPDATE join_requests SET status = 'rejected', processed_at = NOW(), processed_by = ?, rejection_reason = ? WHERE email = (SELECT email FROM users WHERE id = ?)");
                $stmt->execute([$_SESSION['user_id'], $rejection_reason, $user_id]);
                
                $success = 'تم رفض الطلب';
                
                // تسجيل النشاط
                logUserActivity('reject_registration', "تم رفض طلب تسجيل المستخدم ID: {$user_id} - السبب: {$rejection_reason}");
            } else {
                $error = 'فشل في رفض الطلب';
            }
            
        } elseif ($action === 'approve_all') {
            // الموافقة على جميع الطلبات المعلقة
            $stmt = $conn->prepare("UPDATE users SET status = 'active' WHERE status = 'pending'");
            $result = $stmt->execute();
            $approved_count = $stmt->rowCount();
            
            if ($approved_count > 0) {
                // تحديث جميع طلبات الانضمام
                $stmt = $conn->prepare("UPDATE join_requests SET status = 'approved', processed_at = NOW(), processed_by = ? WHERE status = 'pending'");
                $stmt->execute([$_SESSION['user_id']]);
                
                $success = "تم قبول {$approved_count} طلب تسجيل";
                
                // تسجيل النشاط
                logUserActivity('approve_all_registrations', "تم قبول جميع طلبات التسجيل المعلقة ({$approved_count} طلب)");
            } else {
                $error = 'لا توجد طلبات معلقة للموافقة عليها';
            }
        }
        
    } catch (Exception $e) {
        $error = 'حدث خطأ: ' . $e->getMessage();
        error_log("Error in manage_registrations.php: " . $e->getMessage());
    }
}

// جلب الطلبات المعلقة
try {
    $stmt = $conn->prepare("
        SELECT u.id, u.name, u.email, u.phone, u.created_at, u.status,
               jr.status as request_status, jr.created_at as request_date
        FROM users u
        LEFT JOIN join_requests jr ON u.email = jr.email
        WHERE u.status = 'pending'
        ORDER BY u.created_at DESC
    ");
    $stmt->execute();
    $pending_requests = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // جلب إحصائيات
    $stmt = $conn->prepare("SELECT COUNT(*) as total FROM users WHERE status = 'pending'");
    $stmt->execute();
    $pending_count = $stmt->fetch()['total'];
    
    $stmt = $conn->prepare("SELECT COUNT(*) as total FROM users WHERE status = 'active'");
    $stmt->execute();
    $active_count = $stmt->fetch()['total'];
    
    $stmt = $conn->prepare("SELECT COUNT(*) as total FROM users WHERE status = 'inactive'");
    $stmt->execute();
    $inactive_count = $stmt->fetch()['total'];
    
} catch (Exception $e) {
    $error = 'حدث خطأ في جلب البيانات';
    error_log("Error fetching data in manage_registrations.php: " . $e->getMessage());
    $pending_requests = [];
    $pending_count = $active_count = $inactive_count = 0;
}

$pageTitle = 'إدارة طلبات التسجيل';
include 'includes/header.php';
?>

<div class="container-fluid">
    <!-- العنوان والإحصائيات -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2>
                    <i class="fas fa-user-plus me-2"></i>
                    إدارة طلبات التسجيل
                </h2>
                <?php if ($pending_count > 0): ?>
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="action" value="approve_all">
                        <button type="submit" class="btn btn-success" 
                                onclick="return confirm('هل أنت متأكد من الموافقة على جميع الطلبات المعلقة؟')">
                            <i class="fas fa-check-double me-1"></i>
                            الموافقة على الكل (<?php echo $pending_count; ?>)
                        </button>
                    </form>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- الإحصائيات -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?php echo $pending_count; ?></h4>
                            <p class="mb-0">طلبات معلقة</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?php echo $active_count; ?></h4>
                            <p class="mb-0">حسابات مفعلة</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?php echo $inactive_count; ?></h4>
                            <p class="mb-0">حسابات مرفوضة</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-times-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?php echo $pending_count + $active_count + $inactive_count; ?></h4>
                            <p class="mb-0">إجمالي الطلبات</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- الرسائل -->
    <?php if ($success): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo htmlspecialchars($success); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>
    
    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?php echo htmlspecialchars($error); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>
    
    <!-- جدول الطلبات -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>
                الطلبات المعلقة (<?php echo count($pending_requests); ?>)
            </h5>
        </div>
        <div class="card-body">
            <?php if (empty($pending_requests)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد طلبات معلقة</h5>
                    <p class="text-muted">جميع طلبات التسجيل تم معالجتها</p>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>الاسم</th>
                                <th>البريد الإلكتروني</th>
                                <th>رقم الهاتف</th>
                                <th>تاريخ التسجيل</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($pending_requests as $request): ?>
                                <tr>
                                    <td>
                                        <strong><?php echo htmlspecialchars($request['name']); ?></strong>
                                    </td>
                                    <td>
                                        <i class="fas fa-envelope me-1"></i>
                                        <?php echo htmlspecialchars($request['email']); ?>
                                    </td>
                                    <td>
                                        <?php if ($request['phone']): ?>
                                            <i class="fas fa-phone me-1"></i>
                                            <?php echo htmlspecialchars($request['phone']); ?>
                                        <?php else: ?>
                                            <span class="text-muted">غير محدد</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            <?php echo date('Y/m/d H:i', strtotime($request['created_at'])); ?>
                                        </small>
                                    </td>
                                    <td>
                                        <span class="badge bg-warning">
                                            <i class="fas fa-clock me-1"></i>
                                            معلق
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <!-- زر الموافقة -->
                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="action" value="approve">
                                                <input type="hidden" name="user_id" value="<?php echo $request['id']; ?>">
                                                <button type="submit" class="btn btn-success btn-sm"
                                                        onclick="return confirm('هل أنت متأكد من الموافقة على هذا الطلب؟')">
                                                    <i class="fas fa-check me-1"></i>
                                                    موافقة
                                                </button>
                                            </form>
                                            
                                            <!-- زر الرفض -->
                                            <button type="button" class="btn btn-danger btn-sm" 
                                                    data-bs-toggle="modal" 
                                                    data-bs-target="#rejectModal<?php echo $request['id']; ?>">
                                                <i class="fas fa-times me-1"></i>
                                                رفض
                                            </button>
                                        </div>
                                        
                                        <!-- نافذة الرفض -->
                                        <div class="modal fade" id="rejectModal<?php echo $request['id']; ?>" tabindex="-1">
                                            <div class="modal-dialog">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h5 class="modal-title">رفض طلب التسجيل</h5>
                                                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                                    </div>
                                                    <form method="POST">
                                                        <div class="modal-body">
                                                            <input type="hidden" name="action" value="reject">
                                                            <input type="hidden" name="user_id" value="<?php echo $request['id']; ?>">
                                                            
                                                            <p>هل أنت متأكد من رفض طلب تسجيل <strong><?php echo htmlspecialchars($request['name']); ?></strong>؟</p>
                                                            
                                                            <div class="mb-3">
                                                                <label for="rejection_reason<?php echo $request['id']; ?>" class="form-label">سبب الرفض:</label>
                                                                <textarea class="form-control" id="rejection_reason<?php echo $request['id']; ?>" 
                                                                          name="rejection_reason" rows="3" 
                                                                          placeholder="اختياري - أدخل سبب الرفض"></textarea>
                                                            </div>
                                                        </div>
                                                        <div class="modal-footer">
                                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                                            <button type="submit" class="btn btn-danger">
                                                                <i class="fas fa-times me-1"></i>
                                                                رفض الطلب
                                                            </button>
                                                        </div>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
