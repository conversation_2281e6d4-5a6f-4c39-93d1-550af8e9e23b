-- ===== جداول إضافية لواجهات المدير =====

-- جدول الإشعارات المحسن
CREATE TABLE IF NOT EXISTS notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    type ENUM('info', 'success', 'warning', 'error') DEFAULT 'info',
    category ENUM('system', 'course', 'assignment', 'quiz', 'payment', 'user') DEFAULT 'system',
    is_read BOOLEAN DEFAULT FALSE,
    read_at TIMESTAMP NULL,
    action_url VARCHAR(500) DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_unread (user_id, is_read),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول إعدادات النظام
CREATE TABLE IF NOT EXISTS system_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    setting_type ENUM('string', 'number', 'boolean', 'json', 'file') DEFAULT 'string',
    description TEXT,
    category VARCHAR(50) DEFAULT 'general',
    is_public BOOLEAN DEFAULT FALSE,
    validation_rules JSON DEFAULT NULL,
    updated_by INT DEFAULT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_category (category),
    INDEX idx_public (is_public)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول النسخ الاحتياطية
CREATE TABLE IF NOT EXISTS backups (
    id INT AUTO_INCREMENT PRIMARY KEY,
    filename VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size BIGINT DEFAULT NULL,
    backup_type ENUM('full', 'partial', 'structure', 'data') DEFAULT 'full',
    status ENUM('in_progress', 'completed', 'failed', 'cancelled') DEFAULT 'in_progress',
    progress_percentage TINYINT DEFAULT 0,
    error_message TEXT DEFAULT NULL,
    tables_included JSON DEFAULT NULL,
    compression_type ENUM('none', 'gzip', 'zip') DEFAULT 'gzip',
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول سجل الأمان
CREATE TABLE IF NOT EXISTS security_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT DEFAULT NULL,
    event_type ENUM('login', 'logout', 'failed_login', 'password_change', 'permission_change', 'data_access', 'system_change') NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    details JSON DEFAULT NULL,
    risk_level ENUM('low', 'medium', 'high', 'critical') DEFAULT 'low',
    location_country VARCHAR(100) DEFAULT NULL,
    location_city VARCHAR(100) DEFAULT NULL,
    is_suspicious BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_event_type (event_type),
    INDEX idx_risk_level (risk_level),
    INDEX idx_created_at (created_at),
    INDEX idx_suspicious (is_suspicious)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول الصلاحيات
CREATE TABLE IF NOT EXISTS permissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    display_name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(50) DEFAULT 'general',
    resource VARCHAR(100) DEFAULT NULL,
    action VARCHAR(100) DEFAULT NULL,
    is_system BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_category (category),
    INDEX idx_resource (resource)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول صلاحيات الأدوار
CREATE TABLE IF NOT EXISTS role_permissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    role ENUM('admin', 'instructor', 'student') NOT NULL,
    permission_id INT NOT NULL,
    granted BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE,
    UNIQUE KEY unique_role_permission (role, permission_id),
    INDEX idx_role (role)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول إحصائيات النظام اليومية
CREATE TABLE IF NOT EXISTS system_stats (
    id INT AUTO_INCREMENT PRIMARY KEY,
    stat_date DATE NOT NULL,
    total_users INT DEFAULT 0,
    active_users INT DEFAULT 0,
    new_users INT DEFAULT 0,
    total_students INT DEFAULT 0,
    total_instructors INT DEFAULT 0,
    total_courses INT DEFAULT 0,
    active_courses INT DEFAULT 0,
    new_courses INT DEFAULT 0,
    total_sessions INT DEFAULT 0,
    completed_sessions INT DEFAULT 0,
    live_sessions INT DEFAULT 0,
    total_enrollments INT DEFAULT 0,
    new_enrollments INT DEFAULT 0,
    total_assignments INT DEFAULT 0,
    submitted_assignments INT DEFAULT 0,
    total_quizzes INT DEFAULT 0,
    completed_quizzes INT DEFAULT 0,
    total_certificates INT DEFAULT 0,
    new_certificates INT DEFAULT 0,
    total_revenue DECIMAL(12,2) DEFAULT 0,
    platform_commission DECIMAL(12,2) DEFAULT 0,
    instructor_earnings DECIMAL(12,2) DEFAULT 0,
    pending_payments DECIMAL(12,2) DEFAULT 0,
    storage_used BIGINT DEFAULT 0, -- بالبايت
    bandwidth_used BIGINT DEFAULT 0, -- بالبايت
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_date (stat_date),
    INDEX idx_date (stat_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول تتبع الملفات
CREATE TABLE IF NOT EXISTS file_uploads (
    id INT AUTO_INCREMENT PRIMARY KEY,
    original_name VARCHAR(255) NOT NULL,
    stored_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size BIGINT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    file_type ENUM('image', 'video', 'audio', 'document', 'archive', 'other') NOT NULL,
    uploaded_by INT NOT NULL,
    related_type ENUM('course', 'session', 'assignment', 'quiz', 'user', 'library', 'system') DEFAULT NULL,
    related_id INT DEFAULT NULL,
    is_public BOOLEAN DEFAULT FALSE,
    download_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (uploaded_by) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_uploaded_by (uploaded_by),
    INDEX idx_related (related_type, related_id),
    INDEX idx_file_type (file_type),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول قوالب الإشعارات
CREATE TABLE IF NOT EXISTS notification_templates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    type ENUM('email', 'sms', 'push', 'system') NOT NULL,
    category VARCHAR(50) DEFAULT 'general',
    variables JSON DEFAULT NULL, -- متغيرات القالب
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_type (type),
    INDEX idx_category (category),
    INDEX idx_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول سجل إرسال الإشعارات
CREATE TABLE IF NOT EXISTS notification_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    notification_id INT DEFAULT NULL,
    template_id INT DEFAULT NULL,
    user_id INT NOT NULL,
    type ENUM('email', 'sms', 'push', 'system') NOT NULL,
    status ENUM('pending', 'sent', 'failed', 'delivered', 'read') DEFAULT 'pending',
    recipient VARCHAR(255) NOT NULL, -- email أو رقم هاتف
    subject VARCHAR(255) DEFAULT NULL,
    content TEXT NOT NULL,
    error_message TEXT DEFAULT NULL,
    sent_at TIMESTAMP NULL,
    delivered_at TIMESTAMP NULL,
    read_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (notification_id) REFERENCES notifications(id) ON DELETE SET NULL,
    FOREIGN KEY (template_id) REFERENCES notification_templates(id) ON DELETE SET NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_type (type),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول جلسات المستخدمين
CREATE TABLE IF NOT EXISTS user_sessions (
    id VARCHAR(128) PRIMARY KEY,
    user_id INT NOT NULL,
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_last_activity (last_activity),
    INDEX idx_expires_at (expires_at),
    INDEX idx_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدراج الصلاحيات الأساسية
INSERT INTO permissions (name, display_name, description, category, resource, action) VALUES
-- صلاحيات المستخدمين
('users.view', 'عرض المستخدمين', 'عرض قائمة المستخدمين', 'users', 'users', 'view'),
('users.create', 'إنشاء مستخدمين', 'إنشاء مستخدمين جدد', 'users', 'users', 'create'),
('users.edit', 'تعديل المستخدمين', 'تعديل بيانات المستخدمين', 'users', 'users', 'edit'),
('users.delete', 'حذف المستخدمين', 'حذف المستخدمين', 'users', 'users', 'delete'),
('users.suspend', 'تعليق المستخدمين', 'تعليق حسابات المستخدمين', 'users', 'users', 'suspend'),

-- صلاحيات الكورسات
('courses.view', 'عرض الكورسات', 'عرض قائمة الكورسات', 'courses', 'courses', 'view'),
('courses.create', 'إنشاء كورسات', 'إنشاء كورسات جديدة', 'courses', 'courses', 'create'),
('courses.edit', 'تعديل الكورسات', 'تعديل بيانات الكورسات', 'courses', 'courses', 'edit'),
('courses.delete', 'حذف الكورسات', 'حذف الكورسات', 'courses', 'courses', 'delete'),
('courses.approve', 'اعتماد الكورسات', 'اعتماد الكورسات المقترحة', 'courses', 'courses', 'approve'),

-- صلاحيات التقارير
('reports.view', 'عرض التقارير', 'عرض التقارير والإحصائيات', 'reports', 'reports', 'view'),
('reports.export', 'تصدير التقارير', 'تصدير التقارير', 'reports', 'reports', 'export'),
('reports.financial', 'التقارير المالية', 'عرض التقارير المالية', 'reports', 'reports', 'financial'),

-- صلاحيات النظام
('system.settings', 'إعدادات النظام', 'تعديل إعدادات النظام', 'system', 'system', 'settings'),
('system.backup', 'النسخ الاحتياطي', 'إنشاء واستعادة النسخ الاحتياطية', 'system', 'system', 'backup'),
('system.logs', 'سجلات النظام', 'عرض سجلات النظام', 'system', 'system', 'logs'),
('system.security', 'إعدادات الأمان', 'إدارة إعدادات الأمان', 'system', 'system', 'security'),

-- صلاحيات المحتوى
('content.library', 'إدارة المكتبة', 'إدارة مكتبة الموارد', 'content', 'library', 'manage'),
('content.forums', 'إدارة المنتديات', 'إدارة المنتديات والمناقشات', 'content', 'forums', 'manage'),
('content.certificates', 'إدارة الشهادات', 'إدارة الشهادات', 'content', 'certificates', 'manage');

-- إدراج صلاحيات المدير
INSERT INTO role_permissions (role, permission_id, granted) 
SELECT 'admin', id, TRUE FROM permissions;

-- إدراج الإعدادات الأساسية
INSERT INTO system_settings (setting_key, setting_value, setting_type, description, category) VALUES
('site_name', 'نظام إدارة التعلم الإلكتروني', 'string', 'اسم الموقع', 'general'),
('site_description', 'منصة تعليمية متقدمة للتعلم عن بعد', 'string', 'وصف الموقع', 'general'),
('site_logo', '', 'file', 'شعار الموقع', 'general'),
('site_favicon', '', 'file', 'أيقونة الموقع', 'general'),
('admin_email', '<EMAIL>', 'string', 'بريد المدير الإلكتروني', 'general'),
('timezone', 'Asia/Riyadh', 'string', 'المنطقة الزمنية', 'general'),
('language', 'ar', 'string', 'اللغة الافتراضية', 'general'),
('currency', 'SAR', 'string', 'العملة الافتراضية', 'financial'),
('platform_commission', '30', 'number', 'عمولة المنصة بالنسبة المئوية', 'financial'),
('min_commission', '15', 'number', 'أقل عمولة مسموحة', 'financial'),
('max_commission', '50', 'number', 'أعلى عمولة مسموحة', 'financial'),
('auto_approve_instructors', 'false', 'boolean', 'الموافقة التلقائية على المدربين', 'users'),
('auto_approve_courses', 'false', 'boolean', 'الموافقة التلقائية على الكورسات', 'courses'),
('max_file_size', '100', 'number', 'أقصى حجم ملف بالميجابايت', 'uploads'),
('allowed_file_types', '["jpg","jpeg","png","gif","pdf","doc","docx","ppt","pptx","mp4","mp3"]', 'json', 'أنواع الملفات المسموحة', 'uploads'),
('smtp_host', '', 'string', 'خادم البريد الإلكتروني', 'email'),
('smtp_port', '587', 'number', 'منفذ البريد الإلكتروني', 'email'),
('smtp_username', '', 'string', 'اسم مستخدم البريد الإلكتروني', 'email'),
('smtp_password', '', 'string', 'كلمة مرور البريد الإلكتروني', 'email'),
('smtp_encryption', 'tls', 'string', 'تشفير البريد الإلكتروني', 'email'),
('backup_frequency', 'daily', 'string', 'تكرار النسخ الاحتياطي', 'backup'),
('backup_retention', '30', 'number', 'مدة الاحتفاظ بالنسخ الاحتياطية بالأيام', 'backup'),
('maintenance_mode', 'false', 'boolean', 'وضع الصيانة', 'system'),
('registration_enabled', 'true', 'boolean', 'تفعيل التسجيل', 'users'),
('email_verification', 'true', 'boolean', 'تفعيل التحقق من البريد الإلكتروني', 'users');
