<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/activity_logger.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// Check if user is logged in and is an instructor
if (!isLoggedIn() || !isInstructor()) {
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

// Get and validate input
$input = json_decode(file_get_contents('php://input'), true);
$student_id = isset($input['student_id']) ? intval($input['student_id']) : 0;

if (!$student_id) {
    echo json_encode(['success' => false, 'message' => 'Invalid input']);
    exit;
}

try {
    // Verify the student belongs to this instructor
    $stmt = $conn->prepare("
        SELECT u.id 
        FROM users u
        JOIN course_enrollments ce ON u.id = ce.student_id
        JOIN courses c ON ce.course_id = c.id
        WHERE u.id = ? AND c.instructor_id = ? AND u.role = 'student'
        LIMIT 1
    ");
    $stmt->execute([$student_id, $_SESSION['user_id']]);
    
    if ($stmt->rowCount() === 0) {
        echo json_encode(['success' => false, 'message' => 'Student not found']);
        exit;
    }

    $conn->beginTransaction();

    // Remove course enrollments
    $stmt = $conn->prepare("DELETE FROM course_enrollments WHERE student_id = ?");
    $stmt->execute([$student_id]);

    // Remove session attendance records
    $stmt = $conn->prepare("DELETE FROM session_attendance WHERE student_id = ?");
    $stmt->execute([$student_id]);

    // Remove enrollment requests
    $stmt = $conn->prepare("DELETE FROM enrollment_requests WHERE student_id = ?");
    $stmt->execute([$student_id]);

    // Finally, remove the user account
    $stmt = $conn->prepare("DELETE FROM users WHERE id = ? AND role = 'student'");
    $stmt->execute([$student_id]);

    $conn->commit();

    // Log activity
    logUserActivity('remove_student', "Student removed: ID $student_id");

    echo json_encode(['success' => true]);
} catch (PDOException $e) {
    if ($conn->inTransaction()) {
        $conn->rollBack();
    }
    error_log($e->getMessage());
    echo json_encode(['success' => false, 'message' => 'System error']);
}