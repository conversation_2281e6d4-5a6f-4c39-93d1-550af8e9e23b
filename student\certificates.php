<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات الطالب
if (!isLoggedIn() || !isStudent()) {
    header('Location: ../login.php');
    exit;
}

$pageTitle = 'الشهادات';
$breadcrumbs = [
    ['title' => 'الشهادات']
];

$student_id = $_SESSION['user_id'];
$success = '';
$error = '';

// معالجة طلب تحميل الشهادة
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['download_certificate'])) {
    $certificate_id = (int)$_POST['certificate_id'];
    
    try {
        $stmt = $conn->prepare("
            SELECT * FROM certificates 
            WHERE id = ? AND student_id = ? AND status = 'active'
        ");
        $stmt->execute([$certificate_id, $student_id]);
        $certificate = $stmt->fetch();
        
        if (!$certificate) {
            throw new Exception('الشهادة غير متاحة');
        }
        
        // إعادة توجيه لتحميل الشهادة
        if ($certificate['certificate_url']) {
            header("Location: " . $certificate['certificate_url']);
            exit;
        } else {
            // إنشاء الشهادة إذا لم تكن موجودة
            header("Location: generate-certificate.php?id=" . $certificate_id);
            exit;
        }
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

try {
    // جلب الشهادات الصادرة
    $stmt = $conn->prepare("
        SELECT c.*, co.title as course_title, co.description as course_description,
               u.name as instructor_name,
               ce.completion_date,
               ce.progress_percentage
        FROM certificates c
        JOIN courses co ON c.course_id = co.id
        JOIN users u ON co.instructor_id = u.id
        JOIN course_enrollments ce ON co.id = ce.course_id AND ce.student_id = c.student_id
        WHERE c.student_id = ?
        ORDER BY c.issue_date DESC
    ");
    $stmt->execute([$student_id]);
    $certificates = $stmt->fetchAll();
    
    // جلب الكورسات المكتملة التي لم تصدر لها شهادات بعد
    $stmt = $conn->prepare("
        SELECT co.*, u.name as instructor_name,
               ce.completion_date, ce.progress_percentage,
               (SELECT AVG(score) FROM quiz_attempts qa 
                JOIN quizzes q ON qa.quiz_id = q.id 
                WHERE q.course_id = co.id AND qa.student_id = ce.student_id AND qa.status = 'completed') as avg_quiz_score,
               (SELECT AVG((asub.score / a.max_score) * 100) FROM assignment_submissions asub
                JOIN assignments a ON asub.assignment_id = a.id
                WHERE a.course_id = co.id AND asub.student_id = ce.student_id AND asub.score IS NOT NULL) as avg_assignment_score
        FROM courses co
        JOIN course_enrollments ce ON co.id = ce.course_id
        JOIN users u ON co.instructor_id = u.id
        WHERE ce.student_id = ? 
        AND ce.status = 'completed'
        AND ce.progress_percentage >= 80
        AND NOT EXISTS (
            SELECT 1 FROM certificates c 
            WHERE c.course_id = co.id AND c.student_id = ce.student_id
        )
        ORDER BY ce.completion_date DESC
    ");
    $stmt->execute([$student_id]);
    $eligible_courses = $stmt->fetchAll();
    
    // إحصائيات
    $stats = [
        'total_certificates' => count($certificates),
        'active_certificates' => count(array_filter($certificates, fn($c) => $c['status'] === 'active')),
        'eligible_courses' => count($eligible_courses),
        'avg_grade' => 0
    ];
    
    if (!empty($certificates)) {
        $grades = array_filter(array_column($certificates, 'grade'), fn($g) => $g !== null);
        if (!empty($grades)) {
            $stats['avg_grade'] = array_sum($grades) / count($grades);
        }
    }
    
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء جلب الشهادات';
}

include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- العنوان -->
        <div class="col-12 mb-4">
            <h2 class="text-primary">
                <i class="fas fa-certificate me-2"></i>
                الشهادات والإنجازات
            </h2>
            <p class="text-muted">شهاداتك المحصلة والكورسات المؤهلة للحصول على شهادات</p>
        </div>

        <?php if ($success): ?>
        <div class="col-12 mb-3">
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo htmlspecialchars($success); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </div>
        <?php endif; ?>

        <?php if ($error): ?>
        <div class="col-12 mb-3">
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-circle me-2"></i>
                <?php echo htmlspecialchars($error); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </div>
        <?php endif; ?>

        <!-- الإحصائيات -->
        <div class="col-12 mb-4">
            <div class="row">
                <div class="col-md-3 mb-3">
                    <div class="card-student text-center">
                        <div class="card-body">
                            <i class="fas fa-certificate fa-2x text-primary mb-2"></i>
                            <h4 class="text-primary"><?php echo $stats['total_certificates']; ?></h4>
                            <p class="text-muted mb-0">إجمالي الشهادات</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card-student text-center">
                        <div class="card-body">
                            <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                            <h4 class="text-success"><?php echo $stats['active_certificates']; ?></h4>
                            <p class="text-muted mb-0">شهادات نشطة</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card-student text-center">
                        <div class="card-body">
                            <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                            <h4 class="text-warning"><?php echo $stats['eligible_courses']; ?></h4>
                            <p class="text-muted mb-0">كورسات مؤهلة</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card-student text-center">
                        <div class="card-body">
                            <i class="fas fa-star fa-2x text-info mb-2"></i>
                            <h4 class="text-info"><?php echo number_format($stats['avg_grade'], 1); ?>%</h4>
                            <p class="text-muted mb-0">متوسط الدرجات</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- تبويبات الشهادات -->
        <div class="col-12">
            <div class="card-student">
                <div class="card-header">
                    <ul class="nav nav-tabs card-header-tabs" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" data-bs-toggle="tab" href="#certificates-tab">
                                <i class="fas fa-certificate me-1"></i>
                                شهاداتي (<?php echo count($certificates); ?>)
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="tab" href="#eligible-tab">
                                <i class="fas fa-clock me-1"></i>
                                كورسات مؤهلة (<?php echo count($eligible_courses); ?>)
                            </a>
                        </li>
                    </ul>
                </div>
                <div class="card-body">
                    <div class="tab-content">
                        <!-- تبويب الشهادات المحصلة -->
                        <div class="tab-pane fade show active" id="certificates-tab">
                            <?php if (empty($certificates)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-certificate fa-3x text-muted mb-3"></i>
                                <h4 class="text-muted">لا توجد شهادات بعد</h4>
                                <p class="text-muted">أكمل الكورسات للحصول على شهادات إتمام</p>
                            </div>
                            <?php else: ?>
                            <div class="row">
                                <?php foreach ($certificates as $certificate): ?>
                                <div class="col-lg-6 col-xl-4 mb-4">
                                    <div class="card-student h-100 certificate-card">
                                        <div class="card-header d-flex justify-content-between align-items-center">
                                            <span class="badge bg-primary">شهادة إتمام</span>
                                            <span class="badge <?php echo $certificate['status'] === 'active' ? 'bg-success' : 'bg-danger'; ?>">
                                                <?php echo $certificate['status'] === 'active' ? 'نشطة' : 'ملغاة'; ?>
                                            </span>
                                        </div>
                                        <div class="card-body text-center">
                                            <div class="certificate-icon mb-3">
                                                <i class="fas fa-award fa-3x text-warning"></i>
                                            </div>
                                            <h5 class="card-title"><?php echo htmlspecialchars($certificate['course_title']); ?></h5>
                                            <p class="text-muted mb-3">
                                                <?php echo htmlspecialchars(substr($certificate['course_description'], 0, 80)) . '...'; ?>
                                            </p>
                                            
                                            <div class="certificate-details">
                                                <div class="row text-center mb-3">
                                                    <div class="col-6">
                                                        <div class="border-end">
                                                            <div class="h6 text-primary mb-1">
                                                                <?php echo $certificate['certificate_number']; ?>
                                                            </div>
                                                            <small class="text-muted">رقم الشهادة</small>
                                                        </div>
                                                    </div>
                                                    <div class="col-6">
                                                        <div class="h6 text-success mb-1">
                                                            <?php echo $certificate['grade'] ? number_format($certificate['grade'], 1) . '%' : 'مكتمل'; ?>
                                                        </div>
                                                        <small class="text-muted">الدرجة النهائية</small>
                                                    </div>
                                                </div>
                                                
                                                <div class="mb-3">
                                                    <div class="d-flex justify-content-between mb-1">
                                                        <span class="text-muted">المدرب:</span>
                                                        <span><?php echo htmlspecialchars($certificate['instructor_name']); ?></span>
                                                    </div>
                                                    <div class="d-flex justify-content-between mb-1">
                                                        <span class="text-muted">تاريخ الإصدار:</span>
                                                        <span><?php echo date('Y-m-d', strtotime($certificate['issue_date'])); ?></span>
                                                    </div>
                                                    <div class="d-flex justify-content-between">
                                                        <span class="text-muted">تاريخ الإكمال:</span>
                                                        <span><?php echo date('Y-m-d', strtotime($certificate['completion_date'])); ?></span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card-footer">
                                            <?php if ($certificate['status'] === 'active'): ?>
                                            <form method="POST" class="d-inline w-100">
                                                <input type="hidden" name="certificate_id" value="<?php echo $certificate['id']; ?>">
                                                <button type="submit" name="download_certificate" class="btn btn-student-primary w-100">
                                                    <i class="fas fa-download me-1"></i>
                                                    تحميل الشهادة
                                                </button>
                                            </form>
                                            <button class="btn btn-outline-info w-100 mt-2" data-bs-toggle="modal" 
                                                    data-bs-target="#verifyModal<?php echo $certificate['id']; ?>">
                                                <i class="fas fa-shield-alt me-1"></i>
                                                التحقق من الشهادة
                                            </button>
                                            <?php else: ?>
                                            <button class="btn btn-outline-danger w-100" disabled>
                                                <i class="fas fa-ban me-1"></i>
                                                شهادة ملغاة
                                            </button>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                            <?php endif; ?>
                        </div>

                        <!-- تبويب الكورسات المؤهلة -->
                        <div class="tab-pane fade" id="eligible-tab">
                            <?php if (empty($eligible_courses)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-clock fa-3x text-muted mb-3"></i>
                                <h4 class="text-muted">لا توجد كورسات مؤهلة</h4>
                                <p class="text-muted">أكمل الكورسات بنسبة 80% أو أكثر للحصول على شهادات</p>
                            </div>
                            <?php else: ?>
                            <div class="row">
                                <?php foreach ($eligible_courses as $course): ?>
                                <div class="col-lg-6 col-xl-4 mb-4">
                                    <div class="card-student h-100">
                                        <div class="card-header d-flex justify-content-between align-items-center">
                                            <span class="badge bg-warning">مؤهل للشهادة</span>
                                            <span class="badge bg-success"><?php echo number_format($course['progress_percentage'], 1); ?>%</span>
                                        </div>
                                        <div class="card-body">
                                            <h5 class="card-title"><?php echo htmlspecialchars($course['title']); ?></h5>
                                            <p class="text-muted mb-3">
                                                <?php echo htmlspecialchars(substr($course['description'], 0, 80)) . '...'; ?>
                                            </p>
                                            
                                            <div class="course-details mb-3">
                                                <div class="d-flex justify-content-between mb-2">
                                                    <span class="text-muted">المدرب:</span>
                                                    <span><?php echo htmlspecialchars($course['instructor_name']); ?></span>
                                                </div>
                                                <div class="d-flex justify-content-between mb-2">
                                                    <span class="text-muted">تاريخ الإكمال:</span>
                                                    <span><?php echo date('Y-m-d', strtotime($course['completion_date'])); ?></span>
                                                </div>
                                                <div class="d-flex justify-content-between mb-2">
                                                    <span class="text-muted">نسبة الإكمال:</span>
                                                    <span class="text-success fw-bold"><?php echo number_format($course['progress_percentage'], 1); ?>%</span>
                                                </div>
                                                
                                                <?php if ($course['avg_quiz_score']): ?>
                                                <div class="d-flex justify-content-between mb-2">
                                                    <span class="text-muted">متوسط الاختبارات:</span>
                                                    <span><?php echo number_format($course['avg_quiz_score'], 1); ?>%</span>
                                                </div>
                                                <?php endif; ?>
                                                
                                                <?php if ($course['avg_assignment_score']): ?>
                                                <div class="d-flex justify-content-between mb-2">
                                                    <span class="text-muted">متوسط الواجبات:</span>
                                                    <span><?php echo number_format($course['avg_assignment_score'], 1); ?>%</span>
                                                </div>
                                                <?php endif; ?>
                                            </div>
                                            
                                            <!-- شريط التقدم -->
                                            <div class="progress progress-student mb-3">
                                                <div class="progress-bar-student" style="width: <?php echo $course['progress_percentage']; ?>%"></div>
                                            </div>
                                        </div>
                                        <div class="card-footer">
                                            <a href="request-certificate.php?course_id=<?php echo $course['id']; ?>" 
                                               class="btn btn-student-primary w-100">
                                                <i class="fas fa-certificate me-1"></i>
                                                طلب إصدار الشهادة
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modals التحقق من الشهادات -->
<?php foreach ($certificates as $certificate): ?>
<div class="modal fade" id="verifyModal<?php echo $certificate['id']; ?>" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">التحقق من الشهادة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center">
                <div class="mb-4">
                    <i class="fas fa-shield-alt fa-3x text-success mb-3"></i>
                    <h6>معلومات التحقق</h6>
                </div>
                
                <div class="verification-info">
                    <div class="mb-3">
                        <label class="form-label fw-bold">رقم الشهادة:</label>
                        <div class="form-control-plaintext border rounded p-2 bg-light">
                            <?php echo $certificate['certificate_number']; ?>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label fw-bold">رمز التحقق:</label>
                        <div class="form-control-plaintext border rounded p-2 bg-light">
                            <?php echo $certificate['verification_code']; ?>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label fw-bold">رابط التحقق:</label>
                        <div class="form-control-plaintext border rounded p-2 bg-light">
                            <small>
                                <?php echo $_SERVER['HTTP_HOST']; ?>/verify-certificate.php?code=<?php echo $certificate['verification_code']; ?>
                            </small>
                        </div>
                    </div>
                </div>
                
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    يمكن لأي شخص التحقق من صحة هذه الشهادة باستخدام رمز التحقق أو الرابط أعلاه
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary" onclick="copyVerificationCode('<?php echo $certificate['verification_code']; ?>')">
                    <i class="fas fa-copy me-1"></i>
                    نسخ رمز التحقق
                </button>
            </div>
        </div>
    </div>
</div>
<?php endforeach; ?>

<style>
.certificate-card {
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.certificate-card:hover {
    border-color: var(--student-primary);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
}

.certificate-icon {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}
</style>

<script>
function copyVerificationCode(code) {
    navigator.clipboard.writeText(code).then(function() {
        // إظهار رسالة نجاح
        const toast = document.createElement('div');
        toast.className = 'toast align-items-center text-white bg-success border-0 position-fixed top-0 end-0 m-3';
        toast.style.zIndex = '9999';
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    تم نسخ رمز التحقق بنجاح!
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;
        document.body.appendChild(toast);
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();
        
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 3000);
    });
}
</script>

<?php include 'includes/footer.php'; ?>
