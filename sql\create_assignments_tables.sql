-- إن<PERSON>اء جدول الواجبات
CREATE TABLE IF NOT EXISTS assignments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    course_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    instructions TEXT,
    due_date DATETIME NOT NULL,
    max_grade DECIMAL(5,2) DEFAULT 100.00,
    is_required TINYINT(1) DEFAULT 0,
    status ENUM('active', 'inactive', 'draft') DEFAULT 'active',
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- <PERSON><PERSON><PERSON><PERSON>ء جدول تسليمات الواجبات
CREATE TABLE IF NOT EXISTS assignment_submissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    assignment_id INT NOT NULL,
    student_id INT NOT NULL,
    submission_text TEXT,
    file_path VARCHAR(500),
    original_filename VARCHAR(255),
    file_size INT,
    submission_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status ENUM('draft', 'submitted', 'graded', 'returned') DEFAULT 'submitted',
    grade DECIMAL(5,2) NULL,
    feedback TEXT,
    graded_by INT NULL,
    graded_at TIMESTAMP NULL,
    late_submission TINYINT(1) DEFAULT 0,
    UNIQUE KEY unique_submission (assignment_id, student_id)
);

-- إنشاء جدول ملفات الواجبات (للمرفقات المتعددة)
CREATE TABLE IF NOT EXISTS assignment_files (
    id INT AUTO_INCREMENT PRIMARY KEY,
    assignment_id INT NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INT NOT NULL,
    file_type VARCHAR(100),
    uploaded_by INT NOT NULL,
    upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- إنشاء جدول ملفات التسليمات (للمرفقات المتعددة)
CREATE TABLE IF NOT EXISTS submission_files (
    id INT AUTO_INCREMENT PRIMARY KEY,
    submission_id INT NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    file_size INT NOT NULL,
    file_type VARCHAR(100),
    upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- إنشاء فهارس لتحسين الأداء
CREATE INDEX idx_assignments_course_id ON assignments(course_id);
CREATE INDEX idx_assignments_due_date ON assignments(due_date);
CREATE INDEX idx_assignments_status ON assignments(status);
CREATE INDEX idx_submissions_assignment_id ON assignment_submissions(assignment_id);
CREATE INDEX idx_submissions_student_id ON assignment_submissions(student_id);
CREATE INDEX idx_submissions_status ON assignment_submissions(status);
CREATE INDEX idx_assignment_files_assignment_id ON assignment_files(assignment_id);
CREATE INDEX idx_submission_files_submission_id ON submission_files(submission_id);

-- إدراج بيانات تجريبية للواجبات
INSERT IGNORE INTO assignments (course_id, title, description, instructions, due_date, max_grade, is_required, created_by) VALUES
(39, 'الواجب الأول - مقدمة في البرمجة', 'واجب تطبيقي حول أساسيات البرمجة', 'اكتب برنامج بسيط يطبع "Hello World" باستخدام لغة Python. يجب أن يحتوي البرنامج على تعليقات توضح كل خطوة.', DATE_ADD(NOW(), INTERVAL 7 DAY), 100.00, 1, 1),
(39, 'مشروع منتصف الفصل', 'مشروع تطبيقي شامل', 'قم بتطوير تطبيق ويب بسيط يحتوي على صفحة رئيسية وصفحة اتصال. استخدم HTML, CSS, وJavaScript.', DATE_ADD(NOW(), INTERVAL 14 DAY), 200.00, 1, 1),
(39, 'تمرين المتغيرات والدوال', 'تمرين على استخدام المتغيرات والدوال', 'اكتب برنامج يحسب مساحة ومحيط الدائرة باستخدام دوال منفصلة لكل عملية حسابية.', DATE_ADD(NOW(), INTERVAL 3 DAY), 50.00, 0, 1);

-- إدراج بيانات تجريبية للتسليمات
INSERT IGNORE INTO assignment_submissions (assignment_id, student_id, submission_text, status, submission_date) VALUES
(1, 2, 'print("Hello World")\n# هذا برنامج بسيط يطبع Hello World', 'submitted', NOW()),
(1, 3, 'print("Hello World")\n# برنامج Python بسيط', 'graded', DATE_SUB(NOW(), INTERVAL 1 DAY)),
(2, 2, 'تم إنشاء موقع ويب بسيط يحتوي على الصفحات المطلوبة', 'submitted', NOW());

-- تحديث بعض التسليمات بدرجات
UPDATE assignment_submissions SET grade = 95.00, feedback = 'عمل ممتاز! الكود واضح ومنظم.', graded_by = 1, graded_at = NOW() WHERE id = 2;
