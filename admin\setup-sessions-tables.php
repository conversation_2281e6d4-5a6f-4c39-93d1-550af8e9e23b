<?php
require_once '../config/database.php';

echo "<h2>إعداد جداول الجلسات</h2>";

try {
    // إنشاء جدول الجلسات
    $conn->exec("CREATE TABLE IF NOT EXISTS sessions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        course_id INT NOT NULL,
        title VARCHAR(255) NOT NULL,
        description TEXT NULL,
        session_date DATETIME NOT NULL,
        duration INT DEFAULT 60,
        meeting_link VARCHAR(500) NULL,
        status ENUM('scheduled', 'live', 'completed', 'cancelled') DEFAULT 'scheduled',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_course_id (course_id),
        INDEX idx_session_date (session_date),
        INDEX idx_status (status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    
    echo "<p>✅ تم إنشاء جدول sessions</p>";
    
    // إنشاء جدول حضور الجلسات
    $conn->exec("CREATE TABLE IF NOT EXISTS session_attendance (
        id INT AUTO_INCREMENT PRIMARY KEY,
        session_id INT NOT NULL,
        student_id INT NOT NULL,
        attended BOOLEAN DEFAULT FALSE,
        join_time DATETIME NULL,
        leave_time DATETIME NULL,
        duration_minutes INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE KEY unique_session_student (session_id, student_id),
        INDEX idx_session_id (session_id),
        INDEX idx_student_id (student_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    
    echo "<p>✅ تم إنشاء جدول session_attendance</p>";
    
    // إنشاء جدول تسجيل الطلاب في الكورسات
    $conn->exec("CREATE TABLE IF NOT EXISTS course_enrollments (
        id INT AUTO_INCREMENT PRIMARY KEY,
        course_id INT NOT NULL,
        student_id INT NOT NULL,
        enrollment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        status ENUM('active', 'inactive', 'completed', 'dropped') DEFAULT 'active',
        payment_status ENUM('pending', 'paid', 'refunded') DEFAULT 'pending',
        progress DECIMAL(5,2) DEFAULT 0.00,
        UNIQUE KEY unique_course_student (course_id, student_id),
        INDEX idx_course_id (course_id),
        INDEX idx_student_id (student_id),
        INDEX idx_status (status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    
    echo "<p>✅ تم إنشاء جدول course_enrollments</p>";
    
    // التأكد من وجود الأعمدة المطلوبة في جدول sessions
    $columns_to_add = [
        'start_time' => "ALTER TABLE sessions ADD COLUMN start_time TIME NULL",
        'end_time' => "ALTER TABLE sessions ADD COLUMN end_time TIME NULL",
        'max_attendees' => "ALTER TABLE sessions ADD COLUMN max_attendees INT NULL",
        'recording_url' => "ALTER TABLE sessions ADD COLUMN recording_url VARCHAR(500) NULL"
    ];
    
    foreach ($columns_to_add as $column => $sql) {
        try {
            $conn->exec($sql);
            echo "<p>✅ تم إضافة عمود $column إلى جدول sessions</p>";
        } catch (Exception $e) {
            echo "<p>⚠️ عمود $column موجود مسبقاً في جدول sessions</p>";
        }
    }
    
    // إضافة بعض الجلسات التجريبية
    $course_count = $conn->query("SELECT COUNT(*) FROM courses")->fetchColumn();
    
    if ($course_count > 0) {
        echo "<h3>إضافة جلسات تجريبية:</h3>";
        
        // جلب أول كورس
        $course_stmt = $conn->query("SELECT id, title FROM courses LIMIT 1");
        $course = $course_stmt->fetch();
        
        if ($course) {
            // التحقق من وجود جلسات للكورس
            $session_check = $conn->prepare("SELECT COUNT(*) FROM sessions WHERE course_id = ?");
            $session_check->execute([$course['id']]);
            
            if ($session_check->fetchColumn() == 0) {
                $sample_sessions = [
                    [
                        'title' => 'الجلسة التمهيدية - مقدمة عن الكورس',
                        'description' => 'نظرة عامة على محتوى الكورس والأهداف التعليمية',
                        'session_date' => date('Y-m-d H:i:s', strtotime('+1 week')),
                        'duration' => 90,
                        'status' => 'scheduled'
                    ],
                    [
                        'title' => 'الجلسة الأولى - الأساسيات',
                        'description' => 'تعلم الأساسيات والمفاهيم الأولية',
                        'session_date' => date('Y-m-d H:i:s', strtotime('+2 weeks')),
                        'duration' => 120,
                        'status' => 'scheduled'
                    ],
                    [
                        'title' => 'الجلسة الثانية - التطبيق العملي',
                        'description' => 'تطبيق عملي على المفاهيم المتعلمة',
                        'session_date' => date('Y-m-d H:i:s', strtotime('+3 weeks')),
                        'duration' => 150,
                        'status' => 'scheduled'
                    ]
                ];
                
                $stmt = $conn->prepare("
                    INSERT INTO sessions (course_id, title, description, session_date, duration, status) 
                    VALUES (?, ?, ?, ?, ?, ?)
                ");
                
                foreach ($sample_sessions as $session) {
                    $stmt->execute([
                        $course['id'],
                        $session['title'],
                        $session['description'],
                        $session['session_date'],
                        $session['duration'],
                        $session['status']
                    ]);
                    echo "<p>✅ تم إضافة جلسة: " . htmlspecialchars($session['title']) . "</p>";
                }
            } else {
                echo "<p>⚠️ يوجد جلسات مسبقاً للكورس: " . htmlspecialchars($course['title']) . "</p>";
            }
        }
    } else {
        echo "<p>⚠️ لا توجد كورسات في النظام لإضافة جلسات لها</p>";
    }
    
    // إضافة بعض الطلاب التجريبيين للكورسات
    echo "<h3>إضافة تسجيلات تجريبية:</h3>";
    
    $students = $conn->query("SELECT id, name FROM users WHERE role = 'student' LIMIT 3")->fetchAll();
    $courses = $conn->query("SELECT id, title FROM courses LIMIT 2")->fetchAll();
    
    if (!empty($students) && !empty($courses)) {
        foreach ($courses as $course) {
            foreach ($students as $student) {
                try {
                    $stmt = $conn->prepare("INSERT IGNORE INTO course_enrollments (course_id, student_id, status, payment_status) VALUES (?, ?, 'active', 'paid')");
                    $stmt->execute([$course['id'], $student['id']]);
                    echo "<p>✅ تم تسجيل " . htmlspecialchars($student['name']) . " في كورس " . htmlspecialchars($course['title']) . "</p>";
                } catch (Exception $e) {
                    echo "<p>⚠️ الطالب مسجل مسبقاً في الكورس</p>";
                }
            }
        }
    } else {
        echo "<p>⚠️ لا توجد طلاب أو كورسات كافية لإنشاء تسجيلات</p>";
    }
    
    // عرض بنية الجداول
    echo "<h3>بنية جدول sessions:</h3>";
    $stmt = $conn->query("DESCRIBE sessions");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // عرض الإحصائيات
    echo "<h3>الإحصائيات الحالية:</h3>";
    
    $stats = [
        'sessions' => $conn->query("SELECT COUNT(*) FROM sessions")->fetchColumn(),
        'enrollments' => $conn->query("SELECT COUNT(*) FROM course_enrollments")->fetchColumn(),
        'attendance_records' => $conn->query("SELECT COUNT(*) FROM session_attendance")->fetchColumn()
    ];
    
    echo "<ul>";
    echo "<li>الجلسات: " . $stats['sessions'] . "</li>";
    echo "<li>التسجيلات: " . $stats['enrollments'] . "</li>";
    echo "<li>سجلات الحضور: " . $stats['attendance_records'] . "</li>";
    echo "</ul>";
    
    echo "<h3>✅ تم إعداد جداول الجلسات بنجاح!</h3>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
}
?>

<p><a href="manage-courses.php">عرض الكورسات</a></p>
<p><a href="dashboard.php">لوحة التحكم</a></p>
