<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من صلاحيات المدرب
if (!isLoggedIn() || !isInstructor()) {
    header('Location: ../login.php');
    exit;
}

$course_id = (int)($_GET['course_id'] ?? 0);
$error = '';
$success = '';

// التحقق من أن الكورس ينتمي للمدرب
try {
    $stmt = $conn->prepare("SELECT title FROM courses WHERE id = ? AND instructor_id = ?");
    $stmt->execute([$course_id, $_SESSION['user_id']]);
    $course = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$course) {
        header('Location: courses.php');
        exit;
    }
} catch (PDOException $e) {
    $error = 'خطأ في التحقق من الكورس';
}

// جلب قائمة الطلاب المعتمدين
try {
    $stmt = $conn->prepare("
        SELECT u.id, u.name, u.email 
        FROM users u 
        JOIN enrollments e ON u.id = e.student_id 
        WHERE e.course_id = ? AND e.status = 'approved'
        ORDER BY u.name
    ");
    $stmt->execute([$course_id]);
    $students = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $students = [];
}

// معالجة إضافة الدرجات الجماعية
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $assignment_name = trim($_POST['assignment_name'] ?? '');
    $grade_type = $_POST['grade_type'] ?? 'assignment';
    $max_grade = (float)($_POST['max_grade'] ?? 100);
    $notes = trim($_POST['notes'] ?? '');
    $student_grades = $_POST['student_grades'] ?? [];
    
    if (empty($assignment_name)) {
        $error = 'اسم التكليف مطلوب';
    } elseif ($max_grade <= 0) {
        $error = 'الدرجة الكاملة يجب أن تكون أكبر من صفر';
    } elseif (empty($student_grades)) {
        $error = 'يرجى إدخال درجات للطلاب';
    } else {
        try {
            $conn->beginTransaction();
            
            $added_count = 0;
            $errors = [];
            
            foreach ($student_grades as $student_id => $grade_data) {
                $student_id = (int)$student_id;
                $grade = (float)($grade_data['grade'] ?? 0);
                $student_notes = trim($grade_data['notes'] ?? '');
                
                // تخطي الطلاب بدون درجات
                if ($grade_data['grade'] === '' || $grade_data['grade'] === null) {
                    continue;
                }
                
                // التحقق من صحة الدرجة
                if ($grade < 0 || $grade > $max_grade) {
                    $student_name = '';
                    foreach ($students as $student) {
                        if ($student['id'] == $student_id) {
                            $student_name = $student['name'];
                            break;
                        }
                    }
                    $errors[] = "درجة الطالب $student_name غير صحيحة";
                    continue;
                }
                
                // التحقق من أن الطالب مسجل في الكورس
                if (!in_array($student_id, array_column($students, 'id'))) {
                    continue;
                }
                
                // إضافة الدرجة
                $final_notes = $notes;
                if (!empty($student_notes)) {
                    $final_notes .= ($final_notes ? ' | ' : '') . $student_notes;
                }
                
                $stmt = $conn->prepare("
                    INSERT INTO grades (student_id, course_id, instructor_id, assignment_name, grade, max_grade, grade_type, notes) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ");
                $stmt->execute([
                    $student_id, 
                    $course_id, 
                    $_SESSION['user_id'], 
                    $assignment_name, 
                    $grade, 
                    $max_grade, 
                    $grade_type, 
                    $final_notes
                ]);
                
                $added_count++;
            }
            
            $conn->commit();
            
            if ($added_count > 0) {
                $success = "تم إضافة الدرجات لـ $added_count طالب بنجاح";
            }
            
            if (!empty($errors)) {
                $error = implode('<br>', $errors);
            }
            
        } catch (PDOException $e) {
            $conn->rollBack();
            $error = 'خطأ في إضافة الدرجات: ' . $e->getMessage();
        }
    }
}

$pageTitle = 'إضافة درجات جماعية - ' . $course['title'];
$breadcrumbs = [
    ['title' => 'الكورسات', 'url' => 'courses.php'],
    ['title' => $course['title'], 'url' => 'course-details.php?id=' . $course_id],
    ['title' => 'الطلاب', 'url' => 'students.php?course_id=' . $course_id],
    ['title' => 'درجات جماعية']
];

include 'includes/header.php';
?>

<div class="row">
    <div class="col-md-8">
        <!-- نموذج الدرجات الجماعية -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-users me-2"></i>
                    إضافة درجات جماعية
                </h5>
            </div>
            <div class="card-body">
                <?php if ($error): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?php echo $error; ?>
                    </div>
                <?php endif; ?>

                <?php if ($success): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        <?php echo htmlspecialchars($success); ?>
                    </div>
                <?php endif; ?>

                <?php if (empty($students)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا يوجد طلاب</h5>
                        <p class="text-muted">لا يوجد طلاب معتمدون في هذا الكورس</p>
                        <a href="students.php?course_id=<?php echo $course_id; ?>" class="btn btn-primary">
                            <i class="fas fa-users me-2"></i>
                            إدارة الطلاب
                        </a>
                    </div>
                <?php else: ?>
                    <form method="POST" action="" id="bulkGradeForm">
                        <!-- معلومات التكليف -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label for="assignment_name" class="form-label">اسم التكليف/الاختبار</label>
                                <input type="text" class="form-control" id="assignment_name" name="assignment_name" 
                                       placeholder="مثال: اختبار الوحدة الثانية" required>
                            </div>
                            <div class="col-md-3">
                                <label for="grade_type" class="form-label">نوع التقييم</label>
                                <select class="form-select" id="grade_type" name="grade_type">
                                    <option value="assignment">واجب</option>
                                    <option value="quiz">اختبار قصير</option>
                                    <option value="exam">امتحان</option>
                                    <option value="project">مشروع</option>
                                    <option value="participation">مشاركة</option>
                                    <option value="attendance">حضور</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="max_grade" class="form-label">الدرجة الكاملة</label>
                                <input type="number" class="form-control" id="max_grade" name="max_grade" 
                                       value="100" min="1" step="0.5" required>
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="notes" class="form-label">ملاحظات عامة (اختياري)</label>
                            <textarea class="form-control" id="notes" name="notes" rows="2" 
                                      placeholder="ملاحظات تطبق على جميع الطلاب..."></textarea>
                        </div>

                        <hr>

                        <!-- أدوات سريعة -->
                        <div class="mb-3">
                            <div class="row align-items-center">
                                <div class="col-md-6">
                                    <h6>درجات الطلاب:</h6>
                                </div>
                                <div class="col-md-6 text-end">
                                    <div class="btn-group btn-group-sm">
                                        <button type="button" class="btn btn-outline-success" id="fillAll">
                                            <i class="fas fa-fill me-1"></i>
                                            ملء الكل
                                        </button>
                                        <button type="button" class="btn btn-outline-warning" id="clearAll">
                                            <i class="fas fa-eraser me-1"></i>
                                            مسح الكل
                                        </button>
                                        <button type="button" class="btn btn-outline-info" id="randomFill">
                                            <i class="fas fa-random me-1"></i>
                                            ملء عشوائي
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- جدول الطلاب والدرجات -->
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead class="table-light">
                                    <tr>
                                        <th width="5%">#</th>
                                        <th width="30%">اسم الطالب</th>
                                        <th width="25%">البريد الإلكتروني</th>
                                        <th width="15%">الدرجة</th>
                                        <th width="10%">النسبة</th>
                                        <th width="15%">ملاحظات خاصة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($students as $index => $student): ?>
                                        <tr>
                                            <td><?php echo $index + 1; ?></td>
                                            <td>
                                                <strong><?php echo htmlspecialchars($student['name']); ?></strong>
                                            </td>
                                            <td>
                                                <small class="text-muted"><?php echo htmlspecialchars($student['email']); ?></small>
                                            </td>
                                            <td>
                                                <input type="number" 
                                                       class="form-control grade-input" 
                                                       name="student_grades[<?php echo $student['id']; ?>][grade]" 
                                                       min="0" 
                                                       step="0.5"
                                                       data-student-id="<?php echo $student['id']; ?>"
                                                       placeholder="0">
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary percentage-display" 
                                                      id="percentage-<?php echo $student['id']; ?>">
                                                    0%
                                                </span>
                                            </td>
                                            <td>
                                                <input type="text" 
                                                       class="form-control form-control-sm" 
                                                       name="student_grades[<?php echo $student['id']; ?>][notes]" 
                                                       placeholder="ملاحظة خاصة...">
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <div class="d-flex gap-2 mt-4">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save me-2"></i>
                                حفظ جميع الدرجات
                            </button>
                            <button type="button" class="btn btn-warning" id="resetForm">
                                <i class="fas fa-undo me-2"></i>
                                إعادة تعيين
                            </button>
                            <a href="students.php?course_id=<?php echo $course_id; ?>" class="btn btn-secondary">
                                <i class="fas fa-arrow-right me-2"></i>
                                العودة للطلاب
                            </a>
                        </div>
                    </form>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <!-- معلومات الكورس -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-graduation-cap me-2"></i>
                    معلومات الكورس
                </h6>
            </div>
            <div class="card-body">
                <h6><?php echo htmlspecialchars($course['title']); ?></h6>
                <p class="text-muted mb-3">عدد الطلاب: <?php echo count($students); ?></p>
                
                <div class="d-grid gap-2">
                    <a href="students.php?course_id=<?php echo $course_id; ?>" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-users me-2"></i>
                        عرض جميع الطلاب
                    </a>
                    <a href="grades.php?course_id=<?php echo $course_id; ?>" class="btn btn-outline-info btn-sm">
                        <i class="fas fa-chart-bar me-2"></i>
                        إدارة الدرجات
                    </a>
                </div>
            </div>
        </div>

        <!-- نصائح -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>
                    نصائح
                </h6>
            </div>
            <div class="card-body">
                <div class="small">
                    <div class="mb-3">
                        <i class="fas fa-info-circle text-info me-2"></i>
                        <strong>الحقول الفارغة:</strong>
                        <p class="mb-0">سيتم تجاهل الطلاب الذين لا تحتوي حقولهم على درجات</p>
                    </div>
                    
                    <div class="mb-3">
                        <i class="fas fa-calculator text-success me-2"></i>
                        <strong>النسبة المئوية:</strong>
                        <p class="mb-0">يتم حساب النسبة تلقائياً عند إدخال الدرجة</p>
                    </div>
                    
                    <div class="mb-0">
                        <i class="fas fa-tools text-warning me-2"></i>
                        <strong>الأدوات السريعة:</strong>
                        <p class="mb-0">استخدم الأزرار العلوية لملء أو مسح الدرجات بسرعة</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.card-header {
    background: var(--instructor-light);
    border-bottom: 1px solid var(--gray-200);
    border-radius: 12px 12px 0 0 !important;
}

.form-control:focus,
.form-select:focus {
    border-color: var(--instructor-primary);
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.grade-input {
    text-align: center;
    font-weight: bold;
}

.percentage-display {
    font-size: 0.85rem;
    font-weight: bold;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: var(--gray-700);
}
</style>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const maxGradeInput = document.getElementById('max_grade');
    const gradeInputs = document.querySelectorAll('.grade-input');
    const fillAllBtn = document.getElementById('fillAll');
    const clearAllBtn = document.getElementById('clearAll');
    const randomFillBtn = document.getElementById('randomFill');
    const resetBtn = document.getElementById('resetForm');
    
    // تحديث النسبة المئوية
    function updatePercentage(studentId) {
        const gradeInput = document.querySelector(`input[data-student-id="${studentId}"]`);
        const percentageDisplay = document.getElementById(`percentage-${studentId}`);
        
        if (gradeInput && percentageDisplay) {
            const grade = parseFloat(gradeInput.value) || 0;
            const maxGrade = parseFloat(maxGradeInput.value) || 100;
            const percentage = maxGrade > 0 ? ((grade / maxGrade) * 100) : 0;
            
            percentageDisplay.textContent = percentage.toFixed(1) + '%';
            
            // تغيير لون النسبة
            percentageDisplay.className = 'badge percentage-display ';
            if (percentage >= 90) {
                percentageDisplay.className += 'bg-success';
            } else if (percentage >= 70) {
                percentageDisplay.className += 'bg-warning';
            } else if (percentage >= 50) {
                percentageDisplay.className += 'bg-info';
            } else if (percentage > 0) {
                percentageDisplay.className += 'bg-danger';
            } else {
                percentageDisplay.className += 'bg-secondary';
            }
        }
    }
    
    // إضافة مستمعي الأحداث للدرجات
    gradeInputs.forEach(input => {
        const studentId = input.getAttribute('data-student-id');
        
        input.addEventListener('input', function() {
            const maxGrade = parseFloat(maxGradeInput.value) || 100;
            const grade = parseFloat(this.value) || 0;
            
            // التحقق من عدم تجاوز الدرجة الكاملة
            if (grade > maxGrade) {
                this.value = maxGrade;
            }
            
            updatePercentage(studentId);
        });
    });
    
    // تحديث جميع النسب عند تغيير الدرجة الكاملة
    maxGradeInput.addEventListener('input', function() {
        gradeInputs.forEach(input => {
            const studentId = input.getAttribute('data-student-id');
            const grade = parseFloat(input.value) || 0;
            const maxGrade = parseFloat(this.value) || 100;
            
            // تعديل الدرجة إذا كانت تتجاوز الدرجة الكاملة الجديدة
            if (grade > maxGrade) {
                input.value = maxGrade;
            }
            
            updatePercentage(studentId);
        });
    });
    
    // ملء جميع الحقول
    fillAllBtn.addEventListener('click', function() {
        const grade = prompt('أدخل الدرجة لجميع الطلاب:', '');
        if (grade !== null && grade !== '') {
            const gradeValue = parseFloat(grade);
            const maxGrade = parseFloat(maxGradeInput.value) || 100;
            
            if (gradeValue >= 0 && gradeValue <= maxGrade) {
                gradeInputs.forEach(input => {
                    input.value = gradeValue;
                    const studentId = input.getAttribute('data-student-id');
                    updatePercentage(studentId);
                });
            } else {
                alert('الدرجة يجب أن تكون بين 0 و ' + maxGrade);
            }
        }
    });
    
    // مسح جميع الحقول
    clearAllBtn.addEventListener('click', function() {
        if (confirm('هل أنت متأكد من مسح جميع الدرجات؟')) {
            gradeInputs.forEach(input => {
                input.value = '';
                const studentId = input.getAttribute('data-student-id');
                updatePercentage(studentId);
            });
        }
    });
    
    // ملء عشوائي
    randomFillBtn.addEventListener('click', function() {
        if (confirm('هل تريد ملء الدرجات بقيم عشوائية؟')) {
            const maxGrade = parseFloat(maxGradeInput.value) || 100;
            
            gradeInputs.forEach(input => {
                const randomGrade = Math.floor(Math.random() * (maxGrade + 1));
                input.value = randomGrade;
                const studentId = input.getAttribute('data-student-id');
                updatePercentage(studentId);
            });
        }
    });
    
    // إعادة تعيين النموذج
    resetBtn.addEventListener('click', function() {
        if (confirm('هل أنت متأكد من إعادة تعيين النموذج؟')) {
            document.getElementById('bulkGradeForm').reset();
            
            gradeInputs.forEach(input => {
                const studentId = input.getAttribute('data-student-id');
                updatePercentage(studentId);
            });
        }
    });
});
</script>

</body>
</html>
