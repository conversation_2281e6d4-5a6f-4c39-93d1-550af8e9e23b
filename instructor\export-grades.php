<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من صلاحيات المدربf (!isLoggedIn() || !isInstructor()) {   header('Location: ../login.php');   exit;
$course_id = (int)($_GET['course_id'] ?? 0);
$format = $_GET['format'] ?? 'excel';

// التحقق من أن الكورس ينتمي للمدرب
try {
    $stmt = $conn->prepare("SELECT title FROM courses WHERE id = ? AND instructor_id = ?");
    $stmt->execute([$course_id, $_SESSION['user_id']]);
    $course = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$course) {
        header('Location: courses.php');
        exit;
    }
} catch (PDOException $e) {
    die('خطأ في التحقق من الكورس');
}

// جلب بيانات الطلاب مع درجاتهم
try {
    $stmt = $conn->prepare("
        SELECT 
            u.id as student_id,
            u.name as student_name,
            u.email as student_email,
            e.enrollment_date,
            GROUP_CONCAT(
                CONCAT(g.assignment_name, ':', g.grade, '/', g.max_grade, ':', g.grade_type, ':', IFNULL(g.notes, ''))
                ORDER BY g.created_at
                SEPARATOR '|'
            ) as grades_data,
            COUNT(g.id) as total_assignments,
            AVG(CASE WHEN g.max_grade > 0 THEN (g.grade / g.max_grade) * 100 END) as avg_percentage,
            SUM(g.grade) as total_points,
            SUM(g.max_grade) as total_max_points
        FROM users u
        JOIN enrollments e ON u.id = e.student_id
        LEFT JOIN grades g ON u.id = g.student_id AND g.course_id = e.course_id
        WHERE e.course_id = ? AND e.status = 'approved'
        GROUP BY u.id, u.name, u.email, e.enrollment_date
        ORDER BY u.name
    ");
    $stmt->execute([$course_id]);
    $students_data = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    die('خطأ في جلب البيانات: ' . $e->getMessage());
}

// جلب جميع أسماء التكاليف الفريدة
try {
    $stmt = $conn->prepare("
        SELECT DISTINCT assignment_name, grade_type 
        FROM grades 
        WHERE course_id = ? AND instructor_id = ?
        ORDER BY assignment_name
    ");
    $stmt->execute([$course_id, $_SESSION['user_id']]);
    $assignments = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $assignments = [];
}

// معالجة التصدير
if (isset($_GET['download']) && $_GET['download'] === '1') {
    
    if ($format === 'excel') {
        // تصدير Excel
        header('Content-Type: application/vnd.ms-excel; charset=utf-8');
        header('Content-Disposition: attachment; filename="grades_' . $course_id . '_' . date('Y-m-d') . '.xls"');
        header('Pragma: no-cache');
        header('Expires: 0');
        
        echo "\xEF\xBB\xBF"; // UTF-8 BOM
        
        echo "<table border='1'>";
        echo "<tr>";
        echo "<th>اسم الطالب</th>";
        echo "<th>البريد الإلكتروني</th>";
        echo "<th>تاريخ التسجيل</th>";
        echo "<th>عدد التكاليف</th>";
        echo "<th>إجمالي النقاط</th>";
        echo "<th>إجمالي النقاط الكاملة</th>";
        echo "<th>المعدل العام</th>";
        
        // إضافة عمود لكل تكليف
        foreach ($assignments as $assignment) {
            echo "<th>" . htmlspecialchars($assignment['assignment_name']) . "</th>";
        }
        
        echo "</tr>";
        
        foreach ($students_data as $student) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($student['student_name']) . "</td>";
            echo "<td>" . htmlspecialchars($student['student_email']) . "</td>";
            echo "<td>" . date('Y-m-d', strtotime($student['enrollment_date'])) . "</td>";
            echo "<td>" . $student['total_assignments'] . "</td>";
            echo "<td>" . ($student['total_points'] ?? 0) . "</td>";
            echo "<td>" . ($student['total_max_points'] ?? 0) . "</td>";
            echo "<td>" . ($student['avg_percentage'] ? round($student['avg_percentage'], 2) . '%' : '0%') . "</td>";
            
            // تحليل درجات الطالب
            $student_grades = [];
            if ($student['grades_data']) {
                $grades_array = explode('|', $student['grades_data']);
                foreach ($grades_array as $grade_info) {
                    $parts = explode(':', $grade_info);
                    if (count($parts) >= 3) {
                        $assignment_name = $parts[0];
                        $grade = $parts[1];
                        $max_grade = $parts[2];
                        $student_grades[$assignment_name] = "$grade/$max_grade";
                    }
                }
            }
            
            // إضافة درجة كل تكليف
            foreach ($assignments as $assignment) {
                $grade_value = $student_grades[$assignment['assignment_name']] ?? '-';
                echo "<td>" . htmlspecialchars($grade_value) . "</td>";
            }
            
            echo "</tr>";
        }
        
        echo "</table>";
        exit;
        
    } elseif ($format === 'csv') {
        // تصدير CSV
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename="grades_' . $course_id . '_' . date('Y-m-d') . '.csv"');
        header('Pragma: no-cache');
        header('Expires: 0');
        
        echo "\xEF\xBB\xBF"; // UTF-8 BOM
        
        $output = fopen('php://output', 'w');
        
        // العناوين
        $headers = [
            'اسم الطالب',
            'البريد الإلكتروني',
            'تاريخ التسجيل',
            'عدد التكاليف',
            'إجمالي النقاط',
            'إجمالي النقاط الكاملة',
            'المعدل العام'
        ];
        
        foreach ($assignments as $assignment) {
            $headers[] = $assignment['assignment_name'];
        }
        
        fputcsv($output, $headers);
        
        // البيانات
        foreach ($students_data as $student) {
            $row = [
                $student['student_name'],
                $student['student_email'],
                date('Y-m-d', strtotime($student['enrollment_date'])),
                $student['total_assignments'],
                $student['total_points'] ?? 0,
                $student['total_max_points'] ?? 0,
                $student['avg_percentage'] ? round($student['avg_percentage'], 2) . '%' : '0%'
            ];
            
            // تحليل درجات الطالب
            $student_grades = [];
            if ($student['grades_data']) {
                $grades_array = explode('|', $student['grades_data']);
                foreach ($grades_array as $grade_info) {
                    $parts = explode(':', $grade_info);
                    if (count($parts) >= 3) {
                        $assignment_name = $parts[0];
                        $grade = $parts[1];
                        $max_grade = $parts[2];
                        $student_grades[$assignment_name] = "$grade/$max_grade";
                    }
                }
            }
            
            // إضافة درجة كل تكليف
            foreach ($assignments as $assignment) {
                $row[] = $student_grades[$assignment['assignment_name']] ?? '-';
            }
            
            fputcsv($output, $row);
        }
        
        fclose($output);
        exit;
    }
}

$pageTitle = 'تصدير الدرجات - ' . $course['title'];
$breadcrumbs = [
    ['title' => 'الكورسات', 'url' => 'courses.php'],
    ['title' => $course['title'], 'url' => 'course-details.php?id=' . $course_id],
    ['title' => 'الطلاب', 'url' => 'students.php?course_id=' . $course_id],
    ['title' => 'تصدير الدرجات']
];

include 'includes/header.php';
?>

<div class="row">
    <div class="col-md-8">
        <!-- خيارات التصدير -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-download me-2"></i>
                    تصدير درجات الكورس
                </h5>
            </div>
            <div class="card-body">
                <p class="text-muted mb-4">
                    اختر تنسيق التصدير المناسب لتحميل جميع درجات الطلاب في الكورس.
                </p>

                <div class="row g-3">
                    <!-- تصدير Excel -->
                    <div class="col-md-6">
                        <div class="card border-success h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-file-excel fa-3x text-success mb-3"></i>
                                <h6>ملف Excel</h6>
                                <p class="small text-muted">
                                    ملف .xls يحتوي على جميع الدرجات والإحصائيات مع عمود منفصل لكل تكليف
                                </p>
                                <a href="?course_id=<?php echo $course_id; ?>&format=excel&download=1" 
                                   class="btn btn-success">
                                    <i class="fas fa-download me-2"></i>
                                    تحميل Excel
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- تصدير CSV -->
                    <div class="col-md-6">
                        <div class="card border-primary h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-file-csv fa-3x text-primary mb-3"></i>
                                <h6>ملف CSV</h6>
                                <p class="small text-muted">
                                    ملف نصي منفصل بفواصل للاستيراد في برامج أخرى مثل Google Sheets
                                </p>
                                <a href="?course_id=<?php echo $course_id; ?>&format=csv&download=1" 
                                   class="btn btn-primary">
                                    <i class="fas fa-download me-2"></i>
                                    تحميل CSV
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <hr class="my-4">

                <!-- معاينة البيانات -->
                <h6 class="mb-3">معاينة البيانات المراد تصديرها:</h6>
                
                <div class="table-responsive">
                    <table class="table table-sm table-striped">
                        <thead class="table-light">
                            <tr>
                                <th>الطالب</th>
                                <th>التكاليف</th>
                                <th>النقاط</th>
                                <th>المعدل</th>
                                <th>آخر تكليف</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach (array_slice($students_data, 0, 5) as $student): ?>
                                <tr>
                                    <td>
                                        <div>
                                            <strong><?php echo htmlspecialchars($student['student_name']); ?></strong>
                                            <br>
                                            <small class="text-muted"><?php echo htmlspecialchars($student['student_email']); ?></small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-info"><?php echo $student['total_assignments']; ?></span>
                                    </td>
                                    <td>
                                        <?php echo ($student['total_points'] ?? 0); ?>/<?php echo ($student['total_max_points'] ?? 0); ?>
                                    </td>
                                    <td>
                                        <?php 
                                        $percentage = $student['avg_percentage'] ?? 0;
                                        $color = $percentage >= 90 ? 'success' : ($percentage >= 70 ? 'warning' : 'danger');
                                        ?>
                                        <span class="badge bg-<?php echo $color; ?>">
                                            <?php echo $percentage ? round($percentage, 1) . '%' : '0%'; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php
                                        if ($student['grades_data']) {
                                            $grades_array = explode('|', $student['grades_data']);
                                            $last_grade = end($grades_array);
                                            $parts = explode(':', $last_grade);
                                            if (count($parts) >= 3) {
                                                echo '<small>' . htmlspecialchars($parts[0]) . '</small>';
                                            } else {
                                                echo '<small class="text-muted">-</small>';
                                            }
                                        } else {
                                            echo '<small class="text-muted">لا يوجد</small>';
                                        }
                                        ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                            
                            <?php if (count($students_data) > 5): ?>
                                <tr>
                                    <td colspan="5" class="text-center text-muted">
                                        ... و <?php echo count($students_data) - 5; ?> طالب آخر
                                    </td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <!-- إحصائيات التصدير -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    إحصائيات التصدير
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h4 class="text-primary"><?php echo count($students_data); ?></h4>
                            <small class="text-muted">إجمالي الطلاب</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success"><?php echo count($assignments); ?></h4>
                        <small class="text-muted">التكاليف</small>
                    </div>
                </div>
                
                <hr>
                
                <?php if (!empty($assignments)): ?>
                    <h6 class="mb-2">التكاليف المتضمنة:</h6>
                    <div class="small">
                        <?php foreach (array_slice($assignments, 0, 5) as $assignment): ?>
                            <div class="d-flex justify-content-between mb-1">
                                <span><?php echo htmlspecialchars($assignment['assignment_name']); ?></span>
                                <span class="badge bg-secondary"><?php echo $assignment['grade_type']; ?></span>
                            </div>
                        <?php endforeach; ?>
                        
                        <?php if (count($assignments) > 5): ?>
                            <div class="text-center mt-2">
                                <small class="text-muted">... و <?php echo count($assignments) - 5; ?> تكليف آخر</small>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- معلومات الكورس -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات الكورس
                </h6>
            </div>
            <div class="card-body">
                <h6><?php echo htmlspecialchars($course['title']); ?></h6>
                <small class="text-muted">
                    تاريخ التصدير: <?php echo date('Y-m-d H:i'); ?>
                </small>
                
                <hr>
                
                <div class="d-grid gap-2">
                    <a href="grade-statistics.php?course_id=<?php echo $course_id; ?>" class="btn btn-outline-info btn-sm">
                        <i class="fas fa-chart-pie me-2"></i>
                        إحصائيات الدرجات
                    </a>
                    <a href="students.php?course_id=<?php echo $course_id; ?>" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-users me-2"></i>
                        عرض جميع الطلاب
                    </a>
                    <a href="course-details.php?id=<?php echo $course_id; ?>" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للكورس
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.card-header {
    background: var(--instructor-light);
    border-bottom: 1px solid var(--gray-200);
    border-radius: 12px 12px 0 0 !important;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}
</style>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

</body>
</html>
