<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات المدرب
if (!isLoggedIn() || !isInstructor()) {
    header('Location: ../login.php');
    exit;
}

$pageTitle = 'لوحة تحكم المدرب';
include 'includes/header.php';

// جلب إحصائيات المدرب
try {
    // عدد الكورسات النشطة
    $stmt = $conn->prepare("
        SELECT COUNT(*) as active_courses 
        FROM courses 
        WHERE instructor_id = ? AND status = 'active'
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $active_courses = $stmt->fetch(PDO::FETCH_ASSOC)['active_courses'];

    // إجمالي عدد الطلاب
    $stmt = $conn->prepare("
        SELECT COUNT(DISTINCT e.student_id) as total_students
        FROM courses c
        JOIN enrollments e ON c.id = e.course_id
        WHERE c.instructor_id = ?
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $total_students = $stmt->fetch(PDO::FETCH_ASSOC)['total_students'];

    // عدد الجلسات القادمة
    $stmt = $conn->prepare("
        SELECT COUNT(*) as upcoming_sessions
        FROM courses c
        JOIN sessions s ON c.id = s.course_id
        WHERE c.instructor_id = ?
        AND s.session_date >= CURDATE()
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $upcoming_sessions = $stmt->fetch(PDO::FETCH_ASSOC)['upcoming_sessions'];

    // الجلسات القادمة
    $stmt = $conn->prepare("
        SELECT s.*, c.title as course_title,
        (SELECT COUNT(*) FROM session_attendance WHERE session_id = s.id) as registered_students,
        CASE
            WHEN s.end_time IS NOT NULL AND s.start_time IS NOT NULL
            THEN TIMESTAMPDIFF(MINUTE, s.start_time, s.end_time)
            ELSE 60
        END as duration
        FROM courses c
        JOIN sessions s ON c.id = s.course_id
        WHERE c.instructor_id = ?
        AND s.session_date >= CURDATE()
        ORDER BY s.session_date ASC, s.start_time ASC
        LIMIT 5
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $upcoming_sessions_list = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // طلبات الانضمام الجديدة
    $stmt = $conn->prepare("
        SELECT u.*, e.created_at as request_date, c.title as course_title
        FROM users u
        INNER JOIN enrollments e ON u.id = e.student_id
        INNER JOIN courses c ON e.course_id = c.id
        WHERE c.instructor_id = ? AND e.status = 'pending'
        ORDER BY e.created_at DESC
        LIMIT 5
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $pending_requests = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // آخر الكورسات
    $stmt = $conn->prepare("
        SELECT c.*, 
        (SELECT COUNT(*) FROM course_enrollments WHERE course_id = c.id) as enrolled_students,
        (SELECT COUNT(*) FROM sessions WHERE course_id = c.id) as total_sessions
        FROM courses c
        WHERE c.instructor_id = ?
        ORDER BY c.created_at DESC
        LIMIT 5
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $recent_courses = $stmt->fetchAll(PDO::FETCH_ASSOC);

} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء جلب البيانات';
}
?>

<!-- رأس الصفحة المتطور -->
<div class="welcome-section-modern animate-fadeInUp">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h1 class="welcome-title">مرحباً، <?php echo htmlspecialchars($_SESSION['name']); ?>! 🎓</h1>
            <p class="welcome-subtitle">إليك نظرة عامة شاملة على أنشطتك التعليمية وإنجازاتك اليوم</p>
        </div>
        <div class="col-md-4 text-end">
            <div class="d-flex flex-column align-items-end">
                <div class="badge-modern bg-success mb-2 animate-pulse">
                    <i class="fas fa-circle me-2"></i>متصل الآن
                </div>
                <small class="text-white-50">آخر تسجيل دخول</small>
                <strong class="text-white real-time"><?php echo date('Y-m-d H:i'); ?></strong>
            </div>
        </div>
    </div>
</div>

<!-- بطاقات الإحصائيات المتطورة -->
<div class="row g-4 mb-5">
    <div class="col-lg-3 col-md-6" data-aos="fade-up" data-aos-delay="100">
        <div class="stat-card-modern">
            <div class="stat-icon-modern bg-primary">
                <i class="fas fa-graduation-cap"></i>
            </div>
            <div class="stat-number-modern counter" data-target="<?php echo $active_courses; ?>">0</div>
            <div class="stat-label-modern">الكورسات النشطة</div>
            <div class="stat-detail-modern">
                <small class="text-white-50">كورس نشط ومتاح</small>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6" data-aos="fade-up" data-aos-delay="200">
        <div class="stat-card-modern">
            <div class="stat-icon-modern bg-success">
                <i class="fas fa-users"></i>
            </div>
            <div class="stat-number-modern counter" data-target="<?php echo $total_students; ?>">0</div>
            <div class="stat-label-modern">إجمالي الطلاب</div>
            <div class="stat-detail-modern">
                <button class="btn btn-sm btn-modern mt-2" onclick="copyInviteLink()">
                    <i class="fas fa-user-plus me-1"></i>دعوة طلاب
                </button>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6" data-aos="fade-up" data-aos-delay="300">
        <div class="stat-card-modern">
            <div class="stat-icon-modern bg-info">
                <i class="fas fa-calendar"></i>
            </div>
            <div class="stat-number-modern counter" data-target="<?php echo $upcoming_sessions; ?>">0</div>
            <div class="stat-label-modern">الجلسات القادمة</div>
            <div class="stat-detail-modern">
                <small class="text-white-50">جلسة مجدولة</small>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6" data-aos="fade-up" data-aos-delay="400">
        <div class="stat-card-modern">
            <div class="stat-icon-modern bg-warning">
                <i class="fas fa-chart-line"></i>
            </div>
            <div class="stat-number-modern">95%</div>
            <div class="stat-label-modern">معدل الرضا</div>
            <div class="stat-detail-modern">
                <small class="text-white-50">تقييم الطلاب</small>
            </div>
        </div>
    </div>
</div>

<!-- طلبات الانضمام الجديدة -->
<div class="row">
    <div class="col-12 mb-4" data-aos="fade-up" data-aos-delay="500">
        <div class="modern-card">
            <div class="card-header d-flex justify-content-between align-items-center" style="background: var(--glass-bg); backdrop-filter: blur(20px); border-bottom: 1px solid var(--glass-border); color: white;">
                <h5 class="mb-0">
                    <i class="fas fa-user-clock me-2"></i>
                    طلبات الانضمام الجديدة
                </h5>
                <a href="manage_students.php" class="btn btn-modern btn-sm">عرض الكل</a>
            </div>
            <div class="card-body" style="background: var(--glass-bg); backdrop-filter: blur(20px);">
                <?php if (!empty($pending_requests)): ?>
                    <div class="table-responsive">
                        <table class="table-modern">
                            <thead>
                                <tr>
                                    <th>الاسم</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>رقم الهاتف</th>
                                    <th>تاريخ الطلب</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                                <tbody>
                                    <?php foreach ($pending_requests as $request): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($request['name']); ?></td>
                                            <td><?php echo htmlspecialchars($request['email']); ?></td>
                                            <td><?php echo htmlspecialchars($request['phone']); ?></td>
                                            <td><?php echo date('Y-m-d H:i', strtotime($request['request_date'])); ?></td>
                                            <td>
                                            <form method="POST" action="manage_students.php" class="d-inline">
                                                <input type="hidden" name="student_id" value="<?php echo $request['id']; ?>">
                                                <button type="submit" name="action" value="approve" class="btn btn-modern btn-sm me-1" style="background: var(--success-gradient);">
                                                    <i class="fas fa-check"></i> قبول
                                                </button>
                                                <button type="submit" name="action" value="reject" class="btn btn-modern btn-sm" style="background: var(--danger-gradient);">
                                                    <i class="fas fa-times"></i> رفض
                                                </button>
                                            </form>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <p class="text-muted mb-0">لا توجد طلبات انضمام جديدة</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <!-- الجلسات القادمة -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">الجلسات القادمة</h5>
                    <a href="sessions.php" class="btn btn-sm btn-primary">عرض الكل</a>
                </div>
                <div class="card-body">
                    <?php if (empty($upcoming_sessions_list)): ?>
                        <p class="text-muted">لا توجد جلسات قادمة</p>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>الكورس</th>
                                        <th>الموعد</th>
                                        <th>المدة</th>
                                        <th>الطلاب</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($upcoming_sessions_list as $session): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($session['course_title']); ?></td>
                                            <td><?php echo date('Y-m-d H:i', strtotime($session['session_date'] . ' ' . $session['start_time'])); ?></td>
                                            <td><?php echo isset($session['duration']) ? $session['duration'] : 60; ?> دقيقة</td>
                                            <td><?php echo $session['registered_students']; ?></td>
                                            <td>
                                                <a href="session-details.php?id=<?php echo $session['id']; ?>" class="btn btn-sm btn-info">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <?php
                                                $session_datetime = strtotime($session['session_date'] . ' ' . $session['start_time']);
                                                if ($session_datetime <= time() + 300): // 5 minutes before start
                                                ?>
                                                    <a href="start-session.php?id=<?php echo $session['id']; ?>" class="btn btn-sm btn-success">
                                                        <i class="fas fa-play"></i>
                                                    </a>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- آخر الكورسات -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">آخر الكورسات</h5>
                    <a href="courses.php" class="btn btn-sm btn-primary">عرض الكل</a>
                </div>
                <div class="card-body">
                    <?php if (empty($recent_courses)): ?>
                        <p class="text-muted">لا توجد كورسات</p>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>العنوان</th>
                                        <th>الطلاب</th>
                                        <th>الجلسات</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recent_courses as $course): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($course['title']); ?></td>
                                            <td><?php echo $course['enrolled_students']; ?></td>
                                            <td><?php echo $course['total_sessions']; ?></td>
                                            <td>
                                                <span class="badge bg-<?php echo $course['status'] === 'active' ? 'success' : 'secondary'; ?>">
                                                    <?php echo $course['status'] === 'active' ? 'نشط' : 'غير نشط'; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <a href="course-details.php?id=<?php echo $course['id']; ?>" class="btn btn-sm btn-info">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="manage-sessions.php?course_id=<?php echo $course['id']; ?>" class="btn btn-sm btn-primary">
                                                    <i class="fas fa-video"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- روابط سريعة -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a href="manage_students.php" class="btn btn-primary w-100">
                                <i class="fas fa-users"></i> إدارة الطلاب
                            </a>
                        </div>
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">روابط سريعة</h5>
                </div>
                <div class="card-body">
                    <div class="row g-4">
                        <div class="col-md-3">
                            <a href="add-course.php" class="btn btn-lg btn-primary w-100">
                                <i class="fas fa-plus-circle mb-2"></i>
                                <br>
                                إضافة كورس جديد
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="manage-students.php" class="btn btn-lg btn-success w-100">
                                <i class="fas fa-users mb-2"></i>
                                <br>
                                إدارة الطلاب
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="schedule-session.php" class="btn btn-lg btn-info w-100">
                                <i class="fas fa-calendar-plus mb-2"></i>
                                <br>
                                جدولة جلسة جديدة
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="reports.php" class="btn btn-lg btn-warning w-100">
                                <i class="fas fa-chart-bar mb-2"></i>
                                <br>
                                التقارير والإحصائيات
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة DataTables
    const tables = document.querySelectorAll('table');
    tables.forEach(table => {
        new DataTable(table, {
            pageLength: 5,
            searching: false,
            paging: false,
            info: false,
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/ar.json'
            }
        });
    });
});
</script>

<script>
// تهيئة العدادات المتحركة
function animateCounters() {
    const counters = document.querySelectorAll('.counter');

    const observerOptions = {
        threshold: 0.5,
        rootMargin: '0px 0px -100px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const target = parseInt(entry.target.getAttribute('data-target'));
                animateCounter(entry.target, target);
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);

    counters.forEach(counter => {
        observer.observe(counter);
    });
}

function animateCounter(element, target) {
    const start = 0;
    const duration = 2000;
    const increment = target / (duration / 16);
    let current = start;

    const timer = setInterval(() => {
        current += increment;
        element.textContent = Math.floor(current);

        if (current >= target) {
            element.textContent = target;
            clearInterval(timer);
        }
    }, 16);
}

// نسخ رابط الدعوة مع تأثيرات بصرية
function copyInviteLink() {
    var inviteLink = '<?php echo rtrim(dirname($_SERVER['PHP_SELF'], 2), '\/') . "/register_student.php?instructor=" . $_SESSION["user_id"]; ?>';

    navigator.clipboard.writeText(window.location.origin + inviteLink).then(function() {
        showModernToast('تم نسخ رابط الدعوة بنجاح! 🎉', 'success');
    }).catch(function() {
        showModernToast('فشل في نسخ الرابط', 'error');
    });
}

// إشعارات متطورة
function showModernToast(message, type = 'success') {
    const toast = document.createElement('div');
    toast.className = `modern-toast toast-${type}`;
    toast.innerHTML = `
        <div class="toast-content">
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} toast-icon"></i>
            <span>${message}</span>
        </div>
        <button class="toast-close" onclick="this.parentElement.remove()">
            <i class="fas fa-times"></i>
        </button>
    `;

    document.body.appendChild(toast);

    // تأثير الظهور
    setTimeout(() => toast.classList.add('show'), 100);

    // إزالة تلقائية
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => toast.remove(), 300);
    }, 4000);
}

// تحديث الوقت الحقيقي
function updateRealTime() {
    const timeElements = document.querySelectorAll('.real-time');
    const now = new Date();
    const timeString = now.toLocaleString('ar-SA', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });

    timeElements.forEach(element => {
        element.textContent = timeString;
    });
}

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    animateCounters();
    updateRealTime();
    setInterval(updateRealTime, 60000); // تحديث كل دقيقة

    // تأثيرات hover للبطاقات
    const cards = document.querySelectorAll('.stat-card-modern, .modern-card');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-10px) scale(1.02)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
});
</script>

<style>
/* إضافات CSS للتوست */
.modern-toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-sm);
    padding: 1rem 1.5rem;
    color: white;
    box-shadow: var(--glass-shadow);
    transform: translateX(400px);
    transition: var(--transition-smooth);
    z-index: 9999;
    min-width: 300px;
}

.modern-toast.show {
    transform: translateX(0);
}

.modern-toast.toast-success {
    border-left: 4px solid #4facfe;
}

.modern-toast.toast-error {
    border-left: 4px solid #fa709a;
}

.toast-content {
    display: flex;
    align-items: center;
    gap: 10px;
}

.toast-icon {
    font-size: 1.2rem;
}

.toast-close {
    position: absolute;
    top: 5px;
    right: 10px;
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    opacity: 0.7;
    transition: var(--transition-smooth);
}

.toast-close:hover {
    opacity: 1;
}
</style>

<?php include 'includes/footer.php'; ?>
