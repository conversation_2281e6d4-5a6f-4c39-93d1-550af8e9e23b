/* ===== مكونات النظام - التنبيهات والرسائل ===== */

/* ===== التوست (الرسائل المنبثقة) ===== */
.toast-notification {
    position: fixed;
    top: 20px;
    left: 20px;
    min-width: 300px;
    max-width: 500px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    transform: translateX(-100%);
    transition: transform 0.3s ease-in-out;
    z-index: 9999;
    overflow: hidden;
}

.toast-notification.show {
    transform: translateX(0);
}

.toast-content {
    padding: 1rem 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.toast-content i {
    font-size: 1.25rem;
}

.toast-close {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    background: none;
    border: none;
    font-size: 1.25rem;
    cursor: pointer;
    color: #666;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
}

.toast-close:hover {
    background-color: rgba(0,0,0,0.1);
}

/* أنواع التوست */
.toast-success {
    border-left: 4px solid #4CAF50;
}

.toast-success .toast-content i {
    color: #4CAF50;
}

.toast-error {
    border-left: 4px solid #F44336;
}

.toast-error .toast-content i {
    color: #F44336;
}

.toast-warning {
    border-left: 4px solid #FF9800;
}

.toast-warning .toast-content i {
    color: #FF9800;
}

.toast-info {
    border-left: 4px solid #2196F3;
}

.toast-info .toast-content i {
    color: #2196F3;
}

/* ===== تأثير الموجة على الأزرار ===== */
.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255,255,255,0.6);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* ===== مؤشر التحميل ===== */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255,255,255,0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9998;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.loading-overlay.show {
    opacity: 1;
    visibility: visible;
}

.loading-spinner-large {
    width: 50px;
    height: 50px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #2196F3;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* ===== البطاقات التفاعلية ===== */
.interactive-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    overflow: hidden;
    position: relative;
}

.interactive-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.interactive-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, #2196F3, #FF9800);
}

/* ===== الأزرار العائمة ===== */
.floating-action-btn {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #2196F3, #1976D2);
    color: white;
    border: none;
    box-shadow: 0 4px 12px rgba(33, 150, 243, 0.4);
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.floating-action-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(33, 150, 243, 0.6);
}

/* ===== شريط التقدم المحسن ===== */
.progress-enhanced {
    height: 8px;
    border-radius: 10px;
    background-color: #E0E0E0;
    overflow: hidden;
    position: relative;
}

.progress-bar-enhanced {
    height: 100%;
    background: linear-gradient(90deg, #4CAF50, #8BC34A);
    border-radius: 10px;
    transition: width 0.6s ease;
    position: relative;
    overflow: hidden;
}

.progress-bar-enhanced::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-image: linear-gradient(
        -45deg,
        rgba(255, 255, 255, .2) 25%,
        transparent 25%,
        transparent 50%,
        rgba(255, 255, 255, .2) 50%,
        rgba(255, 255, 255, .2) 75%,
        transparent 75%,
        transparent
    );
    background-size: 50px 50px;
    animation: move 2s linear infinite;
}

@keyframes move {
    0% {
        background-position: 0 0;
    }
    100% {
        background-position: 50px 50px;
    }
}

/* ===== القوائم المنسدلة المحسنة ===== */
.dropdown-enhanced .dropdown-menu {
    border: none;
    border-radius: 10px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    padding: 0.5rem 0;
    margin-top: 0.5rem;
}

.dropdown-enhanced .dropdown-item {
    padding: 0.75rem 1.5rem;
    transition: all 0.2s ease;
    border-radius: 0;
}

.dropdown-enhanced .dropdown-item:hover {
    background-color: #f8f9fa;
    transform: translateX(5px);
}

.dropdown-enhanced .dropdown-item i {
    margin-left: 0.5rem;
    width: 20px;
    text-align: center;
}

/* ===== التبويبات المحسنة ===== */
.nav-tabs-enhanced {
    border-bottom: 2px solid #E0E0E0;
    margin-bottom: 2rem;
}

.nav-tabs-enhanced .nav-link {
    border: none;
    border-radius: 10px 10px 0 0;
    padding: 1rem 1.5rem;
    color: #666;
    transition: all 0.3s ease;
    position: relative;
    margin-left: 0.25rem;
}

.nav-tabs-enhanced .nav-link:hover {
    background-color: #f8f9fa;
    color: #2196F3;
}

.nav-tabs-enhanced .nav-link.active {
    background-color: #2196F3;
    color: white;
    border-bottom: 2px solid #2196F3;
}

.nav-tabs-enhanced .nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: #2196F3;
}

/* ===== البحث المحسن ===== */
.search-box-enhanced {
    position: relative;
    max-width: 400px;
}

.search-box-enhanced input {
    padding-right: 3rem;
    border-radius: 25px;
    border: 2px solid #E0E0E0;
    transition: all 0.3s ease;
}

.search-box-enhanced input:focus {
    border-color: #2196F3;
    box-shadow: 0 0 0 0.2rem rgba(33, 150, 243, 0.25);
}

.search-box-enhanced .search-icon {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
    pointer-events: none;
}

/* ===== الرسوم المتحركة ===== */
.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.animate-on-scroll.animate-in {
    opacity: 1;
    transform: translateY(0);
}

.fade-in-up {
    animation: fadeInUp 0.6s ease forwards;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in-left {
    animation: slideInLeft 0.6s ease forwards;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* ===== حالات التحميل ===== */
.loading-state {
    position: relative;
    overflow: hidden;
}

.loading-state::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* ===== الاستجابة للشاشات الصغيرة ===== */
@media (max-width: 768px) {
    .toast-notification {
        left: 10px;
        right: 10px;
        min-width: auto;
        max-width: none;
    }
    
    .floating-action-btn {
        bottom: 20px;
        right: 20px;
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }
    
    .search-box-enhanced {
        max-width: 100%;
    }
    
    .nav-tabs-enhanced .nav-link {
        padding: 0.75rem 1rem;
        margin-left: 0.1rem;
    }
}
