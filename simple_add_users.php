<?php
/**
 * إضافة المستخدمين التجريبيين - نسخة مبسطة
 * Simple Add Demo Users Script
 */

// تفعيل عرض الأخطاء
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// تضمين ملف الاتصال بقاعدة البيانات
require_once 'config/database.php';

echo "<h1>إضافة المستخدمين التجريبيين</h1>";

try {
    // التحقق من الاتصال
    echo "<p>✅ تم الاتصال بقاعدة البيانات بنجاح</p>";
    
    // فحص جدول المستخدمين
    $stmt = $conn->query("DESCRIBE users");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<h3>أعمدة جدول المستخدمين:</h3>";
    echo "<ul>";
    foreach ($columns as $column) {
        echo "<li>$column</li>";
    }
    echo "</ul>";
    
    // حذف المستخدمين التجريبيين الموجودين
    $conn->exec("DELETE FROM users WHERE email IN ('<EMAIL>', '<EMAIL>', '<EMAIL>')");
    echo "<p>🧹 تم تنظيف المستخدمين السابقين</p>";
    
    // كلمة المرور المشفرة
    $password = password_hash('password', PASSWORD_DEFAULT);
    echo "<p>🔐 تم تشفير كلمة المرور: " . substr($password, 0, 20) . "...</p>";
    
    // إضافة المدير
    $stmt = $conn->prepare("INSERT INTO users (name, email, username, password, role, status) VALUES (?, ?, ?, ?, ?, ?)");
    
    // المدير
    $result1 = $stmt->execute([
        'مدير النظام',
        '<EMAIL>', 
        'admin',
        $password,
        'admin',
        'active'
    ]);
    
    if ($result1) {
        echo "<p>✅ تم إنشاء المدير بنجاح</p>";
    } else {
        echo "<p>❌ فشل في إنشاء المدير</p>";
    }
    
    // المدرب
    $result2 = $stmt->execute([
        'أحمد محمد - مدرب',
        '<EMAIL>',
        'instructor_demo', 
        $password,
        'instructor',
        'active'
    ]);
    
    if ($result2) {
        echo "<p>✅ تم إنشاء المدرب بنجاح</p>";
    } else {
        echo "<p>❌ فشل في إنشاء المدرب</p>";
    }
    
    // الطالب
    $result3 = $stmt->execute([
        'سارة أحمد - طالبة',
        '<EMAIL>',
        'student_demo',
        $password, 
        'student',
        'active'
    ]);
    
    if ($result3) {
        echo "<p>✅ تم إنشاء الطالب بنجاح</p>";
    } else {
        echo "<p>❌ فشل في إنشاء الطالب</p>";
    }
    
    // التحقق من النتائج
    $stmt = $conn->query("SELECT id, name, email, role, status FROM users ORDER BY id");
    $users = $stmt->fetchAll();
    
    echo "<h3>المستخدمون في قاعدة البيانات:</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>الاسم</th><th>البريد الإلكتروني</th><th>الدور</th><th>الحالة</th></tr>";
    
    foreach ($users as $user) {
        echo "<tr>";
        echo "<td>{$user['id']}</td>";
        echo "<td>{$user['name']}</td>";
        echo "<td>{$user['email']}</td>";
        echo "<td>{$user['role']}</td>";
        echo "<td>{$user['status']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // اختبار تسجيل الدخول
    echo "<h3>اختبار تسجيل الدخول:</h3>";
    
    $testEmail = '<EMAIL>';
    $testPassword = 'password';
    
    $stmt = $conn->prepare("SELECT id, name, email, password, role FROM users WHERE email = ?");
    $stmt->execute([$testEmail]);
    $testUser = $stmt->fetch();
    
    if ($testUser) {
        echo "<p>✅ تم العثور على المستخدم: {$testUser['name']}</p>";
        
        if (password_verify($testPassword, $testUser['password'])) {
            echo "<p>✅ كلمة المرور صحيحة!</p>";
        } else {
            echo "<p>❌ كلمة المرور غير صحيحة!</p>";
        }
    } else {
        echo "<p>❌ لم يتم العثور على المستخدم!</p>";
    }
    
    echo "<h2>🎉 تم الانتهاء!</h2>";
    echo "<h3>بيانات تسجيل الدخول:</h3>";
    echo "<ul>";
    echo "<li><strong>المدير:</strong> <EMAIL> / password</li>";
    echo "<li><strong>المدرب:</strong> <EMAIL> / password</li>";
    echo "<li><strong>الطالب:</strong> <EMAIL> / password</li>";
    echo "</ul>";
    
    echo "<p><a href='login.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>تجربة تسجيل الدخول</a></p>";
    
} catch (Exception $e) {
    echo "<h2>❌ حدث خطأ:</h2>";
    echo "<p><strong>الخطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    
    // طباعة معلومات إضافية للتشخيص
    echo "<h3>معلومات التشخيص:</h3>";
    echo "<p><strong>PHP Version:</strong> " . phpversion() . "</p>";
    echo "<p><strong>PDO Available:</strong> " . (class_exists('PDO') ? 'Yes' : 'No') . "</p>";
    
    if (isset($conn)) {
        try {
            $stmt = $conn->query("SELECT VERSION()");
            $version = $stmt->fetchColumn();
            echo "<p><strong>MySQL Version:</strong> $version</p>";
        } catch (Exception $e2) {
            echo "<p><strong>MySQL Version:</strong> غير متاح</p>";
        }
    }
}
?>

<style>
body {
    font-family: 'Tahoma', sans-serif;
    direction: rtl;
    text-align: right;
    margin: 20px;
    background-color: #f8f9fa;
}

h1, h2, h3 {
    color: #333;
}

table {
    background: white;
    margin: 10px 0;
}

th {
    background: #007bff;
    color: white;
    padding: 10px;
}

td {
    padding: 8px;
    border: 1px solid #ddd;
}

p {
    margin: 5px 0;
}

ul {
    background: white;
    padding: 15px;
    border-radius: 5px;
    border-left: 4px solid #007bff;
}
</style>
