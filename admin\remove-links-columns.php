<?php
require_once '../config/database.php';

echo "<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>حذف أعمدة الروابط</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css' rel='stylesheet'>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f8f9fa; }
        .container { max-width: 800px; margin: 30px auto; }
        .log-item { padding: 8px; margin: 3px 0; border-radius: 5px; font-size: 14px; }
        .log-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .log-error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .log-info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .log-warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
    </style>
</head>
<body>
<div class='container'>
    <div class='card'>
        <div class='card-header bg-danger text-white'>
            <h4 class='mb-0'><i class='fas fa-trash me-2'></i>حذف أعمدة الروابط من قاعدة البيانات</h4>
        </div>
        <div class='card-body'>";

function logMessage($message, $type = 'info') {
    $icon = $type === 'success' ? 'check-circle' : ($type === 'error' ? 'exclamation-circle' : ($type === 'warning' ? 'exclamation-triangle' : 'info-circle'));
    echo "<div class='log-item log-$type'><i class='fas fa-$icon me-2'></i>$message</div>";
    flush();
    ob_flush();
}

try {
    logMessage('🗑️ بدء حذف أعمدة الروابط من قاعدة البيانات...', 'info');
    
    // قائمة الأعمدة المراد حذفها
    $columns_to_remove = [
        'linkedin_url',
        'website_url'
    ];
    
    foreach ($columns_to_remove as $column) {
        try {
            // التحقق من وجود العمود أولاً
            $check = $conn->query("SHOW COLUMNS FROM users LIKE '$column'");
            if ($check->rowCount() > 0) {
                // حذف العمود
                $conn->exec("ALTER TABLE users DROP COLUMN $column");
                logMessage("✓ تم حذف العمود: $column", 'success');
            } else {
                logMessage("⚠ العمود غير موجود: $column", 'warning');
            }
        } catch (Exception $e) {
            logMessage("❌ خطأ في حذف العمود $column: " . $e->getMessage(), 'error');
        }
    }
    
    // التحقق من الأعمدة المتبقية
    logMessage('=== التحقق من الأعمدة المتبقية ===', 'info');
    
    $remaining_columns = $conn->query("SHOW COLUMNS FROM users")->fetchAll(PDO::FETCH_COLUMN);
    $instructor_columns = [
        'name', 'email', 'username', 'password', 'role', 'status', 'phone', 'bio',
        'specialization', 'experience_years', 'education_level', 'university', 'major',
        'certifications', 'skills', 'languages', 'hourly_rate', 'availability',
        'teaching_style', 'preferred_subjects', 'email_verified', 'created_at', 'last_login'
    ];
    
    logMessage("الأعمدة المتبقية في جدول users:", 'info');
    foreach ($remaining_columns as $column) {
        if (in_array($column, $instructor_columns)) {
            logMessage("✓ $column", 'success');
        } else {
            logMessage("• $column", 'info');
        }
    }
    
    // تحديث البيانات الموجودة (إزالة أي مراجع للروابط)
    logMessage('=== تنظيف البيانات ===', 'info');
    
    $instructors_count = $conn->query("SELECT COUNT(*) FROM users WHERE role = 'instructor'")->fetchColumn();
    logMessage("عدد المدربين في النظام: $instructors_count", 'info');
    
    logMessage('🎉 تم حذف أعمدة الروابط بنجاح!', 'success');
    
} catch (Exception $e) {
    logMessage('❌ خطأ عام: ' . $e->getMessage(), 'error');
}

echo "
        </div>
        <div class='mt-4 text-center'>
            <div class='alert alert-success'>
                <h5><i class='fas fa-check-circle me-2'></i>تم حذف أعمدة الروابط بنجاح!</h5>
                <p class='mb-0'>تم إزالة حقول LinkedIn والموقع الشخصي من قاعدة البيانات</p>
            </div>
            <a href='add-instructor.php' class='btn btn-primary btn-lg me-2'>
                <i class='fas fa-user-plus me-2'></i>إضافة مدرب جديد
            </a>
            <a href='manage-instructors.php' class='btn btn-success me-2'>
                <i class='fas fa-users me-2'></i>إدارة المدربين
            </a>
            <a href='dashboard.php' class='btn btn-info'>
                <i class='fas fa-tachometer-alt me-2'></i>لوحة التحكم
            </a>
        </div>
    </div>
</div>
</body>
</html>";
?>
