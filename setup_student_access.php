<?php
/**
 * إعداد وصول الطلاب للكورسات
 * Setup student access to courses
 */

require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>إعداد وصول الطلاب للكورسات</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "</head>";
echo "<body>";
echo "<div class='container mt-4'>";
echo "<h2>🎓 إعداد وصول الطلاب للكورسات</h2>";

try {
    // 1. التحقق من وجود مجلد student
    echo "<h4>📁 فحص مجلد الطلاب</h4>";
    
    if (!file_exists('student')) {
        mkdir('student', 0777, true);
        echo "<div class='alert alert-success'>✅ تم إنشاء مجلد student</div>";
    } else {
        echo "<div class='alert alert-info'>ℹ️ مجلد student موجود</div>";
    }

    // 2. التحقق من جدول course_materials
    echo "<h4>📚 فحص جدول المواد التعليمية</h4>";
    
    $stmt = $conn->query("SHOW TABLES LIKE 'course_materials'");
    if ($stmt->rowCount() == 0) {
        $conn->exec("
            CREATE TABLE course_materials (
                id INT AUTO_INCREMENT PRIMARY KEY,
                course_id INT NOT NULL,
                title VARCHAR(255) NOT NULL,
                description TEXT,
                file_path VARCHAR(500) NOT NULL,
                file_type VARCHAR(50),
                file_size INT DEFAULT 0,
                status ENUM('active', 'inactive') DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                
                INDEX idx_course_id (course_id),
                INDEX idx_status (status),
                
                FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        echo "<div class='alert alert-success'>✅ تم إنشاء جدول المواد التعليمية</div>";
    } else {
        echo "<div class='alert alert-info'>ℹ️ جدول المواد التعليمية موجود</div>";
    }

    // 3. إضافة عمود meeting_link لجدول sessions
    echo "<h4>🔗 فحص رابط الاجتماع في الجلسات</h4>";
    
    $stmt = $conn->query("SHOW COLUMNS FROM sessions LIKE 'meeting_link'");
    if ($stmt->rowCount() == 0) {
        $conn->exec("ALTER TABLE sessions ADD COLUMN meeting_link VARCHAR(500) DEFAULT NULL AFTER description");
        echo "<div class='alert alert-success'>✅ تم إضافة عمود رابط الاجتماع</div>";
    } else {
        echo "<div class='alert alert-info'>ℹ️ عمود رابط الاجتماع موجود</div>";
    }

    // 4. تحديث طلبات الانضمام المقبولة لإضافة الطلاب للكورسات
    echo "<h4>✅ معالجة طلبات الانضمام المقبولة</h4>";
    
    $stmt = $conn->query("
        SELECT jr.*, c.title as course_title, u.name as student_name
        FROM course_join_requests jr
        JOIN courses c ON jr.course_id = c.id
        JOIN users u ON jr.student_id = u.id
        WHERE jr.status = 'approved'
    ");
    $approved_requests = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $processed_count = 0;
    
    foreach ($approved_requests as $request) {
        // التحقق من عدم وجود تسجيل سابق
        $stmt = $conn->prepare("SELECT id FROM course_enrollments WHERE course_id = ? AND student_id = ?");
        $stmt->execute([$request['course_id'], $request['student_id']]);
        
        if (!$stmt->fetch()) {
            $stmt = $conn->prepare("
                INSERT INTO course_enrollments (course_id, student_id, enrollment_date, status)
                VALUES (?, ?, NOW(), 'active')
            ");
            $stmt->execute([$request['course_id'], $request['student_id']]);
            $processed_count++;
            
            echo "<div class='alert alert-success'>✅ تم تسجيل الطالب " . htmlspecialchars($request['student_name']) . " في كورس " . htmlspecialchars($request['course_title']) . "</div>";
        }
    }
    
    if ($processed_count == 0) {
        echo "<div class='alert alert-info'>ℹ️ جميع الطلاب المقبولين مسجلين في الكورسات</div>";
    }

    // 5. إنشاء مواد تعليمية تجريبية
    echo "<h4>📄 إنشاء مواد تعليمية تجريبية</h4>";
    
    $stmt = $conn->query("SELECT id, title FROM courses WHERE status = 'active' LIMIT 3");
    $courses = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $materials_created = 0;
    
    foreach ($courses as $course) {
        $materials = [
            ['ملف المنهج الدراسي', 'منهج شامل للكورس مع جميع المواضيع المطلوبة', 'uploads/materials/curriculum.pdf'],
            ['عرض تقديمي - المحاضرة الأولى', 'عرض تقديمي للمحاضرة الأولى', 'uploads/materials/lecture1.pptx'],
            ['ملف التمارين العملية', 'تمارين عملية لتطبيق ما تم تعلمه', 'uploads/materials/exercises.docx']
        ];
        
        foreach ($materials as $material) {
            $stmt = $conn->prepare("SELECT id FROM course_materials WHERE course_id = ? AND title = ?");
            $stmt->execute([$course['id'], $material[0]]);
            
            if (!$stmt->fetch()) {
                $stmt = $conn->prepare("
                    INSERT INTO course_materials (course_id, title, description, file_path, file_type, status, created_at)
                    VALUES (?, ?, ?, ?, ?, 'active', NOW())
                ");
                $file_ext = pathinfo($material[2], PATHINFO_EXTENSION);
                $stmt->execute([$course['id'], $material[0], $material[1], $material[2], $file_ext]);
                $materials_created++;
            }
        }
    }
    
    if ($materials_created > 0) {
        echo "<div class='alert alert-success'>✅ تم إنشاء $materials_created مادة تعليمية تجريبية</div>";
    } else {
        echo "<div class='alert alert-info'>ℹ️ المواد التعليمية موجودة مسبقاً</div>";
    }

    // 6. إضافة روابط اجتماع تجريبية للجلسات
    echo "<h4>🎥 إضافة روابط اجتماع للجلسات</h4>";
    
    $stmt = $conn->query("SELECT id, title FROM sessions WHERE meeting_link IS NULL OR meeting_link = '' LIMIT 5");
    $sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $links_added = 0;
    
    foreach ($sessions as $session) {
        $meeting_link = "https://zoom.us/j/" . rand(1000000000, 9999999999) . "?pwd=" . bin2hex(random_bytes(8));
        
        $stmt = $conn->prepare("UPDATE sessions SET meeting_link = ? WHERE id = ?");
        $stmt->execute([$meeting_link, $session['id']]);
        $links_added++;
        
        echo "<div class='alert alert-success'>✅ تم إضافة رابط اجتماع للجلسة: " . htmlspecialchars($session['title']) . "</div>";
    }
    
    if ($links_added == 0) {
        echo "<div class='alert alert-info'>ℹ️ جميع الجلسات لديها روابط اجتماع</div>";
    }

    // 7. إحصائيات النظام
    echo "<h4>📊 إحصائيات النظام</h4>";
    
    // إحصائيات الطلاب
    $stmt = $conn->query("SELECT COUNT(*) FROM users WHERE role = 'student'");
    $total_students = $stmt->fetchColumn();
    
    $stmt = $conn->query("SELECT COUNT(DISTINCT student_id) FROM course_enrollments WHERE status = 'active'");
    $enrolled_students = $stmt->fetchColumn();
    
    // إحصائيات الكورسات
    $stmt = $conn->query("SELECT COUNT(*) FROM courses WHERE status = 'active'");
    $active_courses = $stmt->fetchColumn();
    
    $stmt = $conn->query("SELECT COUNT(*) FROM course_enrollments WHERE status = 'active'");
    $total_enrollments = $stmt->fetchColumn();
    
    // إحصائيات الجلسات
    $stmt = $conn->query("SELECT COUNT(*) FROM sessions");
    $total_sessions = $stmt->fetchColumn();
    
    $stmt = $conn->query("SELECT COUNT(*) FROM sessions WHERE meeting_link IS NOT NULL AND meeting_link != ''");
    $sessions_with_links = $stmt->fetchColumn();
    
    // إحصائيات المواد
    $stmt = $conn->query("SELECT COUNT(*) FROM course_materials WHERE status = 'active'");
    $total_materials = $stmt->fetchColumn();
    
    echo "<div class='row'>";
    
    echo "<div class='col-md-3'>";
    echo "<div class='card bg-primary text-white'>";
    echo "<div class='card-body text-center'>";
    echo "<h3>$total_students</h3>";
    echo "<p class='mb-0'>إجمالي الطلاب</p>";
    echo "</div></div></div>";
    
    echo "<div class='col-md-3'>";
    echo "<div class='card bg-success text-white'>";
    echo "<div class='card-body text-center'>";
    echo "<h3>$enrolled_students</h3>";
    echo "<p class='mb-0'>طلاب مسجلين</p>";
    echo "</div></div></div>";
    
    echo "<div class='col-md-3'>";
    echo "<div class='card bg-info text-white'>";
    echo "<div class='card-body text-center'>";
    echo "<h3>$total_enrollments</h3>";
    echo "<p class='mb-0'>إجمالي التسجيلات</p>";
    echo "</div></div></div>";
    
    echo "<div class='col-md-3'>";
    echo "<div class='card bg-warning text-white'>";
    echo "<div class='card-body text-center'>";
    echo "<h3>$active_courses</h3>";
    echo "<p class='mb-0'>كورسات نشطة</p>";
    echo "</div></div></div>";
    
    echo "</div>";
    
    echo "<div class='row mt-3'>";
    
    echo "<div class='col-md-4'>";
    echo "<div class='card bg-secondary text-white'>";
    echo "<div class='card-body text-center'>";
    echo "<h3>$total_sessions</h3>";
    echo "<p class='mb-0'>إجمالي الجلسات</p>";
    echo "</div></div></div>";
    
    echo "<div class='col-md-4'>";
    echo "<div class='card bg-dark text-white'>";
    echo "<div class='card-body text-center'>";
    echo "<h3>$sessions_with_links</h3>";
    echo "<p class='mb-0'>جلسات بروابط</p>";
    echo "</div></div></div>";
    
    echo "<div class='col-md-4'>";
    echo "<div class='card bg-danger text-white'>";
    echo "<div class='card-body text-center'>";
    echo "<h3>$total_materials</h3>";
    echo "<p class='mb-0'>مواد تعليمية</p>";
    echo "</div></div></div>";
    
    echo "</div>";

    // 8. عرض بيانات تسجيل الدخول للطلاب
    echo "<h4>🔑 بيانات تسجيل الدخول للطلاب</h4>";
    
    $stmt = $conn->query("
        SELECT u.*, 
               (SELECT COUNT(*) FROM course_enrollments WHERE student_id = u.id AND status = 'active') as enrolled_courses
        FROM users u 
        WHERE u.role = 'student' 
        ORDER BY u.created_at DESC 
        LIMIT 5
    ");
    $students = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($students)) {
        echo "<div class='table-responsive'>";
        echo "<table class='table table-striped'>";
        echo "<thead class='table-dark'>";
        echo "<tr><th>الاسم</th><th>البريد الإلكتروني</th><th>كلمة المرور</th><th>الكورسات</th><th>الحالة</th></tr>";
        echo "</thead><tbody>";
        
        foreach ($students as $student) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($student['name']) . "</td>";
            echo "<td>" . htmlspecialchars($student['email']) . "</td>";
            echo "<td><code>student123</code></td>";
            echo "<td><span class='badge bg-info'>{$student['enrolled_courses']}</span></td>";
            echo "<td><span class='badge bg-success'>{$student['status']}</span></td>";
            echo "</tr>";
        }
        
        echo "</tbody></table>";
        echo "</div>";
    }

    echo "<div class='alert alert-success mt-4'>";
    echo "<h5>🎉 تم إعداد نظام الطلاب بنجاح!</h5>";
    echo "<ul class='mb-0'>";
    echo "<li>✅ تم إنشاء واجهة الطلاب</li>";
    echo "<li>✅ تم تسجيل الطلاب المقبولين في الكورسات</li>";
    echo "<li>✅ تم إضافة المواد التعليمية</li>";
    echo "<li>✅ تم إضافة روابط الاجتماعات</li>";
    echo "<li>✅ النظام جاهز للاستخدام</li>";
    echo "</ul>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div class='alert alert-danger'>❌ خطأ: " . htmlspecialchars($e->getMessage()) . "</div>";
}

echo "<div class='text-center mt-4'>";
echo "<a href='login.php' class='btn btn-primary btn-lg me-2'>🔐 تسجيل الدخول</a>";
echo "<a href='student/dashboard.php' class='btn btn-success btn-lg me-2'>🎓 لوحة الطالب</a>";
echo "<a href='instructor/course-join-requests.php?course_id=18' class='btn btn-info btn-lg'>📋 طلبات الانضمام</a>";
echo "</div>";

echo "<div class='mt-4'>";
echo "<div class='alert alert-info'>";
echo "<h6>💡 خطوات الاختبار:</h6>";
echo "<ol>";
echo "<li>ادخل كمدرب وقبل طلبات الانضمام</li>";
echo "<li>سجل دخول كطالب باستخدام البيانات أعلاه</li>";
echo "<li>ستجد الكورسات المقبول فيها في لوحة التحكم</li>";
echo "<li>ادخل للكورس وشاهد الجلسات والمواد</li>";
echo "<li>جرب الانضمام للجلسات المباشرة</li>";
echo "</ol>";
echo "</div>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
