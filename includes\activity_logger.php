<?php
/**
 * نظام تسجيل الأنشطة الشامل
 * Comprehensive Activity Logging System
 */

/**
 * ملاحظة: تم نقل دالة logActivity إلى ملف functions.php لتجنب التكرار
 */

/**
 * ملاحظة: تم نقل دوال logLoginAttempt و logUserActivity إلى ملف functions.php لتجنب التكرار
 * هذا الملف يحتوي على الدوال المساعدة الإضافية فقط
 */

/**
 * دالة logActivity الأساسية - تستخدم الدالة من functions.php
 */
function logActivity($user_id, $action, $description, $additionalData = []) {
    // تأكد من تضمين functions.php
    if (!function_exists('logUserActivity')) {
        require_once __DIR__ . '/functions.php';
    }
    return logUserActivity($user_id, $action, $description);
}

/**
 * تسجيل نشاط المدير
 */
function logAdminActivity($action, $description, $additionalData = []) {
    if (!function_exists('logUserActivity')) {
        require_once __DIR__ . '/functions.php';
    }
    return logUserActivity($_SESSION['user_id'] ?? null, $action, $description . ' (إجراء إداري)');
}

/**
 * تسجيل أنشطة الكورسات
 */
function logCourseActivity($action, $courseId, $description, $additionalData = []) {
    if (!function_exists('logUserActivity')) {
        require_once __DIR__ . '/functions.php';
    }
    return logUserActivity($_SESSION['user_id'] ?? null, $action, $description . " (كورس ID: $courseId)");
}

/**
 * تسجيل أنشطة الجلسات
 */
function logSessionActivity($action, $sessionId, $description, $additionalData = []) {
    if (!function_exists('logUserActivity')) {
        require_once __DIR__ . '/functions.php';
    }
    return logUserActivity($_SESSION['user_id'] ?? null, $action, $description . " (جلسة ID: $sessionId)");
}

/**
 * تسجيل أنشطة الطلاب
 */
function logStudentActivity($action, $studentId, $description, $additionalData = []) {
    if (!function_exists('logUserActivity')) {
        require_once __DIR__ . '/functions.php';
    }
    return logUserActivity($studentId, $action, $description . " (طالب ID: $studentId)");
}

/**
 * الحصول على آخر الأنشطة
 */
function getRecentActivities($limit = 50) {
    global $conn;
    
    try {
        $stmt = $conn->prepare("
            SELECT 
                al.*,
                u.name as user_name,
                u.email as user_email,
                u.role as user_role
            FROM activity_logs al
            LEFT JOIN users u ON al.user_id = u.id
            ORDER BY al.created_at DESC
            LIMIT ?
        ");
        
        $stmt->execute([$limit]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
        
    } catch (Exception $e) {
        error_log("Get activities error: " . $e->getMessage());
        return [];
    }
}

/**
 * الحصول على محاولات تسجيل الدخول
 */
function getLoginAttempts($limit = 50) {
    global $conn;
    
    try {
        $stmt = $conn->prepare("
            SELECT * FROM login_attempts 
            ORDER BY attempt_time DESC 
            LIMIT ?
        ");
        
        $stmt->execute([$limit]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
        
    } catch (Exception $e) {
        error_log("Get login attempts error: " . $e->getMessage());
        return [];
    }
}

/**
 * إحصائيات الأنشطة
 */
function getActivityStats() {
    global $conn;
    
    try {
        $stats = [];
        
        // إجمالي الأنشطة
        $stmt = $conn->query("SELECT COUNT(*) FROM activity_logs");
        $stats['total_activities'] = $stmt->fetchColumn();
        
        // الأنشطة اليوم
        $stmt = $conn->query("SELECT COUNT(*) FROM activity_logs WHERE DATE(created_at) = CURDATE()");
        $stats['today_activities'] = $stmt->fetchColumn();
        
        // محاولات تسجيل الدخول الناجحة
        $stmt = $conn->query("SELECT COUNT(*) FROM login_attempts WHERE success = 1");
        $stats['successful_logins'] = $stmt->fetchColumn();
        
        // محاولات تسجيل الدخول الفاشلة
        $stmt = $conn->query("SELECT COUNT(*) FROM login_attempts WHERE success = 0");
        $stats['failed_logins'] = $stmt->fetchColumn();
        
        // أكثر الأنشطة
        $stmt = $conn->query("
            SELECT action, COUNT(*) as count 
            FROM activity_logs 
            GROUP BY action 
            ORDER BY count DESC 
            LIMIT 5
        ");
        $stats['top_actions'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        return $stats;
        
    } catch (Exception $e) {
        error_log("Get activity stats error: " . $e->getMessage());
        return [];
    }
}

/**
 * تنظيف الأنشطة القديمة (أكثر من 90 يوم)
 */
function cleanOldActivities() {
    global $conn;
    
    try {
        $stmt = $conn->prepare("DELETE FROM activity_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 90 DAY)");
        $stmt->execute();
        
        $stmt = $conn->prepare("DELETE FROM login_attempts WHERE attempt_time < DATE_SUB(NOW(), INTERVAL 90 DAY)");
        $stmt->execute();
        
        return true;
        
    } catch (Exception $e) {
        error_log("Clean old activities error: " . $e->getMessage());
        return false;
    }
}
?>
