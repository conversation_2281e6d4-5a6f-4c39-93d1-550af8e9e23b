<?php
require_once '../../includes/session_config.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';
require_once '../../includes/security.php';

// التحقق من صلاحيات المدير
if (!isLoggedIn() || !isAdmin()) {
    echo json_encode(['success' => false, 'message' => 'غير مصرح لك بهذا الإجراء']);
    exit;
}

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'طريقة طلب غير صحيحة']);
    exit;
}

$student_id = $_POST['student_id'] ?? 0;
$course_id = $_POST['course_id'] ?? 0;

if (!$student_id || !$course_id) {
    echo json_encode(['success' => false, 'message' => 'معرف الطالب والكورس مطلوبان']);
    exit;
}

try {
    // بدء المعاملة
    $conn->beginTransaction();
    
    // جلب معلومات الطالب والكورس قبل الحذف
    $stmt = $conn->prepare("
        SELECT u.name as student_name, c.title as course_title
        FROM course_enrollments e
        JOIN users u ON e.student_id = u.id
        JOIN courses c ON e.course_id = c.id
        WHERE e.student_id = ? AND e.course_id = ?
    ");
    $stmt->execute([$student_id, $course_id]);
    $enrollment = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$enrollment) {
        throw new Exception('التسجيل غير موجود');
    }
    
    // حذف سجلات الحضور للطالب في جلسات هذا الكورس
    $stmt = $conn->prepare("
        DELETE sa FROM session_attendance sa
        JOIN sessions s ON sa.session_id = s.id
        WHERE sa.student_id = ? AND s.course_id = ?
    ");
    $stmt->execute([$student_id, $course_id]);
    
    // حذف تسجيل الطالب من الكورس
    $stmt = $conn->prepare("DELETE FROM course_enrollments WHERE student_id = ? AND course_id = ?");
    $stmt->execute([$student_id, $course_id]);
    
    // تسجيل النشاط
    logUserActivity($_SESSION['user_id'], 'إلغاء تسجيل طالب', "تم إلغاء تسجيل الطالب '{$enrollment['student_name']}' من الكورس: {$enrollment['course_title']}");
    
    // تأكيد المعاملة
    $conn->commit();
    
    echo json_encode([
        'success' => true, 
        'message' => 'تم إلغاء تسجيل الطالب بنجاح'
    ]);
    
} catch (Exception $e) {
    // التراجع عن المعاملة في حالة الخطأ
    $conn->rollBack();
    
    error_log("Error removing student: " . $e->getMessage());
    echo json_encode([
        'success' => false, 
        'message' => 'حدث خطأ أثناء إلغاء تسجيل الطالب: ' . $e->getMessage()
    ]);
}
?>
