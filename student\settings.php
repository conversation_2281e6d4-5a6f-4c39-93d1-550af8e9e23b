<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات الطالب
if (!isLoggedIn() || !isStudent()) {
    header('Location: ../login.php');
    exit;
}

$pageTitle = 'الإعدادات';
$breadcrumbs = [
    ['title' => 'الإعدادات']
];

$student_id = $_SESSION['user_id'];
$success = '';
$error = '';

// معالجة تحديث إعدادات الإشعارات
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_notifications'])) {
    $email_notifications = isset($_POST['email_notifications']) ? 1 : 0;
    $sms_notifications = isset($_POST['sms_notifications']) ? 1 : 0;
    $push_notifications = isset($_POST['push_notifications']) ? 1 : 0;
    $course_updates = isset($_POST['course_updates']) ? 1 : 0;
    $assignment_reminders = isset($_POST['assignment_reminders']) ? 1 : 0;
    $quiz_reminders = isset($_POST['quiz_reminders']) ? 1 : 0;
    $session_reminders = isset($_POST['session_reminders']) ? 1 : 0;
    $marketing_emails = isset($_POST['marketing_emails']) ? 1 : 0;
    
    try {
        // التحقق من وجود إعدادات الإشعارات
        $stmt = $conn->prepare("SELECT id FROM user_notification_settings WHERE user_id = ?");
        $stmt->execute([$student_id]);
        $existing = $stmt->fetch();
        
        if ($existing) {
            // تحديث الإعدادات الموجودة
            $stmt = $conn->prepare("
                UPDATE user_notification_settings SET
                    email_notifications = ?, sms_notifications = ?, push_notifications = ?,
                    course_updates = ?, assignment_reminders = ?, quiz_reminders = ?,
                    session_reminders = ?, marketing_emails = ?, updated_at = NOW()
                WHERE user_id = ?
            ");
            $stmt->execute([
                $email_notifications, $sms_notifications, $push_notifications,
                $course_updates, $assignment_reminders, $quiz_reminders,
                $session_reminders, $marketing_emails, $student_id
            ]);
        } else {
            // إنشاء إعدادات جديدة
            $stmt = $conn->prepare("
                INSERT INTO user_notification_settings 
                (user_id, email_notifications, sms_notifications, push_notifications,
                 course_updates, assignment_reminders, quiz_reminders, session_reminders, marketing_emails)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $student_id, $email_notifications, $sms_notifications, $push_notifications,
                $course_updates, $assignment_reminders, $quiz_reminders,
                $session_reminders, $marketing_emails
            ]);
        }
        
        $success = 'تم تحديث إعدادات الإشعارات بنجاح';
        
    } catch (Exception $e) {
        $error = 'حدث خطأ أثناء تحديث الإعدادات';
    }
}

// معالجة تحديث إعدادات الخصوصية
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_privacy'])) {
    $profile_visibility = $_POST['profile_visibility'];
    $show_progress = isset($_POST['show_progress']) ? 1 : 0;
    $show_certificates = isset($_POST['show_certificates']) ? 1 : 0;
    $allow_messages = isset($_POST['allow_messages']) ? 1 : 0;
    $show_online_status = isset($_POST['show_online_status']) ? 1 : 0;
    
    try {
        // التحقق من وجود إعدادات الخصوصية
        $stmt = $conn->prepare("SELECT id FROM user_privacy_settings WHERE user_id = ?");
        $stmt->execute([$student_id]);
        $existing = $stmt->fetch();
        
        if ($existing) {
            // تحديث الإعدادات الموجودة
            $stmt = $conn->prepare("
                UPDATE user_privacy_settings SET
                    profile_visibility = ?, show_progress = ?, show_certificates = ?,
                    allow_messages = ?, show_online_status = ?, updated_at = NOW()
                WHERE user_id = ?
            ");
            $stmt->execute([
                $profile_visibility, $show_progress, $show_certificates,
                $allow_messages, $show_online_status, $student_id
            ]);
        } else {
            // إنشاء إعدادات جديدة
            $stmt = $conn->prepare("
                INSERT INTO user_privacy_settings 
                (user_id, profile_visibility, show_progress, show_certificates, allow_messages, show_online_status)
                VALUES (?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $student_id, $profile_visibility, $show_progress, $show_certificates,
                $allow_messages, $show_online_status
            ]);
        }
        
        $success = 'تم تحديث إعدادات الخصوصية بنجاح';
        
    } catch (Exception $e) {
        $error = 'حدث خطأ أثناء تحديث الإعدادات';
    }
}

// معالجة حذف الحساب
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['delete_account'])) {
    $password = $_POST['delete_password'];
    $confirmation = $_POST['delete_confirmation'];
    
    try {
        if ($confirmation !== 'DELETE') {
            throw new Exception('يجب كتابة "DELETE" للتأكيد');
        }
        
        // التحقق من كلمة المرور
        $stmt = $conn->prepare("SELECT password FROM users WHERE id = ?");
        $stmt->execute([$student_id]);
        $user = $stmt->fetch();
        
        if (!password_verify($password, $user['password'])) {
            throw new Exception('كلمة المرور غير صحيحة');
        }
        
        // حذف الحساب (تعطيل بدلاً من الحذف الفعلي)
        $stmt = $conn->prepare("UPDATE users SET status = 'inactive', updated_at = NOW() WHERE id = ?");
        $stmt->execute([$student_id]);
        
        // تسجيل الخروج
        session_destroy();
        header('Location: ../index.php?message=account_deleted');
        exit;
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

try {
    // جلب بيانات المستخدم
    $stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$student_id]);
    $user = $stmt->fetch();
    
    // جلب إعدادات الإشعارات
    $stmt = $conn->prepare("SELECT * FROM user_notification_settings WHERE user_id = ?");
    $stmt->execute([$student_id]);
    $notification_settings = $stmt->fetch();
    
    // إعدادات افتراضية إذا لم توجد
    if (!$notification_settings) {
        $notification_settings = [
            'email_notifications' => 1,
            'sms_notifications' => 0,
            'push_notifications' => 1,
            'course_updates' => 1,
            'assignment_reminders' => 1,
            'quiz_reminders' => 1,
            'session_reminders' => 1,
            'marketing_emails' => 0
        ];
    }
    
    // جلب إعدادات الخصوصية
    $stmt = $conn->prepare("SELECT * FROM user_privacy_settings WHERE user_id = ?");
    $stmt->execute([$student_id]);
    $privacy_settings = $stmt->fetch();
    
    // إعدادات افتراضية إذا لم توجد
    if (!$privacy_settings) {
        $privacy_settings = [
            'profile_visibility' => 'public',
            'show_progress' => 1,
            'show_certificates' => 1,
            'allow_messages' => 1,
            'show_online_status' => 1
        ];
    }
    
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء جلب الإعدادات';
}

include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- العنوان -->
        <div class="col-12 mb-4">
            <h2 class="text-primary">
                <i class="fas fa-cog me-2"></i>
                الإعدادات
            </h2>
            <p class="text-muted">إدارة إعدادات حسابك وتفضيلاتك</p>
        </div>

        <?php if ($success): ?>
        <div class="col-12 mb-3">
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo htmlspecialchars($success); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </div>
        <?php endif; ?>

        <?php if ($error): ?>
        <div class="col-12 mb-3">
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-circle me-2"></i>
                <?php echo htmlspecialchars($error); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </div>
        <?php endif; ?>

        <!-- تبويبات الإعدادات -->
        <div class="col-12">
            <div class="card-student">
                <div class="card-header">
                    <ul class="nav nav-tabs card-header-tabs" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" data-bs-toggle="tab" href="#notifications-tab">
                                <i class="fas fa-bell me-1"></i>
                                الإشعارات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="tab" href="#privacy-tab">
                                <i class="fas fa-shield-alt me-1"></i>
                                الخصوصية
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="tab" href="#preferences-tab">
                                <i class="fas fa-sliders-h me-1"></i>
                                التفضيلات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="tab" href="#account-tab">
                                <i class="fas fa-user-cog me-1"></i>
                                إدارة الحساب
                            </a>
                        </li>
                    </ul>
                </div>
                <div class="card-body">
                    <div class="tab-content">
                        <!-- تبويب الإشعارات -->
                        <div class="tab-pane fade show active" id="notifications-tab">
                            <form method="POST">
                                <h5 class="text-primary mb-3">إعدادات الإشعارات</h5>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6 class="mb-3">طرق الإشعار</h6>
                                        
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" name="email_notifications" 
                                                   id="email_notifications" <?php echo $notification_settings['email_notifications'] ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="email_notifications">
                                                <i class="fas fa-envelope text-primary me-2"></i>
                                                إشعارات البريد الإلكتروني
                                            </label>
                                        </div>
                                        
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" name="sms_notifications" 
                                                   id="sms_notifications" <?php echo $notification_settings['sms_notifications'] ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="sms_notifications">
                                                <i class="fas fa-sms text-success me-2"></i>
                                                إشعارات الرسائل النصية
                                            </label>
                                        </div>
                                        
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" name="push_notifications" 
                                                   id="push_notifications" <?php echo $notification_settings['push_notifications'] ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="push_notifications">
                                                <i class="fas fa-bell text-warning me-2"></i>
                                                الإشعارات المنبثقة
                                            </label>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <h6 class="mb-3">أنواع الإشعارات</h6>
                                        
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" name="course_updates" 
                                                   id="course_updates" <?php echo $notification_settings['course_updates'] ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="course_updates">
                                                <i class="fas fa-graduation-cap text-primary me-2"></i>
                                                تحديثات الكورسات
                                            </label>
                                        </div>
                                        
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" name="assignment_reminders" 
                                                   id="assignment_reminders" <?php echo $notification_settings['assignment_reminders'] ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="assignment_reminders">
                                                <i class="fas fa-tasks text-warning me-2"></i>
                                                تذكير بالواجبات
                                            </label>
                                        </div>
                                        
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" name="quiz_reminders" 
                                                   id="quiz_reminders" <?php echo $notification_settings['quiz_reminders'] ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="quiz_reminders">
                                                <i class="fas fa-question-circle text-info me-2"></i>
                                                تذكير بالاختبارات
                                            </label>
                                        </div>
                                        
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" name="session_reminders" 
                                                   id="session_reminders" <?php echo $notification_settings['session_reminders'] ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="session_reminders">
                                                <i class="fas fa-video text-danger me-2"></i>
                                                تذكير بالجلسات
                                            </label>
                                        </div>
                                        
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" name="marketing_emails" 
                                                   id="marketing_emails" <?php echo $notification_settings['marketing_emails'] ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="marketing_emails">
                                                <i class="fas fa-bullhorn text-secondary me-2"></i>
                                                رسائل تسويقية
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="text-end">
                                    <button type="submit" name="update_notifications" class="btn btn-student-primary">
                                        <i class="fas fa-save me-1"></i>
                                        حفظ إعدادات الإشعارات
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- تبويب الخصوصية -->
                        <div class="tab-pane fade" id="privacy-tab">
                            <form method="POST">
                                <h5 class="text-primary mb-3">إعدادات الخصوصية</h5>
                                
                                <div class="mb-4">
                                    <label class="form-label">مستوى ظهور الملف الشخصي</label>
                                    <select name="profile_visibility" class="form-select">
                                        <option value="public" <?php echo $privacy_settings['profile_visibility'] === 'public' ? 'selected' : ''; ?>>
                                            عام - يمكن لأي شخص رؤية ملفي الشخصي
                                        </option>
                                        <option value="students" <?php echo $privacy_settings['profile_visibility'] === 'students' ? 'selected' : ''; ?>>
                                            الطلاب فقط - يمكن للطلاب المسجلين رؤية ملفي الشخصي
                                        </option>
                                        <option value="private" <?php echo $privacy_settings['profile_visibility'] === 'private' ? 'selected' : ''; ?>>
                                            خاص - لا يمكن لأحد رؤية ملفي الشخصي
                                        </option>
                                    </select>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6 class="mb-3">إظهار المعلومات</h6>
                                        
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" name="show_progress" 
                                                   id="show_progress" <?php echo $privacy_settings['show_progress'] ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="show_progress">
                                                <i class="fas fa-chart-line text-primary me-2"></i>
                                                إظهار تقدمي في الكورسات
                                            </label>
                                        </div>
                                        
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" name="show_certificates" 
                                                   id="show_certificates" <?php echo $privacy_settings['show_certificates'] ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="show_certificates">
                                                <i class="fas fa-certificate text-warning me-2"></i>
                                                إظهار شهاداتي
                                            </label>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <h6 class="mb-3">التفاعل</h6>
                                        
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" name="allow_messages" 
                                                   id="allow_messages" <?php echo $privacy_settings['allow_messages'] ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="allow_messages">
                                                <i class="fas fa-envelope text-info me-2"></i>
                                                السماح بإرسال الرسائل إلي
                                            </label>
                                        </div>
                                        
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" name="show_online_status" 
                                                   id="show_online_status" <?php echo $privacy_settings['show_online_status'] ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="show_online_status">
                                                <i class="fas fa-circle text-success me-2"></i>
                                                إظهار حالة الاتصال
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="text-end">
                                    <button type="submit" name="update_privacy" class="btn btn-student-primary">
                                        <i class="fas fa-save me-1"></i>
                                        حفظ إعدادات الخصوصية
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- تبويب التفضيلات -->
                        <div class="tab-pane fade" id="preferences-tab">
                            <h5 class="text-primary mb-3">التفضيلات العامة</h5>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">اللغة المفضلة</label>
                                        <select class="form-select">
                                            <option value="ar" <?php echo $user['language'] === 'ar' ? 'selected' : ''; ?>>العربية</option>
                                            <option value="en" <?php echo $user['language'] === 'en' ? 'selected' : ''; ?>>English</option>
                                        </select>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">المنطقة الزمنية</label>
                                        <select class="form-select">
                                            <option value="Asia/Riyadh" <?php echo $user['timezone'] === 'Asia/Riyadh' ? 'selected' : ''; ?>>الرياض (GMT+3)</option>
                                            <option value="Asia/Dubai" <?php echo $user['timezone'] === 'Asia/Dubai' ? 'selected' : ''; ?>>دبي (GMT+4)</option>
                                            <option value="Asia/Kuwait" <?php echo $user['timezone'] === 'Asia/Kuwait' ? 'selected' : ''; ?>>الكويت (GMT+3)</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">تخطيط الصفحة</label>
                                        <select class="form-select">
                                            <option value="default">افتراضي</option>
                                            <option value="compact">مضغوط</option>
                                            <option value="wide">واسع</option>
                                        </select>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">السمة</label>
                                        <select class="form-select">
                                            <option value="light">فاتح</option>
                                            <option value="dark">داكن</option>
                                            <option value="auto">تلقائي</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="text-end">
                                <button type="button" class="btn btn-student-primary">
                                    <i class="fas fa-save me-1"></i>
                                    حفظ التفضيلات
                                </button>
                            </div>
                        </div>

                        <!-- تبويب إدارة الحساب -->
                        <div class="tab-pane fade" id="account-tab">
                            <h5 class="text-primary mb-3">إدارة الحساب</h5>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card border-info">
                                        <div class="card-header bg-info text-white">
                                            <h6 class="mb-0">
                                                <i class="fas fa-download me-2"></i>
                                                تصدير البيانات
                                            </h6>
                                        </div>
                                        <div class="card-body">
                                            <p class="card-text">احصل على نسخة من جميع بياناتك المخزنة في النظام.</p>
                                            <button class="btn btn-info">
                                                <i class="fas fa-download me-1"></i>
                                                تصدير البيانات
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="card border-danger">
                                        <div class="card-header bg-danger text-white">
                                            <h6 class="mb-0">
                                                <i class="fas fa-exclamation-triangle me-2"></i>
                                                منطقة الخطر
                                            </h6>
                                        </div>
                                        <div class="card-body">
                                            <p class="card-text text-danger">
                                                <strong>تحذير:</strong> حذف الحساب عملية لا يمكن التراجع عنها.
                                            </p>
                                            <button class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteAccountModal">
                                                <i class="fas fa-trash me-1"></i>
                                                حذف الحساب
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal حذف الحساب -->
<div class="modal fade" id="deleteAccountModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title">تأكيد حذف الحساب</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>تحذير:</strong> هذا الإجراء لا يمكن التراجع عنه. سيتم حذف جميع بياناتك نهائياً.
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">كلمة المرور للتأكيد</label>
                        <input type="password" name="delete_password" class="form-control" required>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">اكتب "DELETE" للتأكيد</label>
                        <input type="text" name="delete_confirmation" class="form-control" required 
                               placeholder="DELETE">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" name="delete_account" class="btn btn-danger">
                        <i class="fas fa-trash me-1"></i>
                        حذف الحساب نهائياً
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
