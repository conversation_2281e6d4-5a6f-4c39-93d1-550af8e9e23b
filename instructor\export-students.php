<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من صلاحيات المدرب
if (!isLoggedIn() || !isInstructor()) {
    header('Location: ../login.php');
    exit;
}

$course_id = (int)($_GET['course_id'] ?? 0);
$format = $_GET['format'] ?? 'excel';

// التحقق من أن الكورس ينتمي للمدرب
try {
    $stmt = $conn->prepare("SELECT title FROM courses WHERE id = ? AND instructor_id = ?");
    $stmt->execute([$course_id, $_SESSION['user_id']]);
    $course = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$course) {
        header('Location: courses.php');
        exit;
    }
} catch (PDOException $e) {
    die('خطأ في التحقق من الكورس');
}

// جلب بيانات الطلاب مع الإحصائيات
try {
    $stmt = $conn->prepare("
        SELECT 
            u.id,
            u.name,
            u.email,
            u.phone,
            u.created_at as registration_date,
            e.enrollment_date,
            e.status as enrollment_status,
            e.notes as enrollment_notes,
            COUNT(DISTINCT g.id) as total_grades,
            AVG(CASE WHEN g.max_grade > 0 THEN (g.grade / g.max_grade) * 100 END) as avg_grade,
            COUNT(DISTINCT sa.id) as attended_sessions,
            MAX(g.created_at) as last_grade_date
        FROM users u
        JOIN enrollments e ON u.id = e.student_id
        LEFT JOIN grades g ON u.id = g.student_id AND g.course_id = e.course_id
        LEFT JOIN session_attendance sa ON u.id = sa.student_id AND sa.course_id = e.course_id AND sa.attended = 1
        WHERE e.course_id = ?
        GROUP BY u.id, u.name, u.email, u.phone, u.created_at, e.enrollment_date, e.status, e.notes
        ORDER BY e.enrollment_date DESC
    ");
    $stmt->execute([$course_id]);
    $students = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    die('خطأ في جلب بيانات الطلاب: ' . $e->getMessage());
}

// معالجة التصدير
if (isset($_GET['download']) && $_GET['download'] === '1') {
    
    if ($format === 'excel') {
        // تصدير Excel
        header('Content-Type: application/vnd.ms-excel; charset=utf-8');
        header('Content-Disposition: attachment; filename="students_' . $course_id . '_' . date('Y-m-d') . '.xls"');
        header('Pragma: no-cache');
        header('Expires: 0');
        
        echo "\xEF\xBB\xBF"; // UTF-8 BOM
        
        echo "<table border='1'>";
        echo "<tr>";
        echo "<th>الرقم</th>";
        echo "<th>اسم الطالب</th>";
        echo "<th>البريد الإلكتروني</th>";
        echo "<th>رقم الهاتف</th>";
        echo "<th>تاريخ التسجيل في المنصة</th>";
        echo "<th>تاريخ الانضمام للكورس</th>";
        echo "<th>حالة التسجيل</th>";
        echo "<th>عدد الدرجات</th>";
        echo "<th>متوسط الدرجات</th>";
        echo "<th>الجلسات المحضورة</th>";
        echo "<th>آخر درجة</th>";
        echo "<th>ملاحظات</th>";
        echo "</tr>";
        
        foreach ($students as $index => $student) {
            echo "<tr>";
            echo "<td>" . ($index + 1) . "</td>";
            echo "<td>" . htmlspecialchars($student['name']) . "</td>";
            echo "<td>" . htmlspecialchars($student['email']) . "</td>";
            echo "<td>" . htmlspecialchars($student['phone'] ?? '') . "</td>";
            echo "<td>" . date('Y-m-d', strtotime($student['registration_date'])) . "</td>";
            echo "<td>" . date('Y-m-d', strtotime($student['enrollment_date'])) . "</td>";
            echo "<td>" . htmlspecialchars($student['enrollment_status']) . "</td>";
            echo "<td>" . $student['total_grades'] . "</td>";
            echo "<td>" . ($student['avg_grade'] ? round($student['avg_grade'], 2) . '%' : '0%') . "</td>";
            echo "<td>" . $student['attended_sessions'] . "</td>";
            echo "<td>" . ($student['last_grade_date'] ? date('Y-m-d', strtotime($student['last_grade_date'])) : 'لا يوجد') . "</td>";
            echo "<td>" . htmlspecialchars($student['enrollment_notes'] ?? '') . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
        exit;
        
    } elseif ($format === 'csv') {
        // تصدير CSV
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename="students_' . $course_id . '_' . date('Y-m-d') . '.csv"');
        header('Pragma: no-cache');
        header('Expires: 0');
        
        echo "\xEF\xBB\xBF"; // UTF-8 BOM
        
        $output = fopen('php://output', 'w');
        
        // العناوين
        fputcsv($output, [
            'الرقم',
            'اسم الطالب',
            'البريد الإلكتروني',
            'رقم الهاتف',
            'تاريخ التسجيل في المنصة',
            'تاريخ الانضمام للكورس',
            'حالة التسجيل',
            'عدد الدرجات',
            'متوسط الدرجات',
            'الجلسات المحضورة',
            'آخر درجة',
            'ملاحظات'
        ]);
        
        // البيانات
        foreach ($students as $index => $student) {
            fputcsv($output, [
                $index + 1,
                $student['name'],
                $student['email'],
                $student['phone'] ?? '',
                date('Y-m-d', strtotime($student['registration_date'])),
                date('Y-m-d', strtotime($student['enrollment_date'])),
                $student['enrollment_status'],
                $student['total_grades'],
                $student['avg_grade'] ? round($student['avg_grade'], 2) . '%' : '0%',
                $student['attended_sessions'],
                $student['last_grade_date'] ? date('Y-m-d', strtotime($student['last_grade_date'])) : 'لا يوجد',
                $student['enrollment_notes'] ?? ''
            ]);
        }
        
        fclose($output);
        exit;
        
    } elseif ($format === 'pdf') {
        // تصدير PDF (يتطلب مكتبة PDF)
        // هنا يمكن استخدام مكتبة مثل TCPDF أو FPDF
        // للبساطة، سنعرض HTML قابل للطباعة
        
        ?>
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>قائمة طلاب الكورس - <?php echo htmlspecialchars($course['title']); ?></title>
            <style>
                body { font-family: Arial, sans-serif; direction: rtl; }
                table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                th { background-color: #f2f2f2; font-weight: bold; }
                .header { text-align: center; margin-bottom: 30px; }
                .footer { margin-top: 30px; text-align: center; font-size: 12px; color: #666; }
                @media print {
                    .no-print { display: none; }
                }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>قائمة طلاب الكورس</h1>
                <h2><?php echo htmlspecialchars($course['title']); ?></h2>
                <p>تاريخ التصدير: <?php echo date('Y-m-d H:i'); ?></p>
                <p>إجمالي الطلاب: <?php echo count($students); ?></p>
            </div>
            
            <button class="no-print" onclick="window.print()">طباعة</button>
            
            <table>
                <thead>
                    <tr>
                        <th>الرقم</th>
                        <th>اسم الطالب</th>
                        <th>البريد الإلكتروني</th>
                        <th>تاريخ الانضمام</th>
                        <th>الحالة</th>
                        <th>عدد الدرجات</th>
                        <th>المعدل</th>
                        <th>الحضور</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($students as $index => $student): ?>
                        <tr>
                            <td><?php echo $index + 1; ?></td>
                            <td><?php echo htmlspecialchars($student['name']); ?></td>
                            <td><?php echo htmlspecialchars($student['email']); ?></td>
                            <td><?php echo date('Y-m-d', strtotime($student['enrollment_date'])); ?></td>
                            <td><?php echo htmlspecialchars($student['enrollment_status']); ?></td>
                            <td><?php echo $student['total_grades']; ?></td>
                            <td><?php echo $student['avg_grade'] ? round($student['avg_grade'], 2) . '%' : '0%'; ?></td>
                            <td><?php echo $student['attended_sessions']; ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
            
            <div class="footer">
                <p>تم إنشاء هذا التقرير بواسطة منصة التعلم - <?php echo date('Y-m-d H:i:s'); ?></p>
            </div>
        </body>
        </html>
        <?php
        exit;
    }
}

$pageTitle = 'تصدير بيانات الطلاب - ' . $course['title'];
$breadcrumbs = [
    ['title' => 'الكورسات', 'url' => 'courses.php'],
    ['title' => $course['title'], 'url' => 'course-details.php?id=' . $course_id],
    ['title' => 'الطلاب', 'url' => 'students.php?course_id=' . $course_id],
    ['title' => 'تصدير البيانات']
];

include 'includes/header.php';
?>

<div class="row">
    <div class="col-md-8">
        <!-- خيارات التصدير -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-download me-2"></i>
                    تصدير بيانات الطلاب
                </h5>
            </div>
            <div class="card-body">
                <p class="text-muted mb-4">
                    اختر تنسيق التصدير المناسب لتحميل بيانات طلاب الكورس مع جميع الإحصائيات والدرجات.
                </p>

                <div class="row g-3">
                    <!-- تصدير Excel -->
                    <div class="col-md-4">
                        <div class="card border-success h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-file-excel fa-3x text-success mb-3"></i>
                                <h6>ملف Excel</h6>
                                <p class="small text-muted">
                                    ملف .xls يحتوي على جميع البيانات والإحصائيات
                                </p>
                                <a href="?course_id=<?php echo $course_id; ?>&format=excel&download=1" 
                                   class="btn btn-success">
                                    <i class="fas fa-download me-2"></i>
                                    تحميل Excel
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- تصدير CSV -->
                    <div class="col-md-4">
                        <div class="card border-primary h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-file-csv fa-3x text-primary mb-3"></i>
                                <h6>ملف CSV</h6>
                                <p class="small text-muted">
                                    ملف نصي منفصل بفواصل للاستيراد في برامج أخرى
                                </p>
                                <a href="?course_id=<?php echo $course_id; ?>&format=csv&download=1" 
                                   class="btn btn-primary">
                                    <i class="fas fa-download me-2"></i>
                                    تحميل CSV
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- تصدير PDF -->
                    <div class="col-md-4">
                        <div class="card border-danger h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-file-pdf fa-3x text-danger mb-3"></i>
                                <h6>ملف PDF</h6>
                                <p class="small text-muted">
                                    تقرير قابل للطباعة بتنسيق PDF
                                </p>
                                <a href="?course_id=<?php echo $course_id; ?>&format=pdf&download=1" 
                                   class="btn btn-danger" target="_blank">
                                    <i class="fas fa-download me-2"></i>
                                    عرض PDF
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <hr class="my-4">

                <!-- معاينة البيانات -->
                <h6 class="mb-3">معاينة البيانات المراد تصديرها:</h6>
                
                <div class="table-responsive">
                    <table class="table table-sm table-striped">
                        <thead class="table-light">
                            <tr>
                                <th>الطالب</th>
                                <th>تاريخ الانضمام</th>
                                <th>الحالة</th>
                                <th>الدرجات</th>
                                <th>المعدل</th>
                                <th>الحضور</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach (array_slice($students, 0, 5) as $student): ?>
                                <tr>
                                    <td>
                                        <div>
                                            <strong><?php echo htmlspecialchars($student['name']); ?></strong>
                                            <br>
                                            <small class="text-muted"><?php echo htmlspecialchars($student['email']); ?></small>
                                        </div>
                                    </td>
                                    <td><?php echo date('Y-m-d', strtotime($student['enrollment_date'])); ?></td>
                                    <td>
                                        <?php
                                        $status_classes = [
                                            'pending' => 'bg-warning',
                                            'approved' => 'bg-success',
                                            'suspended' => 'bg-danger',
                                            'rejected' => 'bg-secondary'
                                        ];
                                        ?>
                                        <span class="badge <?php echo $status_classes[$student['enrollment_status']] ?? 'bg-secondary'; ?>">
                                            <?php echo htmlspecialchars($student['enrollment_status']); ?>
                                        </span>
                                    </td>
                                    <td><?php echo $student['total_grades']; ?></td>
                                    <td>
                                        <?php echo $student['avg_grade'] ? round($student['avg_grade'], 1) . '%' : '0%'; ?>
                                    </td>
                                    <td><?php echo $student['attended_sessions']; ?></td>
                                </tr>
                            <?php endforeach; ?>
                            
                            <?php if (count($students) > 5): ?>
                                <tr>
                                    <td colspan="6" class="text-center text-muted">
                                        ... و <?php echo count($students) - 5; ?> طالب آخر
                                    </td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <!-- إحصائيات التصدير -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    إحصائيات التصدير
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h4 class="text-primary"><?php echo count($students); ?></h4>
                            <small class="text-muted">إجمالي الطلاب</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success">
                            <?php 
                            $approved = array_filter($students, function($s) { return $s['enrollment_status'] === 'approved'; });
                            echo count($approved);
                            ?>
                        </h4>
                        <small class="text-muted">طالب معتمد</small>
                    </div>
                </div>
                
                <hr>
                
                <div class="small">
                    <div class="d-flex justify-content-between mb-2">
                        <span>في الانتظار:</span>
                        <span class="badge bg-warning">
                            <?php echo count(array_filter($students, function($s) { return $s['enrollment_status'] === 'pending'; })); ?>
                        </span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>موقوف:</span>
                        <span class="badge bg-danger">
                            <?php echo count(array_filter($students, function($s) { return $s['enrollment_status'] === 'suspended'; })); ?>
                        </span>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span>مرفوض:</span>
                        <span class="badge bg-secondary">
                            <?php echo count(array_filter($students, function($s) { return $s['enrollment_status'] === 'rejected'; })); ?>
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- معلومات الكورس -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات الكورس
                </h6>
            </div>
            <div class="card-body">
                <h6><?php echo htmlspecialchars($course['title']); ?></h6>
                <small class="text-muted">
                    تاريخ التصدير: <?php echo date('Y-m-d H:i'); ?>
                </small>
                
                <hr>
                
                <div class="d-grid gap-2">
                    <a href="students.php?course_id=<?php echo $course_id; ?>" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-users me-2"></i>
                        عرض جميع الطلاب
                    </a>
                    <a href="course-details.php?id=<?php echo $course_id; ?>" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للكورس
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.card-header {
    background: var(--instructor-light);
    border-bottom: 1px solid var(--gray-200);
    border-radius: 12px 12px 0 0 !important;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}
</style>

<!-- JavaScript -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

</body>
</html>
