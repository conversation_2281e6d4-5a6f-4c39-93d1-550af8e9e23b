<?php
require_once '../../includes/session_config.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';
require_once '../../includes/security.php';

// التحقق من صلاحيات المدير
if (!isLoggedIn() || !isAdmin()) {
    echo json_encode(['success' => false, 'message' => 'غير مصرح لك بهذا الإجراء']);
    exit;
}

$student_id = $_GET['student_id'] ?? 0;
$course_id = $_GET['course_id'] ?? 0;

if (!$student_id || !$course_id) {
    echo json_encode(['success' => false, 'message' => 'معرف الطالب والكورس مطلوبان']);
    exit;
}

try {
    // جلب سجل الحضور للطالب في هذا الكورس
    $stmt = $conn->prepare("
        SELECT s.title as session_title, s.session_date,
               COALESCE(sa.attendance_status, 'absent') as attendance_status,
               sa.attended_at, sa.notes
        FROM sessions s
        LEFT JOIN session_attendance sa ON s.id = sa.session_id AND sa.student_id = ?
        WHERE s.course_id = ?
        ORDER BY s.session_date ASC
    ");
    $stmt->execute([$student_id, $course_id]);
    $attendance = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // تنسيق التواريخ
    foreach ($attendance as &$record) {
        $record['session_date'] = date('Y-m-d H:i', strtotime($record['session_date']));
        if ($record['attended_at']) {
            $record['attended_at'] = date('Y-m-d H:i', strtotime($record['attended_at']));
        }
    }
    
    echo json_encode([
        'success' => true,
        'attendance' => $attendance
    ]);
    
} catch (Exception $e) {
    error_log("Error getting student attendance: " . $e->getMessage());
    echo json_encode([
        'success' => false, 
        'message' => 'حدث خطأ في جلب سجل الحضور: ' . $e->getMessage()
    ]);
}
?>
