<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات المدرب
if (!isLoggedIn() || !isInstructor()) {
    header('Location: ../login.php');
    exit;
}

$course_id = isset($_GET['course_id']) ? (int)$_GET['course_id'] : 0;
$instructor_id = $_SESSION['user_id'];

// التحقق من ملكية الكورس
try {
    $stmt = $conn->prepare("SELECT * FROM courses WHERE id = ? AND instructor_id = ?");
    $stmt->execute([$course_id, $instructor_id]);
    $course = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$course) {
        header('Location: courses.php');
        exit;
    }
} catch (PDOException $e) {
    header('Location: courses.php');
    exit;
}

// جلب إحصائيات الجلسات
$statistics = [];
try {
    // إحصائيات عامة
    $stmt = $conn->prepare("
        SELECT 
            COUNT(*) as total_sessions,
            COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_sessions,
            COUNT(CASE WHEN status = 'scheduled' THEN 1 END) as scheduled_sessions,
            COUNT(CASE WHEN status = 'ongoing' THEN 1 END) as ongoing_sessions,
            COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_sessions
        FROM sessions 
        WHERE course_id = ?
    ");
    $stmt->execute([$course_id]);
    $statistics['general'] = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // إحصائيات الحضور
    $stmt = $conn->prepare("
        SELECT 
            SUM(attendance_count) as total_attendance,
            AVG(attendance_count) as avg_attendance,
            MAX(attendance_count) as max_attendance,
            MIN(attendance_count) as min_attendance
        FROM (
            SELECT 
                s.id,
                (SELECT COUNT(*) FROM session_attendance WHERE session_id = s.id) as attendance_count
            FROM sessions s
            WHERE s.course_id = ?
        ) as session_stats
    ");
    $stmt->execute([$course_id]);
    $statistics['attendance'] = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // إحصائيات شهرية
    $stmt = $conn->prepare("
        SELECT 
            DATE_FORMAT(session_date, '%Y-%m') as month,
            COUNT(*) as sessions_count,
            COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_count
        FROM sessions 
        WHERE course_id = ? AND session_date IS NOT NULL
        GROUP BY DATE_FORMAT(session_date, '%Y-%m')
        ORDER BY month DESC
        LIMIT 12
    ");
    $stmt->execute([$course_id]);
    $statistics['monthly'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // أكثر الجلسات حضوراً
    $stmt = $conn->prepare("
        SELECT 
            s.*,
            (SELECT COUNT(*) FROM session_attendance WHERE session_id = s.id) as attendance_count
        FROM sessions s
        WHERE s.course_id = ?
        ORDER BY attendance_count DESC
        LIMIT 5
    ");
    $stmt->execute([$course_id]);
    $statistics['top_sessions'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // إحصائيات أسبوعية (آخر 8 أسابيع)
    $stmt = $conn->prepare("
        SELECT 
            YEARWEEK(session_date) as week,
            COUNT(*) as sessions_count,
            SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_count
        FROM sessions 
        WHERE course_id = ? AND session_date >= DATE_SUB(NOW(), INTERVAL 8 WEEK)
        GROUP BY YEARWEEK(session_date)
        ORDER BY week DESC
    ");
    $stmt->execute([$course_id]);
    $statistics['weekly'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (PDOException $e) {
    $error = 'حدث خطأ في جلب الإحصائيات: ' . $e->getMessage();
}

$pageTitle = 'إحصائيات الجلسات - ' . $course['title'];
include 'includes/header.php';

?>

<div class="container-fluid py-4">
    <!-- معلومات الكورس -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="mb-0">إحصائيات جلسات الكورس</h5>
                        <small><?php echo htmlspecialchars($course['title']); ?></small>
                    </div>
                    <div>
                        <button onclick="window.print()" class="btn btn-light btn-sm me-2">
                            <i class="fas fa-print"></i> طباعة
                        </button>
                        <a href="course-sessions.php?course_id=<?php echo $course_id; ?>" class="btn btn-light btn-sm">
                            <i class="fas fa-arrow-left"></i> العودة
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php if (isset($error)): ?>
        <div class="alert alert-danger"><?php echo $error; ?></div>
    <?php endif; ?>

    <!-- الإحصائيات العامة -->
    <div class="row mb-4">
        <div class="col-12">
            <h6 class="text-muted mb-3">الإحصائيات العامة</h6>
        </div>
        <div class="col-md-2">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h3><?php echo $statistics['general']['total_sessions'] ?? 0; ?></h3>
                    <p class="mb-0">إجمالي الجلسات</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h3><?php echo $statistics['general']['completed_sessions'] ?? 0; ?></h3>
                    <p class="mb-0">جلسة مكتملة</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h3><?php echo $statistics['general']['scheduled_sessions'] ?? 0; ?></h3>
                    <p class="mb-0">جلسة مجدولة</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h3><?php echo $statistics['general']['ongoing_sessions'] ?? 0; ?></h3>
                    <p class="mb-0">جلسة جارية</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-danger text-white">
                <div class="card-body text-center">
                    <h3><?php echo $statistics['general']['cancelled_sessions'] ?? 0; ?></h3>
                    <p class="mb-0">جلسة ملغاة</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-secondary text-white">
                <div class="card-body text-center">
                    <?php
                    $completion_rate = $statistics['general']['total_sessions'] > 0 ? 
                        round(($statistics['general']['completed_sessions'] / $statistics['general']['total_sessions']) * 100, 1) : 0;
                    ?>
                    <h3><?php echo $completion_rate; ?>%</h3>
                    <p class="mb-0">معدل الإكمال</p>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات الحضور -->
    <div class="row mb-4">
        <div class="col-12">
            <h6 class="text-muted mb-3">إحصائيات الحضور</h6>
        </div>
        <div class="col-md-3">
            <div class="card border-primary">
                <div class="card-body text-center">
                    <h4 class="text-primary"><?php echo $statistics['attendance']['total_attendance'] ?? 0; ?></h4>
                    <p class="mb-0">إجمالي الحضور</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-success">
                <div class="card-body text-center">
                    <h4 class="text-success"><?php echo round($statistics['attendance']['avg_attendance'] ?? 0, 1); ?></h4>
                    <p class="mb-0">متوسط الحضور</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-warning">
                <div class="card-body text-center">
                    <h4 class="text-warning"><?php echo $statistics['attendance']['max_attendance'] ?? 0; ?></h4>
                    <p class="mb-0">أعلى حضور</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-info">
                <div class="card-body text-center">
                    <h4 class="text-info"><?php echo $statistics['attendance']['min_attendance'] ?? 0; ?></h4>
                    <p class="mb-0">أقل حضور</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <!-- الإحصائيات الشهرية -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">الإحصائيات الشهرية</h6>
                </div>
                <div class="card-body">
                    <?php if (!empty($statistics['monthly'])): ?>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>الشهر</th>
                                        <th>إجمالي الجلسات</th>
                                        <th>الجلسات المكتملة</th>
                                        <th>معدل الإكمال</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($statistics['monthly'] as $month): ?>
                                        <tr>
                                            <td><?php echo $month['month']; ?></td>
                                            <td><?php echo $month['sessions_count']; ?></td>
                                            <td><?php echo $month['completed_count']; ?></td>
                                            <td>
                                                <?php
                                                $rate = $month['sessions_count'] > 0 ? 
                                                    round(($month['completed_count'] / $month['sessions_count']) * 100, 1) : 0;
                                                ?>
                                                <span class="badge bg-<?php echo $rate >= 80 ? 'success' : ($rate >= 60 ? 'warning' : 'danger'); ?>">
                                                    <?php echo $rate; ?>%
                                                </span>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <p class="text-muted text-center">لا توجد بيانات شهرية</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- أكثر الجلسات حضوراً -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">أكثر الجلسات حضوراً</h6>
                </div>
                <div class="card-body">
                    <?php if (!empty($statistics['top_sessions'])): ?>
                        <div class="list-group list-group-flush">
                            <?php foreach ($statistics['top_sessions'] as $index => $session): ?>
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-1"><?php echo htmlspecialchars($session['title']); ?></h6>
                                        <small class="text-muted">
                                            <?php if (isset($session['session_date'])): ?>
                                                <?php echo $session['session_date']; ?>
                                            <?php endif; ?>
                                        </small>
                                    </div>
                                    <div class="text-end">
                                        <span class="badge bg-primary rounded-pill"><?php echo $session['attendance_count']; ?></span>
                                        <br><small class="text-muted">#<?php echo $index + 1; ?></small>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <p class="text-muted text-center">لا توجد جلسات</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- الرسم البياني للإحصائيات الأسبوعية -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">الإحصائيات الأسبوعية (آخر 8 أسابيع)</h6>
                </div>
                <div class="card-body">
                    <?php if (!empty($statistics['weekly'])): ?>
                        <canvas id="weeklyChart" width="400" height="100"></canvas>
                    <?php else: ?>
                        <p class="text-muted text-center">لا توجد بيانات أسبوعية</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- ملخص التقرير -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">ملخص التقرير</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>نقاط القوة:</h6>
                            <ul class="list-unstyled">
                                <?php if ($completion_rate >= 80): ?>
                                    <li><i class="fas fa-check text-success"></i> معدل إكمال ممتاز (<?php echo $completion_rate; ?>%)</li>
                                <?php endif; ?>
                                <?php if (($statistics['attendance']['avg_attendance'] ?? 0) >= 10): ?>
                                    <li><i class="fas fa-check text-success"></i> متوسط حضور جيد</li>
                                <?php endif; ?>
                                <?php if (($statistics['general']['total_sessions'] ?? 0) >= 5): ?>
                                    <li><i class="fas fa-check text-success"></i> عدد جلسات كافي</li>
                                <?php endif; ?>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>نقاط التحسين:</h6>
                            <ul class="list-unstyled">
                                <?php if ($completion_rate < 60): ?>
                                    <li><i class="fas fa-exclamation-triangle text-warning"></i> معدل إكمال منخفض</li>
                                <?php endif; ?>
                                <?php if (($statistics['attendance']['avg_attendance'] ?? 0) < 5): ?>
                                    <li><i class="fas fa-exclamation-triangle text-warning"></i> متوسط حضور منخفض</li>
                                <?php endif; ?>
                                <?php if (($statistics['general']['cancelled_sessions'] ?? 0) > 0): ?>
                                    <li><i class="fas fa-exclamation-triangle text-warning"></i> وجود جلسات ملغاة</li>
                                <?php endif; ?>
                            </ul>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="text-center">
                        <small class="text-muted">
                            تم إنشاء التقرير في: <?php echo date('Y-m-d H:i:s'); ?> • 
                            الكورس: <?php echo htmlspecialchars($course['title']); ?>
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// الرسم البياني للإحصائيات الأسبوعية
<?php if (!empty($statistics['weekly'])): ?>
const weeklyData = <?php echo json_encode(array_reverse($statistics['weekly'])); ?>;
const ctx = document.getElementById('weeklyChart').getContext('2d');

new Chart(ctx, {
    type: 'line',
    data: {
        labels: weeklyData.map(item => 'أسبوع ' + item.week),
        datasets: [{
            label: 'إجمالي الجلسات',
            data: weeklyData.map(item => item.sessions_count),
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            tension: 0.1
        }, {
            label: 'الجلسات المكتملة',
            data: weeklyData.map(item => item.completed_count),
            borderColor: 'rgb(54, 162, 235)',
            backgroundColor: 'rgba(54, 162, 235, 0.2)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        plugins: {
            title: {
                display: true,
                text: 'إحصائيات الجلسات الأسبوعية'
            }
        },
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});
<?php endif; ?>

// طباعة التقرير
function printReport() {
    window.print();
}
</script>

<style>
@media print {
    .btn, .card-header .btn {
        display: none !important;
    }
    
    .card {
        border: 1px solid #ddd !important;
        box-shadow: none !important;
    }
    
    .bg-primary, .bg-success, .bg-warning, .bg-info, .bg-danger, .bg-secondary {
        background-color: #f8f9fa !important;
        color: #000 !important;
        border: 1px solid #ddd !important;
    }
}
</style>

<?php require_once '../includes/footer.php'; ?>
