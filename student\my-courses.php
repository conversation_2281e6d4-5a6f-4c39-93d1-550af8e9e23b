<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات الطالب
if (!isLoggedIn() || !isStudent()) {
    header('Location: ../login.php');
    exit;
}

$filter = isset($_GET['filter']) ? $_GET['filter'] : 'all';
$search = isset($_GET['search']) ? trim($_GET['search']) : '';

// بناء الاستعلام
$where_conditions = ["ce.student_id = ?"];
$params = [$_SESSION['user_id']];

if ($filter === 'active') {
    $where_conditions[] = "ce.status = 'active'";
} elseif ($filter === 'completed') {
    $where_conditions[] = "ce.status = 'completed'";
} elseif ($filter === 'free') {
    $where_conditions[] = "c.course_type = 'free'";
} elseif ($filter === 'paid') {
    $where_conditions[] = "c.course_type = 'paid'";
}

if (!empty($search)) {
    $where_conditions[] = "(c.title LIKE ? OR c.description LIKE ? OR u.name LIKE ?)";
    $search_param = "%$search%";
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
}

$where_clause = implode(' AND ', $where_conditions);

try {
    // جلب كورسات الطالب
    $stmt = $conn->prepare("
        SELECT c.*, u.name as instructor_name, ce.enrollment_date, ce.status as enrollment_status,
               cp.amount as paid_amount, cp.payment_date,
               (SELECT COUNT(*) FROM course_lessons WHERE course_id = c.id AND status = 'active') as total_lessons,
               (SELECT COUNT(*) FROM student_lesson_progress slp 
                WHERE slp.student_id = ce.student_id AND slp.course_id = c.id AND slp.is_completed = 1) as completed_lessons,
               (SELECT AVG(rating) FROM course_reviews WHERE course_id = c.id) as avg_rating,
               (SELECT COUNT(*) FROM course_reviews WHERE course_id = c.id) as total_reviews
        FROM course_enrollments ce
        JOIN courses c ON ce.course_id = c.id
        JOIN users u ON c.instructor_id = u.id
        LEFT JOIN course_payments cp ON ce.payment_id = cp.id
        WHERE $where_clause
        ORDER BY ce.enrollment_date DESC
    ");
    $stmt->execute($params);
    $my_courses = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // إحصائيات الطالب
    $stmt = $conn->prepare("
        SELECT 
            COUNT(*) as total_courses,
            COUNT(CASE WHEN ce.status = 'active' THEN 1 END) as active_courses,
            COUNT(CASE WHEN c.course_type = 'free' THEN 1 END) as free_courses,
            COUNT(CASE WHEN c.course_type = 'paid' THEN 1 END) as paid_courses,
            COALESCE(SUM(CASE WHEN cp.payment_status = 'completed' THEN cp.amount ELSE 0 END), 0) as total_spent
        FROM course_enrollments ce
        JOIN courses c ON ce.course_id = c.id
        LEFT JOIN course_payments cp ON ce.payment_id = cp.id
        WHERE ce.student_id = ?
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
} catch (PDOException $e) {
    $my_courses = [];
    $stats = ['total_courses' => 0, 'active_courses' => 0, 'free_courses' => 0, 'paid_courses' => 0, 'total_spent' => 0];
}

$pageTitle = 'كورساتي';
require_once '../includes/header.php';
?>

<style>
.course-card {
    transition: all 0.3s ease;
    border: none;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    height: 100%;
}

.course-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.course-image {
    height: 200px;
    background: linear-gradient(45deg, #f093fb 0%, #f5576c 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 3rem;
    position: relative;
}

.progress-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0,0,0,0.7);
    color: white;
    padding: 10px;
    font-size: 0.9rem;
}

.stats-card {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    margin-bottom: 20px;
}

.filter-btn {
    border-radius: 25px;
    padding: 8px 20px;
    margin: 5px;
    border: 2px solid #667eea;
    background: transparent;
    color: #667eea;
    transition: all 0.3s ease;
}

.filter-btn:hover,
.filter-btn.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.progress-circle {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: conic-gradient(#28a745 0deg, #28a745 var(--progress), #e9ecef var(--progress), #e9ecef 360deg);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.progress-circle::before {
    content: '';
    width: 45px;
    height: 45px;
    border-radius: 50%;
    background: white;
    position: absolute;
}

.progress-text {
    position: relative;
    z-index: 1;
    font-weight: bold;
    font-size: 0.9rem;
}
</style>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2>كورساتي</h2>
                    <p class="text-muted">تابع تقدمك في الكورسات المسجل بها</p>
                </div>
                <div>
                    <a href="../courses.php" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        تصفح كورسات جديدة
                    </a>
                </div>
            </div>

            <!-- إحصائيات الطالب -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="stats-card">
                        <h3><?php echo $stats['total_courses']; ?></h3>
                        <p class="mb-0">إجمالي الكورسات</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card">
                        <h3><?php echo $stats['active_courses']; ?></h3>
                        <p class="mb-0">كورسات نشطة</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card">
                        <h3><?php echo $stats['free_courses']; ?></h3>
                        <p class="mb-0">كورسات مجانية</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card">
                        <h3><?php echo number_format($stats['total_spent'], 0); ?></h3>
                        <p class="mb-0">إجمالي المدفوع (ريال)</p>
                    </div>
                </div>
            </div>

            <!-- فلاتر البحث -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row align-items-end">
                        <div class="col-md-6">
                            <label class="form-label">البحث في كورساتي</label>
                            <div class="input-group">
                                <input type="text" class="form-control" name="search" 
                                       value="<?php echo htmlspecialchars($search); ?>" 
                                       placeholder="ابحث عن كورس أو مدرب...">
                                <button class="btn btn-primary" type="submit">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex flex-wrap">
                                <a href="?filter=all" class="filter-btn <?php echo $filter === 'all' ? 'active' : ''; ?>">
                                    الكل
                                </a>
                                <a href="?filter=active" class="filter-btn <?php echo $filter === 'active' ? 'active' : ''; ?>">
                                    نشط
                                </a>
                                <a href="?filter=free" class="filter-btn <?php echo $filter === 'free' ? 'active' : ''; ?>">
                                    مجاني
                                </a>
                                <a href="?filter=paid" class="filter-btn <?php echo $filter === 'paid' ? 'active' : ''; ?>">
                                    مدفوع
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- عرض الكورسات -->
            <?php if (empty($my_courses)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-book-open fa-5x text-muted mb-4"></i>
                    <h4>لا توجد كورسات</h4>
                    <p class="text-muted mb-4">لم تسجل في أي كورس بعد</p>
                    <a href="../courses.php" class="btn btn-primary btn-lg">
                        <i class="fas fa-search me-2"></i>
                        تصفح الكورسات المتاحة
                    </a>
                </div>
            <?php else: ?>
                <div class="row">
                    <?php foreach ($my_courses as $course): ?>
                        <?php
                        $progress_percentage = $course['total_lessons'] > 0 
                            ? round(($course['completed_lessons'] / $course['total_lessons']) * 100) 
                            : 0;
                        ?>
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="card course-card">
                                <div class="course-image">
                                    <?php if ($course['image_path']): ?>
                                        <img src="<?php echo htmlspecialchars($course['image_path']); ?>" 
                                             class="w-100 h-100" style="object-fit: cover;" 
                                             alt="<?php echo htmlspecialchars($course['title']); ?>">
                                    <?php else: ?>
                                        <i class="fas fa-graduation-cap"></i>
                                    <?php endif; ?>
                                    
                                    <div class="progress-overlay">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <span>التقدم: <?php echo $progress_percentage; ?>%</span>
                                            <span><?php echo $course['completed_lessons']; ?>/<?php echo $course['total_lessons']; ?> دروس</span>
                                        </div>
                                        <div class="progress mt-2" style="height: 5px;">
                                            <div class="progress-bar bg-success" style="width: <?php echo $progress_percentage; ?>%"></div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="card-body">
                                    <h5 class="card-title"><?php echo htmlspecialchars($course['title']); ?></h5>
                                    <p class="card-text text-muted">
                                        <?php echo htmlspecialchars(substr($course['description'], 0, 100)) . '...'; ?>
                                    </p>
                                    
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <small class="text-muted">
                                            <i class="fas fa-user me-1"></i>
                                            <?php echo htmlspecialchars($course['instructor_name']); ?>
                                        </small>
                                        <div class="progress-circle" style="--progress: <?php echo $progress_percentage * 3.6; ?>deg;">
                                            <span class="progress-text"><?php echo $progress_percentage; ?>%</span>
                                        </div>
                                    </div>
                                    
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <span class="badge bg-<?php echo $course['course_type'] === 'free' ? 'success' : 'primary'; ?>">
                                            <?php echo $course['course_type'] === 'free' ? 'مجاني' : 'مدفوع'; ?>
                                        </span>
                                        
                                        <?php if ($course['avg_rating']): ?>
                                            <div class="d-flex align-items-center">
                                                <div class="text-warning me-1">
                                                    <?php
                                                    $rating = round($course['avg_rating']);
                                                    for ($i = 1; $i <= 5; $i++) {
                                                        echo $i <= $rating ? '<i class="fas fa-star"></i>' : '<i class="far fa-star"></i>';
                                                    }
                                                    ?>
                                                </div>
                                                <small class="text-muted"><?php echo number_format($course['avg_rating'], 1); ?></small>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <small class="text-muted">
                                            <i class="fas fa-calendar me-1"></i>
                                            تاريخ التسجيل: <?php echo date('Y-m-d', strtotime($course['enrollment_date'])); ?>
                                        </small>
                                        
                                        <?php if ($course['paid_amount']): ?>
                                            <br>
                                            <small class="text-success">
                                                <i class="fas fa-money-bill me-1"></i>
                                                دفعت: <?php echo number_format($course['paid_amount'], 0); ?> ريال
                                            </small>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                
                                <div class="card-footer bg-transparent">
                                    <div class="d-grid gap-2">
                                        <a href="../student/course-content.php?course_id=<?php echo $course['id']; ?>" 
                                           class="btn btn-primary">
                                            <i class="fas fa-play me-2"></i>
                                            <?php echo $progress_percentage > 0 ? 'متابعة التعلم' : 'ابدأ التعلم'; ?>
                                        </a>
                                        <a href="../course-details.php?id=<?php echo $course['id']; ?>" 
                                           class="btn btn-outline-secondary btn-sm">
                                            <i class="fas fa-info-circle me-1"></i>
                                            تفاصيل الكورس
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
// تحديث شريط التقدم بشكل دوري
setInterval(function() {
    // يمكن إضافة AJAX لتحديث التقدم في الوقت الفعلي
}, 30000);
</script>

<?php require_once '../includes/footer.php'; ?>
