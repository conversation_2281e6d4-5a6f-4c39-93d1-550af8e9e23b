<?php
require_once 'config/database.php';

echo "<h2>اختبار طلب الانضمام</h2>";

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = $_POST['name'];
    $email = $_POST['email'];
    $phone = $_POST['phone'];
    $message = $_POST['message'] ?? '';
    
    try {
        $stmt = $conn->prepare("INSERT INTO join_requests (name, email, phone, message, status, created_at) VALUES (?, ?, ?, ?, 'pending', NOW())");
        $result = $stmt->execute([$name, $email, $phone, $message]);
        
        if ($result) {
            echo "<div style='color: green; padding: 10px; border: 1px solid green; margin: 10px 0;'>";
            echo "✅ تم إرسال طلب الانضمام بنجاح!<br>";
            echo "ID: " . $conn->lastInsertId();
            echo "</div>";
        } else {
            echo "<div style='color: red; padding: 10px; border: 1px solid red; margin: 10px 0;'>";
            echo "❌ فشل في إرسال الطلب";
            echo "</div>";
        }
    } catch (Exception $e) {
        echo "<div style='color: red; padding: 10px; border: 1px solid red; margin: 10px 0;'>";
        echo "❌ خطأ: " . $e->getMessage();
        echo "</div>";
    }
}

// عرض آخر 5 طلبات
try {
    $stmt = $conn->prepare("SELECT * FROM join_requests ORDER BY created_at DESC LIMIT 5");
    $stmt->execute();
    $requests = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>آخر 5 طلبات انضمام:</h3>";
    if (count($requests) > 0) {
        echo "<table border='1' style='width: 100%; border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>الاسم</th><th>البريد</th><th>الهاتف</th><th>الحالة</th><th>التاريخ</th></tr>";
        foreach ($requests as $request) {
            echo "<tr>";
            echo "<td>" . $request['id'] . "</td>";
            echo "<td>" . htmlspecialchars($request['name']) . "</td>";
            echo "<td>" . htmlspecialchars($request['email']) . "</td>";
            echo "<td>" . htmlspecialchars($request['phone']) . "</td>";
            echo "<td>" . $request['status'] . "</td>";
            echo "<td>" . $request['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>لا توجد طلبات</p>";
    }
} catch (Exception $e) {
    echo "<p>خطأ في عرض الطلبات: " . $e->getMessage() . "</p>";
}
?>

<h3>إرسال طلب انضمام جديد:</h3>
<form method="POST" style="max-width: 500px;">
    <p>
        <label>الاسم:</label><br>
        <input type="text" name="name" required style="width: 100%; padding: 5px;">
    </p>
    <p>
        <label>البريد الإلكتروني:</label><br>
        <input type="email" name="email" required style="width: 100%; padding: 5px;">
    </p>
    <p>
        <label>الهاتف:</label><br>
        <input type="text" name="phone" required style="width: 100%; padding: 5px;">
    </p>
    <p>
        <label>رسالة (اختياري):</label><br>
        <textarea name="message" style="width: 100%; padding: 5px;" rows="3"></textarea>
    </p>
    <p>
        <input type="submit" value="إرسال طلب الانضمام" style="padding: 10px 20px; background: #007bff; color: white; border: none; cursor: pointer;">
    </p>
</form>
