<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من صلاحيات المدير
if (!isLoggedIn() || !isAdmin()) {
    header('Location: ../login.php');
    exit();
}

// إنشاء الجداول المطلوبة إذا لم تكن موجودة
try {
    // جدول activity_logs
    $conn->exec("
        CREATE TABLE IF NOT EXISTS activity_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT DEFAULT NULL,
            user_type ENUM('admin', 'instructor', 'student', 'visitor') DEFAULT 'visitor',
            action VARCHAR(255) NOT NULL,
            description TEXT,
            page_url VARCHAR(500),
            ip_address VARCHAR(45),
            user_agent TEXT,
            additional_data JSON DEFAULT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

            INDEX idx_user_id (user_id),
            INDEX idx_user_type (user_type),
            INDEX idx_action (action),
            INDEX idx_created_at (created_at),
            INDEX idx_ip_address (ip_address)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");

    // جدول login_attempts
    $conn->exec("
        CREATE TABLE IF NOT EXISTS login_attempts (
            id INT AUTO_INCREMENT PRIMARY KEY,
            email VARCHAR(255) NOT NULL,
            ip_address VARCHAR(45) NOT NULL,
            success BOOLEAN DEFAULT FALSE,
            attempt_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_email (email),
            INDEX idx_ip_address (ip_address),
            INDEX idx_attempt_time (attempt_time)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");

    // جدول blocked_ips
    $conn->exec("
        CREATE TABLE IF NOT EXISTS blocked_ips (
            id INT AUTO_INCREMENT PRIMARY KEY,
            ip VARCHAR(45) NOT NULL UNIQUE,
            blocked_until TIMESTAMP NOT NULL,
            reason VARCHAR(255) DEFAULT 'Too many failed login attempts',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_ip (ip),
            INDEX idx_blocked_until (blocked_until)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");

    // إضافة أعمدة إضافية إذا لم تكن موجودة
    try {
        $conn->exec("ALTER TABLE activity_logs ADD COLUMN user_type ENUM('admin', 'instructor', 'student', 'visitor') DEFAULT 'visitor' AFTER user_id");
    } catch (Exception $e) {
        // العمود موجود مسبقاً
    }

    try {
        $conn->exec("ALTER TABLE activity_logs ADD COLUMN page_url VARCHAR(500) AFTER description");
    } catch (Exception $e) {
        // العمود موجود مسبقاً
    }

} catch (Exception $e) {
    // تجاهل الأخطاء إذا كانت الجداول موجودة
}

// دالة لتسجيل النشاط
function logActivity($user_id, $user_type, $action, $description, $page_url = '', $additional_data = null) {
    global $conn;
    try {
        $stmt = $conn->prepare("
            INSERT INTO activity_logs (user_id, user_type, action, description, page_url, ip_address, user_agent, additional_data)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute([
            $user_id,
            $user_type,
            $action,
            $description,
            $page_url ?: $_SERVER['REQUEST_URI'] ?? '',
            $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            $additional_data ? json_encode($additional_data) : null
        ]);
    } catch (PDOException $e) {
        error_log("Error logging activity: " . $e->getMessage());
    }
}

// تسجيل زيارة المدير لهذه الصفحة
if (isset($_SESSION['user_id'])) {
    logActivity($_SESSION['user_id'], 'admin', 'page_visit', 'زيارة صفحة إدارة الأنشطة', '/admin/activity-logs.php');
}

// إضافة بعض الأنشطة التجريبية للزوار إذا لم تكن موجودة
try {
    $count_stmt = $conn->query("SELECT COUNT(*) as count FROM activity_logs WHERE user_type = 'visitor'");
    $visitor_count = $count_stmt->fetch(PDO::FETCH_ASSOC)['count'];

    if ($visitor_count < 20) {
        // إضافة أنشطة تجريبية للزوار
        $sample_activities = [
            [null, 'visitor', 'page_visit', 'زيارة الصفحة الرئيسية', '/', '***********00'],
            [null, 'visitor', 'course_view', 'عرض تفاصيل كورس البرمجة', '/course-details.php?id=1', '***********01'],
            [null, 'visitor', 'page_visit', 'زيارة صفحة الكورسات', '/courses.php', '***********02'],
            [null, 'visitor', 'search', 'البحث عن "تصميم مواقع"', '/search.php?q=تصميم', '***********03'],
            [null, 'visitor', 'page_visit', 'زيارة صفحة التسجيل', '/register.php', '***********04'],
            [null, 'visitor', 'page_visit', 'زيارة صفحة تسجيل الدخول', '/login.php', '***********05'],
            [null, 'visitor', 'course_view', 'عرض كورس التسويق الرقمي', '/course-details.php?id=2', '***********06'],
            [null, 'visitor', 'page_visit', 'زيارة صفحة من نحن', '/about.php', '***********07'],
            [null, 'visitor', 'page_visit', 'زيارة صفحة اتصل بنا', '/contact.php', '***********08'],
            [null, 'visitor', 'file_download', 'تحميل كتالوج الكورسات', '/downloads/catalog.pdf', '***********09'],
            [null, 'visitor', 'page_visit', 'زيارة صفحة الخدمات', '/services.php', '***********10'],
            [null, 'visitor', 'search', 'البحث عن "تطوير تطبيقات"', '/search.php?q=تطوير', '***********11'],
            [null, 'visitor', 'course_view', 'عرض كورس الذكاء الاصطناعي', '/course-details.php?id=3', '***********12'],
            [null, 'visitor', 'page_visit', 'زيارة صفحة الأسعار', '/pricing.php', '***********13'],
            [null, 'visitor', 'page_visit', 'زيارة صفحة الأسئلة الشائعة', '/faq.php', '***********14'],
            [null, 'visitor', 'course_view', 'عرض كورس إدارة المشاريع', '/course-details.php?id=4', '***********15'],
            [null, 'visitor', 'search', 'البحث عن "أمن المعلومات"', '/search.php?q=أمن', '***********16'],
            [null, 'visitor', 'page_visit', 'زيارة صفحة المدونة', '/blog.php', '***********17'],
            [null, 'visitor', 'file_download', 'تحميل دليل المستخدم', '/downloads/user-guide.pdf', '***********18'],
            [null, 'visitor', 'page_visit', 'زيارة صفحة الشهادات', '/certificates.php', '***********19']
        ];

        foreach ($sample_activities as $activity) {
            $stmt = $conn->prepare("
                INSERT INTO activity_logs (user_id, user_type, action, description, page_url, ip_address, user_agent, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 7) DAY))
            ");
            $stmt->execute([
                $activity[0],
                $activity[1],
                $activity[2],
                $activity[3],
                $activity[4],
                $activity[5],
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            ]);
        }
    }
} catch (PDOException $e) {
    // تجاهل الأخطاء
}

// Get filter parameters
$dateFrom = $_GET['date_from'] ?? date('Y-m-d', strtotime('-7 days'));
$dateTo = $_GET['date_to'] ?? date('Y-m-d');
$userId = (int)($_GET['user_id'] ?? 0);
$action = $_GET['action'] ?? '';
$userType = $_GET['user_type'] ?? '';
$ipAddress = $_GET['ip_address'] ?? '';
$search = $_GET['search'] ?? '';
$page = (int)($_GET['page'] ?? 1);
$perPage = 50;
$offset = ($page - 1) * $perPage;

// بناء استعلام الأنشطة مع الفلاتر
$where_conditions = ["1=1"];
$params = [];

if ($dateFrom) {
    $where_conditions[] = "DATE(al.created_at) >= ?";
    $params[] = $dateFrom;
}
if ($dateTo) {
    $where_conditions[] = "DATE(al.created_at) <= ?";
    $params[] = $dateTo;
}
if ($userId) {
    $where_conditions[] = "al.user_id = ?";
    $params[] = $userId;
}
if ($action) {
    $where_conditions[] = "al.action = ?";
    $params[] = $action;
}
if ($userType) {
    $where_conditions[] = "al.user_type = ?";
    $params[] = $userType;
}
if ($ipAddress) {
    $where_conditions[] = "al.ip_address LIKE ?";
    $params[] = "%$ipAddress%";
}
if ($search) {
    $where_conditions[] = "(al.description LIKE ? OR al.action LIKE ? OR u.name LIKE ? OR u.email LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

$where_clause = implode(' AND ', $where_conditions);

// عدد الأنشطة الإجمالي
try {
    $count_query = "
        SELECT COUNT(*) as total
        FROM activity_logs al
        LEFT JOIN users u ON al.user_id = u.id
        WHERE $where_clause
    ";
    $count_stmt = $conn->prepare($count_query);
    $count_stmt->execute($params);
    $total_activities = $count_stmt->fetch(PDO::FETCH_ASSOC)['total'];
    $total_pages = ceil($total_activities / $perPage);
} catch (PDOException $e) {
    $total_activities = 0;
    $total_pages = 0;
}

// جلب الأنشطة للصفحة الحالية
$query = "
    SELECT
        al.*,
        u.name as user_name,
        u.email as user_email,
        u.role as user_role
    FROM activity_logs al
    LEFT JOIN users u ON al.user_id = u.id
    WHERE $where_clause
    ORDER BY al.created_at DESC
    LIMIT $perPage OFFSET $offset
";

try {
    $stmt = $conn->prepare($query);
    $stmt->execute($params);
    $activities = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    error_log("Database error in activity logs: " . $e->getMessage());
    $error = 'حدث خطأ في قاعدة البيانات: ' . $e->getMessage();
    $activities = [];
}

// جلب المستخدمين للفلترة
try {
    $users_stmt = $conn->query("SELECT id, name, email, role FROM users ORDER BY name");
    $users = $users_stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $users = [];
}

// جلب أنواع الأنشطة الفريدة
try {
    $actions_stmt = $conn->query("SELECT DISTINCT action FROM activity_logs ORDER BY action");
    $actions = $actions_stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $actions = [];
}

// جلب عناوين IP الفريدة
try {
    $ips_stmt = $conn->query("SELECT DISTINCT ip_address FROM activity_logs WHERE ip_address IS NOT NULL ORDER BY ip_address LIMIT 100");
    $ip_addresses = $ips_stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $ip_addresses = [];
}

// إحصائيات شاملة
try {
    $stats_query = "
        SELECT
            COUNT(*) as total_activities,
            COUNT(DISTINCT al.user_id) as unique_users,
            COUNT(DISTINCT al.ip_address) as unique_ips,
            COUNT(CASE WHEN al.user_type = 'visitor' THEN 1 END) as visitor_activities,
            COUNT(CASE WHEN al.user_type = 'student' THEN 1 END) as student_activities,
            COUNT(CASE WHEN al.user_type = 'instructor' THEN 1 END) as instructor_activities,
            COUNT(CASE WHEN al.user_type = 'admin' THEN 1 END) as admin_activities,
            COUNT(CASE WHEN DATE(al.created_at) = CURDATE() THEN 1 END) as today_activities,
            COUNT(CASE WHEN al.created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR) THEN 1 END) as last_hour_activities,
            COUNT(CASE WHEN al.action = 'login' THEN 1 END) as total_logins,
            COUNT(CASE WHEN al.action = 'page_visit' THEN 1 END) as page_visits,
            COUNT(CASE WHEN al.action = 'course_view' THEN 1 END) as course_views,
            COUNT(CASE WHEN al.action = 'search' THEN 1 END) as searches
        FROM activity_logs al
        LEFT JOIN users u ON al.user_id = u.id
    ";
    $stats_stmt = $conn->query($stats_query);
    $stats = $stats_stmt->fetch(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $stats = [
        'total_activities' => 0,
        'unique_users' => 0,
        'unique_ips' => 0,
        'visitor_activities' => 0,
        'student_activities' => 0,
        'instructor_activities' => 0,
        'admin_activities' => 0,
        'today_activities' => 0,
        'last_hour_activities' => 0,
        'total_logins' => 0,
        'page_visits' => 0,
        'course_views' => 0,
        'searches' => 0
    ];
}

// دالة لتحويل الوقت إلى نص عربي
function timeAgo($datetime) {
    $time = time() - strtotime($datetime);

    if ($time < 60) return 'الآن';
    if ($time < 3600) return floor($time/60) . ' دقيقة';
    if ($time < 86400) return floor($time/3600) . ' ساعة';
    if ($time < 2592000) return floor($time/86400) . ' يوم';
    if ($time < 31536000) return floor($time/2592000) . ' شهر';

    return floor($time/31536000) . ' سنة';
}

// دالة لتحديد لون نوع المستخدم
function getUserTypeBadgeClass($userType) {
    switch($userType) {
        case 'admin': return 'bg-danger';
        case 'instructor': return 'bg-primary';
        case 'student': return 'bg-success';
        case 'visitor': return 'bg-secondary';
        default: return 'bg-light text-dark';
    }
}

// دالة لتحديد أيقونة النشاط
function getActivityIcon($action) {
    $icons = [
        'page_visit' => 'fas fa-eye',
        'login' => 'fas fa-sign-in-alt',
        'logout' => 'fas fa-sign-out-alt',
        'register' => 'fas fa-user-plus',
        'course_view' => 'fas fa-book',
        'course_enroll' => 'fas fa-graduation-cap',
        'session_join' => 'fas fa-video',
        'file_download' => 'fas fa-download',
        'search' => 'fas fa-search',
        'profile_update' => 'fas fa-user-edit',
        'password_change' => 'fas fa-key',
        'error' => 'fas fa-exclamation-triangle',
        'security_alert' => 'fas fa-shield-alt'
    ];

    return $icons[$action] ?? 'fas fa-circle';
}

$pageTitle = 'إدارة الأنشطة';
?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - لوحة تحكم المدير</title>

    <!-- الخطوط -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Bootstrap و Font Awesome -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- DataTables -->
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">

    <!-- AOS Animation -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: #f8f9fa;
        }

        .admin-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 3rem 0;
            margin-bottom: 2rem;
        }

        .stat-card {
            transition: transform 0.3s ease;
            border: none;
            border-radius: 15px;
            overflow: hidden;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .activity-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }

        .ip-address {
            font-family: 'Courier New', monospace;
            font-size: 0.85rem;
        }

        .user-agent {
            font-size: 0.75rem;
            color: #6c757d;
        }

        .activity-icon {
            width: 30px;
            text-align: center;
        }

        .table-responsive {
            border-radius: 10px;
            overflow: hidden;
        }

        .filter-card {
            border-radius: 15px;
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <!-- رأس الصفحة -->
    <div class="admin-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="display-5 fw-bold mb-3">إدارة الأنشطة</h1>
                    <p class="lead mb-0">مراقبة جميع أنشطة المستخدمين والزوار في المنصة</p>
                </div>
                <div class="col-md-4 text-center">
                    <i class="fas fa-chart-line" style="font-size: 5rem; opacity: 0.3;"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <!-- بطاقات الإحصائيات -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card stat-card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">إجمالي الأنشطة</h6>
                                <h2 class="mb-0"><?php echo number_format($stats['total_activities']); ?></h2>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-chart-bar fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card stat-card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">أنشطة الزوار</h6>
                                <h2 class="mb-0"><?php echo number_format($stats['visitor_activities']); ?></h2>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-eye fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card stat-card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">أنشطة اليوم</h6>
                                <h2 class="mb-0"><?php echo number_format($stats['today_activities']); ?></h2>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-calendar-day fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card stat-card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">الساعة الأخيرة</h6>
                                <h2 class="mb-0"><?php echo number_format($stats['last_hour_activities']); ?></h2>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-clock fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- إحصائيات إضافية -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card stat-card bg-secondary text-white">
                    <div class="card-body text-center">
                        <h6>مستخدمون فريدون</h6>
                        <h3><?php echo number_format($stats['unique_users']); ?></h3>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card stat-card bg-dark text-white">
                    <div class="card-body text-center">
                        <h6>عناوين IP فريدة</h6>
                        <h3><?php echo number_format($stats['unique_ips']); ?></h3>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card stat-card bg-purple text-white" style="background: #6f42c1 !important;">
                    <div class="card-body text-center">
                        <h6>مشاهدات الكورسات</h6>
                        <h3><?php echo number_format($stats['course_views']); ?></h3>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card stat-card bg-teal text-white" style="background: #20c997 !important;">
                    <div class="card-body text-center">
                        <h6>عمليات البحث</h6>
                        <h3><?php echo number_format($stats['searches']); ?></h3>
                    </div>
                </div>
            </div>
        </div>

        <!-- أدوات الفلترة والبحث -->
        <div class="card filter-card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-filter me-2"></i>
                    فلترة وبحث الأنشطة
                </h5>
            </div>
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-2">
                        <label class="form-label">البحث</label>
                        <input type="text" class="form-control" name="search"
                               value="<?php echo htmlspecialchars($search); ?>"
                               placeholder="البحث في الأنشطة...">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">من تاريخ</label>
                        <input type="date" class="form-control" name="date_from" value="<?php echo $dateFrom; ?>">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">إلى تاريخ</label>
                        <input type="date" class="form-control" name="date_to" value="<?php echo $dateTo; ?>">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">نوع المستخدم</label>
                        <select class="form-select" name="user_type">
                            <option value="">الكل</option>
                            <option value="visitor" <?php echo $userType == 'visitor' ? 'selected' : ''; ?>>زائر</option>
                            <option value="student" <?php echo $userType == 'student' ? 'selected' : ''; ?>>طالب</option>
                            <option value="instructor" <?php echo $userType == 'instructor' ? 'selected' : ''; ?>>مدرب</option>
                            <option value="admin" <?php echo $userType == 'admin' ? 'selected' : ''; ?>>مدير</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">نوع النشاط</label>
                        <select class="form-select" name="action">
                            <option value="">الكل</option>
                            <?php foreach ($actions as $act): ?>
                                <option value="<?php echo htmlspecialchars($act['action']); ?>"
                                        <?php echo $action == $act['action'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($act['action']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">المستخدم</label>
                        <select class="form-select" name="user_id">
                            <option value="">الكل</option>
                            <?php foreach ($users as $user): ?>
                                <option value="<?php echo $user['id']; ?>" <?php echo $userId == $user['id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($user['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">عنوان IP</label>
                        <input type="text" class="form-control" name="ip_address"
                               value="<?php echo htmlspecialchars($ipAddress); ?>"
                               placeholder="***********">
                    </div>
                    <div class="col-md-1">
                        <label class="form-label">&nbsp;</label>
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                    <div class="col-md-1">
                        <label class="form-label">&nbsp;</label>
                        <a href="activity-logs.php" class="btn btn-secondary w-100">
                            <i class="fas fa-undo"></i>
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- جدول الأنشطة -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    سجل الأنشطة
                    <span class="badge bg-primary ms-2"><?php echo number_format($total_activities); ?></span>
                </h5>
                <div>
                    <small class="text-muted">
                        عرض <?php echo count($activities); ?> من أصل <?php echo number_format($total_activities); ?> نشاط
                    </small>
                </div>
            </div>
            <div class="card-body p-0">
                <?php if (isset($error)): ?>
                    <div class="alert alert-danger m-3">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endif; ?>

                <?php if (empty($activities)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد أنشطة</h5>
                        <p class="text-muted">لم يتم العثور على أنشطة تطابق معايير البحث</p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover mb-0" id="activitiesTable">
                            <thead class="table-light">
                                <tr>
                                    <th width="5%">
                                        <i class="fas fa-hashtag"></i>
                                    </th>
                                    <th width="15%">
                                        <i class="fas fa-clock me-1"></i>
                                        التاريخ والوقت
                                    </th>
                                    <th width="15%">
                                        <i class="fas fa-user me-1"></i>
                                        المستخدم
                                    </th>
                                    <th width="10%">
                                        <i class="fas fa-tag me-1"></i>
                                        النوع
                                    </th>
                                    <th width="15%">
                                        <i class="fas fa-activity me-1"></i>
                                        النشاط
                                    </th>
                                    <th width="25%">
                                        <i class="fas fa-info-circle me-1"></i>
                                        التفاصيل
                                    </th>
                                    <th width="10%">
                                        <i class="fas fa-globe me-1"></i>
                                        IP
                                    </th>
                                    <th width="5%">
                                        <i class="fas fa-link me-1"></i>
                                        الصفحة
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($activities as $index => $activity): ?>
                                    <tr>
                                        <td class="text-muted">
                                            <?php echo $offset + $index + 1; ?>
                                        </td>
                                        <td>
                                            <div class="fw-bold">
                                                <?php echo date('Y-m-d', strtotime($activity['created_at'])); ?>
                                            </div>
                                            <small class="text-muted">
                                                <?php echo date('H:i:s', strtotime($activity['created_at'])); ?>
                                            </small>
                                            <br>
                                            <small class="text-info">
                                                <?php echo timeAgo($activity['created_at']); ?>
                                            </small>
                                        </td>
                                        <td>
                                            <?php if ($activity['user_name']): ?>
                                                <div class="fw-bold">
                                                    <?php echo htmlspecialchars($activity['user_name']); ?>
                                                </div>
                                                <small class="text-muted">
                                                    <?php echo htmlspecialchars($activity['user_email']); ?>
                                                </small>
                                            <?php else: ?>
                                                <span class="text-muted">
                                                    <i class="fas fa-user-secret me-1"></i>
                                                    زائر
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="badge <?php echo getUserTypeBadgeClass($activity['user_type']); ?>">
                                                <?php
                                                $userTypeLabels = [
                                                    'admin' => 'مدير',
                                                    'instructor' => 'مدرب',
                                                    'student' => 'طالب',
                                                    'visitor' => 'زائر'
                                                ];
                                                echo $userTypeLabels[$activity['user_type']] ?? $activity['user_type'];
                                                ?>
                                            </span>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <i class="<?php echo getActivityIcon($activity['action']); ?> activity-icon text-primary"></i>
                                                <span class="activity-badge badge bg-light text-dark">
                                                    <?php echo htmlspecialchars($activity['action']); ?>
                                                </span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="text-break">
                                                <?php echo htmlspecialchars($activity['description']); ?>
                                            </div>
                                            <?php if (!empty($activity['page_url'])): ?>
                                                <small class="text-muted d-block mt-1">
                                                    <i class="fas fa-link me-1"></i>
                                                    <?php echo htmlspecialchars($activity['page_url']); ?>
                                                </small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <code class="ip-address">
                                                <?php echo htmlspecialchars($activity['ip_address']); ?>
                                            </code>
                                        </td>
                                        <td>
                                            <?php if (!empty($activity['page_url'])): ?>
                                                <a href="<?php echo htmlspecialchars($activity['page_url']); ?>"
                                                   target="_blank"
                                                   class="btn btn-sm btn-outline-primary"
                                                   title="فتح الصفحة">
                                                    <i class="fas fa-external-link-alt"></i>
                                                </a>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- التصفح -->
                    <?php if ($total_pages > 1): ?>
                        <div class="card-footer">
                            <nav aria-label="تصفح الأنشطة">
                                <ul class="pagination justify-content-center mb-0">
                                    <?php if ($page > 1): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">
                                                <i class="fas fa-chevron-right"></i>
                                            </a>
                                        </li>
                                    <?php endif; ?>

                                    <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                                        <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                            <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>">
                                                <?php echo $i; ?>
                                            </a>
                                        </li>
                                    <?php endfor; ?>

                                    <?php if ($page < $total_pages): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">
                                                <i class="fas fa-chevron-left"></i>
                                            </a>
                                        </li>
                                    <?php endif; ?>
                                </ul>
                            </nav>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize any JavaScript enhancements here
    const table = document.querySelector('table');
    if (table) {
        new DataTable(table, {
            order: [[0, 'desc']],
            pageLength: 25,
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/ar.json'
            }
        });
    }
});
</script>

<?php require_once '../includes/footer.php'; ?>