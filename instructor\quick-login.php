<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// تسجيل دخول سريع للمدرب للاختبار
try {
    // البحث عن المدرب
    $stmt = $conn->prepare("SELECT id, name, email, role FROM users WHERE role = 'instructor' LIMIT 1");
    $stmt->execute();
    $instructor = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($instructor) {
        // تسجيل الدخول
        $_SESSION['user_id'] = $instructor['id'];
        $_SESSION['user_name'] = $instructor['name'];
        $_SESSION['user_email'] = $instructor['email'];
        $_SESSION['role'] = $instructor['role'];
        
        echo "تم تسجيل الدخول بنجاح للمدرب: " . $instructor['name'];
        echo "<br><a href='assignments.php'>الذهاب لصفحة الواجبات</a>";
        echo "<br><a href='dashboard.php'>الذهاب للوحة التحكم</a>";
    } else {
        echo "لم يتم العثور على مدرب في النظام";
    }
} catch (PDOException $e) {
    echo "خطأ: " . $e->getMessage();
}
?>
