<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات الطالب
if (!isLoggedIn() || !isStudent()) {
    header('Location: ../login.php');
    exit;
}

$pageTitle = 'الرسائل';
$breadcrumbs = [
    ['title' => 'الرسائل']
];

$student_id = $_SESSION['user_id'];
$success = '';
$error = '';

// معالجة إرسال رسالة جديدة
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['send_message'])) {
    $receiver_id = (int)$_POST['receiver_id'];
    $subject = trim($_POST['subject']);
    $content = trim($_POST['content']);
    $parent_id = !empty($_POST['parent_id']) ? (int)$_POST['parent_id'] : null;

    try {
        if (empty($subject) || empty($content)) {
            throw new Exception('يرجى ملء جميع الحقول المطلوبة');
        }

        if ($receiver_id === $student_id) {
            throw new Exception('لا يمكنك إرسال رسالة لنفسك');
        }

        // التحقق من وجود المستقبل
        $stmt = $conn->prepare("SELECT id, name FROM users WHERE id = ? AND status = 'active'");
        $stmt->execute([$receiver_id]);
        $receiver = $stmt->fetch();

        if (!$receiver) {
            throw new Exception('المستقبل غير موجود');
        }

        // معالجة رفع المرفق
        $attachment_path = null;
        if (isset($_FILES['attachment']) && $_FILES['attachment']['error'] === UPLOAD_ERR_OK) {
            $upload_dir = '../uploads/messages/';
            if (!is_dir($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }

            $file_extension = pathinfo($_FILES['attachment']['name'], PATHINFO_EXTENSION);
            $allowed_extensions = ['pdf', 'doc', 'docx', 'txt', 'jpg', 'jpeg', 'png', 'zip', 'rar'];

            if (!in_array(strtolower($file_extension), $allowed_extensions)) {
                throw new Exception('نوع الملف غير مسموح');
            }

            $file_name = 'msg_' . time() . '_' . uniqid() . '.' . $file_extension;
            $attachment_path = $upload_dir . $file_name;

            if (!move_uploaded_file($_FILES['attachment']['tmp_name'], $attachment_path)) {
                throw new Exception('فشل في رفع المرفق');
            }
        }

        // إرسال الرسالة
        $stmt = $conn->prepare("
            INSERT INTO messages (sender_id, receiver_id, subject, content, parent_id, attachment_path)
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute([$student_id, $receiver_id, $subject, $content, $parent_id, $attachment_path]);

        $success = 'تم إرسال الرسالة بنجاح';

    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// معالجة تحديد الرسالة كمقروءة
if (isset($_GET['mark_read']) && isset($_GET['id'])) {
    $message_id = (int)$_GET['id'];

    try {
        $stmt = $conn->prepare("
            UPDATE messages SET is_read = 1, read_at = NOW()
            WHERE id = ? AND receiver_id = ? AND is_read = 0
        ");
        $stmt->execute([$message_id, $student_id]);

    } catch (PDOException $e) {
        // تجاهل الأخطاء
    }
}

// معالجة حذف الرسالة
if (isset($_GET['delete']) && isset($_GET['id'])) {
    $message_id = (int)$_GET['id'];

    try {
        $stmt = $conn->prepare("
            DELETE FROM messages
            WHERE id = ? AND (sender_id = ? OR receiver_id = ?)
        ");
        $stmt->execute([$message_id, $student_id, $student_id]);

        $success = 'تم حذف الرسالة بنجاح';

    } catch (PDOException $e) {
        $error = 'حدث خطأ أثناء حذف الرسالة';
    }
}

// معاملات العرض
$view = $_GET['view'] ?? 'inbox'; // inbox, sent, compose
$search = $_GET['search'] ?? '';
$filter = $_GET['filter'] ?? 'all'; // all, unread, read

try {
    // جلب الرسائل حسب النوع
    if ($view === 'sent') {
        // الرسائل المرسلة
        $where_conditions = ["m.sender_id = ?"];
        $params = [$student_id];

        if ($search) {
            $where_conditions[] = "(m.subject LIKE ? OR m.content LIKE ? OR u.name LIKE ?)";
            $search_term = "%$search%";
            $params = array_merge($params, [$search_term, $search_term, $search_term]);
        }

        $where_clause = implode(' AND ', $where_conditions);

        $stmt = $conn->prepare("
            SELECT m.*, u.name as receiver_name, u.email as receiver_email
            FROM messages m
            JOIN users u ON m.receiver_id = u.id
            WHERE $where_clause
            ORDER BY m.created_at DESC
        ");
        $stmt->execute($params);
        $messages = $stmt->fetchAll();

    } else {
        // الرسائل الواردة
        $where_conditions = ["m.receiver_id = ?"];
        $params = [$student_id];

        if ($search) {
            $where_conditions[] = "(m.subject LIKE ? OR m.content LIKE ? OR u.name LIKE ?)";
            $search_term = "%$search%";
            $params = array_merge($params, [$search_term, $search_term, $search_term]);
        }

        if ($filter === 'unread') {
            $where_conditions[] = "m.is_read = 0";
        } elseif ($filter === 'read') {
            $where_conditions[] = "m.is_read = 1";
        }

        $where_clause = implode(' AND ', $where_conditions);

        $stmt = $conn->prepare("
            SELECT m.*, u.name as sender_name, u.email as sender_email
            FROM messages m
            JOIN users u ON m.sender_id = u.id
            WHERE $where_clause
            ORDER BY m.created_at DESC
        ");
        $stmt->execute($params);
        $messages = $stmt->fetchAll();
    }

    // جلب قائمة المستخدمين للإرسال إليهم (المدربين والإداريين)
    $stmt = $conn->prepare("
        SELECT DISTINCT u.id, u.name, u.role
        FROM users u
        LEFT JOIN courses c ON u.id = c.instructor_id
        LEFT JOIN course_enrollments ce ON c.id = ce.course_id AND ce.student_id = ?
        WHERE (u.role = 'admin' OR (u.role = 'instructor' AND ce.id IS NOT NULL))
        AND u.id != ?
        AND u.status = 'active'
        ORDER BY u.role, u.name
    ");
    $stmt->execute([$student_id, $student_id]);
    $contacts = $stmt->fetchAll();

    // إحصائيات الرسائل
    $stmt = $conn->prepare("
        SELECT
            COUNT(*) as total_received,
            SUM(CASE WHEN is_read = 0 THEN 1 ELSE 0 END) as unread_count
        FROM messages
        WHERE receiver_id = ?
    ");
    $stmt->execute([$student_id]);
    $inbox_stats = $stmt->fetch();

    $stmt = $conn->prepare("SELECT COUNT(*) as total_sent FROM messages WHERE sender_id = ?");
    $stmt->execute([$student_id]);
    $sent_stats = $stmt->fetch();

    $stats = [
        'total_received' => $inbox_stats['total_received'],
        'unread_count' => $inbox_stats['unread_count'],
        'total_sent' => $sent_stats['total_sent'],
        'read_count' => $inbox_stats['total_received'] - $inbox_stats['unread_count']
    ];

} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء جلب الرسائل';
}

include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- العنوان -->
        <div class="col-12 mb-4">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="text-primary">
                        <i class="fas fa-envelope me-2"></i>
                        الرسائل
                    </h2>
                    <p class="text-muted">تواصل مع المدربين والإداريين</p>
                </div>
                <button class="btn btn-student-primary" data-bs-toggle="modal" data-bs-target="#composeModal">
                    <i class="fas fa-plus me-1"></i>
                    رسالة جديدة
                </button>
            </div>
        </div>

        <?php if ($success): ?>
        <div class="col-12 mb-3">
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo htmlspecialchars($success); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </div>
        <?php endif; ?>

        <?php if ($error): ?>
        <div class="col-12 mb-3">
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-circle me-2"></i>
                <?php echo htmlspecialchars($error); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </div>
        <?php endif; ?>

        <!-- الإحصائيات -->
        <div class="col-12 mb-4">
            <div class="row">
                <div class="col-md-3 mb-3">
                    <div class="card-student text-center">
                        <div class="card-body">
                            <i class="fas fa-inbox fa-2x text-primary mb-2"></i>
                            <h4 class="text-primary"><?php echo $stats['total_received']; ?></h4>
                            <p class="text-muted mb-0">رسائل واردة</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card-student text-center">
                        <div class="card-body">
                            <i class="fas fa-envelope fa-2x text-danger mb-2"></i>
                            <h4 class="text-danger"><?php echo $stats['unread_count']; ?></h4>
                            <p class="text-muted mb-0">غير مقروءة</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card-student text-center">
                        <div class="card-body">
                            <i class="fas fa-envelope-open fa-2x text-success mb-2"></i>
                            <h4 class="text-success"><?php echo $stats['read_count']; ?></h4>
                            <p class="text-muted mb-0">مقروءة</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card-student text-center">
                        <div class="card-body">
                            <i class="fas fa-paper-plane fa-2x text-info mb-2"></i>
                            <h4 class="text-info"><?php echo $stats['total_sent']; ?></h4>
                            <p class="text-muted mb-0">رسائل مرسلة</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- التبويبات والفلاتر -->
        <div class="col-12 mb-4">
            <div class="card-student">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center flex-wrap">
                        <ul class="nav nav-tabs card-header-tabs">
                            <li class="nav-item">
                                <a class="nav-link <?php echo $view === 'inbox' ? 'active' : ''; ?>"
                                   href="?view=inbox">
                                    <i class="fas fa-inbox me-1"></i>
                                    الواردة (<?php echo $stats['total_received']; ?>)
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link <?php echo $view === 'sent' ? 'active' : ''; ?>"
                                   href="?view=sent">
                                    <i class="fas fa-paper-plane me-1"></i>
                                    المرسلة (<?php echo $stats['total_sent']; ?>)
                                </a>
                            </li>
                        </ul>

                        <?php if ($view === 'inbox'): ?>
                        <div class="d-flex gap-2">
                            <div class="btn-group">
                                <a href="?view=inbox&filter=all&search=<?php echo urlencode($search); ?>"
                                   class="btn btn-sm <?php echo $filter === 'all' ? 'btn-primary' : 'btn-outline-primary'; ?>">
                                    الكل
                                </a>
                                <a href="?view=inbox&filter=unread&search=<?php echo urlencode($search); ?>"
                                   class="btn btn-sm <?php echo $filter === 'unread' ? 'btn-primary' : 'btn-outline-primary'; ?>">
                                    غير مقروءة
                                </a>
                                <a href="?view=inbox&filter=read&search=<?php echo urlencode($search); ?>"
                                   class="btn btn-sm <?php echo $filter === 'read' ? 'btn-primary' : 'btn-outline-primary'; ?>">
                                    مقروءة
                                </a>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="card-body">
                    <!-- شريط البحث -->
                    <form method="GET" class="mb-3">
                        <input type="hidden" name="view" value="<?php echo $view; ?>">
                        <input type="hidden" name="filter" value="<?php echo $filter; ?>">
                        <div class="input-group">
                            <input type="text" name="search" class="form-control"
                                   placeholder="ابحث في الرسائل..." value="<?php echo htmlspecialchars($search); ?>">
                            <button type="submit" class="btn btn-outline-primary">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </form>

                    <!-- قائمة الرسائل -->
                    <?php if (empty($messages)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-envelope fa-3x text-muted mb-3"></i>
                        <h4 class="text-muted">لا توجد رسائل</h4>
                        <p class="text-muted">
                            <?php if ($view === 'sent'): ?>
                                لم ترسل أي رسائل بعد
                            <?php else: ?>
                                لا توجد رسائل واردة
                            <?php endif; ?>
                        </p>
                        <button class="btn btn-student-primary mt-3" data-bs-toggle="modal" data-bs-target="#composeModal">
                            <i class="fas fa-plus me-1"></i>
                            إرسال رسالة جديدة
                        </button>
                    </div>
                    <?php else: ?>
                    <div class="messages-list">
                        <?php foreach ($messages as $message): ?>
                        <div class="message-item <?php echo !$message['is_read'] && $view === 'inbox' ? 'unread' : ''; ?>"
                             data-message-id="<?php echo $message['id']; ?>">
                            <div class="message-header">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="message-info">
                                        <h6 class="mb-1">
                                            <?php if ($view === 'sent'): ?>
                                                إلى: <strong><?php echo htmlspecialchars($message['receiver_name']); ?></strong>
                                            <?php else: ?>
                                                من: <strong><?php echo htmlspecialchars($message['sender_name']); ?></strong>
                                            <?php endif; ?>
                                            <?php if (!$message['is_read'] && $view === 'inbox'): ?>
                                            <span class="badge bg-danger ms-2">جديد</span>
                                            <?php endif; ?>
                                        </h6>
                                        <div class="message-subject">
                                            <strong><?php echo htmlspecialchars($message['subject']); ?></strong>
                                        </div>
                                        <small class="text-muted">
                                            <?php echo date('Y-m-d H:i', strtotime($message['created_at'])); ?>
                                            <?php if ($message['attachment_path']): ?>
                                            <i class="fas fa-paperclip ms-2" title="يحتوي على مرفق"></i>
                                            <?php endif; ?>
                                        </small>
                                    </div>
                                    <div class="message-actions">
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-outline-primary"
                                                    data-bs-toggle="modal"
                                                    data-bs-target="#messageModal<?php echo $message['id']; ?>">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <?php if ($view === 'inbox'): ?>
                                            <button class="btn btn-sm btn-outline-success"
                                                    data-bs-toggle="modal"
                                                    data-bs-target="#replyModal<?php echo $message['id']; ?>">
                                                <i class="fas fa-reply"></i>
                                            </button>
                                            <?php endif; ?>
                                            <a href="?view=<?php echo $view; ?>&delete=1&id=<?php echo $message['id']; ?>"
                                               class="btn btn-sm btn-outline-danger"
                                               onclick="return confirm('هل أنت متأكد من حذف هذه الرسالة؟')">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="message-preview">
                                <p class="mb-0 text-muted">
                                    <?php echo htmlspecialchars(substr($message['content'], 0, 150)) . '...'; ?>
                                </p>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal إنشاء رسالة جديدة -->
<div class="modal fade" id="composeModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">رسالة جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" enctype="multipart/form-data">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">إلى</label>
                        <select name="receiver_id" class="form-select" required>
                            <option value="">اختر المستقبل</option>
                            <?php foreach ($contacts as $contact): ?>
                            <option value="<?php echo $contact['id']; ?>">
                                <?php echo htmlspecialchars($contact['name']); ?>
                                (<?php echo $contact['role'] === 'admin' ? 'إداري' : 'مدرب'; ?>)
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">الموضوع</label>
                        <input type="text" name="subject" class="form-control" required
                               placeholder="اكتب موضوع الرسالة...">
                    </div>

                    <div class="mb-3">
                        <label class="form-label">المحتوى</label>
                        <textarea name="content" class="form-control" rows="8" required
                                  placeholder="اكتب محتوى الرسالة..."></textarea>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">مرفق (اختياري)</label>
                        <input type="file" name="attachment" class="form-control"
                               accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png,.zip,.rar">
                        <div class="form-text">الملفات المسموحة: PDF, DOC, DOCX, TXT, JPG, PNG, ZIP, RAR</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" name="send_message" class="btn btn-student-primary">
                        <i class="fas fa-paper-plane me-1"></i>
                        إرسال
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modals عرض الرسائل -->
<?php foreach ($messages as $message): ?>
<div class="modal fade" id="messageModal<?php echo $message['id']; ?>" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><?php echo htmlspecialchars($message['subject']); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="message-details mb-3">
                    <div class="row">
                        <div class="col-md-6">
                            <strong>
                                <?php if ($view === 'sent'): ?>
                                    إلى: <?php echo htmlspecialchars($message['receiver_name']); ?>
                                <?php else: ?>
                                    من: <?php echo htmlspecialchars($message['sender_name']); ?>
                                <?php endif; ?>
                            </strong>
                        </div>
                        <div class="col-md-6 text-end">
                            <small class="text-muted">
                                <?php echo date('Y-m-d H:i', strtotime($message['created_at'])); ?>
                            </small>
                        </div>
                    </div>
                </div>

                <div class="message-content">
                    <p><?php echo nl2br(htmlspecialchars($message['content'])); ?></p>
                </div>

                <?php if ($message['attachment_path']): ?>
                <div class="message-attachment mt-3">
                    <div class="alert alert-info">
                        <i class="fas fa-paperclip me-2"></i>
                        <strong>مرفق:</strong>
                        <a href="<?php echo htmlspecialchars($message['attachment_path']); ?>"
                           target="_blank" class="btn btn-sm btn-outline-primary ms-2">
                            <i class="fas fa-download me-1"></i>
                            تحميل المرفق
                        </a>
                    </div>
                </div>
                <?php endif; ?>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <?php if ($view === 'inbox'): ?>
                <button type="button" class="btn btn-student-primary"
                        data-bs-dismiss="modal"
                        data-bs-toggle="modal"
                        data-bs-target="#replyModal<?php echo $message['id']; ?>">
                    <i class="fas fa-reply me-1"></i>
                    رد
                </button>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Modal الرد على الرسالة -->
<?php if ($view === 'inbox'): ?>
<div class="modal fade" id="replyModal<?php echo $message['id']; ?>" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">رد على: <?php echo htmlspecialchars($message['subject']); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" enctype="multipart/form-data">
                <div class="modal-body">
                    <input type="hidden" name="receiver_id" value="<?php echo $message['sender_id']; ?>">
                    <input type="hidden" name="parent_id" value="<?php echo $message['id']; ?>">

                    <div class="mb-3">
                        <label class="form-label">إلى</label>
                        <input type="text" class="form-control" readonly
                               value="<?php echo htmlspecialchars($message['sender_name']); ?>">
                    </div>

                    <div class="mb-3">
                        <label class="form-label">الموضوع</label>
                        <input type="text" name="subject" class="form-control" required
                               value="رد: <?php echo htmlspecialchars($message['subject']); ?>">
                    </div>

                    <div class="mb-3">
                        <label class="form-label">المحتوى</label>
                        <textarea name="content" class="form-control" rows="8" required
                                  placeholder="اكتب ردك..."></textarea>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">مرفق (اختياري)</label>
                        <input type="file" name="attachment" class="form-control"
                               accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png,.zip,.rar">
                    </div>

                    <div class="original-message mt-4">
                        <h6 class="text-muted">الرسالة الأصلية:</h6>
                        <div class="border-start border-3 border-secondary ps-3">
                            <small class="text-muted">
                                من: <?php echo htmlspecialchars($message['sender_name']); ?><br>
                                التاريخ: <?php echo date('Y-m-d H:i', strtotime($message['created_at'])); ?>
                            </small>
                            <p class="mt-2"><?php echo nl2br(htmlspecialchars($message['content'])); ?></p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" name="send_message" class="btn btn-student-primary">
                        <i class="fas fa-reply me-1"></i>
                        إرسال الرد
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php endif; ?>
<?php endforeach; ?>

<style>
.messages-list {
    max-height: 600px;
    overflow-y: auto;
}

.message-item {
    border: 1px solid #e2e8f0;
    border-radius: 0.75rem;
    padding: 1rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
    cursor: pointer;
}

.message-item:hover {
    border-color: var(--student-primary);
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.1);
}

.message-item.unread {
    background-color: #f0f9ff;
    border-color: var(--student-primary);
}

.message-item.unread .message-subject {
    font-weight: 600;
}

.message-header {
    margin-bottom: 0.5rem;
}

.message-subject {
    font-size: 1.1rem;
    margin: 0.5rem 0;
}

.message-preview {
    margin-top: 0.5rem;
}

.message-actions .btn {
    margin-left: 0.25rem;
}

.original-message {
    background-color: #f8fafc;
    border-radius: 0.5rem;
    padding: 1rem;
}

@media (max-width: 768px) {
    .message-actions {
        margin-top: 1rem;
    }

    .message-actions .btn-group {
        width: 100%;
    }

    .message-actions .btn {
        flex: 1;
    }
}
</style>

<script>
// تحديد الرسالة كمقروءة عند النقر عليها
document.querySelectorAll('.message-item').forEach(item => {
    item.addEventListener('click', function(e) {
        // تجاهل النقر إذا كان على زر
        if (e.target.closest('.btn')) return;

        const messageId = this.dataset.messageId;
        const isUnread = this.classList.contains('unread');

        if (isUnread) {
            // إرسال طلب لتحديد الرسالة كمقروءة
            fetch(`?mark_read=1&id=${messageId}`)
                .then(() => {
                    this.classList.remove('unread');
                    // تحديث عداد الرسائل غير المقروءة
                    location.reload();
                });
        }
    });
});

// تحديد الرسالة كمقروءة عند فتح النافذة المنبثقة
document.querySelectorAll('[data-bs-target^="#messageModal"]').forEach(button => {
    button.addEventListener('click', function() {
        const modalTarget = this.getAttribute('data-bs-target');
        const messageId = modalTarget.replace('#messageModal', '');
        const messageItem = document.querySelector(`[data-message-id="${messageId}"]`);

        if (messageItem && messageItem.classList.contains('unread')) {
            fetch(`?mark_read=1&id=${messageId}`)
                .then(() => {
                    messageItem.classList.remove('unread');
                });
        }
    });
});
</script>

<?php include 'includes/footer.php'; ?>