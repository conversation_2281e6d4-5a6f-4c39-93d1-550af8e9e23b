    </div> <!-- إنهاء container-fluid من header -->

    <!-- شريط التنقل السفلي للهاتف المحمول -->
    <div class="mobile-bottom-nav d-lg-none">
        <div class="container-fluid">
            <div class="row g-0">
                <div class="col">
                    <a href="dashboard.php" class="mobile-nav-item <?php echo basename($_SERVER['PHP_SELF']) == 'dashboard.php' ? 'active' : ''; ?>">
                        <i class="fas fa-home"></i>
                        <span>الرئيسية</span>
                    </a>
                </div>
                <div class="col">
                    <a href="courses.php" class="mobile-nav-item <?php echo basename($_SERVER['PHP_SELF']) == 'courses.php' ? 'active' : ''; ?>">
                        <i class="fas fa-graduation-cap"></i>
                        <span>الكورسات</span>
                    </a>
                </div>
                <div class="col">
                    <a href="manage_students.php" class="mobile-nav-item <?php echo basename($_SERVER['PHP_SELF']) == 'manage_students.php' ? 'active' : ''; ?>">
                        <i class="fas fa-users"></i>
                        <span>الطلاب</span>
                    </a>
                </div>
                <div class="col">
                    <a href="earnings.php" class="mobile-nav-item <?php echo basename($_SERVER['PHP_SELF']) == 'earnings.php' ? 'active' : ''; ?>">
                        <i class="fas fa-dollar-sign"></i>
                        <span>الأرباح</span>
                    </a>
                </div>
                <div class="col">
                    <a href="profile.php" class="mobile-nav-item <?php echo basename($_SERVER['PHP_SELF']) == 'profile.php' ? 'active' : ''; ?>">
                        <i class="fas fa-user"></i>
                        <span>الملف</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- زر الإجراءات السريعة العائم -->
    <div class="floating-action-btn">
        <button class="fab-main" type="button" data-bs-toggle="dropdown" aria-expanded="false">
            <i class="fas fa-plus"></i>
        </button>
        <ul class="dropdown-menu dropdown-menu-end fab-menu">
            <li>
                <a class="dropdown-item fab-item" href="add-course.php">
                    <i class="fas fa-graduation-cap text-primary"></i>
                    <div>
                        <strong>إضافة كورس</strong>
                        <small>إنشاء كورس جديد</small>
                    </div>
                </a>
            </li>
            <li>
                <a class="dropdown-item fab-item" href="videos.php">
                    <i class="fas fa-video text-success"></i>
                    <div>
                        <strong>رفع فيديو</strong>
                        <small>إضافة محتوى مرئي</small>
                    </div>
                </a>
            </li>
            <li>
                <a class="dropdown-item fab-item" href="materials.php">
                    <i class="fas fa-file-upload text-warning"></i>
                    <div>
                        <strong>إضافة مادة</strong>
                        <small>رفع ملفات تعليمية</small>
                    </div>
                </a>
            </li>
            <li>
                <a class="dropdown-item fab-item" href="assignments.php">
                    <i class="fas fa-tasks text-info"></i>
                    <div>
                        <strong>إنشاء واجب</strong>
                        <small>تكليف الطلاب</small>
                    </div>
                </a>
            </li>
            <li><hr class="dropdown-divider"></li>
            <li>
                <a class="dropdown-item fab-item" href="live-session.php">
                    <i class="fas fa-broadcast-tower text-danger"></i>
                    <div>
                        <strong>جلسة مباشرة</strong>
                        <small>بدء بث مباشر</small>
                    </div>
                </a>
            </li>
        </ul>
    </div>

    <!-- Footer المحسن -->
    <footer class="instructor-footer mt-5">
        <div class="container-fluid">
            <!-- معلومات سريعة -->
            <div class="footer-quick-info mb-4">
                <div class="row g-3">
                    <div class="col-lg-3 col-md-6">
                        <div class="quick-stat">
                            <div class="stat-icon">
                                <i class="fas fa-graduation-cap"></i>
                            </div>
                            <div class="stat-content">
                                <h6 class="mb-1">كورساتي</h6>
                                <p class="mb-0 text-muted">إدارة شاملة للكورسات</p>
                            </div>
                            <a href="courses.php" class="stat-link">
                                <i class="fas fa-arrow-left"></i>
                            </a>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="quick-stat">
                            <div class="stat-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="stat-content">
                                <h6 class="mb-1">طلابي</h6>
                                <p class="mb-0 text-muted">متابعة تقدم الطلاب</p>
                            </div>
                            <a href="manage_students.php" class="stat-link">
                                <i class="fas fa-arrow-left"></i>
                            </a>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="quick-stat">
                            <div class="stat-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="stat-content">
                                <h6 class="mb-1">التحليلات</h6>
                                <p class="mb-0 text-muted">إحصائيات مفصلة</p>
                            </div>
                            <a href="analytics.php" class="stat-link">
                                <i class="fas fa-arrow-left"></i>
                            </a>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="quick-stat">
                            <div class="stat-icon">
                                <i class="fas fa-dollar-sign"></i>
                            </div>
                            <div class="stat-content">
                                <h6 class="mb-1">الأرباح</h6>
                                <p class="mb-0 text-muted">تتبع الدخل والمدفوعات</p>
                            </div>
                            <a href="earnings.php" class="stat-link">
                                <i class="fas fa-arrow-left"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- روابط سريعة -->
            <div class="footer-links mb-4">
                <div class="row">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <h6 class="footer-title">إدارة المحتوى</h6>
                        <ul class="footer-menu">
                            <li><a href="videos.php"><i class="fas fa-video me-2"></i>الفيديوهات</a></li>
                            <li><a href="materials.php"><i class="fas fa-folder me-2"></i>المواد التعليمية</a></li>
                            <li><a href="assignments.php"><i class="fas fa-tasks me-2"></i>الواجبات</a></li>
                            <li><a href="quizzes.php"><i class="fas fa-question-circle me-2"></i>الاختبارات</a></li>
                        </ul>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <h6 class="footer-title">التفاعل</h6>
                        <ul class="footer-menu">
                            <li><a href="messages.php"><i class="fas fa-envelope me-2"></i>الرسائل</a></li>
                            <li><a href="notifications.php"><i class="fas fa-bell me-2"></i>الإشعارات</a></li>
                            <li><a href="reviews.php"><i class="fas fa-star me-2"></i>التقييمات</a></li>
                            <li><a href="forums.php"><i class="fas fa-comments me-2"></i>المنتديات</a></li>
                        </ul>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <h6 class="footer-title">التقارير</h6>
                        <ul class="footer-menu">
                            <li><a href="analytics.php"><i class="fas fa-chart-bar me-2"></i>التحليلات</a></li>
                            <li><a href="reports.php"><i class="fas fa-file-alt me-2"></i>التقارير</a></li>
                            <li><a href="certificates.php"><i class="fas fa-certificate me-2"></i>الشهادات</a></li>
                            <li><a href="exports.php"><i class="fas fa-download me-2"></i>التصدير</a></li>
                        </ul>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <h6 class="footer-title">الحساب</h6>
                        <ul class="footer-menu">
                            <li><a href="profile.php"><i class="fas fa-user me-2"></i>الملف الشخصي</a></li>
                            <li><a href="settings.php"><i class="fas fa-cog me-2"></i>الإعدادات</a></li>
                            <li><a href="help.php"><i class="fas fa-question me-2"></i>المساعدة</a></li>
                            <li><a href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- معلومات النظام -->
            <div class="footer-system-info">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="d-flex align-items-center">
                            <div class="system-status online me-3">
                                <span class="status-dot"></span>
                                <small>النظام متصل</small>
                            </div>
                            <div class="last-update me-3">
                                <i class="fas fa-clock me-1"></i>
                                <small class="real-time"><?php echo date('Y-m-d H:i:s'); ?></small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <div class="footer-actions">
                            <button class="btn btn-sm btn-outline-secondary me-2" onclick="refreshPage()">
                                <i class="fas fa-sync-alt me-1"></i>تحديث
                            </button>
                            <button class="btn btn-sm btn-outline-secondary me-2" onclick="printPage()">
                                <i class="fas fa-print me-1"></i>طباعة
                            </button>
                            <button class="btn btn-sm btn-outline-secondary" onclick="toggleTheme()">
                                <i class="fas fa-moon me-1"></i>الوضع المظلم
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- حقوق النشر -->
            <hr class="my-4">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0 text-muted">
                        &copy; <?php echo date('Y'); ?> منصة التعليم الإلكتروني - لوحة تحكم المدرب
                    </p>
                </div>
                <div class="col-md-6 text-md-end">
                    <div class="footer-social">
                        <a href="#" class="social-link" title="المساعدة">
                            <i class="fas fa-question-circle"></i>
                        </a>
                        <a href="#" class="social-link" title="الدعم الفني">
                            <i class="fas fa-headset"></i>
                        </a>
                        <a href="#" class="social-link" title="التوثيق">
                            <i class="fas fa-book"></i>
                        </a>
                        <a href="#" class="social-link" title="التحديثات">
                            <i class="fas fa-bell"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>

    <!-- Custom JavaScript -->
    <script src="assets/js/enhanced-ui.js"></script>

    <!-- Custom JavaScript -->
    <script>
        // تهيئة AOS
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true
        });

        // تهيئة DataTables
        $(document).ready(function() {
            if ($.fn.DataTable) {
                $('.data-table').DataTable({
                    language: {
                        url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/ar.json'
                    },
                    responsive: true,
                    pageLength: 25,
                    order: [[0, 'desc']]
                });
            }
        });

        // إشعارات Toast
        function showToast(message, type = 'success') {
            const toastContainer = document.getElementById('toast-container') || createToastContainer();
            
            const toast = document.createElement('div');
            toast.className = `toast align-items-center text-white bg-${type} border-0`;
            toast.setAttribute('role', 'alert');
            toast.innerHTML = `
                <div class="d-flex">
                    <div class="toast-body">
                        <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            `;
            
            toastContainer.appendChild(toast);
            
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();
            
            toast.addEventListener('hidden.bs.toast', () => {
                toast.remove();
            });
        }

        function createToastContainer() {
            const container = document.createElement('div');
            container.id = 'toast-container';
            container.className = 'toast-container position-fixed top-0 end-0 p-3';
            container.style.zIndex = '9999';
            document.body.appendChild(container);
            return container;
        }

        // تأكيد الحذف
        function confirmDelete(message = 'هل أنت متأكد من الحذف؟') {
            return confirm(message);
        }

        // تحديث العدادات
        function updateCounter(element, target) {
            const start = 0;
            const increment = target / 100;
            let current = start;
            
            const timer = setInterval(() => {
                current += increment;
                element.textContent = Math.floor(current);
                
                if (current >= target) {
                    element.textContent = target;
                    clearInterval(timer);
                }
            }, 20);
        }

        // تهيئة العدادات
        document.addEventListener('DOMContentLoaded', function() {
            const counters = document.querySelectorAll('.counter');
            
            const observerOptions = {
                threshold: 0.5,
                rootMargin: '0px 0px -100px 0px'
            };
            
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const target = parseInt(entry.target.getAttribute('data-target'));
                        updateCounter(entry.target, target);
                        observer.unobserve(entry.target);
                    }
                });
            }, observerOptions);
            
            counters.forEach(counter => {
                observer.observe(counter);
            });
        });

        // تحديث الوقت الحقيقي
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('ar-SA', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            
            const timeElements = document.querySelectorAll('.real-time');
            timeElements.forEach(element => {
                element.textContent = timeString;
            });
        }

        // تحديث الوقت كل ثانية
        setInterval(updateTime, 1000);

        // تحسين تجربة المستخدم
        document.addEventListener('DOMContentLoaded', function() {
            // إضافة تأثيرات hover للبطاقات
            const cards = document.querySelectorAll('.card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                    this.style.transition = 'transform 0.3s ease';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });

            // تحسين الأزرار
            const buttons = document.querySelectorAll('.btn');
            buttons.forEach(button => {
                button.addEventListener('click', function() {
                    this.style.transform = 'scale(0.98)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 150);
                });
            });

            // تحديث شريط التقدم
            const progressBars = document.querySelectorAll('.progress-bar');
            progressBars.forEach(bar => {
                const width = bar.style.width;
                bar.style.width = '0%';
                setTimeout(() => {
                    bar.style.width = width;
                    bar.style.transition = 'width 1s ease-in-out';
                }, 100);
            });
        });

        // دالة لتحديث الإحصائيات
        function refreshStats() {
            // يمكن استخدامها لتحديث الإحصائيات عبر AJAX
            showToast('تم تحديث الإحصائيات', 'info');
        }

        // دالة للبحث السريع
        function quickSearch(query) {
            const searchableElements = document.querySelectorAll('[data-searchable]');
            
            searchableElements.forEach(element => {
                const text = element.textContent.toLowerCase();
                const isVisible = text.includes(query.toLowerCase());
                
                element.style.display = isVisible ? '' : 'none';
            });
        }

        // تهيئة البحث السريع
        const searchInputs = document.querySelectorAll('.quick-search');
        searchInputs.forEach(input => {
            input.addEventListener('input', function() {
                quickSearch(this.value);
            });
        });

        // حفظ تفضيلات المستخدم
        function savePreference(key, value) {
            localStorage.setItem(`instructor_${key}`, value);
        }

        function getPreference(key, defaultValue = null) {
            return localStorage.getItem(`instructor_${key}`) || defaultValue;
        }

        // تطبيق التفضيلات المحفوظة
        document.addEventListener('DOMContentLoaded', function() {
            // استعادة حالة الشريط الجانبي
            const sidebarState = getPreference('sidebar_collapsed', 'false');
            if (sidebarState === 'true') {
                document.body.classList.add('sidebar-collapsed');
            }
        });

        // إدارة الإشعارات
        function markNotificationAsRead(notificationId) {
            // يمكن إرسال طلب AJAX لتحديث حالة الإشعار
            const notification = document.querySelector(`[data-notification-id="${notificationId}"]`);
            if (notification) {
                notification.classList.add('read');
            }
        }

        // تحديث عداد الإشعارات
        function updateNotificationCount(count) {
            const badge = document.querySelector('.notification-badge');
            if (badge) {
                badge.textContent = count;
                badge.style.display = count > 0 ? 'flex' : 'none';
            }
        }

        // دالة لتصدير البيانات
        function exportData(format, url) {
            const link = document.createElement('a');
            link.href = url + '&format=' + format + '&download=1';
            link.download = '';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            showToast(`تم بدء تحميل الملف بصيغة ${format.toUpperCase()}`, 'success');
        }

        // دالة للطباعة
        function printPage() {
            window.print();
        }

        // تحسين الأداء - تحميل الصور بشكل تدريجي
        document.addEventListener('DOMContentLoaded', function() {
            const images = document.querySelectorAll('img[data-src]');
            
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        observer.unobserve(img);
                    }
                });
            });
            
            images.forEach(img => imageObserver.observe(img));
        });

        // إدارة الأخطاء العامة
        window.addEventListener('error', function(e) {
            console.error('خطأ في الصفحة:', e.error);
            // يمكن إرسال تقرير الخطأ للخادم
        });

        // تحسين الاستجابة للهاتف المحمول
        if ('ontouchstart' in window) {
            document.body.classList.add('touch-device');
        }

        // دوال الفوتر المحسن
        function refreshPage() {
            showToast('جاري تحديث الصفحة...', 'info');
            setTimeout(() => {
                location.reload();
            }, 1000);
        }

        function toggleTheme() {
            const body = document.body;
            const isDark = body.classList.contains('dark-theme');

            if (isDark) {
                body.classList.remove('dark-theme');
                savePreference('theme', 'light');
                showToast('تم التبديل للوضع العادي', 'success');
            } else {
                body.classList.add('dark-theme');
                savePreference('theme', 'dark');
                showToast('تم التبديل للوضع المظلم', 'success');
            }
        }

        // تطبيق الثيم المحفوظ
        document.addEventListener('DOMContentLoaded', function() {
            const savedTheme = getPreference('theme', 'light');
            if (savedTheme === 'dark') {
                document.body.classList.add('dark-theme');
            }
        });

        // تحديث الوقت في الفوتر
        function updateFooterTime() {
            const timeElements = document.querySelectorAll('.real-time');
            const now = new Date();
            const timeString = now.toLocaleString('ar-SA', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });

            timeElements.forEach(element => {
                element.textContent = timeString;
            });
        }

        // تحديث الوقت كل ثانية
        setInterval(updateFooterTime, 1000);

        // تحسين شريط التنقل السفلي
        document.addEventListener('DOMContentLoaded', function() {
            const mobileNavItems = document.querySelectorAll('.mobile-nav-item');

            mobileNavItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    // إزالة التحديد من جميع العناصر
                    mobileNavItems.forEach(nav => nav.classList.remove('active'));

                    // إضافة التحديد للعنصر المختار
                    this.classList.add('active');

                    // تأثير بصري
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                });
            });
        });

        // تحسين زر الإجراءات العائم
        document.addEventListener('DOMContentLoaded', function() {
            const fabMain = document.querySelector('.fab-main');
            const fabItems = document.querySelectorAll('.fab-item');

            if (fabMain) {
                fabMain.addEventListener('click', function() {
                    this.style.transform = 'scale(0.9) rotate(45deg)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1.1) rotate(45deg)';
                    }, 100);
                });
            }

            fabItems.forEach(item => {
                item.addEventListener('click', function() {
                    const icon = this.querySelector('i');
                    icon.style.transform = 'scale(1.2)';
                    setTimeout(() => {
                        icon.style.transform = '';
                    }, 200);
                });
            });
        });

        // تحسين الإحصائيات السريعة
        document.addEventListener('DOMContentLoaded', function() {
            const quickStats = document.querySelectorAll('.quick-stat');

            const observerOptions = {
                threshold: 0.3,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '0';
                        entry.target.style.transform = 'translateY(20px)';

                        setTimeout(() => {
                            entry.target.style.transition = 'all 0.6s ease';
                            entry.target.style.opacity = '1';
                            entry.target.style.transform = 'translateY(0)';
                        }, 100);

                        observer.unobserve(entry.target);
                    }
                });
            }, observerOptions);

            quickStats.forEach(stat => {
                observer.observe(stat);
            });
        });

        // دالة للتحقق من حالة الاتصال
        function checkConnectionStatus() {
            const statusDot = document.querySelector('.status-dot');
            const statusText = document.querySelector('.system-status small');

            if (navigator.onLine) {
                statusDot.style.background = '#10b981';
                statusText.textContent = 'النظام متصل';
            } else {
                statusDot.style.background = '#ef4444';
                statusText.textContent = 'غير متصل';
            }
        }

        // مراقبة حالة الاتصال
        window.addEventListener('online', checkConnectionStatus);
        window.addEventListener('offline', checkConnectionStatus);

        // تحسين الروابط الاجتماعية
        document.addEventListener('DOMContentLoaded', function() {
            const socialLinks = document.querySelectorAll('.social-link');

            socialLinks.forEach(link => {
                link.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-3px) scale(1.1)';
                });

                link.addEventListener('mouseleave', function() {
                    this.style.transform = '';
                });

                link.addEventListener('click', function(e) {
                    e.preventDefault();

                    const title = this.getAttribute('title');
                    showToast(`تم النقر على: ${title}`, 'info');

                    // يمكن إضافة منطق التنقل هنا
                });
            });
        });

        // دالة لحفظ حالة الصفحة
        function savePageState() {
            const pageState = {
                scrollPosition: window.scrollY,
                timestamp: Date.now(),
                page: window.location.pathname
            };

            sessionStorage.setItem('pageState', JSON.stringify(pageState));
        }

        // استعادة حالة الصفحة
        function restorePageState() {
            const savedState = sessionStorage.getItem('pageState');
            if (savedState) {
                const state = JSON.parse(savedState);
                if (state.page === window.location.pathname) {
                    window.scrollTo(0, state.scrollPosition);
                }
            }
        }

        // حفظ الحالة عند مغادرة الصفحة
        window.addEventListener('beforeunload', savePageState);

        // استعادة الحالة عند تحميل الصفحة
        window.addEventListener('load', restorePageState);

        // تحسين أداء التمرير
        let ticking = false;

        function updateScrollPosition() {
            const scrolled = window.scrollY;
            const mobileNav = document.querySelector('.mobile-bottom-nav');
            const fab = document.querySelector('.floating-action-btn');

            if (scrolled > 100) {
                if (mobileNav) mobileNav.style.transform = 'translateY(0)';
                if (fab) fab.style.opacity = '1';
            } else {
                if (mobileNav) mobileNav.style.transform = 'translateY(100%)';
                if (fab) fab.style.opacity = '0.7';
            }

            ticking = false;
        }

        function requestTick() {
            if (!ticking) {
                requestAnimationFrame(updateScrollPosition);
                ticking = true;
            }
        }

        window.addEventListener('scroll', requestTick);

        // دالة لإرسال تقرير الأداء
        function sendPerformanceReport() {
            if ('performance' in window) {
                const perfData = performance.getEntriesByType('navigation')[0];
                const loadTime = perfData.loadEventEnd - perfData.loadEventStart;

                // يمكن إرسال البيانات للخادم لتحليل الأداء
                console.log(`وقت تحميل الصفحة: ${loadTime}ms`);
            }
        }

        // إرسال تقرير الأداء عند اكتمال التحميل
        window.addEventListener('load', sendPerformanceReport);

        // تحسين إمكانية الوصول
        document.addEventListener('DOMContentLoaded', function() {
            // إضافة دعم لوحة المفاتيح
            const focusableElements = document.querySelectorAll('a, button, input, select, textarea, [tabindex]:not([tabindex="-1"])');

            focusableElements.forEach(element => {
                element.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' || e.key === ' ') {
                        if (this.tagName === 'A' || this.tagName === 'BUTTON') {
                            this.click();
                        }
                    }
                });
            });

            // إضافة مؤشرات التركيز
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Tab') {
                    document.body.classList.add('keyboard-navigation');
                }
            });

            document.addEventListener('mousedown', function() {
                document.body.classList.remove('keyboard-navigation');
            });
        });
    </script>

    <style>
        /* شريط التنقل السفلي للهاتف */
        .mobile-bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-top: 1px solid var(--gray-200);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            z-index: 1000;
            padding: 0.5rem 0;
        }

        .mobile-nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 0.5rem;
            color: var(--gray-600);
            text-decoration: none;
            transition: all 0.3s ease;
            border-radius: 0.5rem;
            margin: 0 0.25rem;
        }

        .mobile-nav-item i {
            font-size: 1.2rem;
            margin-bottom: 0.25rem;
        }

        .mobile-nav-item span {
            font-size: 0.7rem;
            font-weight: 500;
        }

        .mobile-nav-item.active,
        .mobile-nav-item:hover {
            color: var(--instructor-primary);
            background-color: rgba(40, 167, 69, 0.1);
            transform: translateY(-2px);
        }

        /* زر الإجراءات العائم */
        .floating-action-btn {
            position: fixed;
            bottom: 80px;
            left: 20px;
            z-index: 1001;
        }

        .fab-main {
            width: 56px;
            height: 56px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--instructor-primary), var(--instructor-secondary));
            border: none;
            color: white;
            font-size: 1.5rem;
            box-shadow: 0 4px 20px rgba(40, 167, 69, 0.3);
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .fab-main:hover {
            transform: scale(1.1) rotate(45deg);
            box-shadow: 0 6px 25px rgba(40, 167, 69, 0.4);
        }

        .fab-menu {
            bottom: 70px;
            left: 0;
            min-width: 280px;
            border: none;
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
            border-radius: 1rem;
            padding: 0.5rem;
        }

        .fab-item {
            display: flex;
            align-items: center;
            padding: 1rem;
            border-radius: 0.75rem;
            transition: all 0.3s ease;
            border: none;
        }

        .fab-item:hover {
            background-color: var(--gray-50);
            transform: translateX(-5px);
        }

        .fab-item i {
            width: 40px;
            height: 40px;
            border-radius: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 1rem;
            font-size: 1.2rem;
        }

        .fab-item div {
            flex: 1;
        }

        .fab-item strong {
            display: block;
            font-weight: 600;
            color: var(--gray-800);
        }

        .fab-item small {
            color: var(--gray-600);
            font-size: 0.8rem;
        }

        /* Footer المحسن */
        .instructor-footer {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-top: 1px solid var(--gray-200);
            padding: 3rem 0 1rem;
            margin-top: auto;
            margin-bottom: 70px;
        }

        .footer-quick-info {
            border-bottom: 1px solid var(--gray-200);
            padding-bottom: 2rem;
        }

        .quick-stat {
            background: white;
            border-radius: 1rem;
            padding: 1.5rem;
            display: flex;
            align-items: center;
            transition: all 0.3s ease;
            border: 1px solid var(--gray-100);
            position: relative;
            overflow: hidden;
        }

        .quick-stat::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: var(--instructor-primary);
            transform: scaleY(0);
            transition: transform 0.3s ease;
        }

        .quick-stat:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .quick-stat:hover::before {
            transform: scaleY(1);
        }

        .stat-icon {
            width: 50px;
            height: 50px;
            border-radius: 0.75rem;
            background: linear-gradient(135deg, var(--instructor-primary), var(--instructor-secondary));
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.3rem;
            margin-left: 1rem;
        }

        .stat-content {
            flex: 1;
        }

        .stat-content h6 {
            color: var(--gray-800);
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        .stat-link {
            width: 35px;
            height: 35px;
            border-radius: 0.5rem;
            background: var(--gray-100);
            color: var(--gray-600);
            display: flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .stat-link:hover {
            background: var(--instructor-primary);
            color: white;
            transform: translateX(-3px);
        }

        .footer-title {
            color: var(--gray-800);
            font-weight: 600;
            margin-bottom: 1rem;
            position: relative;
            padding-bottom: 0.5rem;
        }

        .footer-title::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 30px;
            height: 2px;
            background: var(--instructor-primary);
        }

        .footer-menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .footer-menu li {
            margin-bottom: 0.5rem;
        }

        .footer-menu a {
            color: var(--gray-600);
            text-decoration: none;
            display: flex;
            align-items: center;
            padding: 0.5rem 0;
            transition: all 0.3s ease;
            border-radius: 0.5rem;
        }

        .footer-menu a:hover {
            color: var(--instructor-primary);
            background-color: rgba(40, 167, 69, 0.05);
            transform: translateX(-5px);
            padding-left: 0.5rem;
        }

        .footer-system-info {
            background: white;
            border-radius: 1rem;
            padding: 1.5rem;
            margin-bottom: 1rem;
            border: 1px solid var(--gray-100);
        }

        .system-status {
            display: flex;
            align-items: center;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #10b981;
            margin-left: 0.5rem;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .last-update {
            display: flex;
            align-items: center;
            color: var(--gray-600);
        }

        .footer-actions .btn {
            border-radius: 0.5rem;
            font-weight: 500;
        }

        .footer-social {
            display: flex;
            gap: 0.5rem;
        }

        .social-link {
            width: 40px;
            height: 40px;
            border-radius: 0.5rem;
            background: var(--gray-100);
            color: var(--gray-600);
            display: flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .social-link:hover {
            background: var(--instructor-primary);
            color: white;
            transform: translateY(-2px);
        }

        .toast-container {
            z-index: 9999;
        }

        .card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        }

        .btn {
            transition: all 0.3s ease;
        }

        .progress-bar {
            transition: width 1s ease-in-out;
        }

        .lazy {
            opacity: 0;
            transition: opacity 0.3s;
        }

        .lazy.loaded {
            opacity: 1;
        }

        .touch-device .btn {
            min-height: 44px;
        }

        .touch-device .form-control {
            min-height: 44px;
        }

        @media (max-width: 768px) {
            .instructor-footer {
                text-align: center;
            }
            
            .instructor-footer .col-md-6:last-child {
                text-align: center !important;
                margin-top: 0.5rem;
            }
        }

        /* تحسينات الطباعة */
        @media print {
            .no-print,
            .instructor-navbar,
            .quick-actions,
            .btn,
            .instructor-footer {
                display: none !important;
            }
            
            .card {
                border: 1px solid #ddd !important;
                box-shadow: none !important;
            }
            
            .table {
                font-size: 12px;
            }
        }

        /* تحسينات الوضع المظلم */
        .dark-theme {
            --instructor-light: #1e293b;
            --gray-50: #0f172a;
            --gray-100: #1e293b;
            --gray-200: #334155;
            --gray-600: #94a3b8;
            --gray-700: #cbd5e1;
            --gray-800: #f1f5f9;
        }

        .dark-theme .instructor-footer {
            background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
            border-top-color: #334155;
        }

        .dark-theme .quick-stat,
        .dark-theme .footer-system-info {
            background: #1e293b;
            border-color: #334155;
        }

        .dark-theme .mobile-bottom-nav {
            background: #1e293b;
            border-top-color: #334155;
        }

        .dark-theme .fab-menu {
            background: #1e293b;
            border-color: #334155;
        }

        .dark-theme .fab-item:hover {
            background-color: #334155;
        }

        .dark-theme .social-link,
        .dark-theme .stat-link {
            background: #334155;
            color: #94a3b8;
        }

        .dark-theme .social-link:hover,
        .dark-theme .stat-link:hover {
            background: var(--instructor-primary);
            color: white;
        }

        /* تحسينات الاستجابة */
        @media (max-width: 768px) {
            .instructor-footer {
                text-align: center;
                margin-bottom: 80px;
                padding: 2rem 0 1rem;
            }

            .footer-quick-info .row {
                justify-content: center;
            }

            .quick-stat {
                margin-bottom: 1rem;
            }

            .footer-links .col-lg-3 {
                margin-bottom: 2rem;
            }

            .footer-system-info .row {
                text-align: center;
            }

            .footer-system-info .col-md-6:last-child {
                margin-top: 1rem;
            }

            .footer-actions {
                justify-content: center;
                display: flex;
                flex-wrap: wrap;
                gap: 0.5rem;
            }

            .floating-action-btn {
                bottom: 90px;
                left: 50%;
                transform: translateX(-50%);
            }

            .fab-menu {
                left: 50%;
                transform: translateX(-50%);
                bottom: 80px;
            }
        }

        @media (max-width: 576px) {
            .quick-stat {
                flex-direction: column;
                text-align: center;
                padding: 1rem;
            }

            .stat-icon {
                margin-left: 0;
                margin-bottom: 0.5rem;
            }

            .stat-link {
                margin-top: 0.5rem;
            }

            .footer-menu a {
                justify-content: center;
            }

            .footer-social {
                justify-content: center;
            }
        }

        /* تحسينات إمكانية الوصول */
        .keyboard-navigation *:focus {
            outline: 2px solid var(--instructor-primary);
            outline-offset: 2px;
        }

        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }

        /* تحسينات الأداء */
        .quick-stat,
        .mobile-nav-item,
        .fab-main,
        .social-link {
            will-change: transform;
        }

        /* تحسينات الطباعة */
        @media print {
            .mobile-bottom-nav,
            .floating-action-btn,
            .footer-actions,
            .footer-social {
                display: none !important;
            }

            .instructor-footer {
                background: white !important;
                color: black !important;
                margin-bottom: 0 !important;
            }

            .quick-stat,
            .footer-system-info {
                background: white !important;
                border: 1px solid #ddd !important;
                box-shadow: none !important;
            }

            .footer-menu a {
                color: black !important;
            }
        }

        /* تحسينات الحركة المخفضة */
        @media (prefers-reduced-motion: reduce) {
            *,
            *::before,
            *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }

            .status-dot {
                animation: none;
            }
        }

        /* تحسينات التباين العالي */
        @media (prefers-contrast: high) {
            .quick-stat,
            .footer-system-info,
            .mobile-bottom-nav {
                border: 2px solid currentColor;
            }

            .social-link,
            .stat-link {
                border: 1px solid currentColor;
            }
        }

        /* تحسينات الشفافية المخفضة */
        @media (prefers-reduced-transparency: reduce) {
            .mobile-bottom-nav,
            .fab-menu {
                backdrop-filter: none;
            }
        }

        /* تحسينات للشاشات الكبيرة */
        @media (min-width: 1400px) {
            .instructor-footer {
                padding: 4rem 0 2rem;
            }

            .quick-stat {
                padding: 2rem;
            }

            .footer-system-info {
                padding: 2rem;
            }
        }

        /* تحسينات للشاشات الصغيرة جداً */
        @media (max-width: 320px) {
            .mobile-nav-item span {
                font-size: 0.6rem;
            }

            .fab-main {
                width: 48px;
                height: 48px;
                font-size: 1.2rem;
            }

            .fab-menu {
                min-width: 250px;
            }
        }
    </style>

</body>
</html>
