<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات المدرب
if (!isLoggedIn() || !isInstructor()) {
    header('Location: ../login.php');
    exit;
}

$student_id = (int)($_GET['student_id'] ?? 0);
$course_id = (int)($_GET['course_id'] ?? 0);
$error = '';
$success = '';

if (!$student_id || !$course_id) {
    header('Location: students.php');
    exit;
}

// معالجة إضافة/تعديل الدرجات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    if ($action === 'add_grade') {
        $assignment_name = trim($_POST['assignment_name'] ?? '');
        $grade = floatval($_POST['grade'] ?? 0);
        $max_grade = floatval($_POST['max_grade'] ?? 100);
        $notes = trim($_POST['notes'] ?? '');

        if (!empty($assignment_name) && $grade >= 0 && $max_grade > 0) {
            try {
                $stmt = $conn->prepare("
                    INSERT INTO student_grades (student_id, course_id, assignment_name, grade, max_grade, notes, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, NOW())
                ");
                $stmt->execute([$student_id, $course_id, $assignment_name, $grade, $max_grade, $notes]);
                $success = 'تم إضافة الدرجة بنجاح';
            } catch (PDOException $e) {
                $error = 'حدث خطأ أثناء إضافة الدرجة';
            }
        } else {
            $error = 'يرجى ملء جميع الحقول المطلوبة';
        }
    }

    if ($action === 'delete_grade') {
        $grade_id = intval($_POST['grade_id'] ?? 0);
        if ($grade_id > 0) {
            try {
                $stmt = $conn->prepare("
                    DELETE FROM student_grades
                    WHERE id = ? AND student_id = ? AND course_id = ?
                ");
                $stmt->execute([$grade_id, $student_id, $course_id]);
                $success = 'تم حذف الدرجة بنجاح';
            } catch (PDOException $e) {
                $error = 'حدث خطأ أثناء حذف الدرجة';
            }
        }
    }
}

// التحقق من أن الكورس ينتمي للمدرب
try {
    $stmt = $conn->prepare("SELECT title FROM courses WHERE id = ? AND instructor_id = ?");
    $stmt->execute([$course_id, $_SESSION['user_id']]);
    $course = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$course) {
        header('Location: courses.php');
        exit;
    }
} catch (PDOException $e) {
    $error = 'خطأ في التحقق من الكورس';
}

// جلب بيانات الطالب
try {
    $stmt = $conn->prepare("
        SELECT u.name, u.email, c.title as course_title
        FROM users u
        INNER JOIN courses c ON c.id = ?
        WHERE u.id = ? AND c.instructor_id = ?
    ");
    $stmt->execute([$course_id, $student_id, $_SESSION['user_id']]);
    $student = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$student) {
        header('Location: students.php');
        exit;
    }
} catch (PDOException $e) {
    $error = 'خطأ في جلب بيانات الطالب';
}

// جلب جميع الدرجات
try {
    $stmt = $conn->prepare("
        SELECT sg.*, COALESCE(sg.graded_at, sg.created_at) as graded_at
        FROM student_grades sg
        WHERE sg.student_id = ? AND sg.course_id = ?
        ORDER BY COALESCE(sg.graded_at, sg.created_at) DESC
    ");
    $stmt->execute([$student_id, $course_id]);
    $grades = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $grades = [];
}

// حساب الإحصائيات
$total_grades = count($grades);
$total_points = array_sum(array_column($grades, 'grade'));
$total_max_points = array_sum(array_column($grades, 'max_grade'));
$average_percentage = $total_max_points > 0 ? ($total_points / $total_max_points) * 100 : 0;
$highest_grade = $total_grades > 0 ? max(array_column($grades, 'grade')) : 0;
$lowest_grade = $total_grades > 0 ? min(array_column($grades, 'grade')) : 0;

$pageTitle = 'إدارة درجات الطالب';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .grade-header { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); }
        .stat-card { border-left: 4px solid; transition: all 0.3s ease; }
        .stat-card:hover { transform: translateY(-2px); box-shadow: 0 4px 8px rgba(0,0,0,0.1); }
        .grade-badge { font-size: 1.1rem; padding: 0.5rem 1rem; }
    </style>
</head>
<body class="bg-light">

<?php if ($student): ?>
<div class="container-fluid py-4">
    <!-- رأس الصفحة -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="grade-header text-white p-4">
            <div class="d-flex align-items-center justify-content-between">
                <div>
                    <h3 class="mb-1">إدارة درجات الطالب</h3>
                    <p class="mb-0 opacity-75">
                        <i class="fas fa-user me-2"></i><?php echo htmlspecialchars($student['name']); ?>
                        <span class="mx-2">|</span>
                        <i class="fas fa-book me-2"></i><?php echo htmlspecialchars($student['course_title']); ?>
                    </p>
                </div>
                <div>
                    <button class="btn btn-light me-2" data-bs-toggle="modal" data-bs-target="#addGradeModal">
                        <i class="fas fa-plus me-1"></i>إضافة درجة
                    </button>
                    <button class="btn btn-outline-light" onclick="window.close()">
                        <i class="fas fa-times me-1"></i>إغلاق
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- رسائل النجاح والخطأ -->
    <?php if ($error): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <?php echo htmlspecialchars($error); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <?php if ($success): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle me-2"></i>
        <?php echo htmlspecialchars($success); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <!-- الإحصائيات -->
    <div class="row g-4 mb-4">
        <div class="col-lg-3 col-md-6">
            <div class="card border-0 shadow-sm stat-card" style="border-left-color: #28a745;">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-success bg-opacity-10 rounded-3 p-3">
                                <i class="fas fa-star text-success fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h4 class="mb-1 fw-bold text-success"><?php echo number_format($average_percentage, 1); ?>%</h4>
                            <p class="text-muted mb-0 fw-medium">المعدل العام</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card border-0 shadow-sm stat-card" style="border-left-color: #0d6efd;">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-primary bg-opacity-10 rounded-3 p-3">
                                <i class="fas fa-trophy text-primary fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h4 class="mb-1 fw-bold text-primary"><?php echo number_format($highest_grade, 1); ?></h4>
                            <p class="text-muted mb-0 fw-medium">أعلى درجة</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card border-0 shadow-sm stat-card" style="border-left-color: #ffc107;">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-warning bg-opacity-10 rounded-3 p-3">
                                <i class="fas fa-chart-line text-warning fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h4 class="mb-1 fw-bold text-warning"><?php echo number_format($lowest_grade, 1); ?></h4>
                            <p class="text-muted mb-0 fw-medium">أقل درجة</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card border-0 shadow-sm stat-card" style="border-left-color: #6f42c1;">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-purple bg-opacity-10 rounded-3 p-3" style="background-color: rgba(111, 66, 193, 0.1) !important;">
                                <i class="fas fa-tasks text-purple fs-4" style="color: #6f42c1 !important;"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h4 class="mb-1 fw-bold" style="color: #6f42c1;"><?php echo $total_grades; ?></h4>
                            <p class="text-muted mb-0 fw-medium">إجمالي التقييمات</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- جدول الدرجات -->
    <div class="card border-0 shadow-sm">
        <div class="card-header bg-white border-bottom">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>
                سجل الدرجات
            </h5>
        </div>
        <div class="card-body p-0">

            <?php if (empty($grades)): ?>
            <div class="text-center py-5">
                <i class="fas fa-star text-muted" style="font-size: 3rem;"></i>
                <h5 class="mt-3 text-muted">لا توجد درجات</h5>
                <p class="text-muted">لم يتم إضافة أي درجات لهذا الطالب بعد</p>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addGradeModal">
                    <i class="fas fa-plus me-1"></i>إضافة أول درجة
                </button>
            </div>
            <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>التقييم</th>
                            <th>الدرجة</th>
                            <th>النهاية العظمى</th>
                            <th>النسبة المئوية</th>
                            <th>تاريخ التقييم</th>
                            <th>ملاحظات</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($grades as $grade): ?>
                        <tr>
                            <td>
                                <span class="fw-medium"><?php echo htmlspecialchars($grade['assignment_name']); ?></span>
                            </td>
                            <td>
                                <span class="fw-bold text-primary fs-5"><?php echo $grade['grade']; ?></span>
                            </td>
                            <td>
                                <span class="text-muted"><?php echo $grade['max_grade'] ?? 100; ?></span>
                            </td>
                            <td>
                                <?php
                                $max_grade = $grade['max_grade'] ?? 100;
                                $percentage = ($grade['grade'] / $max_grade) * 100;
                                $color = $percentage >= 85 ? 'success' : ($percentage >= 70 ? 'warning' : 'danger');
                                ?>
                                <span class="badge bg-<?php echo $color; ?> grade-badge">
                                    <?php echo number_format($percentage, 1); ?>%
                                </span>
                            </td>
                            <td>
                                <span class="text-muted"><?php echo date('d/m/Y H:i', strtotime($grade['graded_at'])); ?></span>
                            </td>
                            <td>
                                <small class="text-muted"><?php echo htmlspecialchars($grade['notes'] ?? '-'); ?></small>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button class="btn btn-sm btn-outline-primary"
                                            onclick="editGrade(<?php echo $grade['id']; ?>)"
                                            title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger"
                                            onclick="deleteGrade(<?php echo $grade['id']; ?>, '<?php echo htmlspecialchars($grade['assignment_name'], ENT_QUOTES); ?>')"
                                            title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Modal إضافة درجة -->
<div class="modal fade" id="addGradeModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة درجة جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="add_grade">

                    <div class="mb-3">
                        <label for="assignment_name" class="form-label">اسم التقييم <span class="text-danger">*</span></label>
                        <input type="text" name="assignment_name" id="assignment_name" class="form-control"
                               placeholder="مثال: امتحان الوحدة الأولى" required>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="grade" class="form-label">الدرجة المحصلة <span class="text-danger">*</span></label>
                                <input type="number" name="grade" id="grade" class="form-control"
                                       min="0" step="0.1" placeholder="85" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="max_grade" class="form-label">النهاية العظمى <span class="text-danger">*</span></label>
                                <input type="number" name="max_grade" id="max_grade" class="form-control"
                                       min="1" step="0.1" value="100" required>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات</label>
                        <textarea name="notes" id="notes" class="form-control" rows="3"
                                  placeholder="ملاحظات إضافية حول الدرجة..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-save me-1"></i>حفظ الدرجة
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal حذف درجة -->
<div class="modal fade" id="deleteGradeModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title text-danger">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="delete_grade">
                    <input type="hidden" name="grade_id" id="deleteGradeId">

                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        هل أنت متأكد من حذف درجة <strong id="deleteGradeName"></strong>؟
                    </div>

                    <p class="text-muted">
                        لا يمكن التراجع عن هذا الإجراء بعد التأكيد.
                    </p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-1"></i>تأكيد الحذف
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php else: ?>
<div class="container-fluid py-5">
    <div class="text-center">
        <i class="fas fa-exclamation-triangle text-warning" style="font-size: 3rem;"></i>
        <h3 class="mt-3">خطأ في الوصول</h3>
        <p class="text-muted">لا يمكن الوصول إلى بيانات الطالب</p>
        <button class="btn btn-primary" onclick="window.close()">العودة</button>
    </div>
</div>
<?php endif; ?>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
function editGrade(gradeId) {
    alert('سيتم تطوير ميزة التعديل قريباً');
}

function deleteGrade(gradeId, gradeName) {
    document.getElementById('deleteGradeId').value = gradeId;
    document.getElementById('deleteGradeName').textContent = gradeName;
    new bootstrap.Modal(document.getElementById('deleteGradeModal')).show();
}

// حساب النسبة المئوية تلقائياً
document.getElementById('grade').addEventListener('input', calculatePercentage);
document.getElementById('max_grade').addEventListener('input', calculatePercentage);

function calculatePercentage() {
    const grade = parseFloat(document.getElementById('grade').value) || 0;
    const maxGrade = parseFloat(document.getElementById('max_grade').value) || 100;
    const percentage = (grade / maxGrade) * 100;

    // يمكن إضافة عرض النسبة المئوية هنا
}
</script>

</body>
</html>


