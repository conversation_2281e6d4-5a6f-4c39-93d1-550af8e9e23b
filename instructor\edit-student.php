<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من صلاحيات المدرب
if (!isLoggedIn() || !isInstructor()) {
    header('Location: ../login.php');
    exit;
}

$student_id = (int)($_GET['id'] ?? 0);
$course_id = (int)($_GET['course_id'] ?? 0);
$error = '';
$success = '';

// التحقق من أن الكورس ينتمي للمدرب
try {
    $stmt = $conn->prepare("SELECT title FROM courses WHERE id = ? AND instructor_id = ?");
    $stmt->execute([$course_id, $_SESSION['user_id']]);
    $course = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$course) {
        header('Location: courses.php');
        exit;
    }
} catch (PDOException $e) {
    $error = 'خطأ في التحقق من الكورس';
}

// جلب بيانات الطالب والتسجيل
try {
    $stmt = $conn->prepare("
        SELECT u.*, e.enrollment_date, e.status as enrollment_status, e.notes as enrollment_notes
        FROM users u 
        JOIN enrollments e ON u.id = e.student_id 
        WHERE u.id = ? AND e.course_id = ?
    ");
    $stmt->execute([$student_id, $course_id]);
    $student = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$student) {
        header('Location: students.php?course_id=' . $course_id);
        exit;
    }
} catch (PDOException $e) {
    $error = 'خطأ في جلب بيانات الطالب';
}

// معالجة تحديث البيانات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $enrollment_status = $_POST['enrollment_status'] ?? 'approved';
    $enrollment_notes = trim($_POST['enrollment_notes'] ?? '');
    $student_notes = trim($_POST['student_notes'] ?? '');
    
    try {
        $conn->beginTransaction();
        
        // تحديث حالة التسجيل
        $stmt = $conn->prepare("
            UPDATE enrollments 
            SET status = ?, notes = ?, updated_at = NOW() 
            WHERE student_id = ? AND course_id = ?
        ");
        $stmt->execute([$enrollment_status, $enrollment_notes, $student_id, $course_id]);
        
        // تحديث ملاحظات الطالب في جدول منفصل أو في الملف الشخصي
        if (!empty($student_notes)) {
            $stmt = $conn->prepare("
                INSERT INTO student_notes (student_id, course_id, instructor_id, notes, created_at) 
                VALUES (?, ?, ?, ?, NOW())
                ON DUPLICATE KEY UPDATE notes = VALUES(notes), updated_at = NOW()
            ");
            $stmt->execute([$student_id, $course_id, $_SESSION['user_id'], $student_notes]);
        }
        
        $conn->commit();
        $success = 'تم تحديث بيانات الطالب بنجاح';
        
        // إعادة جلب البيانات المحدثة
        $stmt = $conn->prepare("
            SELECT u.*, e.enrollment_date, e.status as enrollment_status, e.notes as enrollment_notes
            FROM users u 
            JOIN enrollments e ON u.id = e.student_id 
            WHERE u.id = ? AND e.course_id = ?
        ");
        $stmt->execute([$student_id, $course_id]);
        $student = $stmt->fetch(PDO::FETCH_ASSOC);
        
    } catch (PDOException $e) {
        $conn->rollBack();
        $error = 'خطأ في تحديث البيانات: ' . $e->getMessage();
    }
}

// جلب إحصائيات الطالب
try {
    // عدد الدرجات
    $stmt = $conn->prepare("SELECT COUNT(*) as grade_count FROM grades WHERE student_id = ? AND course_id = ?");
    $stmt->execute([$student_id, $course_id]);
    $grade_count = $stmt->fetch(PDO::FETCH_ASSOC)['grade_count'];
    
    // متوسط الدرجات
    $stmt = $conn->prepare("
        SELECT AVG((grade / max_grade) * 100) as avg_grade 
        FROM grades 
        WHERE student_id = ? AND course_id = ?
    ");
    $stmt->execute([$student_id, $course_id]);
    $avg_grade = $stmt->fetch(PDO::FETCH_ASSOC)['avg_grade'];
    
    // عدد الجلسات المحضورة
    $stmt = $conn->prepare("
        SELECT COUNT(*) as attended_sessions 
        FROM session_attendance 
        WHERE student_id = ? AND course_id = ? AND attended = 1
    ");
    $stmt->execute([$student_id, $course_id]);
    $attended_sessions = $stmt->fetch(PDO::FETCH_ASSOC)['attended_sessions'];
    
} catch (PDOException $e) {
    $grade_count = 0;
    $avg_grade = 0;
    $attended_sessions = 0;
}

// جلب الملاحظات السابقة
try {
    $stmt = $conn->prepare("
        SELECT notes, created_at 
        FROM student_notes 
        WHERE student_id = ? AND course_id = ? AND instructor_id = ?
        ORDER BY created_at DESC 
        LIMIT 1
    ");
    $stmt->execute([$student_id, $course_id, $_SESSION['user_id']]);
    $existing_notes = $stmt->fetch(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $existing_notes = null;
}

$pageTitle = 'تعديل بيانات الطالب - ' . $student['name'];
$breadcrumbs = [
    ['title' => 'الكورسات', 'url' => 'courses.php'],
    ['title' => $course['title'], 'url' => 'course-details.php?id=' . $course_id],
    ['title' => 'الطلاب', 'url' => 'students.php?course_id=' . $course_id],
    ['title' => 'تعديل بيانات الطالب']
];

include 'includes/header.php';
?>

<div class="row">
    <div class="col-md-8">
        <!-- نموذج تعديل البيانات -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user-edit me-2"></i>
                    تعديل بيانات الطالب
                </h5>
            </div>
            <div class="card-body">
                <?php if ($error): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endif; ?>

                <?php if ($success): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        <?php echo htmlspecialchars($success); ?>
                    </div>
                <?php endif; ?>

                <form method="POST" action="">
                    <!-- معلومات أساسية (للعرض فقط) -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <label class="form-label">اسم الطالب</label>
                            <input type="text" class="form-control" value="<?php echo htmlspecialchars($student['name']); ?>" readonly>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" value="<?php echo htmlspecialchars($student['email']); ?>" readonly>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-6">
                            <label class="form-label">تاريخ التسجيل</label>
                            <input type="text" class="form-control" value="<?php echo date('Y-m-d H:i', strtotime($student['enrollment_date'])); ?>" readonly>
                        </div>
                        <div class="col-md-6">
                            <label for="enrollment_status" class="form-label">حالة التسجيل</label>
                            <select class="form-select" id="enrollment_status" name="enrollment_status">
                                <option value="pending" <?php echo $student['enrollment_status'] === 'pending' ? 'selected' : ''; ?>>
                                    في الانتظار
                                </option>
                                <option value="approved" <?php echo $student['enrollment_status'] === 'approved' ? 'selected' : ''; ?>>
                                    معتمد
                                </option>
                                <option value="suspended" <?php echo $student['enrollment_status'] === 'suspended' ? 'selected' : ''; ?>>
                                    موقوف
                                </option>
                                <option value="rejected" <?php echo $student['enrollment_status'] === 'rejected' ? 'selected' : ''; ?>>
                                    مرفوض
                                </option>
                            </select>
                        </div>
                    </div>

                    <!-- ملاحظات التسجيل -->
                    <div class="mb-4">
                        <label for="enrollment_notes" class="form-label">ملاحظات التسجيل</label>
                        <textarea class="form-control" id="enrollment_notes" name="enrollment_notes" rows="3" 
                                  placeholder="ملاحظات حول حالة تسجيل الطالب..."><?php echo htmlspecialchars($student['enrollment_notes']); ?></textarea>
                        <div class="form-text">هذه الملاحظات خاصة بحالة التسجيل في الكورس</div>
                    </div>

                    <!-- ملاحظات الطالب -->
                    <div class="mb-4">
                        <label for="student_notes" class="form-label">ملاحظات حول الطالب</label>
                        <textarea class="form-control" id="student_notes" name="student_notes" rows="4" 
                                  placeholder="ملاحظات حول أداء الطالب، سلوكه، أو أي معلومات مهمة..."><?php echo $existing_notes ? htmlspecialchars($existing_notes['notes']) : ''; ?></textarea>
                        <div class="form-text">
                            ملاحظات خاصة بك حول الطالب
                            <?php if ($existing_notes): ?>
                                - آخر تحديث: <?php echo date('Y-m-d H:i', strtotime($existing_notes['created_at'])); ?>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save me-2"></i>
                            حفظ التغييرات
                        </button>
                        <a href="students.php?course_id=<?php echo $course_id; ?>" class="btn btn-secondary">
                            <i class="fas fa-arrow-right me-2"></i>
                            العودة للطلاب
                        </a>
                        <a href="student-grades.php?student_id=<?php echo $student_id; ?>&course_id=<?php echo $course_id; ?>" class="btn btn-info">
                            <i class="fas fa-star me-2"></i>
                            عرض الدرجات
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <!-- معلومات الطالب -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-user me-2"></i>
                    معلومات الطالب
                </h6>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <div class="avatar-lg mx-auto mb-3">
                        <?php echo strtoupper(substr($student['name'], 0, 1)); ?>
                    </div>
                    <h6><?php echo htmlspecialchars($student['name']); ?></h6>
                    <small class="text-muted"><?php echo htmlspecialchars($student['email']); ?></small>
                    
                    <div class="mt-2">
                        <?php
                        $status_classes = [
                            'pending' => 'bg-warning',
                            'approved' => 'bg-success',
                            'suspended' => 'bg-danger',
                            'rejected' => 'bg-secondary'
                        ];
                        $status_labels = [
                            'pending' => 'في الانتظار',
                            'approved' => 'معتمد',
                            'suspended' => 'موقوف',
                            'rejected' => 'مرفوض'
                        ];
                        ?>
                        <span class="badge <?php echo $status_classes[$student['enrollment_status']] ?? 'bg-secondary'; ?>">
                            <?php echo $status_labels[$student['enrollment_status']] ?? $student['enrollment_status']; ?>
                        </span>
                    </div>
                </div>
                
                <hr>
                
                <!-- إحصائيات سريعة -->
                <div class="row text-center">
                    <div class="col-4">
                        <div class="border-end">
                            <h6 class="text-primary"><?php echo $grade_count; ?></h6>
                            <small class="text-muted">الدرجات</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="border-end">
                            <h6 class="text-success">
                                <?php echo $avg_grade ? round($avg_grade, 1) . '%' : '0%'; ?>
                            </h6>
                            <small class="text-muted">المعدل</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <h6 class="text-info"><?php echo $attended_sessions; ?></h6>
                        <small class="text-muted">الحضور</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- إجراءات سريعة -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-tools me-2"></i>
                    إجراءات سريعة
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="add-grade.php?student_id=<?php echo $student_id; ?>&course_id=<?php echo $course_id; ?>" 
                       class="btn btn-outline-success btn-sm">
                        <i class="fas fa-star me-2"></i>
                        إضافة درجة
                    </a>
                    
                    <a href="student-grades.php?student_id=<?php echo $student_id; ?>&course_id=<?php echo $course_id; ?>" 
                       class="btn btn-outline-info btn-sm">
                        <i class="fas fa-chart-line me-2"></i>
                        عرض جميع الدرجات
                    </a>
                    
                    <a href="send-message.php?student_id=<?php echo $student_id; ?>&course_id=<?php echo $course_id; ?>" 
                       class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-envelope me-2"></i>
                        إرسال رسالة
                    </a>
                    
                    <hr>
                    
                    <a href="remove-student.php?id=<?php echo $student_id; ?>&course_id=<?php echo $course_id; ?>" 
                       class="btn btn-outline-danger btn-sm"
                       onclick="return confirm('هل أنت متأكد من إزالة هذا الطالب من الكورس؟')">
                        <i class="fas fa-user-times me-2"></i>
                        إزالة من الكورس
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-lg {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--instructor-primary), var(--instructor-secondary));
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: bold;
}

.card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.card-header {
    background: var(--instructor-light);
    border-bottom: 1px solid var(--gray-200);
    border-radius: 12px 12px 0 0 !important;
}

.form-control:focus,
.form-select:focus {
    border-color: var(--instructor-primary);
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}
</style>

<!-- JavaScript -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // تغيير لون الحالة عند التغيير
    const statusSelect = document.getElementById('enrollment_status');
    
    statusSelect.addEventListener('change', function() {
        const value = this.value;
        const colors = {
            'pending': 'warning',
            'approved': 'success',
            'suspended': 'danger',
            'rejected': 'secondary'
        };
        
        // إضافة تأثير بصري للتغيير
        this.style.borderColor = `var(--bs-${colors[value] || 'primary'})`;
    });
});
</script>

</body>
</html>
