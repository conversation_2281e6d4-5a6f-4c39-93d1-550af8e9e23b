<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';
require_once '../includes/activity_logger.php';

// التحقق من صلاحيات المدرب
if (!isLoggedIn() || !isInstructor()) {
    header('Location: ../login.php');
    exit;
}

$course_id = isset($_GET['course_id']) ? (int)$_GET['course_id'] : 0;
$instructor_id = $_SESSION['user_id'];

// التحقق من ملكية الكورس
try {
    $stmt = $conn->prepare("SELECT * FROM courses WHERE id = ? AND instructor_id = ?");
    $stmt->execute([$course_id, $instructor_id]);
    $course = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$course) {
        header('Location: courses.php');
        exit;
    }
} catch (PDOException $e) {
    header('Location: courses.php');
    exit;
}

$success = '';
$error = '';

// معالجة إضافة الجلسة
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $title = trim($_POST['title']);
    $description = trim($_POST['description']);
    $session_date = $_POST['session_date'];
    $start_time = $_POST['start_time'];
    $end_time = $_POST['end_time'];
    $zoom_link = trim($_POST['zoom_link']);
    $meeting_id = trim($_POST['meeting_id']);
    $passcode = trim($_POST['passcode']);
    
    // التحقق من البيانات المطلوبة
    if (empty($title) || empty($session_date) || empty($start_time) || empty($end_time)) {
        $error = 'يرجى ملء جميع الحقول المطلوبة';
    } else {
        try {
            // إنشاء مجلد الجلسات إذا لم يكن موجود
            $upload_dir = '../uploads/sessions';
            if (!file_exists($upload_dir)) {
                mkdir($upload_dir, 0777, true);
            }
            
            $session_file = null;
            $file_name = null;
            
            // معالجة رفع الملف
            if (isset($_FILES['session_file']) && $_FILES['session_file']['error'] === UPLOAD_ERR_OK) {
                $allowed_types = ['pdf', 'doc', 'docx', 'ppt', 'pptx', 'txt', 'zip', 'rar'];
                $file_extension = strtolower(pathinfo($_FILES['session_file']['name'], PATHINFO_EXTENSION));
                
                if (in_array($file_extension, $allowed_types)) {
                    $file_name = $_FILES['session_file']['name'];
                    $unique_name = uniqid('session_') . '.' . $file_extension;
                    $file_path = $upload_dir . '/' . $unique_name;
                    
                    if (move_uploaded_file($_FILES['session_file']['tmp_name'], $file_path)) {
                        $session_file = 'uploads/sessions/' . $unique_name;
                    } else {
                        $error = 'حدث خطأ أثناء رفع الملف';
                    }
                } else {
                    $error = 'نوع الملف غير مدعوم. الأنواع المدعومة: PDF, DOC, DOCX, PPT, PPTX, TXT, ZIP, RAR';
                }
            }
            
            if (empty($error)) {
                // إضافة الجلسة
                $stmt = $conn->prepare("
                    INSERT INTO sessions (course_id, title, description, session_date, start_time, end_time, 
                                        zoom_link, meeting_id, passcode, session_file, file_name, status) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'scheduled')
                ");
                
                $stmt->execute([
                    $course_id,
                    $title,
                    $description,
                    $session_date,
                    $start_time,
                    $end_time,
                    $zoom_link,
                    $meeting_id,
                    $passcode,
                    $session_file,
                    $file_name
                ]);
                
                $session_id = $conn->lastInsertId();
                
                // تسجيل النشاط
                logUserActivity('إضافة جلسة', "تم إضافة جلسة جديدة: $title للكورس: {$course['title']}");
                
                $success = 'تم إضافة الجلسة بنجاح';
                
                // إعادة توجيه بعد النجاح
                header("Location: course-details.php?id=$course_id&tab=sessions&success=1");
                exit;
            }
            
        } catch (PDOException $e) {
            $error = 'حدث خطأ أثناء إضافة الجلسة: ' . $e->getMessage();
        }
    }
}

$pageTitle = 'إضافة جلسة جديدة - ' . $course['title'];
include 'includes/header.php';

?>

<div class="container-fluid py-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">إضافة جلسة جديدة</h5>
                    <small>الكورس: <?php echo htmlspecialchars($course['title']); ?></small>
                </div>
                <div class="card-body">
                    <?php if ($success): ?>
                        <div class="alert alert-success" role="alert">
                            <?php echo htmlspecialchars($success); ?>
                        </div>
                    <?php endif; ?>

                    <?php if ($error): ?>
                        <div class="alert alert-danger" role="alert">
                            <?php echo htmlspecialchars($error); ?>
                        </div>
                    <?php endif; ?>

                    <form method="POST" action="" enctype="multipart/form-data">
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group mb-3">
                                    <label for="title">عنوان الجلسة <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="title" name="title" required
                                        value="<?php echo isset($_POST['title']) ? htmlspecialchars($_POST['title']) : ''; ?>"
                                        placeholder="مثال: مقدمة في البرمجة">
                                </div>
                            </div>
                        </div>

                        <div class="form-group mb-3">
                            <label for="description">وصف الجلسة</label>
                            <textarea class="form-control" id="description" name="description" rows="3"
                                placeholder="وصف مختصر عن محتوى الجلسة"><?php echo isset($_POST['description']) ? htmlspecialchars($_POST['description']) : ''; ?></textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label for="session_date">تاريخ الجلسة <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="session_date" name="session_date" required
                                        value="<?php echo isset($_POST['session_date']) ? htmlspecialchars($_POST['session_date']) : ''; ?>"
                                        min="<?php echo date('Y-m-d'); ?>">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label for="start_time">وقت البدء <span class="text-danger">*</span></label>
                                    <input type="time" class="form-control" id="start_time" name="start_time" required
                                        value="<?php echo isset($_POST['start_time']) ? htmlspecialchars($_POST['start_time']) : ''; ?>">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label for="end_time">وقت الانتهاء <span class="text-danger">*</span></label>
                                    <input type="time" class="form-control" id="end_time" name="end_time" required
                                        value="<?php echo isset($_POST['end_time']) ? htmlspecialchars($_POST['end_time']) : ''; ?>">
                                </div>
                            </div>
                        </div>

                        <hr>
                        <h6 class="text-primary">معلومات Zoom</h6>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="zoom_link">رابط Zoom</label>
                                    <input type="url" class="form-control" id="zoom_link" name="zoom_link"
                                        value="<?php echo isset($_POST['zoom_link']) ? htmlspecialchars($_POST['zoom_link']) : ''; ?>"
                                        placeholder="https://zoom.us/j/123456789">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group mb-3">
                                    <label for="meeting_id">Meeting ID</label>
                                    <input type="text" class="form-control" id="meeting_id" name="meeting_id"
                                        value="<?php echo isset($_POST['meeting_id']) ? htmlspecialchars($_POST['meeting_id']) : ''; ?>"
                                        placeholder="123 456 789">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group mb-3">
                                    <label for="passcode">كلمة المرور</label>
                                    <input type="text" class="form-control" id="passcode" name="passcode"
                                        value="<?php echo isset($_POST['passcode']) ? htmlspecialchars($_POST['passcode']) : ''; ?>"
                                        placeholder="اختياري">
                                </div>
                            </div>
                        </div>

                        <hr>
                        <h6 class="text-primary">ملف الجلسة (اختياري)</h6>
                        
                        <div class="form-group mb-3">
                            <label for="session_file">رفع ملف للجلسة</label>
                            <input type="file" class="form-control" id="session_file" name="session_file"
                                accept=".pdf,.doc,.docx,.ppt,.pptx,.txt,.zip,.rar">
                            <div class="form-text">
                                الأنواع المدعومة: PDF, DOC, DOCX, PPT, PPTX, TXT, ZIP, RAR (الحد الأقصى: 10MB)
                            </div>
                        </div>

                        <div class="text-center">
                            <button type="submit" class="btn btn-success btn-lg">
                                <i class="fas fa-plus"></i> إضافة الجلسة
                            </button>
                            <a href="course-details.php?id=<?php echo $course_id; ?>" class="btn btn-secondary btn-lg">
                                <i class="fas fa-arrow-left"></i> إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // التحقق من صحة الأوقات
    const startTime = document.getElementById('start_time');
    const endTime = document.getElementById('end_time');
    
    function validateTimes() {
        if (startTime.value && endTime.value) {
            if (startTime.value >= endTime.value) {
                endTime.setCustomValidity('وقت الانتهاء يجب أن يكون بعد وقت البدء');
            } else {
                endTime.setCustomValidity('');
            }
        }
    }
    
    startTime.addEventListener('change', validateTimes);
    endTime.addEventListener('change', validateTimes);
    
    // تحديد الحد الأدنى للتاريخ (اليوم)
    const sessionDate = document.getElementById('session_date');
    const today = new Date().toISOString().split('T')[0];
    sessionDate.min = today;
});
</script>

<?php require_once '../includes/footer.php'; ?>
