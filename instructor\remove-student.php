<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من صلاحيات المدرب
if (!isLoggedIn() || !isInstructor()) {
    header('Location: ../login.php');
    exit;
}

$student_id = (int)($_GET['id'] ?? 0);
$course_id = (int)($_GET['course_id'] ?? 0);
$error = '';
$success = '';

// التحقق من أن الكورس ينتمي للمدرب
try {
    $stmt = $conn->prepare("SELECT title FROM courses WHERE id = ? AND instructor_id = ?");
    $stmt->execute([$course_id, $_SESSION['user_id']]);
    $course = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$course) {
        header('Location: courses.php');
        exit;
    }
} catch (PDOException $e) {
    $error = 'خطأ في التحقق من الكورس';
}

// جلب بيانات الطالب
try {
    $stmt = $conn->prepare("
        SELECT u.*, e.enrollment_date, e.status as enrollment_status
        FROM users u 
        JOIN enrollments e ON u.id = e.student_id 
        WHERE u.id = ? AND e.course_id = ?
    ");
    $stmt->execute([$student_id, $course_id]);
    $student = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$student) {
        header('Location: students.php?course_id=' . $course_id);
        exit;
    }
} catch (PDOException $e) {
    $error = 'خطأ في جلب بيانات الطالب';
}

// جلب إحصائيات الطالب قبل الحذف
try {
    // عدد الدرجات
    $stmt = $conn->prepare("SELECT COUNT(*) as grade_count FROM grades WHERE student_id = ? AND course_id = ?");
    $stmt->execute([$student_id, $course_id]);
    $grade_count = $stmt->fetch(PDO::FETCH_ASSOC)['grade_count'];
    
    // عدد الجلسات المحضورة
    $stmt = $conn->prepare("
        SELECT COUNT(*) as attended_sessions 
        FROM session_attendance 
        WHERE student_id = ? AND course_id = ? AND attended = 1
    ");
    $stmt->execute([$student_id, $course_id]);
    $attended_sessions = $stmt->fetch(PDO::FETCH_ASSOC)['attended_sessions'];
    
} catch (PDOException $e) {
    $grade_count = 0;
    $attended_sessions = 0;
}

// معالجة إزالة الطالب
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['confirm_remove'])) {
    $removal_reason = trim($_POST['removal_reason'] ?? '');
    $keep_data = isset($_POST['keep_data']);
    
    if (empty($removal_reason)) {
        $error = 'يرجى تحديد سبب الإزالة';
    } else {
        try {
            $conn->beginTransaction();
            
            if ($keep_data) {
                // تغيير حالة التسجيل إلى "محذوف" مع الاحتفاظ بالبيانات
                $stmt = $conn->prepare("
                    UPDATE enrollments 
                    SET status = 'removed', notes = ?, updated_at = NOW() 
                    WHERE student_id = ? AND course_id = ?
                ");
                $stmt->execute([$removal_reason, $student_id, $course_id]);
                
                // إضافة سجل للإزالة
                $stmt = $conn->prepare("
                    INSERT INTO student_removals (student_id, course_id, instructor_id, reason, data_kept, created_at) 
                    VALUES (?, ?, ?, ?, 1, NOW())
                ");
                $stmt->execute([$student_id, $course_id, $_SESSION['user_id'], $removal_reason]);
                
            } else {
                // حذف جميع البيانات المرتبطة بالطالب في هذا الكورس
                
                // حذف الدرجات
                $stmt = $conn->prepare("DELETE FROM grades WHERE student_id = ? AND course_id = ?");
                $stmt->execute([$student_id, $course_id]);
                
                // حذف سجلات الحضور
                $stmt = $conn->prepare("DELETE FROM session_attendance WHERE student_id = ? AND course_id = ?");
                $stmt->execute([$student_id, $course_id]);
                
                // حذف الملاحظات
                $stmt = $conn->prepare("DELETE FROM student_notes WHERE student_id = ? AND course_id = ?");
                $stmt->execute([$student_id, $course_id]);
                
                // حذف التسجيل
                $stmt = $conn->prepare("DELETE FROM enrollments WHERE student_id = ? AND course_id = ?");
                $stmt->execute([$student_id, $course_id]);
                
                // إضافة سجل للإزالة
                $stmt = $conn->prepare("
                    INSERT INTO student_removals (student_id, course_id, instructor_id, reason, data_kept, created_at) 
                    VALUES (?, ?, ?, ?, 0, NOW())
                ");
                $stmt->execute([$student_id, $course_id, $_SESSION['user_id'], $removal_reason]);
            }
            
            $conn->commit();
            
            // إرسال إشعار للطالب (اختياري)
            if (function_exists('sendNotification')) {
                sendNotification($student_id, 'تم إزالتك من الكورس: ' . $course['title'], $removal_reason);
            }
            
            $_SESSION['success'] = 'تم إزالة الطالب من الكورس بنجاح';
            header('Location: students.php?course_id=' . $course_id);
            exit;
            
        } catch (PDOException $e) {
            $conn->rollBack();
            $error = 'خطأ في إزالة الطالب: ' . $e->getMessage();
        }
    }
}

$pageTitle = 'إزالة الطالب - ' . $student['name'];
$breadcrumbs = [
    ['title' => 'الكورسات', 'url' => 'courses.php'],
    ['title' => $course['title'], 'url' => 'course-details.php?id=' . $course_id],
    ['title' => 'الطلاب', 'url' => 'students.php?course_id=' . $course_id],
    ['title' => 'إزالة الطالب']
];

include 'includes/header.php';
?>

<div class="row justify-content-center">
    <div class="col-md-8">
        <!-- تحذير الإزالة -->
        <div class="card border-danger">
            <div class="card-header bg-danger text-white">
                <h5 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    تأكيد إزالة الطالب
                </h5>
            </div>
            <div class="card-body">
                <?php if ($error): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endif; ?>

                <!-- معلومات الطالب -->
                <div class="alert alert-warning">
                    <div class="row align-items-center">
                        <div class="col-md-2 text-center">
                            <div class="avatar-lg mx-auto">
                                <?php echo strtoupper(substr($student['name'], 0, 1)); ?>
                            </div>
                        </div>
                        <div class="col-md-10">
                            <h6 class="mb-1"><?php echo htmlspecialchars($student['name']); ?></h6>
                            <p class="mb-1"><?php echo htmlspecialchars($student['email']); ?></p>
                            <small class="text-muted">
                                مسجل منذ: <?php echo date('Y-m-d', strtotime($student['enrollment_date'])); ?>
                            </small>
                        </div>
                    </div>
                </div>

                <!-- إحصائيات الطالب -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h4 class="text-primary"><?php echo $grade_count; ?></h4>
                                <small>درجة مسجلة</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h4 class="text-success"><?php echo $attended_sessions; ?></h4>
                                <small>جلسة محضورة</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h4 class="text-info">
                                    <?php 
                                    $days = floor((time() - strtotime($student['enrollment_date'])) / (60 * 60 * 24));
                                    echo $days;
                                    ?>
                                </h4>
                                <small>يوم في الكورس</small>
                            </div>
                        </div>
                    </div>
                </div>

                <form method="POST" action="">
                    <!-- سبب الإزالة -->
                    <div class="mb-4">
                        <label for="removal_reason" class="form-label">
                            <strong>سبب إزالة الطالب <span class="text-danger">*</span></strong>
                        </label>
                        <textarea class="form-control" id="removal_reason" name="removal_reason" rows="4" 
                                  placeholder="يرجى تحديد سبب إزالة الطالب من الكورس..." required></textarea>
                        <div class="form-text">
                            سيتم إرسال هذا السبب للطالب كإشعار (إذا كان مفعلاً)
                        </div>
                    </div>

                    <!-- خيارات الإزالة -->
                    <div class="mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">خيارات الإزالة</h6>
                            </div>
                            <div class="card-body">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="keep_data" name="keep_data" checked>
                                    <label class="form-check-label" for="keep_data">
                                        <strong>الاحتفاظ ببيانات الطالب</strong>
                                    </label>
                                    <div class="form-text">
                                        إذا تم تحديد هذا الخيار، سيتم الاحتفاظ بجميع الدرجات وسجلات الحضور والملاحظات.
                                        سيتم فقط تغيير حالة التسجيل إلى "محذوف" ولن يتمكن الطالب من الوصول للكورس.
                                    </div>
                                </div>
                                
                                <hr>
                                
                                <div class="alert alert-danger">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <strong>تحذير:</strong> إذا لم يتم تحديد خيار الاحتفاظ بالبيانات، سيتم حذف جميع:
                                    <ul class="mb-0 mt-2">
                                        <li>الدرجات المسجلة (<?php echo $grade_count; ?> درجة)</li>
                                        <li>سجلات الحضور (<?php echo $attended_sessions; ?> جلسة)</li>
                                        <li>الملاحظات والتعليقات</li>
                                        <li>سجل التسجيل في الكورس</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار التأكيد -->
                    <div class="d-flex gap-2 justify-content-end">
                        <a href="students.php?course_id=<?php echo $course_id; ?>" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>
                            إلغاء
                        </a>
                        <button type="submit" name="confirm_remove" class="btn btn-danger" 
                                onclick="return confirm('هل أنت متأكد من إزالة هذا الطالب؟ هذا الإجراء لا يمكن التراجع عنه.')">
                            <i class="fas fa-user-times me-2"></i>
                            تأكيد الإزالة
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-lg {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: bold;
}

.card {
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.card-header {
    border-radius: 12px 12px 0 0 !important;
}

.form-control:focus,
.form-check-input:focus {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
}
</style>

<!-- JavaScript -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const keepDataCheckbox = document.getElementById('keep_data');
    const form = document.querySelector('form');
    
    // تغيير نص الزر حسب الخيار المحدد
    keepDataCheckbox.addEventListener('change', function() {
        const submitBtn = document.querySelector('button[name="confirm_remove"]');
        if (this.checked) {
            submitBtn.innerHTML = '<i class="fas fa-user-times me-2"></i>إزالة مع الاحتفاظ بالبيانات';
            submitBtn.className = 'btn btn-warning';
        } else {
            submitBtn.innerHTML = '<i class="fas fa-trash me-2"></i>إزالة وحذف جميع البيانات';
            submitBtn.className = 'btn btn-danger';
        }
    });
    
    // تأكيد إضافي عند عدم الاحتفاظ بالبيانات
    form.addEventListener('submit', function(e) {
        if (!keepDataCheckbox.checked) {
            const confirmed = confirm(
                'تحذير: سيتم حذف جميع بيانات الطالب نهائياً!\n\n' +
                '- <?php echo $grade_count; ?> درجة مسجلة\n' +
                '- <?php echo $attended_sessions; ?> سجل حضور\n' +
                '- جميع الملاحظات والتعليقات\n\n' +
                'هل أنت متأكد من المتابعة؟'
            );
            
            if (!confirmed) {
                e.preventDefault();
            }
        }
    });
});
</script>

</body>
</html>
