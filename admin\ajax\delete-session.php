<?php
require_once '../../includes/session_config.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';
require_once '../../includes/security.php';

// التحقق من صلاحيات المدير
if (!isLoggedIn() || !isAdmin()) {
    echo json_encode(['success' => false, 'message' => 'غير مصرح لك بهذا الإجراء']);
    exit;
}

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'طريقة طلب غير صحيحة']);
    exit;
}

$session_id = $_POST['session_id'] ?? 0;

if (!$session_id) {
    echo json_encode(['success' => false, 'message' => 'معرف الجلسة مطلوب']);
    exit;
}

try {
    // بدء المعاملة
    $conn->beginTransaction();
    
    // جلب معلومات الجلسة قبل الحذف
    $stmt = $conn->prepare("SELECT title FROM sessions WHERE id = ?");
    $stmt->execute([$session_id]);
    $session = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$session) {
        throw new Exception('الجلسة غير موجودة');
    }
    
    // حذف سجلات الحضور
    $stmt = $conn->prepare("DELETE FROM session_attendance WHERE session_id = ?");
    $stmt->execute([$session_id]);
    
    // حذف الجلسة
    $stmt = $conn->prepare("DELETE FROM sessions WHERE id = ?");
    $stmt->execute([$session_id]);
    
    // تسجيل النشاط
    logUserActivity($_SESSION['user_id'], 'حذف جلسة', "تم حذف الجلسة: " . $session['title']);
    
    // تأكيد المعاملة
    $conn->commit();
    
    echo json_encode([
        'success' => true, 
        'message' => 'تم حذف الجلسة بنجاح'
    ]);
    
} catch (Exception $e) {
    // التراجع عن المعاملة في حالة الخطأ
    $conn->rollBack();
    
    error_log("Error deleting session: " . $e->getMessage());
    echo json_encode([
        'success' => false, 
        'message' => 'حدث خطأ أثناء حذف الجلسة: ' . $e->getMessage()
    ]);
}
?>
