/* ===== نظام التعلم الإلكتروني المتقدم - الأنماط الرئيسية ===== */

:root {
    /* الألوان الأساسية المحدثة */
    --primary-color: #667eea;
    --primary-dark: #5a67d8;
    --primary-light: #a3bffa;
    --primary-lighter: #e0e7ff;
    --secondary-color: #764ba2;
    --secondary-dark: #553c9a;
    --secondary-light: #b794f6;

    /* ألوان الحالة */
    --success-color: #48bb78;
    --success-dark: #38a169;
    --success-light: #9ae6b4;
    --warning-color: #ed8936;
    --warning-dark: #dd6b20;
    --warning-light: #fbd38d;
    --danger-color: #f56565;
    --danger-dark: #e53e3e;
    --danger-light: #feb2b2;
    --info-color: #4299e1;
    --info-dark: #3182ce;
    --info-light: #90cdf4;

    /* ألوان محايدة */
    --white: #ffffff;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;

    /* الخطوط المحدثة */
    --font-family-arabic: 'Cairo', 'Tajawal', 'Amiri', system-ui, -apple-system, sans-serif;
    --font-family-english: 'Inter', 'Roboto', system-ui, -apple-system, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;

    /* أوزان الخطوط */
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --font-weight-extrabold: 800;

    /* المسافات المحدثة */
    --spacing-0: 0;
    --spacing-1: 0.25rem;
    --spacing-2: 0.5rem;
    --spacing-3: 0.75rem;
    --spacing-4: 1rem;
    --spacing-5: 1.25rem;
    --spacing-6: 1.5rem;
    --spacing-8: 2rem;
    --spacing-10: 2.5rem;
    --spacing-12: 3rem;
    --spacing-16: 4rem;
    --spacing-20: 5rem;
    --spacing-24: 6rem;
    --spacing-32: 8rem;

    /* الظلال المحدثة */
    --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1), 0 10px 10px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px rgba(0, 0, 0, 0.25);
    --shadow-inner: inset 0 2px 4px rgba(0, 0, 0, 0.06);

    /* الانتقالات المحدثة */
    --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: 600ms cubic-bezier(0.68, -0.55, 0.265, 1.55);

    /* نصف القطر */
    --border-radius-none: 0;
    --border-radius-sm: 0.125rem;
    --border-radius-md: 0.375rem;
    --border-radius-lg: 0.5rem;
    --border-radius-xl: 0.75rem;
    --border-radius-2xl: 1rem;
    --border-radius-3xl: 1.5rem;
    --border-radius-full: 9999px;

    /* الحدود */
    --border-width-0: 0;
    --border-width-1: 1px;
    --border-width-2: 2px;
    --border-width-4: 4px;
    --border-width-8: 8px;

    /* التدرجات */
    --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    --gradient-success: linear-gradient(135deg, var(--success-color) 0%, var(--success-dark) 100%);
    --gradient-warning: linear-gradient(135deg, var(--warning-color) 0%, var(--warning-dark) 100%);
    --gradient-danger: linear-gradient(135deg, var(--danger-color) 0%, var(--danger-dark) 100%);
    --gradient-info: linear-gradient(135deg, var(--info-color) 0%, var(--info-dark) 100%);

    /* الشفافية */
    --opacity-0: 0;
    --opacity-25: 0.25;
    --opacity-50: 0.5;
    --opacity-75: 0.75;
    --opacity-100: 1;

    /* Z-index */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
}

/* ===== الإعدادات العامة المحدثة ===== */
*,
*::before,
*::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
    -webkit-text-size-adjust: 100%;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

body {
    font-family: var(--font-family-arabic);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-normal);
    line-height: 1.6;
    color: var(--gray-800);
    background-color: var(--gray-50);
    direction: rtl;
    text-align: right;
    overflow-x: hidden;
    transition: var(--transition-normal);
}

/* تحسين الخطوط العربية */
body[lang="ar"] {
    font-family: var(--font-family-arabic);
}

body[lang="en"] {
    font-family: var(--font-family-english);
    direction: ltr;
    text-align: left;
}

/* إعدادات الصور */
img {
    max-width: 100%;
    height: auto;
    display: block;
}

/* إعدادات الروابط */
a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition-fast);
}

a:hover {
    color: var(--primary-dark);
    text-decoration: none;
}

a:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* إعدادات العناوين */
h1, h2, h3, h4, h5, h6 {
    font-weight: var(--font-weight-bold);
    line-height: 1.2;
    margin-bottom: var(--spacing-4);
    color: var(--gray-900);
}

h1 { font-size: var(--font-size-5xl); }
h2 { font-size: var(--font-size-4xl); }
h3 { font-size: var(--font-size-3xl); }
h4 { font-size: var(--font-size-2xl); }
h5 { font-size: var(--font-size-xl); }
h6 { font-size: var(--font-size-lg); }

/* إعدادات الفقرات */
p {
    margin-bottom: var(--spacing-4);
    color: var(--gray-700);
}

/* إعدادات القوائم */
ul, ol {
    margin-bottom: var(--spacing-4);
    padding-right: var(--spacing-6);
}

li {
    margin-bottom: var(--spacing-1);
}

/* إعدادات الجداول */
table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: var(--spacing-4);
}

th, td {
    padding: var(--spacing-3);
    text-align: right;
    border-bottom: var(--border-width-1) solid var(--gray-200);
}

th {
    font-weight: var(--font-weight-semibold);
    background-color: var(--gray-100);
}

/* إعدادات النماذج */
input, textarea, select, button {
    font-family: inherit;
    font-size: inherit;
}

/* إعدادات التمرير */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--gray-100);
}

::-webkit-scrollbar-thumb {
    background: var(--gray-400);
    border-radius: var(--border-radius-full);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--gray-500);
}

/* إعدادات التحديد */
::selection {
    background-color: var(--primary-light);
    color: var(--white);
}

::-moz-selection {
    background-color: var(--primary-light);
    color: var(--white);
}

/* ===== مكونات UI المتقدمة ===== */

/* شريط التنقل المتطور */
.navbar-enhanced {
    background: var(--white);
    backdrop-filter: blur(20px);
    border-bottom: var(--border-width-1) solid var(--gray-200);
    box-shadow: var(--shadow-sm);
    transition: var(--transition-normal);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: var(--z-fixed);
}

.navbar-enhanced.scrolled {
    background: rgba(255, 255, 255, 0.95);
    box-shadow: var(--shadow-lg);
}

.navbar-brand-enhanced {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    color: var(--primary-color) !important;
    text-decoration: none;
    transition: var(--transition-normal);
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
}

.navbar-brand-enhanced:hover {
    transform: scale(1.05);
    color: var(--primary-dark) !important;
}

.navbar-brand-enhanced .logo-icon {
    width: 40px;
    height: 40px;
    background: var(--gradient-primary);
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: var(--font-size-xl);
}

/* أزرار متقدمة */
.btn-enhanced {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-2);
    padding: var(--spacing-3) var(--spacing-6);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    line-height: 1;
    border: var(--border-width-2) solid transparent;
    border-radius: var(--border-radius-lg);
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
    white-space: nowrap;
    user-select: none;
}

.btn-enhanced:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.btn-enhanced:disabled {
    opacity: var(--opacity-50);
    cursor: not-allowed;
    pointer-events: none;
}

/* أنواع الأزرار */
.btn-primary-enhanced {
    background: var(--gradient-primary);
    color: var(--white);
    border-color: var(--primary-color);
}

.btn-primary-enhanced:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    background: linear-gradient(135deg, var(--primary-dark) 0%, var(--secondary-dark) 100%);
}

.btn-success-enhanced {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    color: var(--white);
    border-color: #48bb78;
}

.btn-success-enhanced:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    background: linear-gradient(135deg, #38a169 0%, #2f855a 100%);
    color: var(--white);
}

.btn-secondary-enhanced {
    background: var(--white);
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-secondary-enhanced:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-success-enhanced {
    background: var(--gradient-success);
    color: var(--white);
    border-color: var(--success-color);
}

.btn-success-enhanced:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-warning-enhanced {
    background: var(--gradient-warning);
    color: var(--white);
    border-color: var(--warning-color);
}

.btn-warning-enhanced:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-danger-enhanced {
    background: var(--gradient-danger);
    color: var(--white);
    border-color: var(--danger-color);
}

.btn-danger-enhanced:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* أحجام الأزرار */
.btn-sm-enhanced {
    padding: var(--spacing-2) var(--spacing-4);
    font-size: var(--font-size-sm);
}

.btn-lg-enhanced {
    padding: var(--spacing-4) var(--spacing-8);
    font-size: var(--font-size-lg);
}

.btn-xl-enhanced {
    padding: var(--spacing-5) var(--spacing-10);
    font-size: var(--font-size-xl);
}

/* تأثير الموجة */
.btn-enhanced::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: var(--border-radius-full);
    transform: translate(-50%, -50%);
    transition: var(--transition-fast);
}

.btn-enhanced:active::before {
    width: 300px;
    height: 300px;
}

.nav-link {
    color: white !important;
    padding: 0.75rem 1.25rem;
    margin: 0 0.25rem;
    border-radius: 8px;
    transition: var(--transition-normal);
    font-weight: 500;
    position: relative;
    overflow: hidden;
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: var(--transition-normal);
}

.nav-link:hover::before {
    left: 100%;
}

.nav-link:hover {
    background-color: rgba(255,255,255,0.15);
    transform: translateY(-2px);
}

.nav-link.active {
    background-color: rgba(255,255,255,0.2);
    box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
}

/* ===== البطاقات المتطورة ===== */
.card-modern {
    background: var(--white);
    border: var(--border-width-1) solid var(--gray-200);
    border-radius: var(--border-radius-2xl);
    box-shadow: var(--shadow-sm);
    transition: var(--transition-normal);
    overflow: hidden;
    position: relative;
}

.card-modern:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-light);
}

.card-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: var(--transition-normal);
}

.card-modern:hover::before {
    transform: scaleX(1);
}

.card-header-modern {
    padding: var(--spacing-6);
    border-bottom: var(--border-width-1) solid var(--gray-100);
    background: var(--gray-50);
}

.card-body-modern {
    padding: var(--spacing-6);
}

.card-footer-modern {
    padding: var(--spacing-6);
    border-top: var(--border-width-1) solid var(--gray-100);
    background: var(--gray-50);
}

/* بطاقات الكورسات */
.course-card {
    background: var(--white);
    border-radius: var(--border-radius-2xl);
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
    overflow: hidden;
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.course-card:hover {
    transform: translateY(-12px);
    box-shadow: var(--shadow-2xl);
}

.course-card-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.course-card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-slow);
}

.course-card:hover .course-card-image img {
    transform: scale(1.1);
}

.course-card-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, transparent 0%, rgba(0,0,0,0.7) 100%);
    opacity: 0;
    transition: var(--transition-normal);
    display: flex;
    align-items: center;
    justify-content: center;
}

.course-card:hover .course-card-overlay {
    opacity: 1;
}

.course-card-play {
    width: 60px;
    height: 60px;
    background: var(--white);
    border-radius: var(--border-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    font-size: var(--font-size-2xl);
    transform: scale(0);
    transition: var(--transition-bounce);
}

.course-card:hover .course-card-play {
    transform: scale(1);
}

.course-card-content {
    padding: var(--spacing-6);
    flex: 1;
    display: flex;
    flex-direction: column;
}

.course-card-title {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    color: var(--gray-900);
    margin-bottom: var(--spacing-3);
    line-height: 1.3;
}

.course-card-description {
    color: var(--gray-600);
    font-size: var(--font-size-sm);
    line-height: 1.5;
    margin-bottom: var(--spacing-4);
    flex: 1;
}

.course-card-meta {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-4);
    font-size: var(--font-size-sm);
    color: var(--gray-500);
}

.course-card-instructor {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
}

.course-card-stats {
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
}

.course-card-price {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    color: var(--primary-color);
    margin-bottom: var(--spacing-4);
}

.course-card-price.free {
    color: var(--success-color);
}

.course-card-footer {
    padding: 0 var(--spacing-6) var(--spacing-6);
}

/* شارات الأسعار */
.price-badge {
    position: absolute;
    top: var(--spacing-4);
    right: var(--spacing-4);
    padding: var(--spacing-2) var(--spacing-4);
    border-radius: var(--border-radius-full);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    color: var(--white);
    z-index: 10;
}

.price-badge.free {
    background: var(--gradient-success);
}

.price-badge.paid {
    background: var(--gradient-warning);
}

/* ===== الأزرار المحسنة ===== */
.btn-custom {
    border-radius: 25px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: var(--transition-normal);
    border: none;
    position: relative;
    overflow: hidden;
}

.btn-custom::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255,255,255,0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: var(--transition-fast);
}

.btn-custom:hover::before {
    width: 300px;
    height: 300px;
}

.btn-primary-custom {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
}

.btn-primary-custom:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-success-custom {
    background: linear-gradient(135deg, var(--success-color), #388E3C);
    color: white;
}

.btn-warning-custom {
    background: linear-gradient(135deg, var(--warning-color), #F57C00);
    color: white;
}

.btn-danger-custom {
    background: linear-gradient(135deg, var(--danger-color), #D32F2F);
    color: white;
}

/* ===== الإحصائيات ===== */
.stats-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: var(--shadow-sm);
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.stats-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-lg);
}

.stats-number {
    font-size: 3rem;
    font-weight: bold;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}

.stats-label {
    color: #666;
    font-size: 1.1rem;
    font-weight: 500;
}

.stats-icon {
    position: absolute;
    top: 1rem;
    left: 1rem;
    font-size: 2.5rem;
    color: var(--primary-light);
    opacity: 0.3;
}

/* ===== الجداول المحسنة ===== */
.table-enhanced {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.table-enhanced thead {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
}

.table-enhanced thead th {
    border: none;
    padding: 1.25rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.table-enhanced tbody tr {
    transition: var(--transition-fast);
}

.table-enhanced tbody tr:hover {
    background-color: var(--primary-light);
    transform: scale(1.01);
}

.table-enhanced tbody td {
    padding: 1rem 1.25rem;
    border-color: #E3F2FD;
    vertical-align: middle;
}

/* ===== النماذج المحسنة ===== */
.form-control-custom {
    border: 2px solid #E0E0E0;
    border-radius: 10px;
    padding: 0.75rem 1rem;
    transition: var(--transition-normal);
    font-size: var(--font-size-base);
}

.form-control-custom:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(33, 150, 243, 0.25);
    transform: translateY(-2px);
}

.form-label-custom {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

/* ===== الرسائل والتنبيهات ===== */
.alert-custom {
    border: none;
    border-radius: 10px;
    padding: 1rem 1.5rem;
    margin-bottom: 1.5rem;
    position: relative;
    overflow: hidden;
}

.alert-custom::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
}

.alert-success-custom {
    background-color: #E8F5E8;
    color: #2E7D32;
    border-left: 4px solid var(--success-color);
}

.alert-warning-custom {
    background-color: #FFF3E0;
    color: #E65100;
    border-left: 4px solid var(--warning-color);
}

.alert-danger-custom {
    background-color: #FFEBEE;
    color: #C62828;
    border-left: 4px solid var(--danger-color);
}

.alert-info-custom {
    background-color: #E3F2FD;
    color: #1565C0;
    border-left: 4px solid var(--info-color);
}

/* ===== التحميل والانتظار ===== */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* ===== الاستجابة المتقدمة للأجهزة ===== */

/* الشاشات الكبيرة جداً */
@media (min-width: 1400px) {
    .container-fluid {
        max-width: 1320px;
        margin: 0 auto;
    }

    h1 { font-size: var(--font-size-5xl); }
    h2 { font-size: var(--font-size-4xl); }

    .hero-section {
        padding: 150px 0 100px;
    }
}

/* الشاشات الكبيرة */
@media (min-width: 1200px) and (max-width: 1399px) {
    .container {
        max-width: 1140px;
    }

    .course-card-image {
        height: 220px;
    }
}

/* الشاشات المتوسطة */
@media (min-width: 992px) and (max-width: 1199px) {
    .container {
        max-width: 960px;
    }

    .hero-section {
        padding: 100px 0 60px;
    }

    h1 { font-size: var(--font-size-4xl); }
    h2 { font-size: var(--font-size-3xl); }
}

/* الأجهزة اللوحية */
@media (min-width: 768px) and (max-width: 991px) {
    .container {
        max-width: 720px;
    }

    .hero-section {
        padding: 80px 0 40px;
        text-align: center;
    }

    .navbar-brand-enhanced {
        font-size: var(--font-size-xl);
    }

    .course-card-image {
        height: 180px;
    }

    .btn-enhanced {
        padding: var(--spacing-3) var(--spacing-5);
        font-size: var(--font-size-sm);
    }

    .card-body-modern {
        padding: var(--spacing-4);
    }

    h1 { font-size: var(--font-size-3xl); }
    h2 { font-size: var(--font-size-2xl); }
    h3 { font-size: var(--font-size-xl); }
}

/* الهواتف الذكية الكبيرة */
@media (min-width: 576px) and (max-width: 767px) {
    .container {
        max-width: 540px;
    }

    .hero-section {
        padding: 60px 0 30px;
        text-align: center;
    }

    .navbar-brand-enhanced {
        font-size: var(--font-size-lg);
    }

    .navbar-brand-enhanced .logo-icon {
        width: 35px;
        height: 35px;
        font-size: var(--font-size-lg);
    }

    .course-card {
        margin-bottom: var(--spacing-6);
    }

    .course-card-image {
        height: 160px;
    }

    .course-card-content {
        padding: var(--spacing-4);
    }

    .btn-enhanced {
        padding: var(--spacing-2) var(--spacing-4);
        font-size: var(--font-size-sm);
        width: 100%;
        justify-content: center;
    }

    .btn-lg-enhanced {
        padding: var(--spacing-3) var(--spacing-6);
    }

    .card-body-modern {
        padding: var(--spacing-4);
    }

    .card-header-modern,
    .card-footer-modern {
        padding: var(--spacing-4);
    }

    h1 { font-size: var(--font-size-2xl); }
    h2 { font-size: var(--font-size-xl); }
    h3 { font-size: var(--font-size-lg); }
    h4 { font-size: var(--font-size-base); }
}

/* الهواتف الذكية الصغيرة */
@media (max-width: 575px) {
    .container {
        padding: 0 var(--spacing-4);
    }

    .hero-section {
        padding: 40px 0 20px;
        text-align: center;
    }

    .navbar-enhanced {
        padding: var(--spacing-2) 0;
    }

    .navbar-brand-enhanced {
        font-size: var(--font-size-base);
    }

    .navbar-brand-enhanced .logo-icon {
        width: 30px;
        height: 30px;
        font-size: var(--font-size-base);
    }

    .course-card {
        margin-bottom: var(--spacing-4);
    }

    .course-card-image {
        height: 140px;
    }

    .course-card-content {
        padding: var(--spacing-3);
    }

    .course-card-title {
        font-size: var(--font-size-lg);
    }

    .course-card-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-2);
    }

    .course-card-stats {
        gap: var(--spacing-2);
    }

    .btn-enhanced {
        padding: var(--spacing-3) var(--spacing-4);
        font-size: var(--font-size-sm);
        width: 100%;
        justify-content: center;
    }

    .btn-sm-enhanced {
        padding: var(--spacing-2) var(--spacing-3);
        font-size: var(--font-size-xs);
    }

    .btn-lg-enhanced {
        padding: var(--spacing-4) var(--spacing-6);
        font-size: var(--font-size-base);
    }

    .card-modern {
        margin-bottom: var(--spacing-4);
    }

    .card-body-modern {
        padding: var(--spacing-3);
    }

    .card-header-modern,
    .card-footer-modern {
        padding: var(--spacing-3);
    }

    h1 { font-size: var(--font-size-xl); }
    h2 { font-size: var(--font-size-lg); }
    h3 { font-size: var(--font-size-base); }
    h4 { font-size: var(--font-size-sm); }

    /* إخفاء بعض العناصر في الشاشات الصغيرة */
    .hide-on-mobile {
        display: none !important;
    }

    /* تحسين النصوص للشاشات الصغيرة */
    .text-responsive {
        font-size: var(--font-size-sm) !important;
        line-height: 1.4 !important;
    }

    /* تحسين الأزرار للشاشات الصغيرة */
    .btn-group-mobile {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-2);
    }

    .btn-group-mobile .btn-enhanced {
        width: 100%;
    }
}

/* تحسينات للشاشات عالية الدقة */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .course-card-image img {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* تحسينات للوضع الأفقي على الهواتف */
@media (max-height: 500px) and (orientation: landscape) {
    .hero-section {
        padding: 20px 0;
        min-height: auto;
    }

    .navbar-enhanced {
        padding: var(--spacing-1) 0;
    }
}

/* تحسينات للطباعة */
@media print {
    .navbar-enhanced,
    .btn-enhanced,
    .course-card-overlay,
    .animate-fade-in,
    .animate-fade-in-up {
        display: none !important;
    }

    .course-card {
        box-shadow: none !important;
        border: var(--border-width-1) solid var(--gray-300) !important;
    }

    body {
        background: var(--white) !important;
        color: var(--gray-900) !important;
    }
}

/* ===== الرسوم المتحركة والتأثيرات المتقدمة ===== */

/* رسوم متحركة للظهور */
.animate-fade-in {
    animation: fadeIn 0.6s ease-out;
}

.animate-fade-in-up {
    animation: fadeInUp 0.8s ease-out;
}

.animate-fade-in-down {
    animation: fadeInDown 0.8s ease-out;
}

.animate-fade-in-left {
    animation: fadeInLeft 0.8s ease-out;
}

.animate-fade-in-right {
    animation: fadeInRight 0.8s ease-out;
}

.animate-scale-in {
    animation: scaleIn 0.5s ease-out;
}

.animate-bounce-in {
    animation: bounceIn 0.8s ease-out;
}

.animate-slide-up {
    animation: slideUp 0.6s ease-out;
}

/* تأثيرات التمرير */
.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: var(--transition-slow);
}

.animate-on-scroll.in-view {
    opacity: 1;
    transform: translateY(0);
}

/* تأثيرات التحويم */
.hover-lift {
    transition: var(--transition-normal);
}

.hover-lift:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
}

.hover-scale {
    transition: var(--transition-normal);
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-rotate {
    transition: var(--transition-normal);
}

.hover-rotate:hover {
    transform: rotate(5deg);
}

.hover-glow {
    transition: var(--transition-normal);
}

.hover-glow:hover {
    box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
}

/* تأثيرات النبض */
.pulse {
    animation: pulse 2s infinite;
}

.pulse-slow {
    animation: pulse 3s infinite;
}

/* تأثيرات الاهتزاز */
.shake {
    animation: shake 0.5s ease-in-out;
}

/* تأثيرات الدوران */
.rotate {
    animation: rotate 2s linear infinite;
}

.rotate-slow {
    animation: rotate 4s linear infinite;
}

/* تأثيرات التموج */
.ripple {
    position: relative;
    overflow: hidden;
}

.ripple::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: var(--border-radius-full);
    background: rgba(255, 255, 255, 0.5);
    transform: translate(-50%, -50%);
    transition: var(--transition-fast);
}

.ripple:active::before {
    width: 300px;
    height: 300px;
}

/* تأثيرات التدرج المتحرك */
.gradient-animation {
    background: linear-gradient(-45deg, var(--primary-color), var(--secondary-color), var(--info-color), var(--success-color));
    background-size: 400% 400%;
    animation: gradientShift 4s ease infinite;
}

/* تأثيرات الكتابة */
.typewriter {
    overflow: hidden;
    border-left: 2px solid var(--primary-color);
    white-space: nowrap;
    animation: typing 3s steps(40, end), blink-caret 0.75s step-end infinite;
}

/* تأثيرات الجسيمات */
.particles {
    position: relative;
    overflow: hidden;
}

.particles::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 20% 80%, rgba(102, 126, 234, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(118, 75, 162, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(72, 187, 120, 0.2) 0%, transparent 50%);
    animation: particleFloat 6s ease-in-out infinite;
}

/* تأثيرات الموجات */
.wave-animation {
    position: relative;
    overflow: hidden;
}

.wave-animation::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 200%;
    height: 100px;
    background: linear-gradient(to right, transparent, rgba(102, 126, 234, 0.1), transparent);
    animation: wave 3s linear infinite;
}

/* تأثيرات الضوء */
.light-effect {
    position: relative;
    overflow: hidden;
}

.light-effect::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transform: rotate(45deg);
    animation: lightSweep 3s ease-in-out infinite;
}

/* ===== Keyframes للرسوم المتحركة ===== */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(100%);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.05);
        opacity: 0.8;
    }
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
    20%, 40%, 60%, 80% { transform: translateX(5px); }
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

@keyframes typing {
    from { width: 0; }
    to { width: 100%; }
}

@keyframes blink-caret {
    from, to { border-color: transparent; }
    50% { border-color: var(--primary-color); }
}

@keyframes particleFloat {
    0%, 100% { transform: translateY(0) rotate(0deg); }
    33% { transform: translateY(-10px) rotate(120deg); }
    66% { transform: translateY(5px) rotate(240deg); }
}

@keyframes wave {
    0% { transform: translateX(-50%); }
    100% { transform: translateX(0); }
}

@keyframes lightSweep {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% { transform: translateY(0); }
    40%, 43% { transform: translateY(-30px); }
    70% { transform: translateY(-15px); }
    90% { transform: translateY(-4px); }
}

/* ===== تحسين النصوص المتقدم ===== */
.text-gradient {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: var(--font-weight-bold);
    display: inline-block;
}

.text-gradient-success {
    background: var(--gradient-success);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: var(--font-weight-bold);
    display: inline-block;
}

.text-gradient-warning {
    background: var(--gradient-warning);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: var(--font-weight-bold);
    display: inline-block;
}

.text-shadow-sm {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.text-shadow-md {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.text-shadow-lg {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.text-glow {
    text-shadow: 0 0 10px rgba(102, 126, 234, 0.5);
}

.text-outline {
    -webkit-text-stroke: 1px var(--primary-color);
    text-stroke: 1px var(--primary-color);
}
