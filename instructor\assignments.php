<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات المدرب
if (!isLoggedIn() || !isInstructor()) {
    // للاختبار: إنشاء جلسة مؤقتة إذا لم تكن موجودة
    if (!isset($_SESSION['user_id'])) {
        try {
            $stmt = $conn->prepare("SELECT id, name, email, role FROM users WHERE role = 'instructor' LIMIT 1");
            $stmt->execute();
            $instructor = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($instructor) {
                $_SESSION['user_id'] = $instructor['id'];
                $_SESSION['user_name'] = $instructor['name'];
                $_SESSION['user_email'] = $instructor['email'];
                $_SESSION['role'] = $instructor['role'];
            } else {
                header('Location: ../login.php');
                exit;
            }
        } catch (PDOException $e) {
            header('Location: ../login.php');
            exit;
        }
    }
}

// التأكد من وجود جدول الواجبات
try {
    $conn->exec("CREATE TABLE IF NOT EXISTS assignments (
        id INT AUTO_INCREMENT PRIMARY KEY,
        course_id INT NOT NULL,
        instructor_id INT NOT NULL,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        instructions TEXT,
        due_date DATETIME NOT NULL,
        max_grade INT DEFAULT 100,
        is_required TINYINT(1) DEFAULT 0,
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_by INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
        FOREIGN KEY (instructor_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

    // التأكد من وجود جدول تسليمات الواجبات
    $conn->exec("CREATE TABLE IF NOT EXISTS assignment_submissions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        assignment_id INT NOT NULL,
        student_id INT NOT NULL,
        submission_text TEXT,
        file_path VARCHAR(500),
        score INT NULL,
        feedback TEXT,
        submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        graded_at TIMESTAMP NULL,
        status ENUM('submitted', 'graded', 'late') DEFAULT 'submitted',
        FOREIGN KEY (assignment_id) REFERENCES assignments(id) ON DELETE CASCADE,
        FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
        UNIQUE KEY unique_submission (assignment_id, student_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
} catch (PDOException $e) {
    // تجاهل أخطاء إنشاء الجداول إذا كانت موجودة
}

$pageTitle = 'إدارة الواجبات';
$breadcrumbs = [
    ['title' => 'إدارة الواجبات']
];

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'create_assignment') {
        $course_id = $_POST['course_id'] ?? 0;
        $title = trim($_POST['title'] ?? '');
        $description = trim($_POST['description'] ?? '');
        $due_date = $_POST['due_date'] ?? '';
        $max_grade = $_POST['max_grade'] ?? 100;
        $instructions = trim($_POST['instructions'] ?? '');
        $is_required = isset($_POST['is_required']) ? 1 : 0;
        
        $errors = [];
        
        if (empty($title)) {
            $errors[] = 'عنوان الواجب مطلوب';
        }
        
        if (empty($course_id)) {
            $errors[] = 'يجب اختيار كورس';
        }
        
        if (empty($due_date)) {
            $errors[] = 'تاريخ التسليم مطلوب';
        }
        
        if (empty($errors)) {
            try {
                // التحقق من أن الكورس ينتمي للمدرب
                $stmt = $conn->prepare("SELECT id FROM courses WHERE id = ? AND instructor_id = ?");
                $stmt->execute([$course_id, $_SESSION['user_id']]);
                
                if ($stmt->fetch()) {
                    $stmt = $conn->prepare("
                        INSERT INTO assignments (course_id, instructor_id, title, description, instructions, due_date, max_grade, is_required, created_by)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ");
                    $stmt->execute([$course_id, $_SESSION['user_id'], $title, $description, $instructions, $due_date, $max_grade, $is_required, $_SESSION['user_id']]);

                    $assignment_id = $conn->lastInsertId();

                    // التحقق من عدد الطلاب المسجلين في الكورس
                    $stmt = $conn->prepare("
                        SELECT COUNT(DISTINCT ce.student_id) as student_count,
                               c.title as course_title
                        FROM course_enrollments ce
                        INNER JOIN courses c ON ce.course_id = c.id
                        WHERE ce.course_id = ? AND ce.status = 'active'
                        GROUP BY c.id, c.title
                    ");
                    $stmt->execute([$course_id]);
                    $course_info = $stmt->fetch();

                    $student_count = $course_info['student_count'] ?? 0;
                    $course_title = $course_info['course_title'] ?? 'الكورس';

                    // إنشاء رسالة النجاح المفصلة
                    if ($student_count > 0) {
                        $success_message = "
                            <div class='alert alert-success alert-dismissible fade show' role='alert'>
                                <div class='d-flex align-items-center mb-2'>
                                    <i class='fas fa-check-circle text-success me-2'></i>
                                    <strong>تم إنشاء الواجب بنجاح!</strong>
                                </div>
                                <hr>
                                <div class='row'>
                                    <div class='col-md-6'>
                                        <p class='mb-1'><strong>عنوان الواجب:</strong> $title</p>
                                        <p class='mb-1'><strong>الكورس:</strong> $course_title</p>
                                        <p class='mb-1'><strong>تاريخ التسليم:</strong> " . date('Y-m-d H:i', strtotime($due_date)) . "</p>
                                    </div>
                                    <div class='col-md-6'>
                                        <div class='bg-light p-3 rounded'>
                                            <div class='d-flex align-items-center mb-2'>
                                                <i class='fas fa-users text-primary me-2'></i>
                                                <strong>حالة الإشعار:</strong>
                                            </div>
                                            <p class='text-success mb-1'>
                                                <i class='fas fa-check me-1'></i>
                                                تم إرسال إشعار لـ <strong>$student_count طالب/طالبة</strong>
                                            </p>
                                            <p class='text-success mb-0'>
                                                <i class='fas fa-eye me-1'></i>
                                                الواجب متاح الآن للطلاب
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                <button type='button' class='btn-close' data-bs-dismiss='alert'></button>
                            </div>
                        ";

                        // إرسال إشعارات للطلاب (اختياري)
                        try {
                            $notification_stmt = $conn->prepare("
                                INSERT INTO notifications (user_id, title, message, type, category, action_url)
                                SELECT ce.student_id,
                                       'واجب جديد متاح',
                                       CONCAT('تم إضافة واجب جديد: ', ?, ' في كورس ', ?),
                                       'info',
                                       'assignment',
                                       CONCAT('/student/assignments.php?id=', ?)
                                FROM course_enrollments ce
                                WHERE ce.course_id = ? AND ce.status = 'active'
                            ");
                            $notification_stmt->execute([$title, $course_title, $assignment_id, $course_id]);
                        } catch (PDOException $e) {
                            // تجاهل أخطاء الإشعارات
                        }

                    } else {
                        $success_message = "
                            <div class='alert alert-warning alert-dismissible fade show' role='alert'>
                                <div class='d-flex align-items-center mb-2'>
                                    <i class='fas fa-exclamation-triangle text-warning me-2'></i>
                                    <strong>تم إنشاء الواجب بنجاح!</strong>
                                </div>
                                <hr>
                                <div class='row'>
                                    <div class='col-md-6'>
                                        <p class='mb-1'><strong>عنوان الواجب:</strong> $title</p>
                                        <p class='mb-1'><strong>الكورس:</strong> $course_title</p>
                                        <p class='mb-1'><strong>تاريخ التسليم:</strong> " . date('Y-m-d H:i', strtotime($due_date)) . "</p>
                                    </div>
                                    <div class='col-md-6'>
                                        <div class='bg-light p-3 rounded'>
                                            <div class='d-flex align-items-center mb-2'>
                                                <i class='fas fa-info-circle text-warning me-2'></i>
                                                <strong>تنبيه:</strong>
                                            </div>
                                            <p class='text-warning mb-1'>
                                                <i class='fas fa-users me-1'></i>
                                                لا يوجد طلاب مسجلين في هذا الكورس حالياً
                                            </p>
                                            <p class='text-info mb-0'>
                                                <i class='fas fa-clock me-1'></i>
                                                سيظهر الواجب للطلاب عند تسجيلهم في الكورس
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                <button type='button' class='btn-close' data-bs-dismiss='alert'></button>
                            </div>
                        ";
                    }
                }
            } catch (PDOException $e) {
                $error_message = "
                    <div class='alert alert-danger alert-dismissible fade show' role='alert'>
                        <div class='d-flex align-items-center'>
                            <i class='fas fa-exclamation-circle text-danger me-2'></i>
                            <strong>خطأ في قاعدة البيانات!</strong>
                        </div>
                        <hr>
                        <p class='mb-1'>حدث خطأ أثناء إنشاء الواجب. يرجى المحاولة مرة أخرى.</p>
                        <small class='text-muted'>إذا استمرت المشكلة، يرجى التواصل مع الدعم الفني.</small>
                        <button type='button' class='btn-close' data-bs-dismiss='alert'></button>
                    </div>
                ";
                error_log("Assignment creation error: " . $e->getMessage());
            }
        }
    }
    
    if ($action === 'delete_assignment') {
        $assignment_id = $_POST['assignment_id'] ?? 0;
        
        try {
            $stmt = $conn->prepare("
                DELETE a FROM assignments a
                INNER JOIN courses c ON a.course_id = c.id
                WHERE a.id = ? AND c.instructor_id = ?
            ");
            $affected_rows = $stmt->execute([$assignment_id, $_SESSION['user_id']]);

            if ($stmt->rowCount() > 0) {
                $success_message = "
                    <div class='alert alert-success alert-dismissible fade show' role='alert'>
                        <div class='d-flex align-items-center'>
                            <i class='fas fa-check-circle text-success me-2'></i>
                            <strong>تم حذف الواجب بنجاح!</strong>
                        </div>
                        <hr>
                        <p class='mb-0'>تم حذف الواجب وجميع التسليمات المرتبطة به من النظام.</p>
                        <button type='button' class='btn-close' data-bs-dismiss='alert'></button>
                    </div>
                ";
            } else {
                $error_message = "
                    <div class='alert alert-warning alert-dismissible fade show' role='alert'>
                        <div class='d-flex align-items-center'>
                            <i class='fas fa-exclamation-triangle text-warning me-2'></i>
                            <strong>لم يتم العثور على الواجب!</strong>
                        </div>
                        <hr>
                        <p class='mb-0'>الواجب المطلوب حذفه غير موجود أو لا تملك صلاحية لحذفه.</p>
                        <button type='button' class='btn-close' data-bs-dismiss='alert'></button>
                    </div>
                ";
            }
        } catch (PDOException $e) {
            $error_message = "
                <div class='alert alert-danger alert-dismissible fade show' role='alert'>
                    <div class='d-flex align-items-center'>
                        <i class='fas fa-exclamation-circle text-danger me-2'></i>
                        <strong>خطأ في حذف الواجب!</strong>
                    </div>
                    <hr>
                    <p class='mb-1'>حدث خطأ أثناء حذف الواجب. يرجى المحاولة مرة أخرى.</p>
                    <small class='text-muted'>قد يكون هناك تسليمات مرتبطة بالواجب تمنع حذفه.</small>
                    <button type='button' class='btn-close' data-bs-dismiss='alert'></button>
                </div>
            ";
            error_log("Assignment deletion error: " . $e->getMessage());
        }
    }
}

// فلاتر البحث
$course_filter = $_GET['course_id'] ?? '';
$status_filter = $_GET['status'] ?? '';

// جلب الكورسات الخاصة بالمدرب
try {
    $stmt = $conn->prepare("
        SELECT id, title 
        FROM courses 
        WHERE instructor_id = ? AND status = 'active'
        ORDER BY title
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $instructor_courses = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $instructor_courses = [];
}

// جلب الواجبات
try {
    $where_conditions = ["c.instructor_id = ?"];
    $params = [$_SESSION['user_id']];
    
    if (!empty($course_filter)) {
        $where_conditions[] = "c.id = ?";
        $params[] = $course_filter;
    }
    
    if (!empty($status_filter)) {
        switch ($status_filter) {
            case 'active':
                $where_conditions[] = "a.due_date >= CURDATE()";
                break;
            case 'expired':
                $where_conditions[] = "a.due_date < CURDATE()";
                break;
            case 'required':
                $where_conditions[] = "a.is_required = 1";
                break;
        }
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    $stmt = $conn->prepare("
        SELECT 
            a.*,
            c.title as course_title,
            COUNT(DISTINCT asub.id) as total_submissions,
            COUNT(DISTINCT CASE WHEN asub.status = 'submitted' THEN asub.id END) as submitted_count,
            COUNT(DISTINCT CASE WHEN asub.status = 'graded' THEN asub.id END) as graded_count,
            AVG(CASE WHEN asub.score IS NOT NULL THEN asub.score END) as avg_grade
        FROM assignments a
        INNER JOIN courses c ON a.course_id = c.id
        LEFT JOIN assignment_submissions asub ON a.id = asub.assignment_id
        WHERE $where_clause
        GROUP BY a.id
        ORDER BY a.due_date DESC, a.created_at DESC
    ");
    $stmt->execute($params);
    $assignments = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // إحصائيات سريعة
    $stmt = $conn->prepare("
        SELECT 
            COUNT(DISTINCT a.id) as total_assignments,
            COUNT(DISTINCT CASE WHEN a.due_date >= CURDATE() THEN a.id END) as active_assignments,
            COUNT(DISTINCT CASE WHEN a.due_date < CURDATE() THEN a.id END) as expired_assignments,
            COUNT(DISTINCT asub.id) as total_submissions
        FROM assignments a
        INNER JOIN courses c ON a.course_id = c.id
        LEFT JOIN assignment_submissions asub ON a.id = asub.assignment_id
        WHERE c.instructor_id = ?
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
} catch (PDOException $e) {
    $error_message = 'حدث خطأ أثناء جلب البيانات: ' . $e->getMessage();
    $assignments = [];
    $stats = ['total_assignments' => 0, 'active_assignments' => 0, 'expired_assignments' => 0, 'total_submissions' => 0];
    error_log("Assignments error: " . $e->getMessage());
}

include 'includes/header.php';
?>

<!-- رأس الصفحة -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="h3 mb-2">
            <i class="fas fa-tasks text-primary me-2"></i>
            إدارة الواجبات
        </h2>
        <p class="text-muted mb-0">إنشاء وإدارة واجبات الطلاب في الكورسات</p>
    </div>
    <div class="d-flex gap-2">
        <button class="btn btn-primary btn-lg shadow-sm" data-bs-toggle="modal" data-bs-target="#createAssignmentModal">
            <i class="fas fa-plus me-2"></i>إنشاء واجب جديد
        </button>
        <a href="quizzes.php" class="btn btn-outline-info">
            <i class="fas fa-question-circle me-1"></i>الاختبارات
        </a>
    </div>
</div>

<!-- زر إنشاء واجب بارز إضافي -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm bg-gradient" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
            <div class="card-body text-center text-white py-4">
                <div class="row align-items-center">
                    <div class="col-md-8 text-md-start text-center">
                        <h4 class="mb-2">
                            <i class="fas fa-lightbulb me-2"></i>
                            هل تريد إضافة واجب جديد للطلاب؟
                        </h4>
                        <p class="mb-0 opacity-75">
                            أنشئ واجباً جديداً وحدد تاريخ التسليم والدرجات، وسيتم إشعار الطلاب تلقائياً
                        </p>
                    </div>
                    <div class="col-md-4 text-center mt-3 mt-md-0">
                        <button class="btn btn-light btn-lg px-4 py-2 shadow" data-bs-toggle="modal" data-bs-target="#createAssignmentModal">
                            <i class="fas fa-plus me-2 text-primary"></i>
                            <strong>إنشاء واجب الآن</strong>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- رسائل النجاح والخطأ -->
<?php if (isset($success_message)): ?>
    <?php echo $success_message; ?>
<?php endif; ?>

<?php if (isset($error_message)): ?>
    <?php echo $error_message; ?>
<?php endif; ?>

<!-- بطاقات الإحصائيات -->
<div class="row g-4 mb-4">
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-primary bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-tasks text-primary fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h5 class="mb-1"><?php echo $stats['total_assignments']; ?></h5>
                        <p class="text-muted mb-0">إجمالي الواجبات</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-success bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-clock text-success fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h5 class="mb-1"><?php echo $stats['active_assignments']; ?></h5>
                        <p class="text-muted mb-0">واجبات نشطة</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-warning bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-exclamation-triangle text-warning fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h5 class="mb-1"><?php echo $stats['expired_assignments']; ?></h5>
                        <p class="text-muted mb-0">واجبات منتهية</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-info bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-file-upload text-info fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h5 class="mb-1"><?php echo $stats['total_submissions']; ?></h5>
                        <p class="text-muted mb-0">إجمالي التسليمات</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- فلاتر البحث -->
<div class="card border-0 shadow-sm mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3 align-items-end">
            <div class="col-md-5">
                <label for="course_id" class="form-label">الكورس</label>
                <select name="course_id" id="course_id" class="form-select">
                    <option value="">جميع الكورسات</option>
                    <?php foreach ($instructor_courses as $course): ?>
                    <option value="<?php echo $course['id']; ?>" 
                            <?php echo $course_filter == $course['id'] ? 'selected' : ''; ?>>
                        <?php echo htmlspecialchars($course['title']); ?>
                    </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-4">
                <label for="status" class="form-label">الحالة</label>
                <select name="status" id="status" class="form-select">
                    <option value="">جميع الحالات</option>
                    <option value="active" <?php echo $status_filter == 'active' ? 'selected' : ''; ?>>نشطة</option>
                    <option value="expired" <?php echo $status_filter == 'expired' ? 'selected' : ''; ?>>منتهية</option>
                    <option value="required" <?php echo $status_filter == 'required' ? 'selected' : ''; ?>>مطلوبة</option>
                </select>
            </div>
            <div class="col-md-3">
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-search me-1"></i>بحث
                </button>
            </div>
        </form>
    </div>
</div>

<!-- قائمة الواجبات -->
<div class="card border-0 shadow-sm">
    <div class="card-header bg-white border-bottom">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>
            قائمة الواجبات
        </h5>
    </div>
    <div class="card-body p-0">
        <?php if (empty($assignments)): ?>
        <div class="text-center py-5">
            <div class="mb-4">
                <i class="fas fa-tasks text-muted" style="font-size: 4rem; opacity: 0.3;"></i>
            </div>
            <h4 class="text-muted mb-3">لا توجد واجبات حتى الآن</h4>
            <p class="text-muted mb-4">ابدأ رحلتك التعليمية بإنشاء أول واجب للطلاب</p>

            <div class="d-flex flex-column align-items-center gap-3">
                <button class="btn btn-primary btn-lg px-5 py-3 shadow" data-bs-toggle="modal" data-bs-target="#createAssignmentModal">
                    <i class="fas fa-plus me-2"></i>
                    <strong>إنشاء أول واجب</strong>
                </button>

                <div class="text-muted small">
                    <i class="fas fa-info-circle me-1"></i>
                    سيتم إشعار الطلاب تلقائياً عند إنشاء الواجب
                </div>
            </div>

            <!-- نصائح سريعة -->
            <div class="row mt-5 text-start">
                <div class="col-md-4">
                    <div class="card border-0 bg-light h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-clock text-primary mb-2" style="font-size: 2rem;"></i>
                            <h6>حدد موعد التسليم</h6>
                            <small class="text-muted">اختر تاريخ ووقت مناسب للطلاب</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card border-0 bg-light h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-star text-warning mb-2" style="font-size: 2rem;"></i>
                            <h6>حدد الدرجات</h6>
                            <small class="text-muted">ضع درجة مناسبة لأهمية الواجب</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card border-0 bg-light h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-bell text-success mb-2" style="font-size: 2rem;"></i>
                            <h6>إشعار تلقائي</h6>
                            <small class="text-muted">سيتم إشعار جميع الطلاب فوراً</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php else: ?>
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>الواجب</th>
                        <th>الكورس</th>
                        <th>تاريخ التسليم</th>
                        <th>الحالة</th>
                        <th>التسليمات</th>
                        <th>متوسط الدرجات</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($assignments as $assignment): ?>
                    <tr>
                        <td>
                            <div>
                                <h6 class="mb-1"><?php echo htmlspecialchars($assignment['title']); ?></h6>
                                <?php if ($assignment['description']): ?>
                                <small class="text-muted">
                                    <?php echo mb_substr(htmlspecialchars($assignment['description']), 0, 50) . '...'; ?>
                                </small>
                                <?php endif; ?>
                                <?php if ($assignment['is_required']): ?>
                                <br><span class="badge bg-warning text-dark">مطلوب</span>
                                <?php endif; ?>
                            </div>
                        </td>
                        <td><?php echo htmlspecialchars($assignment['course_title']); ?></td>
                        <td>
                            <?php 
                            $due_date = new DateTime($assignment['due_date']);
                            $now = new DateTime();
                            $is_expired = $due_date < $now;
                            ?>
                            <span class="<?php echo $is_expired ? 'text-danger' : 'text-muted'; ?>">
                                <?php echo $due_date->format('Y-m-d H:i'); ?>
                            </span>
                            <?php if ($is_expired): ?>
                            <br><small class="text-danger">منتهي</small>
                            <?php else: ?>
                            <br><small class="text-success">
                                <?php 
                                $diff = $now->diff($due_date);
                                echo $diff->days . ' يوم متبقي';
                                ?>
                            </small>
                            <?php endif; ?>
                        </td>
                        <td>
                            <?php if ($is_expired): ?>
                            <span class="badge bg-danger">منتهي</span>
                            <?php else: ?>
                            <span class="badge bg-success">نشط</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <div class="text-center">
                                <strong class="text-primary"><?php echo $assignment['submitted_count']; ?></strong>
                                <span class="text-muted">/ <?php echo $assignment['total_submissions']; ?></span>
                                <br><small class="text-muted">
                                    <?php echo $assignment['graded_count']; ?> مُقيم
                                </small>
                            </div>
                        </td>
                        <td>
                            <?php if ($assignment['avg_grade']): ?>
                            <div class="text-center">
                                <strong class="text-success"><?php echo number_format($assignment['avg_grade'], 1); ?></strong>
                                <br><small class="text-muted">من <?php echo $assignment['max_grade']; ?></small>
                            </div>
                            <?php else: ?>
                            <span class="text-muted">لا توجد درجات</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="assignment-submissions.php?id=<?php echo $assignment['id']; ?>" 
                                   class="btn btn-sm btn-outline-primary" title="عرض التسليمات">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="edit-assignment.php?id=<?php echo $assignment['id']; ?>" 
                                   class="btn btn-sm btn-outline-warning" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button class="btn btn-sm btn-outline-danger" 
                                        onclick="deleteAssignment(<?php echo $assignment['id']; ?>, '<?php echo htmlspecialchars($assignment['title'], ENT_QUOTES); ?>')"
                                        title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- Modal إنشاء واجب جديد -->
<div class="modal fade" id="createAssignmentModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">
                    <i class="fas fa-plus me-2"></i>إنشاء واجب جديد
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="">
                <div class="modal-body">
                    <input type="hidden" name="action" value="create_assignment">

                    <!-- رسالة تنبيه -->
                    <div class="alert alert-info border-0 mb-4">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>ملاحظة:</strong> سيتم إشعار جميع الطلاب المسجلين في الكورس تلقائياً عند إنشاء الواجب.
                    </div>

                    <!-- الكورس -->
                    <div class="mb-4">
                        <label for="course_id" class="form-label fw-bold">
                            <i class="fas fa-book me-1 text-primary"></i>الكورس <span class="text-danger">*</span>
                        </label>
                        <select name="course_id" id="course_id" class="form-select form-select-lg" required>
                            <option value="">-- اختر الكورس --</option>
                            <?php foreach ($instructor_courses as $course): ?>
                            <option value="<?php echo $course['id']; ?>">
                                <?php echo htmlspecialchars($course['title']); ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <!-- عنوان الواجب -->
                    <div class="mb-4">
                        <label for="title" class="form-label fw-bold">
                            <i class="fas fa-heading me-1 text-primary"></i>عنوان الواجب <span class="text-danger">*</span>
                        </label>
                        <input type="text" name="title" id="title" class="form-control form-control-lg"
                               placeholder="مثال: الواجب الأول - مقدمة في البرمجة" required>
                    </div>

                    <!-- وصف الواجب -->
                    <div class="mb-4">
                        <label for="description" class="form-label fw-bold">
                            <i class="fas fa-align-left me-1 text-primary"></i>وصف الواجب
                        </label>
                        <textarea name="description" id="description" class="form-control" rows="3"
                                  placeholder="وصف مختصر عن الواجب وأهدافه..."></textarea>
                    </div>

                    <!-- التاريخ والدرجة -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <label for="due_date" class="form-label fw-bold">
                                <i class="fas fa-calendar me-1 text-primary"></i>تاريخ التسليم <span class="text-danger">*</span>
                            </label>
                            <input type="datetime-local" name="due_date" id="due_date" class="form-control"
                                   value="<?php echo date('Y-m-d\TH:i', strtotime('+1 week')); ?>" required>
                        </div>
                        <div class="col-md-6">
                            <label for="max_grade" class="form-label fw-bold">
                                <i class="fas fa-star me-1 text-primary"></i>الدرجة الكاملة <span class="text-danger">*</span>
                            </label>
                            <input type="number" name="max_grade" id="max_grade" class="form-control"
                                   value="100" min="1" max="1000" required>
                        </div>
                    </div>

                    <!-- تعليمات الواجب -->
                    <div class="mb-4">
                        <label for="instructions" class="form-label fw-bold">
                            <i class="fas fa-list-ul me-1 text-primary"></i>تعليمات الواجب
                        </label>
                        <textarea name="instructions" id="instructions" class="form-control" rows="4"
                                  placeholder="تعليمات مفصلة للطلاب حول كيفية إنجاز الواجب..."></textarea>
                    </div>

                    <!-- خيارات إضافية -->
                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" name="is_required" id="is_required" value="1">
                            <label class="form-check-label fw-bold" for="is_required">
                                <i class="fas fa-exclamation-triangle text-warning me-1"></i>
                                واجب إجباري (مطلوب)
                            </label>
                        </div>
                    </div>
                </div>

                <div class="modal-footer bg-light">
                    <button type="button" class="btn btn-secondary btn-lg" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>إلغاء
                    </button>
                    <button type="submit" class="btn btn-primary btn-lg px-4">
                        <i class="fas fa-check me-2"></i>إنشاء الواجب الآن
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal حذف واجب -->
<div class="modal fade" id="deleteAssignmentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title text-danger">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="delete_assignment">
                    <input type="hidden" name="assignment_id" id="deleteAssignmentId">

                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        هل أنت متأكد من حذف الواجب <strong id="deleteAssignmentTitle"></strong>؟
                    </div>

                    <p class="text-muted">
                        <strong>تحذير:</strong> سيتم حذف الواجب وجميع التسليمات والدرجات المرتبطة به نهائياً.
                        هذا الإجراء لا يمكن التراجع عنه.
                    </p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-danger">تأكيد الحذف</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- زر عائم لإنشاء واجب -->
<div class="floating-action-btn">
    <button class="btn btn-primary btn-lg rounded-circle shadow-lg"
            data-bs-toggle="modal"
            data-bs-target="#createAssignmentModal"
            title="إنشاء واجب جديد"
            style="position: fixed; bottom: 30px; right: 30px; width: 60px; height: 60px; z-index: 1000;">
        <i class="fas fa-plus" style="font-size: 1.5rem;"></i>
    </button>
</div>

<style>
.floating-action-btn button {
    transition: all 0.3s ease;
    box-shadow: 0 4px 20px rgba(0,123,255,0.3);
}

.floating-action-btn button:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 25px rgba(0,123,255,0.4);
}

.floating-action-btn button:active {
    transform: scale(0.95);
}

/* تحسين البطاقة المتدرجة */
.bg-gradient {
    position: relative;
    overflow: hidden;
}

.bg-gradient::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    pointer-events: none;
}

/* تحسين الأزرار */
.btn-lg.shadow {
    transition: all 0.3s ease;
}

.btn-lg.shadow:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* تحسين البطاقات الفارغة */
.card.bg-light {
    transition: all 0.3s ease;
}

.card.bg-light:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

/* تحسين الجدول */
.table-hover tbody tr:hover {
    background-color: rgba(0,123,255,0.05);
    transform: scale(1.01);
    transition: all 0.2s ease;
}

/* تحسين الإحصائيات */
.card.shadow-sm:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1) !important;
    transition: all 0.3s ease;
}
</style>

<script>
// حذف واجب
function deleteAssignment(assignmentId, assignmentTitle) {
    document.getElementById('deleteAssignmentId').value = assignmentId;
    document.getElementById('deleteAssignmentTitle').textContent = assignmentTitle;

    new bootstrap.Modal(document.getElementById('deleteAssignmentModal')).show();
}

// تحديث الحد الأدنى لتاريخ التسليم
document.addEventListener('DOMContentLoaded', function() {
    const dueDateInput = document.getElementById('due_date');
    if (dueDateInput) {
        // تعيين الحد الأدنى للتاريخ (الآن)
        const now = new Date();
        dueDateInput.min = now.toISOString().slice(0, 16);
    }

    // التحقق البسيط من النموذج
    const createForm = document.querySelector('#createAssignmentModal form');
    if (createForm) {
        createForm.addEventListener('submit', function(e) {
            const title = document.getElementById('title').value.trim();
            const courseId = document.getElementById('course_id').value;
            const dueDate = document.getElementById('due_date').value;
            const maxGrade = document.getElementById('max_grade').value;

            // التحقق من الحقول المطلوبة
            if (!title) {
                alert('يرجى إدخال عنوان الواجب');
                document.getElementById('title').focus();
                e.preventDefault();
                return false;
            }

            if (!courseId) {
                alert('يرجى اختيار الكورس');
                document.getElementById('course_id').focus();
                e.preventDefault();
                return false;
            }

            if (!dueDate) {
                alert('يرجى تحديد تاريخ التسليم');
                document.getElementById('due_date').focus();
                e.preventDefault();
                return false;
            }

            // التحقق من صحة التاريخ
            const selectedDate = new Date(dueDate);
            const now = new Date();
            if (selectedDate <= now) {
                alert('تاريخ التسليم يجب أن يكون في المستقبل');
                document.getElementById('due_date').focus();
                e.preventDefault();
                return false;
            }

            // التحقق من الدرجة
            if (!maxGrade || maxGrade <= 0) {
                alert('يرجى إدخال درجة صحيحة أكبر من صفر');
                document.getElementById('max_grade').focus();
                e.preventDefault();
                return false;
            }

            // تأكيد الإنشاء
            const confirmMessage = `هل أنت متأكد من إنشاء الواجب "${title}" للكورس المحدد؟`;
            if (!confirm(confirmMessage)) {
                e.preventDefault();
                return false;
            }

            // السماح بإرسال النموذج
            return true;
        });
    }


});

// تحسين تجربة إنشاء الواجب
document.addEventListener('DOMContentLoaded', function() {
    const createForm = document.querySelector('#createAssignmentModal form');
    const createBtn = document.getElementById('createAssignmentBtn');

    if (createForm && createBtn) {
        const btnText = createBtn.querySelector('.btn-text');
        const btnLoading = createBtn.querySelector('.btn-loading');

        createForm.addEventListener('submit', function(e) {
            // التحقق من صحة البيانات
            const dueDate = new Date(document.getElementById('due_date').value);
            const now = new Date();
            const courseId = document.getElementById('course_id').value;
            const title = document.getElementById('title').value.trim();

            let hasErrors = false;
            let errorMessage = '';

            // التحقق من الحقول المطلوبة
            if (!courseId) {
                hasErrors = true;
                errorMessage = 'يرجى اختيار الكورس';
                document.getElementById('course_id').classList.add('is-invalid');
            }

            if (!title) {
                hasErrors = true;
                errorMessage = 'يرجى إدخال عنوان الواجب';
                document.getElementById('title').classList.add('is-invalid');
            }

            if (dueDate <= now) {
                hasErrors = true;
                errorMessage = 'تاريخ التسليم يجب أن يكون في المستقبل';
                document.getElementById('due_date').classList.add('is-invalid');
            }

            if (hasErrors) {
                e.preventDefault();
                showNotification(errorMessage, 'error');
                return false;
            }

            // إظهار حالة التحميل
            if (btnText && btnLoading) {
                btnText.classList.add('d-none');
                btnLoading.classList.remove('d-none');
                createBtn.disabled = true;
                createBtn.classList.add('btn-loading-state');
            }

            // إظهار رسالة تحميل
            showNotification('جاري إنشاء الواجب...', 'info');
        });

        // إعادة تعيين النموذج عند إغلاق النافذة المنبثقة
        const createModal = document.getElementById('createAssignmentModal');
        if (createModal) {
            createModal.addEventListener('hidden.bs.modal', function() {
                // إعادة تعيين الزر
                if (btnText && btnLoading) {
                    btnText.classList.remove('d-none');
                    btnLoading.classList.add('d-none');
                    createBtn.disabled = false;
                    createBtn.classList.remove('btn-loading-state');
                }

                // إعادة تعيين النموذج
                createForm.reset();
                createForm.querySelectorAll('.is-invalid, .is-valid').forEach(field => {
                    field.classList.remove('is-invalid', 'is-valid');
                });

                // إعادة تعيين التاريخ الافتراضي
                const tomorrow = new Date();
                tomorrow.setDate(tomorrow.getDate() + 1);
                document.getElementById('due_date').value = tomorrow.toISOString().slice(0, 16);
            });
        }

        // التحقق من صحة البيانات في الوقت الفعلي
        const inputs = createForm.querySelectorAll('input[required], select[required], textarea[required]');
        inputs.forEach(input => {
            input.addEventListener('blur', function() {
                validateField(this);
            });

            input.addEventListener('input', function() {
                if (this.classList.contains('is-invalid')) {
                    validateField(this);
                }
            });
        });
    }
});

// دالة التحقق من صحة الحقل
function validateField(field) {
    const value = field.value.trim();
    const fieldType = field.type;
    const fieldId = field.id;

    let isValid = true;

    if (field.hasAttribute('required') && !value) {
        isValid = false;
    }

    if (fieldId === 'due_date' && value) {
        const dueDate = new Date(value);
        const now = new Date();
        if (dueDate <= now) {
            isValid = false;
        }
    }

    if (fieldId === 'max_grade' && value) {
        const grade = parseFloat(value);
        if (grade <= 0) {
            isValid = false;
        }
    }

    if (isValid) {
        field.classList.remove('is-invalid');
        field.classList.add('is-valid');
    } else {
        field.classList.remove('is-valid');
        field.classList.add('is-invalid');
    }

    return isValid;
}

// دالة لإظهار الإشعارات
function showNotification(message, type = 'info') {
    const alertClass = type === 'error' ? 'alert-danger' :
                      type === 'success' ? 'alert-success' :
                      type === 'warning' ? 'alert-warning' : 'alert-info';

    const icon = type === 'error' ? 'fas fa-exclamation-circle' :
                type === 'success' ? 'fas fa-check-circle' :
                type === 'warning' ? 'fas fa-exclamation-triangle' : 'fas fa-info-circle';

    // إزالة الإشعارات السابقة
    const existingNotifications = document.querySelectorAll('.notification-toast');
    existingNotifications.forEach(notification => notification.remove());

    const notification = document.createElement('div');
    notification.className = `alert ${alertClass} alert-dismissible fade show position-fixed notification-toast`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 350px; max-width: 500px;';
    notification.innerHTML = `
        <div class="d-flex align-items-center">
            <i class="${icon} me-2"></i>
            <div class="flex-grow-1">${message}</div>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    document.body.appendChild(notification);

    // إزالة الإشعار تلقائياً بعد 5 ثوان
    setTimeout(() => {
        if (notification.parentNode) {
            notification.classList.remove('show');
            setTimeout(() => notification.remove(), 150);
        }
    }, 5000);
}

// تحديث الصفحة كل 5 دقائق لتحديث حالة الواجبات
setInterval(function() {
    // يمكن إضافة AJAX لتحديث البيانات
    console.log('تحديث حالة الواجبات...');
}, 300000);
</script>

<style>
.table th {
    border-top: none;
    font-weight: 600;
    color: var(--bs-gray-700);
}

.btn-group .btn {
    border-radius: 0.375rem;
}

.btn-group .btn:not(:last-child) {
    margin-right: 0.25rem;
}

.badge {
    font-size: 0.75rem;
}

.card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1) !important;
}

@media (max-width: 768px) {
    .btn-group {
        display: flex;
        flex-direction: column;
        width: 100%;
    }

    .btn-group .btn {
        margin-right: 0;
        margin-bottom: 0.25rem;
    }

    .table-responsive {
        font-size: 0.875rem;
    }
}

/* تحسين عرض التواريخ */
.text-danger {
    font-weight: 500;
}

.text-success {
    font-weight: 500;
}

/* تحسين عرض الإحصائيات */
.card-body .text-center strong {
    font-size: 1.1rem;
}
</style>

<?php include 'includes/footer.php'; ?>
