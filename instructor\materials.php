<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات المدرب
if (!isLoggedIn() || !isInstructor()) {
    header('Location: ../login.php');
    exit;
}

$pageTitle = 'إدارة المواد التعليمية';
$breadcrumbs = [
    ['title' => 'إدارة المواد التعليمية']
];

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'upload_material') {
        $course_id = $_POST['course_id'] ?? 0;
        $chapter_id = $_POST['chapter_id'] ?? 0;
        $title = trim($_POST['title'] ?? '');
        $description = trim($_POST['description'] ?? '');
        $material_type = $_POST['material_type'] ?? 'file';
        $file_url = trim($_POST['file_url'] ?? '');
        $is_downloadable = isset($_POST['is_downloadable']) ? 1 : 0;
        $is_free = isset($_POST['is_free']) ? 1 : 0;
        $order_number = $_POST['order_number'] ?? 1;
        
        $errors = [];
        
        if (empty($title)) {
            $errors[] = 'عنوان المادة مطلوب';
        }
        
        if (empty($course_id)) {
            $errors[] = 'يجب اختيار كورس';
        }
        
        // معالجة رفع الملف
        $file_path = '';
        if ($material_type === 'file' && isset($_FILES['material_file']) && $_FILES['material_file']['error'] === UPLOAD_ERR_OK) {
            $upload_dir = '../uploads/materials/';
            if (!is_dir($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }
            
            $file_extension = pathinfo($_FILES['material_file']['name'], PATHINFO_EXTENSION);
            $allowed_extensions = ['pdf', 'doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx', 'txt', 'zip', 'rar'];
            
            if (!in_array(strtolower($file_extension), $allowed_extensions)) {
                $errors[] = 'نوع الملف غير مدعوم';
            } else {
                $file_name = uniqid() . '.' . $file_extension;
                $file_path = $upload_dir . $file_name;
                
                if (!move_uploaded_file($_FILES['material_file']['tmp_name'], $file_path)) {
                    $errors[] = 'فشل في رفع الملف';
                }
            }
        } elseif ($material_type === 'url' && empty($file_url)) {
            $errors[] = 'رابط المادة مطلوب';
        }
        
        if (empty($errors)) {
            try {
                // التحقق من أن الكورس ينتمي للمدرب
                $stmt = $conn->prepare("SELECT id FROM courses WHERE id = ? AND instructor_id = ?");
                $stmt->execute([$course_id, $_SESSION['user_id']]);
                
                if ($stmt->fetch()) {
                    $final_url = $material_type === 'file' ? $file_path : $file_url;
                    
                    $stmt = $conn->prepare("
                        INSERT INTO course_materials 
                        (course_id, chapter_id, title, description, material_type, file_path, file_url, 
                         is_downloadable, is_free, order_number, uploaded_by) 
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ");
                    $stmt->execute([
                        $course_id, $chapter_id ?: null, $title, $description, $material_type,
                        $material_type === 'file' ? $final_url : null,
                        $material_type === 'url' ? $final_url : null,
                        $is_downloadable, $is_free, $order_number, $_SESSION['user_id']
                    ]);
                    
                    $success_message = 'تم رفع المادة التعليمية بنجاح';
                } else {
                    $error_message = 'الكورس غير موجود أو لا تملك صلاحية للوصول إليه';
                }
            } catch (PDOException $e) {
                $error_message = 'حدث خطأ أثناء رفع المادة';
            }
        }
    }
    
    if ($action === 'delete_material') {
        $material_id = $_POST['material_id'] ?? 0;
        
        try {
            // جلب معلومات المادة لحذف الملف
            $stmt = $conn->prepare("
                SELECT cm.file_path 
                FROM course_materials cm
                INNER JOIN courses c ON cm.course_id = c.id
                WHERE cm.id = ? AND c.instructor_id = ?
            ");
            $stmt->execute([$material_id, $_SESSION['user_id']]);
            $material = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($material) {
                // حذف الملف من الخادم
                if ($material['file_path'] && file_exists($material['file_path'])) {
                    unlink($material['file_path']);
                }
                
                // حذف السجل من قاعدة البيانات
                $stmt = $conn->prepare("
                    DELETE cm FROM course_materials cm
                    INNER JOIN courses c ON cm.course_id = c.id
                    WHERE cm.id = ? AND c.instructor_id = ?
                ");
                $stmt->execute([$material_id, $_SESSION['user_id']]);
                
                $success_message = 'تم حذف المادة التعليمية بنجاح';
            }
        } catch (PDOException $e) {
            $error_message = 'حدث خطأ أثناء حذف المادة';
        }
    }
}

// فلاتر البحث
$course_filter = $_GET['course_id'] ?? '';
$type_filter = $_GET['type'] ?? '';
$status_filter = $_GET['status'] ?? '';

// جلب الكورسات الخاصة بالمدرب
try {
    $stmt = $conn->prepare("
        SELECT id, title 
        FROM courses 
        WHERE instructor_id = ? AND status = 'active'
        ORDER BY title
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $instructor_courses = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $instructor_courses = [];
}

// جلب المواد التعليمية
try {
    $where_conditions = ["c.instructor_id = ?"];
    $params = [$_SESSION['user_id']];
    
    if (!empty($course_filter)) {
        $where_conditions[] = "cm.course_id = ?";
        $params[] = $course_filter;
    }
    
    if (!empty($type_filter)) {
        $where_conditions[] = "cm.material_type = ?";
        $params[] = $type_filter;
    }
    
    if (!empty($status_filter)) {
        if ($status_filter === 'free') {
            $where_conditions[] = "cm.is_free = 1";
        } elseif ($status_filter === 'premium') {
            $where_conditions[] = "cm.is_free = 0";
        } elseif ($status_filter === 'downloadable') {
            $where_conditions[] = "cm.is_downloadable = 1";
        }
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    $stmt = $conn->prepare("
        SELECT 
            cm.*,
            c.title as course_title,
            ch.title as chapter_title,
            COUNT(DISTINCT md.id) as total_downloads
        FROM course_materials cm
        INNER JOIN courses c ON cm.course_id = c.id
        LEFT JOIN course_chapters ch ON cm.chapter_id = ch.id
        LEFT JOIN material_downloads md ON cm.id = md.material_id
        WHERE $where_clause
        GROUP BY cm.id
        ORDER BY c.title, ch.order_number, cm.order_number
    ");
    $stmt->execute($params);
    $materials = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // إحصائيات المواد
    $stmt = $conn->prepare("
        SELECT 
            COUNT(DISTINCT cm.id) as total_materials,
            COUNT(DISTINCT CASE WHEN cm.material_type = 'file' THEN cm.id END) as file_materials,
            COUNT(DISTINCT CASE WHEN cm.material_type = 'url' THEN cm.id END) as url_materials,
            COUNT(DISTINCT CASE WHEN cm.is_free = 1 THEN cm.id END) as free_materials,
            COUNT(DISTINCT md.id) as total_downloads
        FROM course_materials cm
        INNER JOIN courses c ON cm.course_id = c.id
        LEFT JOIN material_downloads md ON cm.id = md.material_id
        WHERE c.instructor_id = ?
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
} catch (PDOException $e) {
    $error_message = 'حدث خطأ أثناء جلب البيانات';
    $materials = [];
    $stats = ['total_materials' => 0, 'file_materials' => 0, 'url_materials' => 0, 'free_materials' => 0, 'total_downloads' => 0];
}

include 'includes/header.php';
?>

<!-- رأس الصفحة -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="h3 mb-2">
            <i class="fas fa-folder-open text-info me-2"></i>
            إدارة المواد التعليمية
        </h2>
        <p class="text-muted mb-0">رفع وإدارة الملفات والمواد التعليمية للكورسات</p>
    </div>
    <div class="d-flex gap-2">
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#uploadMaterialModal">
            <i class="fas fa-upload me-1"></i>رفع مادة جديدة
        </button>
        <button class="btn btn-outline-success" onclick="bulkUpload()">
            <i class="fas fa-layer-group me-1"></i>رفع متعدد
        </button>
    </div>
</div>

<!-- رسائل النجاح والخطأ -->
<?php if (isset($success_message)): ?>
<div class="alert alert-success alert-dismissible fade show" role="alert">
    <i class="fas fa-check-circle me-2"></i>
    <?php echo $success_message; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<?php if (isset($error_message)): ?>
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <i class="fas fa-exclamation-triangle me-2"></i>
    <?php echo $error_message; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<?php if (!empty($errors)): ?>
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <i class="fas fa-exclamation-triangle me-2"></i>
    <ul class="mb-0">
        <?php foreach ($errors as $error): ?>
        <li><?php echo htmlspecialchars($error); ?></li>
        <?php endforeach; ?>
    </ul>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<!-- إحصائيات المواد -->
<div class="row g-4 mb-4">
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-info bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-folder-open text-info fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h5 class="mb-1"><?php echo $stats['total_materials']; ?></h5>
                        <p class="text-muted mb-0">إجمالي المواد</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-primary bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-file text-primary fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h5 class="mb-1"><?php echo $stats['file_materials']; ?></h5>
                        <p class="text-muted mb-0">ملفات</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-success bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-link text-success fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h5 class="mb-1"><?php echo $stats['url_materials']; ?></h5>
                        <p class="text-muted mb-0">روابط</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-warning bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-download text-warning fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h5 class="mb-1"><?php echo $stats['total_downloads']; ?></h5>
                        <p class="text-muted mb-0">تحميلات</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- فلاتر البحث -->
<div class="card border-0 shadow-sm mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3 align-items-end">
            <div class="col-md-3">
                <label for="course_id" class="form-label">الكورس</label>
                <select name="course_id" id="course_id" class="form-select">
                    <option value="">جميع الكورسات</option>
                    <?php foreach ($instructor_courses as $course): ?>
                    <option value="<?php echo $course['id']; ?>"
                            <?php echo $course_filter == $course['id'] ? 'selected' : ''; ?>>
                        <?php echo htmlspecialchars($course['title']); ?>
                    </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-3">
                <label for="type" class="form-label">نوع المادة</label>
                <select name="type" id="type" class="form-select">
                    <option value="">جميع الأنواع</option>
                    <option value="file" <?php echo $type_filter == 'file' ? 'selected' : ''; ?>>ملف</option>
                    <option value="url" <?php echo $type_filter == 'url' ? 'selected' : ''; ?>>رابط</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="status" class="form-label">الحالة</label>
                <select name="status" id="status" class="form-select">
                    <option value="">جميع الحالات</option>
                    <option value="free" <?php echo $status_filter == 'free' ? 'selected' : ''; ?>>مجاني</option>
                    <option value="premium" <?php echo $status_filter == 'premium' ? 'selected' : ''; ?>>مدفوع</option>
                    <option value="downloadable" <?php echo $status_filter == 'downloadable' ? 'selected' : ''; ?>>قابل للتحميل</option>
                </select>
            </div>
            <div class="col-md-3">
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-search me-1"></i>بحث
                </button>
            </div>
        </form>
    </div>
</div>

<!-- قائمة المواد التعليمية -->
<div class="card border-0 shadow-sm">
    <div class="card-header bg-white border-bottom">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>
            قائمة المواد التعليمية
        </h5>
    </div>
    <div class="card-body p-0">
        <?php if (empty($materials)): ?>
        <div class="text-center py-5">
            <i class="fas fa-folder-open text-muted" style="font-size: 3rem;"></i>
            <h5 class="mt-3 text-muted">لا توجد مواد تعليمية</h5>
            <p class="text-muted">ابدأ برفع مواد تعليمية للكورسات</p>
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#uploadMaterialModal">
                <i class="fas fa-upload me-1"></i>رفع مادة جديدة
            </button>
        </div>
        <?php else: ?>
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>المادة</th>
                        <th>الكورس</th>
                        <th>النوع</th>
                        <th>الحالة</th>
                        <th>التحميلات</th>
                        <th>تاريخ الرفع</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($materials as $material): ?>
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0 me-3">
                                    <?php
                                    $icon_class = 'fas fa-file text-secondary';
                                    if ($material['material_type'] === 'url') {
                                        $icon_class = 'fas fa-link text-primary';
                                    } else {
                                        $extension = pathinfo($material['file_path'], PATHINFO_EXTENSION);
                                        switch (strtolower($extension)) {
                                            case 'pdf':
                                                $icon_class = 'fas fa-file-pdf text-danger';
                                                break;
                                            case 'doc':
                                            case 'docx':
                                                $icon_class = 'fas fa-file-word text-primary';
                                                break;
                                            case 'ppt':
                                            case 'pptx':
                                                $icon_class = 'fas fa-file-powerpoint text-warning';
                                                break;
                                            case 'xls':
                                            case 'xlsx':
                                                $icon_class = 'fas fa-file-excel text-success';
                                                break;
                                            case 'zip':
                                            case 'rar':
                                                $icon_class = 'fas fa-file-archive text-info';
                                                break;
                                        }
                                    }
                                    ?>
                                    <i class="<?php echo $icon_class; ?> fs-4"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1"><?php echo htmlspecialchars($material['title']); ?></h6>
                                    <?php if ($material['description']): ?>
                                    <small class="text-muted">
                                        <?php echo mb_substr(htmlspecialchars($material['description']), 0, 80) . '...'; ?>
                                    </small>
                                    <?php endif; ?>
                                    <?php if ($material['chapter_title']): ?>
                                    <br><small class="text-info">
                                        <i class="fas fa-folder me-1"></i><?php echo htmlspecialchars($material['chapter_title']); ?>
                                    </small>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </td>
                        <td><?php echo htmlspecialchars($material['course_title']); ?></td>
                        <td>
                            <?php if ($material['material_type'] === 'file'): ?>
                            <span class="badge bg-primary">ملف</span>
                            <?php else: ?>
                            <span class="badge bg-info">رابط</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <div class="d-flex flex-column gap-1">
                                <?php if ($material['is_free']): ?>
                                <span class="badge bg-success">مجاني</span>
                                <?php else: ?>
                                <span class="badge bg-warning">مدفوع</span>
                                <?php endif; ?>

                                <?php if ($material['is_downloadable']): ?>
                                <span class="badge bg-secondary">قابل للتحميل</span>
                                <?php endif; ?>
                            </div>
                        </td>
                        <td>
                            <div class="text-center">
                                <strong class="text-primary"><?php echo $material['total_downloads']; ?></strong>
                                <br><small class="text-muted">تحميل</small>
                            </div>
                        </td>
                        <td>
                            <small class="text-muted">
                                <?php echo date('Y-m-d H:i', strtotime($material['created_at'])); ?>
                            </small>
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <?php if ($material['material_type'] === 'file'): ?>
                                <a href="<?php echo htmlspecialchars($material['file_path']); ?>"
                                   class="btn btn-sm btn-outline-primary" target="_blank" title="عرض">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="download-material.php?id=<?php echo $material['id']; ?>"
                                   class="btn btn-sm btn-outline-success" title="تحميل">
                                    <i class="fas fa-download"></i>
                                </a>
                                <?php else: ?>
                                <a href="<?php echo htmlspecialchars($material['file_url']); ?>"
                                   class="btn btn-sm btn-outline-primary" target="_blank" title="فتح الرابط">
                                    <i class="fas fa-external-link-alt"></i>
                                </a>
                                <?php endif; ?>
                                <button class="btn btn-sm btn-outline-warning"
                                        onclick="editMaterial(<?php echo $material['id']; ?>)" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-danger"
                                        onclick="deleteMaterial(<?php echo $material['id']; ?>)" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- Modal رفع مادة جديدة -->
<div class="modal fade" id="uploadMaterialModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">رفع مادة تعليمية جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" enctype="multipart/form-data">
                <div class="modal-body">
                    <input type="hidden" name="action" value="upload_material">

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="upload_course_id" class="form-label">الكورس <span class="text-danger">*</span></label>
                            <select name="course_id" id="upload_course_id" class="form-select" required onchange="loadMaterialChapters()">
                                <option value="">-- اختر كورس --</option>
                                <?php foreach ($instructor_courses as $course): ?>
                                <option value="<?php echo $course['id']; ?>">
                                    <?php echo htmlspecialchars($course['title']); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="upload_chapter_id" class="form-label">الفصل (اختياري)</label>
                            <select name="chapter_id" id="upload_chapter_id" class="form-select" disabled>
                                <option value="">-- اختر الكورس أولاً --</option>
                            </select>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="title" class="form-label">عنوان المادة <span class="text-danger">*</span></label>
                        <input type="text" name="title" id="title" class="form-control"
                               placeholder="مثال: ملخص الدرس الأول" required>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">وصف المادة</label>
                        <textarea name="description" id="description" class="form-control" rows="3"
                                  placeholder="وصف مختصر عن محتوى المادة..."></textarea>
                    </div>

                    <!-- نوع المادة -->
                    <div class="mb-3">
                        <label class="form-label">نوع المادة <span class="text-danger">*</span></label>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="material_type"
                                           id="type_file" value="file" checked onchange="toggleMaterialType()">
                                    <label class="form-check-label" for="type_file">
                                        <i class="fas fa-file me-2"></i>رفع ملف
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="material_type"
                                           id="type_url" value="url" onchange="toggleMaterialType()">
                                    <label class="form-check-label" for="type_url">
                                        <i class="fas fa-link me-2"></i>رابط خارجي
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- رفع ملف -->
                    <div id="fileUploadSection" class="mb-3">
                        <label for="material_file" class="form-label">اختر الملف</label>
                        <input type="file" name="material_file" id="material_file" class="form-control"
                               accept=".pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx,.txt,.zip,.rar">
                        <div class="form-text">
                            الملفات المدعومة: PDF, DOC, DOCX, PPT, PPTX, XLS, XLSX, TXT, ZIP, RAR
                            <br>الحد الأقصى: 50 ميجابايت
                        </div>
                    </div>

                    <!-- رابط خارجي -->
                    <div id="urlSection" class="mb-3" style="display: none;">
                        <label for="file_url" class="form-label">رابط المادة</label>
                        <input type="url" name="file_url" id="file_url" class="form-control"
                               placeholder="https://example.com/document.pdf">
                        <div class="form-text">
                            يمكنك إضافة رابط لملف على Google Drive, Dropbox, أو أي موقع آخر
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="order_number" class="form-label">ترتيب المادة</label>
                            <input type="number" name="order_number" id="order_number" class="form-control"
                                   value="1" min="1" required>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" name="is_free" id="is_free">
                                <label class="form-check-label" for="is_free">
                                    <i class="fas fa-unlock text-success me-1"></i>
                                    مادة مجانية
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" name="is_downloadable" id="is_downloadable" checked>
                                <label class="form-check-label" for="is_downloadable">
                                    <i class="fas fa-download text-primary me-1"></i>
                                    قابلة للتحميل
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">رفع المادة</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal حذف مادة -->
<div class="modal fade" id="deleteMaterialModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title text-danger">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="delete_material">
                    <input type="hidden" name="material_id" id="deleteMaterialId">

                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        هل أنت متأكد من حذف هذه المادة التعليمية؟
                    </div>

                    <p class="text-muted">
                        سيتم حذف المادة وجميع إحصائيات التحميل المرتبطة بها نهائياً.
                        هذا الإجراء لا يمكن التراجع عنه.
                    </p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-danger">تأكيد الحذف</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// تبديل نوع المادة
function toggleMaterialType() {
    const fileSection = document.getElementById('fileUploadSection');
    const urlSection = document.getElementById('urlSection');
    const fileRadio = document.getElementById('type_file');
    const materialFile = document.getElementById('material_file');
    const fileUrl = document.getElementById('file_url');

    if (fileRadio.checked) {
        fileSection.style.display = 'block';
        urlSection.style.display = 'none';
        materialFile.required = true;
        fileUrl.required = false;
    } else {
        fileSection.style.display = 'none';
        urlSection.style.display = 'block';
        materialFile.required = false;
        fileUrl.required = true;
    }
}

// تحميل فصول الكورس
function loadMaterialChapters() {
    const courseId = document.getElementById('upload_course_id').value;
    const chapterSelect = document.getElementById('upload_chapter_id');

    if (!courseId) {
        chapterSelect.disabled = true;
        chapterSelect.innerHTML = '<option value="">-- اختر الكورس أولاً --</option>';
        return;
    }

    chapterSelect.disabled = false;
    chapterSelect.innerHTML = '<option value="">-- جاري التحميل... --</option>';

    setTimeout(() => {
        chapterSelect.innerHTML = `
            <option value="">بدون فصل</option>
            <option value="1">الفصل الأول: المقدمة</option>
            <option value="2">الفصل الثاني: الأساسيات</option>
            <option value="3">الفصل الثالث: التطبيق العملي</option>
        `;
    }, 500);
}

// تعديل مادة
function editMaterial(materialId) {
    window.location.href = `edit-material.php?id=${materialId}`;
}

// حذف مادة
function deleteMaterial(materialId) {
    document.getElementById('deleteMaterialId').value = materialId;
    new bootstrap.Modal(document.getElementById('deleteMaterialModal')).show();
}

// رفع متعدد
function bulkUpload() {
    window.location.href = 'bulk-upload-materials.php';
}

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تحسين نموذج الرفع
    const uploadForm = document.querySelector('#uploadMaterialModal form');
    if (uploadForm) {
        uploadForm.addEventListener('submit', function() {
            const submitButton = this.querySelector('button[type="submit"]');
            if (submitButton) {
                submitButton.disabled = true;
                submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري الرفع...';
            }
        });
    }

    // مراقبة حجم الملف
    const fileInput = document.getElementById('material_file');
    if (fileInput) {
        fileInput.addEventListener('change', function() {
            const file = this.files[0];
            if (file) {
                const maxSize = 50 * 1024 * 1024; // 50 MB
                if (file.size > maxSize) {
                    alert('حجم الملف كبير جداً. الحد الأقصى 50 ميجابايت');
                    this.value = '';
                    return;
                }

                // تحديث اسم المادة تلقائياً
                const titleInput = document.getElementById('title');
                if (!titleInput.value) {
                    titleInput.value = file.name.replace(/\.[^/.]+$/, "");
                }
            }
        });
    }

    // تحسين البحث
    const searchForm = document.querySelector('.card-body form');
    if (searchForm) {
        const selects = searchForm.querySelectorAll('select');
        selects.forEach(select => {
            select.addEventListener('change', function() {
                // يمكن إضافة بحث مباشر عبر AJAX
            });
        });
    }

    // إضافة تأثيرات بصرية
    const tableRows = document.querySelectorAll('tbody tr');
    tableRows.forEach(row => {
        row.addEventListener('mouseenter', function() {
            this.style.backgroundColor = 'rgba(0,123,255,0.05)';
        });

        row.addEventListener('mouseleave', function() {
            this.style.backgroundColor = '';
        });
    });
});

// دوال مساعدة
function exportMaterials() {
    window.open('export-materials.php', '_blank');
}

function materialStatistics() {
    window.location.href = 'material-statistics.php';
}

function organizeMaterials() {
    window.location.href = 'organize-materials.php';
}
</script>

<style>
.table th {
    border-top: none;
    font-weight: 600;
    color: var(--bs-gray-700);
}

.btn-group .btn {
    border-radius: 0.375rem;
    margin-left: 0.25rem;
}

.badge {
    font-size: 0.75rem;
}

.card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1) !important;
}

.form-check-input:checked {
    background-color: var(--bs-primary);
    border-color: var(--bs-primary);
}

.form-switch .form-check-input:checked {
    background-color: var(--bs-success);
    border-color: var(--bs-success);
}

@media (max-width: 768px) {
    .btn-group {
        display: flex;
        flex-direction: column;
        width: 100%;
    }

    .btn-group .btn {
        margin-left: 0;
        margin-bottom: 0.25rem;
        border-radius: 0.375rem;
    }

    .table-responsive {
        font-size: 0.875rem;
    }

    .modal-dialog {
        margin: 0.5rem;
    }

    .d-flex.flex-column.gap-1 {
        flex-direction: row !important;
        gap: 0.25rem !important;
    }
}

/* تحسين عرض الأيقونات */
.fas.fs-4 {
    width: 2rem;
    text-align: center;
}

/* تحسين النماذج */
.form-text {
    font-size: 0.8rem;
    color: var(--bs-gray-600);
}

.form-check-label {
    cursor: pointer;
}

/* تحسين الجداول */
tbody tr {
    transition: background-color 0.3s ease;
}

tbody tr:hover {
    background-color: rgba(0,123,255,0.05);
}

/* تحسين البطاقات */
.card-body .text-primary {
    font-weight: 600;
}
</style>

<?php include 'includes/footer.php'; ?>
