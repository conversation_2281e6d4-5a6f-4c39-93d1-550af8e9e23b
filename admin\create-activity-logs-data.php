<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/auth_functions.php';

// التحقق من صلاحيات المدير
if (!isLoggedIn() || !isAdmin()) {
    header('Location: ../login.php');
    exit();
}

echo "<h2>إنشاء بيانات تجريبية لسجلات الأنشطة</h2>";

try {
    // إنشاء أنشطة تجريبية للمدراء
    $admin_activities = [
        ['إضافة مستخدم جديد', 'تم إضافة مستخدم جديد: أحمد محمد', '/admin/manage-users.php'],
        ['تحديث إعدادات النظام', 'تم تحديث إعدادات العمولة إلى 25%', '/admin/system-settings.php'],
        ['حذف كورس', 'تم حذف كورس: مقدمة في البرمجة', '/admin/manage-courses.php'],
        ['قبول طلب انضمام', 'تم قبول طلب انضمام الطالب: فاطمة أحمد', '/admin/join-requests.php'],
        ['تحديث حالة دفع', 'تم تحديث حالة الدفع رقم 123 إلى مكتملة', '/admin/payments.php'],
        ['إنشاء تصنيف جديد', 'تم إنشاء تصنيف جديد: الأمن السيبراني', '/admin/manage-categories.php'],
        ['تحديث ملف شخصي', 'تم تحديث بيانات الملف الشخصي', '/admin/profile.php'],
        ['عرض تقرير', 'تم عرض تقرير الإيرادات الشهرية', '/admin/reports.php'],
        ['إدارة الجلسات', 'تم إضافة جلسة جديدة للكورس: تطوير المواقع', '/admin/manage-sessions.php'],
        ['مراجعة الأنشطة', 'تم مراجعة سجلات الأنشطة للأسبوع الماضي', '/admin/activity-logs.php']
    ];

    // إنشاء أنشطة تجريبية للمدربين
    $instructor_activities = [
        ['تسجيل دخول', 'تم تسجيل الدخول بنجاح', '/instructor/dashboard.php'],
        ['إنشاء كورس جديد', 'تم إنشاء كورس: أساسيات التصميم الجرافيكي', '/instructor/create-course.php'],
        ['تحديث محتوى كورس', 'تم تحديث محتوى الكورس: البرمجة المتقدمة', '/instructor/edit-course.php'],
        ['إضافة جلسة', 'تم إضافة جلسة جديدة: مقدمة في React', '/instructor/add-session.php'],
        ['رفع ملف', 'تم رفع ملف PDF: دليل المبتدئين', '/instructor/upload-material.php'],
        ['الرد على استفسار', 'تم الرد على استفسار الطالب حول الواجب', '/instructor/messages.php'],
        ['تحديث الملف الشخصي', 'تم تحديث السيرة الذاتية والتخصصات', '/instructor/profile.php'],
        ['مراجعة الواجبات', 'تم مراجعة وتقييم 15 واجب', '/instructor/assignments.php'],
        ['بدء جلسة مباشرة', 'تم بدء الجلسة المباشرة: أساسيات قواعد البيانات', '/instructor/live-session.php'],
        ['تحديث الجدول', 'تم تحديث جدول الجلسات للأسبوع القادم', '/instructor/schedule.php']
    ];

    // إنشاء أنشطة تجريبية للطلاب
    $student_activities = [
        ['تسجيل دخول', 'تم تسجيل الدخول بنجاح', '/student/dashboard.php'],
        ['التسجيل في كورس', 'تم التسجيل في كورس: تطوير تطبيقات الجوال', '/student/enroll-course.php'],
        ['حضور جلسة', 'تم حضور الجلسة: مقدمة في JavaScript', '/student/join-session.php'],
        ['تحميل مادة', 'تم تحميل ملف: شرح الدرس الثالث', '/student/download-material.php'],
        ['إرسال واجب', 'تم إرسال الواجب الأول لكورس البرمجة', '/student/submit-assignment.php'],
        ['مشاهدة فيديو', 'تم مشاهدة فيديو: أساسيات HTML', '/student/watch-video.php'],
        ['تحديث الملف الشخصي', 'تم تحديث معلومات الاتصال', '/student/profile.php'],
        ['إجراء اختبار', 'تم إجراء اختبار الوحدة الثانية', '/student/take-quiz.php'],
        ['طلب شهادة', 'تم طلب شهادة إتمام الكورس', '/student/request-certificate.php'],
        ['تقييم كورس', 'تم تقييم كورس التسويق الرقمي بـ 5 نجوم', '/student/rate-course.php']
    ];

    // جلب المستخدمين الموجودين
    $admins = $conn->query("SELECT id FROM users WHERE role = 'admin' LIMIT 3")->fetchAll();
    $instructors = $conn->query("SELECT id FROM users WHERE role = 'instructor' LIMIT 5")->fetchAll();
    $students = $conn->query("SELECT id FROM users WHERE role = 'student' LIMIT 10")->fetchAll();

    $total_added = 0;

    // إضافة أنشطة المدراء
    foreach ($admins as $admin) {
        foreach ($admin_activities as $activity) {
            $stmt = $conn->prepare("
                INSERT INTO activity_logs (user_id, user_type, action, description, page_url, ip_address, user_agent, created_at)
                VALUES (?, 'admin', ?, ?, ?, ?, ?, DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 30) DAY))
            ");
            $stmt->execute([
                $admin['id'],
                $activity[0],
                $activity[1],
                $activity[2],
                '192.168.1.' . rand(10, 50),
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            ]);
            $total_added++;
        }
    }

    // إضافة أنشطة المدربين
    foreach ($instructors as $instructor) {
        foreach ($instructor_activities as $activity) {
            $stmt = $conn->prepare("
                INSERT INTO activity_logs (user_id, user_type, action, description, page_url, ip_address, user_agent, created_at)
                VALUES (?, 'instructor', ?, ?, ?, ?, ?, DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 20) DAY))
            ");
            $stmt->execute([
                $instructor['id'],
                $activity[0],
                $activity[1],
                $activity[2],
                '192.168.1.' . rand(51, 100),
                'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
            ]);
            $total_added++;
        }
    }

    // إضافة أنشطة الطلاب
    foreach ($students as $student) {
        foreach ($student_activities as $activity) {
            $stmt = $conn->prepare("
                INSERT INTO activity_logs (user_id, user_type, action, description, page_url, ip_address, user_agent, created_at)
                VALUES (?, 'student', ?, ?, ?, ?, ?, DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 15) DAY))
            ");
            $stmt->execute([
                $student['id'],
                $activity[0],
                $activity[1],
                $activity[2],
                '192.168.1.' . rand(101, 200),
                'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15'
            ]);
            $total_added++;
        }
    }

    // إضافة أنشطة إضافية للزوار
    $visitor_activities = [
        ['زيارة الصفحة الرئيسية', 'زائر جديد دخل الموقع', '/'],
        ['البحث عن كورس', 'بحث عن "تطوير المواقع"', '/search.php'],
        ['عرض تفاصيل كورس', 'عرض تفاصيل كورس البرمجة', '/course-details.php'],
        ['زيارة صفحة التسجيل', 'زيارة صفحة إنشاء حساب جديد', '/register.php'],
        ['تحميل كتالوج', 'تحميل كتالوج الكورسات المتاحة', '/downloads/catalog.pdf'],
        ['زيارة صفحة الأسعار', 'مراجعة أسعار الكورسات', '/pricing.php'],
        ['زيارة صفحة من نحن', 'قراءة معلومات عن المنصة', '/about.php'],
        ['زيارة صفحة اتصل بنا', 'عرض معلومات التواصل', '/contact.php'],
        ['مشاركة على وسائل التواصل', 'مشاركة رابط الموقع على فيسبوك', '/'],
        ['الاشتراك في النشرة', 'الاشتراك في النشرة البريدية', '/newsletter.php']
    ];

    for ($i = 0; $i < 50; $i++) {
        $activity = $visitor_activities[array_rand($visitor_activities)];
        $stmt = $conn->prepare("
            INSERT INTO activity_logs (user_id, user_type, action, description, page_url, ip_address, user_agent, created_at)
            VALUES (NULL, 'visitor', ?, ?, ?, ?, ?, DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 7) DAY))
        ");
        $stmt->execute([
            $activity[0],
            $activity[1],
            $activity[2],
            '192.168.1.' . rand(201, 255),
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        ]);
        $total_added++;
    }

    echo "<p>✅ تم إضافة $total_added نشاط تجريبي بنجاح!</p>";

    // عرض الإحصائيات
    $stats = [
        'total' => $conn->query("SELECT COUNT(*) FROM activity_logs")->fetchColumn(),
        'admin' => $conn->query("SELECT COUNT(*) FROM activity_logs WHERE user_type = 'admin'")->fetchColumn(),
        'instructor' => $conn->query("SELECT COUNT(*) FROM activity_logs WHERE user_type = 'instructor'")->fetchColumn(),
        'student' => $conn->query("SELECT COUNT(*) FROM activity_logs WHERE user_type = 'student'")->fetchColumn(),
        'visitor' => $conn->query("SELECT COUNT(*) FROM activity_logs WHERE user_type = 'visitor'")->fetchColumn(),
        'today' => $conn->query("SELECT COUNT(*) FROM activity_logs WHERE DATE(created_at) = CURDATE()")->fetchColumn(),
        'this_week' => $conn->query("SELECT COUNT(*) FROM activity_logs WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)")->fetchColumn()
    ];

    echo "<h3>الإحصائيات الحالية:</h3>";
    echo "<ul>";
    echo "<li>إجمالي الأنشطة: " . number_format($stats['total']) . "</li>";
    echo "<li>أنشطة المدراء: " . number_format($stats['admin']) . "</li>";
    echo "<li>أنشطة المدربين: " . number_format($stats['instructor']) . "</li>";
    echo "<li>أنشطة الطلاب: " . number_format($stats['student']) . "</li>";
    echo "<li>أنشطة الزوار: " . number_format($stats['visitor']) . "</li>";
    echo "<li>أنشطة اليوم: " . number_format($stats['today']) . "</li>";
    echo "<li>أنشطة هذا الأسبوع: " . number_format($stats['this_week']) . "</li>";
    echo "</ul>";

    echo "<h3>✅ تم إنشاء البيانات التجريبية بنجاح!</h3>";

} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
}
?>

<p><a href="activity-logs.php">عرض سجلات الأنشطة</a></p>
<p><a href="dashboard.php">لوحة التحكم</a></p>
