<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات الطالب
if (!isLoggedIn() || !isStudent()) {
    header('Location: ../login.php');
    exit;
}

$pageTitle = 'المكتبة';
$breadcrumbs = [
    ['title' => 'المكتبة']
];

$student_id = $_SESSION['user_id'];
$success = '';
$error = '';

// معالجة تحميل الملف
if (isset($_GET['download']) && isset($_GET['id'])) {
    $resource_id = (int)$_GET['id'];
    
    try {
        // التحقق من صلاحية الوصول للملف
        $stmt = $conn->prepare("
            SELECT lr.*, c.title as course_title
            FROM library_resources lr
            LEFT JOIN courses c ON lr.course_id = c.id
            LEFT JOIN course_enrollments ce ON c.id = ce.course_id AND ce.student_id = ?
            WHERE lr.id = ? 
            AND (
                lr.access_level = 'public' 
                OR (lr.access_level = 'enrolled' AND ce.id IS NOT NULL)
                OR lr.course_id IS NULL
            )
        ");
        $stmt->execute([$student_id, $resource_id]);
        $resource = $stmt->fetch();
        
        if (!$resource) {
            throw new Exception('الملف غير متاح');
        }
        
        // تحديث عداد التحميل
        $stmt = $conn->prepare("UPDATE library_resources SET download_count = download_count + 1 WHERE id = ?");
        $stmt->execute([$resource_id]);
        
        // إعادة توجيه للتحميل
        if ($resource['external_url']) {
            header("Location: " . $resource['external_url']);
        } elseif ($resource['file_path'] && file_exists($resource['file_path'])) {
            header('Content-Type: application/octet-stream');
            header('Content-Disposition: attachment; filename="' . basename($resource['file_path']) . '"');
            header('Content-Length: ' . filesize($resource['file_path']));
            readfile($resource['file_path']);
        } else {
            throw new Exception('الملف غير موجود');
        }
        exit;
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// معاملات البحث والفلترة
$search = $_GET['search'] ?? '';
$category = $_GET['category'] ?? '';
$type = $_GET['type'] ?? '';
$course_filter = $_GET['course'] ?? '';

// بناء استعلام البحث
$where_conditions = [
    "(lr.access_level = 'public' OR (lr.access_level = 'enrolled' AND ce.id IS NOT NULL) OR lr.course_id IS NULL)"
];
$params = [$student_id];

if ($search) {
    $where_conditions[] = "(lr.title LIKE ? OR lr.description LIKE ? OR lr.tags LIKE ?)";
    $search_term = "%$search%";
    $params = array_merge($params, [$search_term, $search_term, $search_term]);
}

if ($category) {
    $where_conditions[] = "lr.category = ?";
    $params[] = $category;
}

if ($type) {
    $where_conditions[] = "lr.resource_type = ?";
    $params[] = $type;
}

if ($course_filter) {
    $where_conditions[] = "lr.course_id = ?";
    $params[] = $course_filter;
}

$where_clause = implode(' AND ', $where_conditions);

try {
    // جلب الموارد
    $stmt = $conn->prepare("
        SELECT lr.*, c.title as course_title, u.name as uploaded_by_name
        FROM library_resources lr
        LEFT JOIN courses c ON lr.course_id = c.id
        LEFT JOIN course_enrollments ce ON c.id = ce.course_id AND ce.student_id = ?
        LEFT JOIN users u ON lr.uploaded_by = u.id
        WHERE $where_clause
        ORDER BY lr.created_at DESC
    ");
    $stmt->execute($params);
    $resources = $stmt->fetchAll();
    
    // جلب التصنيفات المتاحة
    $stmt = $conn->prepare("
        SELECT DISTINCT lr.category
        FROM library_resources lr
        LEFT JOIN courses c ON lr.course_id = c.id
        LEFT JOIN course_enrollments ce ON c.id = ce.course_id AND ce.student_id = ?
        WHERE lr.category IS NOT NULL 
        AND (lr.access_level = 'public' OR (lr.access_level = 'enrolled' AND ce.id IS NOT NULL) OR lr.course_id IS NULL)
        ORDER BY lr.category
    ");
    $stmt->execute([$student_id]);
    $categories = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    // جلب الكورسات المتاحة
    $stmt = $conn->prepare("
        SELECT DISTINCT c.id, c.title
        FROM courses c
        JOIN course_enrollments ce ON c.id = ce.course_id
        JOIN library_resources lr ON c.id = lr.course_id
        WHERE ce.student_id = ?
        ORDER BY c.title
    ");
    $stmt->execute([$student_id]);
    $courses = $stmt->fetchAll();
    
    // إحصائيات
    $stats = [
        'total_resources' => count($resources),
        'pdf_count' => count(array_filter($resources, fn($r) => $r['resource_type'] === 'pdf')),
        'video_count' => count(array_filter($resources, fn($r) => $r['resource_type'] === 'video')),
        'document_count' => count(array_filter($resources, fn($r) => in_array($r['resource_type'], ['document', 'pdf'])))
    ];
    
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء جلب الموارد';
}

include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- العنوان -->
        <div class="col-12 mb-4">
            <h2 class="text-primary">
                <i class="fas fa-book-open me-2"></i>
                المكتبة الرقمية
            </h2>
            <p class="text-muted">مجموعة شاملة من الموارد التعليمية والمواد المرجعية</p>
        </div>

        <?php if ($error): ?>
        <div class="col-12 mb-3">
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-circle me-2"></i>
                <?php echo htmlspecialchars($error); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </div>
        <?php endif; ?>

        <!-- الإحصائيات -->
        <div class="col-12 mb-4">
            <div class="row">
                <div class="col-md-3 mb-3">
                    <div class="card-student text-center">
                        <div class="card-body">
                            <i class="fas fa-folder fa-2x text-primary mb-2"></i>
                            <h4 class="text-primary"><?php echo $stats['total_resources']; ?></h4>
                            <p class="text-muted mb-0">إجمالي الموارد</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card-student text-center">
                        <div class="card-body">
                            <i class="fas fa-file-pdf fa-2x text-danger mb-2"></i>
                            <h4 class="text-danger"><?php echo $stats['pdf_count']; ?></h4>
                            <p class="text-muted mb-0">ملفات PDF</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card-student text-center">
                        <div class="card-body">
                            <i class="fas fa-video fa-2x text-success mb-2"></i>
                            <h4 class="text-success"><?php echo $stats['video_count']; ?></h4>
                            <p class="text-muted mb-0">مقاطع فيديو</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card-student text-center">
                        <div class="card-body">
                            <i class="fas fa-file-alt fa-2x text-info mb-2"></i>
                            <h4 class="text-info"><?php echo $stats['document_count']; ?></h4>
                            <p class="text-muted mb-0">مستندات</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- فلاتر البحث -->
        <div class="col-12 mb-4">
            <div class="card-student">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label class="form-label">البحث</label>
                            <input type="text" name="search" class="form-control" 
                                   placeholder="ابحث في الموارد..." value="<?php echo htmlspecialchars($search); ?>">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">التصنيف</label>
                            <select name="category" class="form-select">
                                <option value="">جميع التصنيفات</option>
                                <?php foreach ($categories as $cat): ?>
                                <option value="<?php echo htmlspecialchars($cat); ?>" 
                                        <?php echo $category === $cat ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($cat); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">نوع الملف</label>
                            <select name="type" class="form-select">
                                <option value="">جميع الأنواع</option>
                                <option value="pdf" <?php echo $type === 'pdf' ? 'selected' : ''; ?>>PDF</option>
                                <option value="video" <?php echo $type === 'video' ? 'selected' : ''; ?>>فيديو</option>
                                <option value="audio" <?php echo $type === 'audio' ? 'selected' : ''; ?>>صوت</option>
                                <option value="document" <?php echo $type === 'document' ? 'selected' : ''; ?>>مستند</option>
                                <option value="image" <?php echo $type === 'image' ? 'selected' : ''; ?>>صورة</option>
                                <option value="link" <?php echo $type === 'link' ? 'selected' : ''; ?>>رابط</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">الكورس</label>
                            <select name="course" class="form-select">
                                <option value="">جميع الكورسات</option>
                                <?php foreach ($courses as $course): ?>
                                <option value="<?php echo $course['id']; ?>" 
                                        <?php echo $course_filter == $course['id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($course['title']); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-1">
                            <label class="form-label">&nbsp;</label>
                            <button type="submit" class="btn btn-student-primary w-100">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- قائمة الموارد -->
        <div class="col-12">
            <?php if (empty($resources)): ?>
            <div class="card-student text-center py-5">
                <div class="card-body">
                    <i class="fas fa-book-open fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">لا توجد موارد</h4>
                    <p class="text-muted">لم يتم العثور على موارد تطابق معايير البحث</p>
                </div>
            </div>
            <?php else: ?>
            <div class="row">
                <?php foreach ($resources as $resource): ?>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card-student h-100 resource-card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <span class="badge bg-primary">
                                <?php echo htmlspecialchars($resource['category'] ?? 'عام'); ?>
                            </span>
                            <span class="resource-type-icon">
                                <?php
                                $type_icons = [
                                    'pdf' => 'fas fa-file-pdf text-danger',
                                    'video' => 'fas fa-video text-success',
                                    'audio' => 'fas fa-volume-up text-warning',
                                    'document' => 'fas fa-file-alt text-info',
                                    'image' => 'fas fa-image text-primary',
                                    'link' => 'fas fa-link text-secondary'
                                ];
                                $icon_class = $type_icons[$resource['resource_type']] ?? 'fas fa-file text-muted';
                                ?>
                                <i class="<?php echo $icon_class; ?> fa-lg"></i>
                            </span>
                        </div>
                        <div class="card-body">
                            <h5 class="card-title"><?php echo htmlspecialchars($resource['title']); ?></h5>
                            <p class="card-text text-muted">
                                <?php echo htmlspecialchars(substr($resource['description'], 0, 100)) . '...'; ?>
                            </p>
                            
                            <div class="resource-meta mb-3">
                                <?php if ($resource['course_title']): ?>
                                <div class="d-flex justify-content-between mb-2">
                                    <span class="text-muted">الكورس:</span>
                                    <span class="badge bg-secondary"><?php echo htmlspecialchars($resource['course_title']); ?></span>
                                </div>
                                <?php endif; ?>
                                
                                <div class="d-flex justify-content-between mb-2">
                                    <span class="text-muted">النوع:</span>
                                    <span>
                                        <?php
                                        $type_names = [
                                            'pdf' => 'PDF',
                                            'video' => 'فيديو',
                                            'audio' => 'صوت',
                                            'document' => 'مستند',
                                            'image' => 'صورة',
                                            'link' => 'رابط'
                                        ];
                                        echo $type_names[$resource['resource_type']] ?? $resource['resource_type'];
                                        ?>
                                    </span>
                                </div>
                                
                                <?php if ($resource['file_size']): ?>
                                <div class="d-flex justify-content-between mb-2">
                                    <span class="text-muted">الحجم:</span>
                                    <span><?php echo formatFileSize($resource['file_size']); ?></span>
                                </div>
                                <?php endif; ?>
                                
                                <div class="d-flex justify-content-between mb-2">
                                    <span class="text-muted">التحميلات:</span>
                                    <span><?php echo number_format($resource['download_count']); ?></span>
                                </div>
                                
                                <div class="d-flex justify-content-between mb-2">
                                    <span class="text-muted">المشاهدات:</span>
                                    <span><?php echo number_format($resource['view_count']); ?></span>
                                </div>
                                
                                <div class="d-flex justify-content-between">
                                    <span class="text-muted">تاريخ الإضافة:</span>
                                    <span><?php echo date('Y-m-d', strtotime($resource['created_at'])); ?></span>
                                </div>
                            </div>
                            
                            <?php if ($resource['tags']): ?>
                            <div class="resource-tags mb-3">
                                <?php
                                $tags = explode(',', $resource['tags']);
                                foreach ($tags as $tag) {
                                    $tag = trim($tag);
                                    if ($tag) {
                                        echo '<span class="badge bg-light text-dark me-1 mb-1">' . htmlspecialchars($tag) . '</span>';
                                    }
                                }
                                ?>
                            </div>
                            <?php endif; ?>
                        </div>
                        <div class="card-footer">
                            <?php if ($resource['resource_type'] === 'link'): ?>
                            <a href="<?php echo htmlspecialchars($resource['external_url']); ?>" 
                               target="_blank" class="btn btn-student-primary w-100">
                                <i class="fas fa-external-link-alt me-1"></i>
                                فتح الرابط
                            </a>
                            <?php else: ?>
                            <a href="?download=1&id=<?php echo $resource['id']; ?>" 
                               class="btn btn-student-primary w-100">
                                <i class="fas fa-download me-1"></i>
                                تحميل
                            </a>
                            <?php endif; ?>
                            
                            <button class="btn btn-outline-info w-100 mt-2" data-bs-toggle="modal" 
                                    data-bs-target="#resourceModal<?php echo $resource['id']; ?>">
                                <i class="fas fa-eye me-1"></i>
                                عرض التفاصيل
                            </button>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Modals تفاصيل الموارد -->
<?php foreach ($resources as $resource): ?>
<div class="modal fade" id="resourceModal<?php echo $resource['id']; ?>" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><?php echo htmlspecialchars($resource['title']); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-8">
                        <div class="mb-3">
                            <h6 class="text-primary">الوصف:</h6>
                            <p><?php echo nl2br(htmlspecialchars($resource['description'])); ?></p>
                        </div>
                        
                        <?php if ($resource['tags']): ?>
                        <div class="mb-3">
                            <h6 class="text-primary">الكلمات المفتاحية:</h6>
                            <div>
                                <?php
                                $tags = explode(',', $resource['tags']);
                                foreach ($tags as $tag) {
                                    $tag = trim($tag);
                                    if ($tag) {
                                        echo '<span class="badge bg-primary me-1 mb-1">' . htmlspecialchars($tag) . '</span>';
                                    }
                                }
                                ?>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                    <div class="col-md-4">
                        <h6 class="text-primary">معلومات الملف:</h6>
                        <ul class="list-unstyled">
                            <li><strong>النوع:</strong> 
                                <?php
                                $type_names = [
                                    'pdf' => 'PDF',
                                    'video' => 'فيديو',
                                    'audio' => 'صوت',
                                    'document' => 'مستند',
                                    'image' => 'صورة',
                                    'link' => 'رابط'
                                ];
                                echo $type_names[$resource['resource_type']] ?? $resource['resource_type'];
                                ?>
                            </li>
                            <?php if ($resource['course_title']): ?>
                            <li><strong>الكورس:</strong> <?php echo htmlspecialchars($resource['course_title']); ?></li>
                            <?php endif; ?>
                            <li><strong>التصنيف:</strong> <?php echo htmlspecialchars($resource['category'] ?? 'عام'); ?></li>
                            <?php if ($resource['file_size']): ?>
                            <li><strong>الحجم:</strong> <?php echo formatFileSize($resource['file_size']); ?></li>
                            <?php endif; ?>
                            <li><strong>التحميلات:</strong> <?php echo number_format($resource['download_count']); ?></li>
                            <li><strong>المشاهدات:</strong> <?php echo number_format($resource['view_count']); ?></li>
                            <li><strong>رفع بواسطة:</strong> <?php echo htmlspecialchars($resource['uploaded_by_name']); ?></li>
                            <li><strong>تاريخ الإضافة:</strong> <?php echo date('Y-m-d H:i', strtotime($resource['created_at'])); ?></li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <?php if ($resource['resource_type'] === 'link'): ?>
                <a href="<?php echo htmlspecialchars($resource['external_url']); ?>" 
                   target="_blank" class="btn btn-student-primary">
                    <i class="fas fa-external-link-alt me-1"></i>
                    فتح الرابط
                </a>
                <?php else: ?>
                <a href="?download=1&id=<?php echo $resource['id']; ?>" 
                   class="btn btn-student-primary">
                    <i class="fas fa-download me-1"></i>
                    تحميل
                </a>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php endforeach; ?>

<style>
.resource-card {
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.resource-card:hover {
    border-color: var(--student-primary);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
}

.resource-type-icon {
    font-size: 1.2rem;
}

.resource-tags .badge {
    font-size: 0.75rem;
}
</style>

<?php
// دالة مساعدة لتنسيق حجم الملف
function formatFileSize($bytes) {
    if ($bytes >= 1073741824) {
        return number_format($bytes / 1073741824, 2) . ' GB';
    } elseif ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' bytes';
    }
}
?>

<?php include 'includes/footer.php'; ?>
