<?php
require_once '../config/database.php';

echo "<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>إصلاح سريع</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css' rel='stylesheet'>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f8f9fa; }
        .container { max-width: 800px; margin: 30px auto; }
        .log-item { padding: 8px; margin: 3px 0; border-radius: 5px; font-size: 14px; }
        .log-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .log-error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .log-info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
<div class='container'>
    <div class='card'>
        <div class='card-header bg-success text-white'>
            <h4 class='mb-0'><i class='fas fa-wrench me-2'></i>إصلاح سريع للمشاكل</h4>
        </div>
        <div class='card-body'>";

function logMessage($message, $type = 'info') {
    $icon = $type === 'success' ? 'check-circle' : ($type === 'error' ? 'exclamation-circle' : 'info-circle');
    echo "<div class='log-item log-$type'><i class='fas fa-$icon me-2'></i>$message</div>";
    flush();
    ob_flush();
}

try {
    logMessage('🔧 بدء الإصلاح السريع...', 'info');
    
    // 1. إضافة بيانات مفقودة للكورسات
    logMessage('=== إصلاح بيانات الكورسات ===', 'info');
    
    // تحديث الكورسات التي لا تحتوي على صور
    $stmt = $conn->prepare("UPDATE courses SET image = 'uploads/courses/default-course.jpg' WHERE image IS NULL OR image = ''");
    $stmt->execute();
    $updated = $stmt->rowCount();
    logMessage("✓ تم تحديث $updated كورس بصور افتراضية", 'success');
    
    // تحديث أنواع الكورسات
    $stmt = $conn->prepare("UPDATE courses SET course_type = 'free', price = 0 WHERE course_type IS NULL OR course_type = ''");
    $stmt->execute();
    $updated = $stmt->rowCount();
    logMessage("✓ تم تحديث $updated كورس بأنواع افتراضية", 'success');
    
    // تحديث حالات الكورسات
    $stmt = $conn->prepare("UPDATE courses SET status = 'active' WHERE status IS NULL OR status = ''");
    $stmt->execute();
    $updated = $stmt->rowCount();
    logMessage("✓ تم تحديث $updated كورس بحالات افتراضية", 'success');
    
    // 2. إضافة بيانات مفقودة للمستخدمين
    logMessage('=== إصلاح بيانات المستخدمين ===', 'info');
    
    // تحديث حالات المستخدمين
    $stmt = $conn->prepare("UPDATE users SET status = 'active' WHERE status IS NULL OR status = ''");
    $stmt->execute();
    $updated = $stmt->rowCount();
    logMessage("✓ تم تحديث $updated مستخدم بحالات افتراضية", 'success');
    
    // إضافة أرقام هواتف تجريبية للمدربين
    $instructors = $conn->query("SELECT id FROM users WHERE role = 'instructor' AND (phone IS NULL OR phone = '')")->fetchAll(PDO::FETCH_COLUMN);
    foreach ($instructors as $instructor_id) {
        $phone = '05' . rand(10000000, 99999999);
        $stmt = $conn->prepare("UPDATE users SET phone = ? WHERE id = ?");
        $stmt->execute([$phone, $instructor_id]);
    }
    logMessage("✓ تم إضافة أرقام هواتف لـ " . count($instructors) . " مدرب", 'success');
    
    // 3. إضافة تسجيلات تجريبية
    logMessage('=== إضافة تسجيلات تجريبية ===', 'info');
    
    $students = $conn->query("SELECT id FROM users WHERE role = 'student' LIMIT 15")->fetchAll(PDO::FETCH_COLUMN);
    $courses = $conn->query("SELECT id, course_type, price FROM courses LIMIT 10")->fetchAll(PDO::FETCH_ASSOC);
    
    $enrollments_added = 0;
    foreach ($students as $student_id) {
        foreach ($courses as $course) {
            if (rand(0, 2) == 0) { // 33% احتمال التسجيل
                try {
                    $payment_amount = $course['course_type'] === 'paid' ? $course['price'] : 0;
                    $payment_status = $course['course_type'] === 'paid' ? 'completed' : 'completed';
                    
                    $stmt = $conn->prepare("
                        INSERT IGNORE INTO course_enrollments (student_id, course_id, payment_status, payment_amount, status) 
                        VALUES (?, ?, ?, ?, 'active')
                    ");
                    $stmt->execute([$student_id, $course['id'], $payment_status, $payment_amount]);
                    if ($stmt->rowCount() > 0) {
                        $enrollments_added++;
                    }
                } catch (Exception $e) {
                    // تجاهل الأخطاء
                }
            }
        }
    }
    logMessage("✓ تم إضافة $enrollments_added تسجيل جديد", 'success');
    
    // 4. إضافة جلسات تجريبية
    logMessage('=== إضافة جلسات تجريبية ===', 'info');
    
    $sessions_added = 0;
    foreach ($courses as $course) {
        $existing_sessions = $conn->query("SELECT COUNT(*) FROM sessions WHERE course_id = " . $course['id'])->fetchColumn();
        
        if ($existing_sessions < 3) {
            $sessions_to_add = 5 - $existing_sessions;
            
            for ($i = 1; $i <= $sessions_to_add; $i++) {
                try {
                    $stmt = $conn->prepare("
                        INSERT INTO sessions (course_id, title, description, session_date, duration, status) 
                        VALUES (?, ?, ?, ?, ?, ?)
                    ");
                    
                    $title = "الجلسة رقم " . ($existing_sessions + $i);
                    $description = "محتوى الجلسة رقم " . ($existing_sessions + $i) . " من الكورس";
                    $session_date = date('Y-m-d H:i:s', strtotime("+$i days +2 hours"));
                    $duration = rand(60, 120);
                    $status = $i <= 2 ? 'completed' : 'scheduled';
                    
                    $stmt->execute([$course['id'], $title, $description, $session_date, $duration, $status]);
                    $sessions_added++;
                } catch (Exception $e) {
                    // تجاهل الأخطاء
                }
            }
        }
    }
    logMessage("✓ تم إضافة $sessions_added جلسة جديدة", 'success');
    
    // 5. إضافة سجلات حضور
    logMessage('=== إضافة سجلات حضور ===', 'info');
    
    $sessions = $conn->query("SELECT id FROM sessions WHERE status = 'completed' LIMIT 20")->fetchAll(PDO::FETCH_COLUMN);
    $attendance_added = 0;
    
    foreach ($sessions as $session_id) {
        foreach ($students as $student_id) {
            if (rand(0, 1) == 0) { // 50% احتمال الحضور
                try {
                    $attendance_status = ['present', 'late'][rand(0, 1)]; // حضور أو تأخير فقط
                    
                    $stmt = $conn->prepare("
                        INSERT IGNORE INTO session_attendance (session_id, student_id, attendance_status) 
                        VALUES (?, ?, ?)
                    ");
                    $stmt->execute([$session_id, $student_id, $attendance_status]);
                    if ($stmt->rowCount() > 0) {
                        $attendance_added++;
                    }
                } catch (Exception $e) {
                    // تجاهل الأخطاء
                }
            }
        }
    }
    logMessage("✓ تم إضافة $attendance_added سجل حضور", 'success');
    
    // 6. إضافة تقييمات
    logMessage('=== إضافة تقييمات ===', 'info');
    
    $reviews_added = 0;
    foreach ($students as $student_id) {
        foreach ($courses as $course) {
            if (rand(0, 3) == 0) { // 25% احتمال إضافة تقييم
                try {
                    $rating = rand(4, 5); // تقييمات عالية
                    $reviews = [
                        'كورس ممتاز ومفيد جداً، استفدت منه كثيراً',
                        'المدرب محترف والمحتوى شامل ومفصل',
                        'أنصح بهذا الكورس بشدة لكل المهتمين',
                        'كورس رائع وطريقة الشرح واضحة ومفهومة',
                        'محتوى قيم وتطبيق عملي ممتاز'
                    ];
                    $review_text = $reviews[array_rand($reviews)];
                    
                    $stmt = $conn->prepare("
                        INSERT IGNORE INTO course_reviews (course_id, student_id, rating, review_text) 
                        VALUES (?, ?, ?, ?)
                    ");
                    $stmt->execute([$course['id'], $student_id, $rating, $review_text]);
                    if ($stmt->rowCount() > 0) {
                        $reviews_added++;
                    }
                } catch (Exception $e) {
                    // تجاهل الأخطاء
                }
            }
        }
    }
    logMessage("✓ تم إضافة $reviews_added تقييم جديد", 'success');
    
    // 7. عرض الإحصائيات النهائية
    logMessage('=== الإحصائيات النهائية ===', 'info');
    
    $final_stats = [
        'الكورسات' => $conn->query("SELECT COUNT(*) FROM courses")->fetchColumn(),
        'المستخدمين' => $conn->query("SELECT COUNT(*) FROM users")->fetchColumn(),
        'التسجيلات' => $conn->query("SELECT COUNT(*) FROM course_enrollments")->fetchColumn(),
        'الجلسات' => $conn->query("SELECT COUNT(*) FROM sessions")->fetchColumn(),
        'سجلات الحضور' => $conn->query("SELECT COUNT(*) FROM session_attendance")->fetchColumn(),
        'التقييمات' => $conn->query("SELECT COUNT(*) FROM course_reviews")->fetchColumn()
    ];
    
    foreach ($final_stats as $key => $value) {
        logMessage("📊 $key: " . number_format($value), 'info');
    }
    
    logMessage('🎉 تم الإصلاح السريع بنجاح!', 'success');
    
} catch (Exception $e) {
    logMessage('❌ خطأ: ' . $e->getMessage(), 'error');
}

echo "
        </div>
        <div class='mt-4 text-center'>
            <div class='alert alert-success'>
                <h5><i class='fas fa-check-circle me-2'></i>تم الإصلاح بنجاح!</h5>
                <p class='mb-0'>الآن جرب الصفحات مرة أخرى</p>
            </div>
            <a href='course-details.php?id=25' class='btn btn-primary btn-lg me-2'>
                <i class='fas fa-eye me-2'></i>تفاصيل الكورس
            </a>
            <a href='edit-course.php?id=25' class='btn btn-warning me-2'>
                <i class='fas fa-edit me-2'></i>تعديل الكورس
            </a>
            <a href='manage-sessions.php?course_id=25' class='btn btn-info me-2'>
                <i class='fas fa-video me-2'></i>إدارة الجلسات
            </a>
            <a href='course-students.php?course_id=25' class='btn btn-success'>
                <i class='fas fa-users me-2'></i>إدارة الطلاب
            </a>
        </div>
    </div>
</div>
</body>
</html>";
?>
