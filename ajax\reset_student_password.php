<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/activity_logger.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// Check if user is logged in and is an instructor
if (!isLoggedIn() || !isInstructor()) {
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

// Get and validate input
$input = json_decode(file_get_contents('php://input'), true);
$student_id = isset($input['student_id']) ? intval($input['student_id']) : 0;
$new_password = isset($input['new_password']) ? trim($input['new_password']) : '';

if (!$student_id || empty($new_password)) {
    echo json_encode(['success' => false, 'message' => 'Invalid input']);
    exit;
}

try {
    // Verify the student belongs to this instructor
    $stmt = $conn->prepare("
        SELECT u.id 
        FROM users u
        JOIN course_enrollments ce ON u.id = ce.student_id
        JOIN courses c ON ce.course_id = c.id
        WHERE u.id = ? AND c.instructor_id = ? AND u.role = 'student'
        LIMIT 1
    ");
    $stmt->execute([$student_id, $_SESSION['user_id']]);
    
    if ($stmt->rowCount() === 0) {
        echo json_encode(['success' => false, 'message' => 'Student not found']);
        exit;
    }

    // Update password
    $stmt = $conn->prepare("UPDATE users SET password = ? WHERE id = ?");
    $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
    $stmt->execute([$hashed_password, $student_id]);

    // Log activity
    logUserActivity('reset_password', "Password reset for student ID: $student_id");

    echo json_encode(['success' => true]);
} catch (PDOException $e) {
    error_log($e->getMessage());
    echo json_encode(['success' => false, 'message' => 'System error']);
}