<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';
require_once '../includes/activity_logger.php';

// التحقق من صلاحيات المدرب
if (!isLoggedIn() || !isInstructor()) {
    header('Location: ../login.php');
    exit;
}

$course_id = isset($_GET['course_id']) ? (int)$_GET['course_id'] : 0;
$instructor_id = $_SESSION['user_id'];

// التحقق من ملكية الكورس
try {
    $stmt = $conn->prepare("SELECT * FROM courses WHERE id = ? AND instructor_id = ?");
    $stmt->execute([$course_id, $instructor_id]);
    $course = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$course) {
        header('Location: courses.php');
        exit;
    }
} catch (PDOException $e) {
    header('Location: courses.php');
    exit;
}

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $request_id = (int)$_POST['request_id'];
    $action = $_POST['action'];
    
    try {
        if ($action === 'approve') {
            // الموافقة على الطلب
            $stmt = $conn->prepare("UPDATE join_requests SET status = 'approved', processed_date = NOW(), processed_by = ? WHERE id = ? AND course_id = ?");
            $stmt->execute([$instructor_id, $request_id, $course_id]);
            
            // إضافة الطالب للكورس (إذا كان مسجل في النظام)
            $stmt = $conn->prepare("SELECT * FROM join_requests WHERE id = ?");
            $stmt->execute([$request_id]);
            $request = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($request) {
                // البحث عن المستخدم بالبريد الإلكتروني
                $stmt = $conn->prepare("SELECT id FROM users WHERE email = ?");
                $stmt->execute([$request['student_email']]);
                $user = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($user) {
                    // إضافة الطالب للكورس
                    $stmt = $conn->prepare("INSERT IGNORE INTO course_enrollments (course_id, student_id) VALUES (?, ?)");
                    $stmt->execute([$course_id, $user['id']]);
                }
                
                logUserActivity('الموافقة على طلب انضمام', "تم الموافقة على طلب انضمام: {$request['student_name']} للكورس: {$course['title']}");
                $success = 'تم الموافقة على الطلب بنجاح';
            }
            
        } elseif ($action === 'reject') {
            $rejection_reason = trim($_POST['rejection_reason']);
            
            // رفض الطلب
            $stmt = $conn->prepare("UPDATE join_requests SET status = 'rejected', processed_date = NOW(), processed_by = ?, rejection_reason = ? WHERE id = ? AND course_id = ?");
            $stmt->execute([$instructor_id, $rejection_reason, $request_id, $course_id]);
            
            logUserActivity('رفض طلب انضمام', "تم رفض طلب انضمام للكورس: {$course['title']}");
            $success = 'تم رفض الطلب';
        }
        
    } catch (PDOException $e) {
        $error = 'حدث خطأ أثناء معالجة الطلب';
    }
}

// جلب طلبات الانضمام
$requests = [];
try {
    $stmt = $conn->prepare("
        SELECT jr.*, u.name as processed_by_name
        FROM join_requests jr
        LEFT JOIN users u ON jr.processed_by = u.id
        WHERE jr.course_id = ?
        ORDER BY jr.request_date DESC
    ");
    $stmt->execute([$course_id]);
    $requests = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $error = 'حدث خطأ في جلب طلبات الانضمام: ' . $e->getMessage();
}

$pageTitle = 'طلبات الانضمام - ' . $course['title'];
include 'includes/header.php';

?>

<div class="container-fluid py-4">
    <!-- معلومات الكورس -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="mb-0">طلبات الانضمام للكورس</h5>
                        <small><?php echo htmlspecialchars($course['title']); ?></small>
                    </div>
                    <div>
                        <a href="course-details.php?id=<?php echo $course_id; ?>" class="btn btn-light btn-sm">
                            <i class="fas fa-arrow-left"></i> العودة
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php if (isset($success)): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo $success; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($error)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- إحصائيات الطلبات -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h3><?php echo count($requests); ?></h3>
                    <p class="mb-0">إجمالي الطلبات</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h3><?php echo count(array_filter($requests, function($r) { return $r['status'] === 'pending'; })); ?></h3>
                    <p class="mb-0">في الانتظار</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h3><?php echo count(array_filter($requests, function($r) { return $r['status'] === 'approved'; })); ?></h3>
                    <p class="mb-0">موافق عليها</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body text-center">
                    <h3><?php echo count(array_filter($requests, function($r) { return $r['status'] === 'rejected'; })); ?></h3>
                    <p class="mb-0">مرفوضة</p>
                </div>
            </div>
        </div>
    </div>

    <!-- قائمة الطلبات -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">قائمة طلبات الانضمام</h6>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="filterRequests('all')">الكل</button>
                        <button class="btn btn-outline-warning" onclick="filterRequests('pending')">في الانتظار</button>
                        <button class="btn btn-outline-success" onclick="filterRequests('approved')">موافق عليها</button>
                        <button class="btn btn-outline-danger" onclick="filterRequests('rejected')">مرفوضة</button>
                    </div>
                </div>
                <div class="card-body">
                    <?php if (empty($requests)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-user-plus fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد طلبات انضمام</h5>
                            <p class="text-muted">لم يتم تقديم أي طلبات انضمام لهذا الكورس بعد</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover" id="requestsTable">
                                <thead class="table-light">
                                    <tr>
                                        <th>الطالب</th>
                                        <th>البريد الإلكتروني</th>
                                        <th>الهاتف</th>
                                        <th>الرسالة</th>
                                        <th>تاريخ الطلب</th>
                                        <th>الحالة</th>
                                        <th>معالج بواسطة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($requests as $request): ?>
                                        <tr data-status="<?php echo $request['status']; ?>">
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-sm bg-info text-white rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 40px; height: 40px;">
                                                        <?php echo strtoupper(substr($request['student_name'], 0, 1)); ?>
                                                    </div>
                                                    <strong><?php echo htmlspecialchars($request['student_name']); ?></strong>
                                                </div>
                                            </td>
                                            <td><?php echo htmlspecialchars($request['student_email']); ?></td>
                                            <td><?php echo htmlspecialchars($request['student_phone'] ?? 'غير محدد'); ?></td>
                                            <td>
                                                <?php if ($request['message']): ?>
                                                    <span class="text-truncate d-inline-block" style="max-width: 200px;" title="<?php echo htmlspecialchars($request['message']); ?>">
                                                        <?php echo htmlspecialchars(substr($request['message'], 0, 50)); ?>...
                                                    </span>
                                                <?php else: ?>
                                                    <span class="text-muted">لا توجد رسالة</span>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo date('Y-m-d H:i', strtotime($request['request_date'])); ?></td>
                                            <td>
                                                <?php
                                                $status_class = '';
                                                $status_text = '';
                                                switch($request['status']) {
                                                    case 'pending':
                                                        $status_class = 'warning';
                                                        $status_text = 'في الانتظار';
                                                        break;
                                                    case 'approved':
                                                        $status_class = 'success';
                                                        $status_text = 'موافق عليه';
                                                        break;
                                                    case 'rejected':
                                                        $status_class = 'danger';
                                                        $status_text = 'مرفوض';
                                                        break;
                                                }
                                                ?>
                                                <span class="badge bg-<?php echo $status_class; ?>"><?php echo $status_text; ?></span>
                                            </td>
                                            <td>
                                                <?php if ($request['processed_by_name']): ?>
                                                    <small><?php echo htmlspecialchars($request['processed_by_name']); ?></small>
                                                    <br><small class="text-muted"><?php echo date('Y-m-d', strtotime($request['processed_date'])); ?></small>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($request['status'] === 'pending'): ?>
                                                    <div class="btn-group btn-group-sm">
                                                        <button class="btn btn-outline-success" onclick="approveRequest(<?php echo $request['id']; ?>)" title="موافقة">
                                                            <i class="fas fa-check"></i>
                                                        </button>
                                                        <button class="btn btn-outline-danger" onclick="rejectRequest(<?php echo $request['id']; ?>)" title="رفض">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    </div>
                                                <?php else: ?>
                                                    <button class="btn btn-outline-info btn-sm" onclick="viewRequestDetails(<?php echo $request['id']; ?>)" title="عرض التفاصيل">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal الموافقة -->
<div class="modal fade" id="approveModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">الموافقة على طلب الانضمام</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" id="approveRequestId" name="request_id">
                    <input type="hidden" name="action" value="approve">
                    <p>هل أنت متأكد من الموافقة على هذا الطلب؟</p>
                    <div class="alert alert-info">
                        <small>سيتم إضافة الطالب تلقائياً للكورس إذا كان مسجلاً في النظام.</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-success">موافقة</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal الرفض -->
<div class="modal fade" id="rejectModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">رفض طلب الانضمام</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" id="rejectRequestId" name="request_id">
                    <input type="hidden" name="action" value="reject">
                    
                    <div class="mb-3">
                        <label for="rejectionReason" class="form-label">سبب الرفض (اختياري)</label>
                        <textarea class="form-control" id="rejectionReason" name="rejection_reason" rows="3" placeholder="اكتب سبب رفض الطلب..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-danger">رفض الطلب</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
function approveRequest(requestId) {
    document.getElementById('approveRequestId').value = requestId;
    const modal = new bootstrap.Modal(document.getElementById('approveModal'));
    modal.show();
}

function rejectRequest(requestId) {
    document.getElementById('rejectRequestId').value = requestId;
    const modal = new bootstrap.Modal(document.getElementById('rejectModal'));
    modal.show();
}

function viewRequestDetails(requestId) {
    // يمكن إضافة نافذة منبثقة لعرض تفاصيل الطلب
    alert('عرض تفاصيل الطلب - قريباً');
}

function filterRequests(status) {
    const rows = document.querySelectorAll('#requestsTable tbody tr');
    
    rows.forEach(row => {
        if (status === 'all' || row.dataset.status === status) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
    
    // تحديث أزرار الفلترة
    document.querySelectorAll('.btn-group .btn').forEach(btn => {
        btn.classList.remove('active');
    });
    event.target.classList.add('active');
}

// تهيئة DataTable إذا كان متوفر
document.addEventListener('DOMContentLoaded', function() {
    const table = document.getElementById('requestsTable');
    if (table && typeof DataTable !== 'undefined') {
        new DataTable(table, {
            order: [[4, 'desc']],
            pageLength: 25,
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/ar.json'
            }
        });
    }
});
</script>

<?php require_once '../includes/footer.php'; ?>
