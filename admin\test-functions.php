<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

echo "<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>اختبار الدوال</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
</head>
<body>
<div class='container mt-5'>
    <div class='card'>
        <div class='card-header bg-primary text-white'>
            <h4>اختبار الدوال</h4>
        </div>
        <div class='card-body'>";

try {
    echo "<div class='alert alert-info'>بدء اختبار الدوال...</div>";
    
    // اختبار دالة logActivity
    echo "<h5>اختبار دالة logActivity:</h5>";
    $result = logActivity(1, 'test_activity', 'اختبار تسجيل النشاط');
    echo $result ? "<div class='alert alert-success'>✓ دالة logActivity تعمل بنجاح</div>" : "<div class='alert alert-danger'>✗ خطأ في دالة logActivity</div>";
    
    // اختبار دالة sendNotification
    echo "<h5>اختبار دالة sendNotification:</h5>";
    $result = sendNotification(1, 'اختبار', 'رسالة اختبار', 'info', 'system');
    echo $result ? "<div class='alert alert-success'>✓ دالة sendNotification تعمل بنجاح</div>" : "<div class='alert alert-danger'>✗ خطأ في دالة sendNotification</div>";
    
    // اختبار دالة getSystemSetting
    echo "<h5>اختبار دالة getSystemSetting:</h5>";
    $result = getSystemSetting('site_name', 'اسم افتراضي');
    echo "<div class='alert alert-info'>قيمة site_name: " . htmlspecialchars($result) . "</div>";
    
    // اختبار دالة updateSystemSetting
    echo "<h5>اختبار دالة updateSystemSetting:</h5>";
    $result = updateSystemSetting('test_setting', 'قيمة اختبار', 1);
    echo $result ? "<div class='alert alert-success'>✓ دالة updateSystemSetting تعمل بنجاح</div>" : "<div class='alert alert-danger'>✗ خطأ في دالة updateSystemSetting</div>";
    
    // التحقق من الجداول
    echo "<h5>التحقق من الجداول:</h5>";
    $tables = ['notifications', 'system_settings', 'security_logs', 'backups'];
    foreach ($tables as $table) {
        try {
            $stmt = $conn->query("SELECT COUNT(*) FROM $table");
            $count = $stmt->fetchColumn();
            echo "<div class='alert alert-success'>✓ جدول $table موجود ($count سجل)</div>";
        } catch (Exception $e) {
            echo "<div class='alert alert-warning'>⚠ جدول $table غير موجود</div>";
        }
    }
    
    echo "<div class='alert alert-success mt-4'><strong>تم الانتهاء من الاختبار!</strong></div>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>خطأ: " . htmlspecialchars($e->getMessage()) . "</div>";
}

echo "
        </div>
        <div class='card-footer'>
            <a href='dashboard.php' class='btn btn-primary'>الذهاب إلى لوحة التحكم</a>
            <a href='setup-admin-tables.php' class='btn btn-warning'>إعداد الجداول</a>
        </div>
    </div>
</div>
</body>
</html>";
?>
