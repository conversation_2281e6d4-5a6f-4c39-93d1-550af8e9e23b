<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات الطالب
if (!isLoggedIn() || !isStudent()) {
    header('Location: ../login.php');
    exit;
}

$pageTitle = 'الواجبات';
$breadcrumbs = [
    ['title' => 'الواجبات']
];

$student_id = $_SESSION['user_id'];
$success = '';
$error = '';

// معالجة تسليم الواجب
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['submit_assignment'])) {
    $assignment_id = (int)$_POST['assignment_id'];
    $submission_text = trim($_POST['submission_text'] ?? '');
    
    try {
        // التحقق من أن الطالب مسجل في الكورس
        $stmt = $conn->prepare("
            SELECT a.*, c.title as course_title 
            FROM assignments a 
            JOIN courses c ON a.course_id = c.id
            JOIN course_enrollments ce ON c.id = ce.course_id
            WHERE a.id = ? AND ce.student_id = ? AND a.status = 'published'
            AND a.due_date > NOW()
        ");
        $stmt->execute([$assignment_id, $student_id]);
        $assignment = $stmt->fetch();
        
        if (!$assignment) {
            throw new Exception('الواجب غير متاح أو انتهت مهلة التسليم');
        }
        
        // التحقق من عدم وجود تسليم سابق
        $stmt = $conn->prepare("SELECT id FROM assignment_submissions WHERE assignment_id = ? AND student_id = ?");
        $stmt->execute([$assignment_id, $student_id]);
        if ($stmt->fetch()) {
            throw new Exception('تم تسليم هذا الواجب مسبقاً');
        }
        
        $file_path = null;
        
        // معالجة رفع الملف
        if (isset($_FILES['assignment_file']) && $_FILES['assignment_file']['error'] === UPLOAD_ERR_OK) {
            $upload_dir = '../uploads/assignments/';
            if (!is_dir($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }
            
            $file_extension = pathinfo($_FILES['assignment_file']['name'], PATHINFO_EXTENSION);
            $allowed_extensions = ['pdf', 'doc', 'docx', 'txt', 'zip', 'rar'];
            
            if (!in_array(strtolower($file_extension), $allowed_extensions)) {
                throw new Exception('نوع الملف غير مسموح');
            }
            
            $file_name = 'assignment_' . $assignment_id . '_student_' . $student_id . '_' . time() . '.' . $file_extension;
            $file_path = $upload_dir . $file_name;
            
            if (!move_uploaded_file($_FILES['assignment_file']['tmp_name'], $file_path)) {
                throw new Exception('فشل في رفع الملف');
            }
        }
        
        // إدراج التسليم
        $stmt = $conn->prepare("
            INSERT INTO assignment_submissions (assignment_id, student_id, submission_text, file_path, status)
            VALUES (?, ?, ?, ?, 'submitted')
        ");
        $stmt->execute([$assignment_id, $student_id, $submission_text, $file_path]);
        
        $success = 'تم تسليم الواجب بنجاح';
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// جلب الواجبات
try {
    $stmt = $conn->prepare("
        SELECT a.*, c.title as course_title, c.id as course_id,
               u.name as instructor_name,
               asub.id as submission_id,
               asub.submitted_at,
               asub.score,
               asub.feedback,
               asub.status as submission_status,
               CASE 
                   WHEN a.due_date < NOW() THEN 'expired'
                   WHEN asub.id IS NOT NULL THEN 'submitted'
                   ELSE 'pending'
               END as assignment_status
        FROM assignments a
        JOIN courses c ON a.course_id = c.id
        JOIN course_enrollments ce ON c.id = ce.course_id
        JOIN users u ON c.instructor_id = u.id
        LEFT JOIN assignment_submissions asub ON a.id = asub.assignment_id AND asub.student_id = ?
        WHERE ce.student_id = ? AND a.status = 'published'
        ORDER BY a.due_date ASC
    ");
    $stmt->execute([$student_id, $student_id]);
    $assignments = $stmt->fetchAll();
    
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء جلب الواجبات';
}

include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- العنوان والإحصائيات -->
        <div class="col-12 mb-4">
            <div class="d-flex justify-content-between align-items-center">
                <h2 class="text-primary">
                    <i class="fas fa-tasks me-2"></i>
                    الواجبات
                </h2>
                <div class="d-flex gap-3">
                    <?php
                    $pending_count = count(array_filter($assignments, fn($a) => $a['assignment_status'] === 'pending'));
                    $submitted_count = count(array_filter($assignments, fn($a) => $a['assignment_status'] === 'submitted'));
                    $expired_count = count(array_filter($assignments, fn($a) => $a['assignment_status'] === 'expired'));
                    ?>
                    <div class="badge bg-warning fs-6">معلقة: <?php echo $pending_count; ?></div>
                    <div class="badge bg-success fs-6">مسلمة: <?php echo $submitted_count; ?></div>
                    <div class="badge bg-danger fs-6">منتهية: <?php echo $expired_count; ?></div>
                </div>
            </div>
        </div>

        <?php if ($success): ?>
        <div class="col-12 mb-3">
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo htmlspecialchars($success); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </div>
        <?php endif; ?>

        <?php if ($error): ?>
        <div class="col-12 mb-3">
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-circle me-2"></i>
                <?php echo htmlspecialchars($error); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </div>
        <?php endif; ?>

        <!-- قائمة الواجبات -->
        <div class="col-12">
            <?php if (empty($assignments)): ?>
            <div class="card-student text-center py-5">
                <div class="card-body">
                    <i class="fas fa-tasks fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">لا توجد واجبات متاحة</h4>
                    <p class="text-muted">لم يتم تعيين أي واجبات في الكورسات المسجل بها حالياً</p>
                </div>
            </div>
            <?php else: ?>
            <div class="row">
                <?php foreach ($assignments as $assignment): ?>
                <div class="col-lg-6 col-xl-4 mb-4">
                    <div class="card-student h-100">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h6 class="mb-0 text-primary"><?php echo htmlspecialchars($assignment['course_title']); ?></h6>
                            <?php
                            $status_class = '';
                            $status_text = '';
                            switch ($assignment['assignment_status']) {
                                case 'pending':
                                    $status_class = 'bg-warning';
                                    $status_text = 'معلق';
                                    break;
                                case 'submitted':
                                    $status_class = 'bg-success';
                                    $status_text = 'مسلم';
                                    break;
                                case 'expired':
                                    $status_class = 'bg-danger';
                                    $status_text = 'منتهي';
                                    break;
                            }
                            ?>
                            <span class="badge <?php echo $status_class; ?>"><?php echo $status_text; ?></span>
                        </div>
                        <div class="card-body">
                            <h5 class="card-title"><?php echo htmlspecialchars($assignment['title']); ?></h5>
                            <p class="card-text text-muted">
                                <?php echo htmlspecialchars(substr($assignment['description'], 0, 100)) . '...'; ?>
                            </p>
                            
                            <div class="assignment-details mb-3">
                                <div class="d-flex justify-content-between mb-2">
                                    <span class="text-muted">المدرب:</span>
                                    <span><?php echo htmlspecialchars($assignment['instructor_name']); ?></span>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span class="text-muted">تاريخ التسليم:</span>
                                    <span class="<?php echo $assignment['assignment_status'] === 'expired' ? 'text-danger' : ''; ?>">
                                        <?php echo date('Y-m-d H:i', strtotime($assignment['due_date'])); ?>
                                    </span>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span class="text-muted">الدرجة الكاملة:</span>
                                    <span><?php echo $assignment['max_score']; ?> نقطة</span>
                                </div>
                                
                                <?php if ($assignment['submission_id']): ?>
                                <div class="submission-info mt-3 p-3 bg-light rounded">
                                    <h6 class="text-success mb-2">
                                        <i class="fas fa-check-circle me-1"></i>
                                        تم التسليم
                                    </h6>
                                    <div class="d-flex justify-content-between mb-1">
                                        <span class="text-muted">تاريخ التسليم:</span>
                                        <span><?php echo date('Y-m-d H:i', strtotime($assignment['submitted_at'])); ?></span>
                                    </div>
                                    <?php if ($assignment['score'] !== null): ?>
                                    <div class="d-flex justify-content-between mb-1">
                                        <span class="text-muted">الدرجة:</span>
                                        <span class="fw-bold text-primary">
                                            <?php echo $assignment['score']; ?> / <?php echo $assignment['max_score']; ?>
                                        </span>
                                    </div>
                                    <?php endif; ?>
                                    <?php if ($assignment['feedback']): ?>
                                    <div class="mt-2">
                                        <span class="text-muted">التعليق:</span>
                                        <p class="mb-0 mt-1"><?php echo htmlspecialchars($assignment['feedback']); ?></p>
                                    </div>
                                    <?php endif; ?>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="card-footer">
                            <?php if ($assignment['assignment_status'] === 'pending'): ?>
                            <button class="btn btn-student-primary w-100" data-bs-toggle="modal" 
                                    data-bs-target="#submitModal<?php echo $assignment['id']; ?>">
                                <i class="fas fa-upload me-1"></i>
                                تسليم الواجب
                            </button>
                            <?php elseif ($assignment['assignment_status'] === 'submitted'): ?>
                            <button class="btn btn-outline-success w-100" disabled>
                                <i class="fas fa-check me-1"></i>
                                تم التسليم
                            </button>
                            <?php else: ?>
                            <button class="btn btn-outline-danger w-100" disabled>
                                <i class="fas fa-times me-1"></i>
                                انتهت المهلة
                            </button>
                            <?php endif; ?>
                            
                            <button class="btn btn-outline-primary w-100 mt-2" data-bs-toggle="modal" 
                                    data-bs-target="#detailsModal<?php echo $assignment['id']; ?>">
                                <i class="fas fa-eye me-1"></i>
                                عرض التفاصيل
                            </button>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Modals للتسليم والتفاصيل -->
<?php foreach ($assignments as $assignment): ?>
<!-- Modal تسليم الواجب -->
<?php if ($assignment['assignment_status'] === 'pending'): ?>
<div class="modal fade" id="submitModal<?php echo $assignment['id']; ?>" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تسليم الواجب: <?php echo htmlspecialchars($assignment['title']); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" enctype="multipart/form-data">
                <div class="modal-body">
                    <input type="hidden" name="assignment_id" value="<?php echo $assignment['id']; ?>">
                    
                    <div class="mb-3">
                        <label class="form-label">النص (اختياري)</label>
                        <textarea name="submission_text" class="form-control" rows="5" 
                                  placeholder="اكتب إجابتك هنا..."></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">رفع ملف (اختياري)</label>
                        <input type="file" name="assignment_file" class="form-control" 
                               accept=".pdf,.doc,.docx,.txt,.zip,.rar">
                        <div class="form-text">الملفات المسموحة: PDF, DOC, DOCX, TXT, ZIP, RAR</div>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>تاريخ التسليم:</strong> <?php echo date('Y-m-d H:i', strtotime($assignment['due_date'])); ?>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" name="submit_assignment" class="btn btn-student-primary">
                        <i class="fas fa-upload me-1"></i>
                        تسليم الواجب
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Modal تفاصيل الواجب -->
<div class="modal fade" id="detailsModal<?php echo $assignment['id']; ?>" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><?php echo htmlspecialchars($assignment['title']); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <h6 class="text-primary">الوصف:</h6>
                    <p><?php echo nl2br(htmlspecialchars($assignment['description'])); ?></p>
                </div>
                
                <?php if ($assignment['instructions']): ?>
                <div class="mb-3">
                    <h6 class="text-primary">التعليمات:</h6>
                    <p><?php echo nl2br(htmlspecialchars($assignment['instructions'])); ?></p>
                </div>
                <?php endif; ?>
                
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary">معلومات الواجب:</h6>
                        <ul class="list-unstyled">
                            <li><strong>الكورس:</strong> <?php echo htmlspecialchars($assignment['course_title']); ?></li>
                            <li><strong>المدرب:</strong> <?php echo htmlspecialchars($assignment['instructor_name']); ?></li>
                            <li><strong>الدرجة الكاملة:</strong> <?php echo $assignment['max_score']; ?> نقطة</li>
                            <li><strong>تاريخ التسليم:</strong> <?php echo date('Y-m-d H:i', strtotime($assignment['due_date'])); ?></li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>
<?php endforeach; ?>

<?php include 'includes/footer.php'; ?>
