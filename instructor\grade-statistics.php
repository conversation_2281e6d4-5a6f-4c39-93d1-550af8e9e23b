<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من صلاحيات المدرب
if (!isLoggedIn() || !isInstructor()) {
    header('Location: ../login.php');
    exit;
}

$course_id = (int)($_GET['course_id'] ?? 0);
$error = '';

// التحقق من أن الكورس ينتمي للمدرب
try {
    $stmt = $conn->prepare("SELECT title FROM courses WHERE id = ? AND instructor_id = ?");
    $stmt->execute([$course_id, $_SESSION['user_id']]);
    $course = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$course) {
        header('Location: courses.php');
        exit;
    }
} catch (PDOException $e) {
    $error = 'خطأ في التحقق من الكورس';
}

// جلب الإحصائيات العامة
try {
    $stmt = $conn->prepare("
        SELECT 
            COUNT(DISTINCT g.student_id) as students_with_grades,
            COUNT(g.id) as total_grades,
            COUNT(DISTINCT g.assignment_name) as total_assignments,
            AVG(CASE WHEN g.max_grade > 0 THEN (g.grade / g.max_grade) * 100 END) as overall_average,
            MIN(CASE WHEN g.max_grade > 0 THEN (g.grade / g.max_grade) * 100 END) as min_percentage,
            MAX(CASE WHEN g.max_grade > 0 THEN (g.grade / g.max_grade) * 100 END) as max_percentage
        FROM grades g
        WHERE g.course_id = ? AND g.instructor_id = ?
    ");
    $stmt->execute([$course_id, $_SESSION['user_id']]);
    $general_stats = $stmt->fetch(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $general_stats = [
        'students_with_grades' => 0,
        'total_grades' => 0,
        'total_assignments' => 0,
        'overall_average' => 0,
        'min_percentage' => 0,
        'max_percentage' => 0
    ];
}

// إحصائيات حسب نوع التقييم
try {
    $stmt = $conn->prepare("
        SELECT 
            g.grade_type,
            COUNT(g.id) as count,
            AVG(CASE WHEN g.max_grade > 0 THEN (g.grade / g.max_grade) * 100 END) as avg_percentage,
            MIN(CASE WHEN g.max_grade > 0 THEN (g.grade / g.max_grade) * 100 END) as min_percentage,
            MAX(CASE WHEN g.max_grade > 0 THEN (g.grade / g.max_grade) * 100 END) as max_percentage
        FROM grades g
        WHERE g.course_id = ? AND g.instructor_id = ?
        GROUP BY g.grade_type
        ORDER BY count DESC
    ");
    $stmt->execute([$course_id, $_SESSION['user_id']]);
    $type_stats = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $type_stats = [];
}

// توزيع الدرجات (النطاقات)
try {
    $stmt = $conn->prepare("
        SELECT 
            CASE 
                WHEN (g.grade / g.max_grade) * 100 >= 90 THEN 'A (90-100%)'
                WHEN (g.grade / g.max_grade) * 100 >= 80 THEN 'B (80-89%)'
                WHEN (g.grade / g.max_grade) * 100 >= 70 THEN 'C (70-79%)'
                WHEN (g.grade / g.max_grade) * 100 >= 60 THEN 'D (60-69%)'
                ELSE 'F (أقل من 60%)'
            END as grade_range,
            COUNT(*) as count
        FROM grades g
        WHERE g.course_id = ? AND g.instructor_id = ? AND g.max_grade > 0
        GROUP BY grade_range
        ORDER BY 
            CASE 
                WHEN (g.grade / g.max_grade) * 100 >= 90 THEN 1
                WHEN (g.grade / g.max_grade) * 100 >= 80 THEN 2
                WHEN (g.grade / g.max_grade) * 100 >= 70 THEN 3
                WHEN (g.grade / g.max_grade) * 100 >= 60 THEN 4
                ELSE 5
            END
    ");
    $stmt->execute([$course_id, $_SESSION['user_id']]);
    $grade_distribution = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $grade_distribution = [];
}

// أفضل وأسوأ الطلاب
try {
    $stmt = $conn->prepare("
        SELECT 
            u.name as student_name,
            u.email as student_email,
            COUNT(g.id) as total_grades,
            AVG(CASE WHEN g.max_grade > 0 THEN (g.grade / g.max_grade) * 100 END) as avg_percentage,
            SUM(g.grade) as total_points,
            SUM(g.max_grade) as total_max_points
        FROM users u
        JOIN grades g ON u.id = g.student_id
        WHERE g.course_id = ? AND g.instructor_id = ?
        GROUP BY u.id, u.name, u.email
        HAVING COUNT(g.id) >= 3
        ORDER BY avg_percentage DESC
        LIMIT 10
    ");
    $stmt->execute([$course_id, $_SESSION['user_id']]);
    $top_students = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $top_students = [];
}

// إحصائيات التكاليف
try {
    $stmt = $conn->prepare("
        SELECT 
            g.assignment_name,
            g.grade_type,
            COUNT(g.id) as submissions,
            AVG(CASE WHEN g.max_grade > 0 THEN (g.grade / g.max_grade) * 100 END) as avg_percentage,
            MIN(CASE WHEN g.max_grade > 0 THEN (g.grade / g.max_grade) * 100 END) as min_percentage,
            MAX(CASE WHEN g.max_grade > 0 THEN (g.grade / g.max_grade) * 100 END) as max_percentage,
            AVG(g.max_grade) as avg_max_grade
        FROM grades g
        WHERE g.course_id = ? AND g.instructor_id = ?
        GROUP BY g.assignment_name, g.grade_type
        ORDER BY submissions DESC, avg_percentage DESC
    ");
    $stmt->execute([$course_id, $_SESSION['user_id']]);
    $assignment_stats = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $assignment_stats = [];
}

$pageTitle = 'إحصائيات الدرجات - ' . $course['title'];
$breadcrumbs = [
    ['title' => 'الكورسات', 'url' => 'courses.php'],
    ['title' => $course['title'], 'url' => 'course-details.php?id=' . $course_id],
    ['title' => 'الطلاب', 'url' => 'students.php?course_id=' . $course_id],
    ['title' => 'إحصائيات الدرجات']
];

include 'includes/header.php';
?>

<div class="row">
    <!-- الإحصائيات العامة -->
    <div class="col-12 mb-4">
        <div class="row g-3">
            <div class="col-lg-3 col-md-6">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">إجمالي الدرجات</h6>
                                <h2 class="mb-0"><?php echo number_format($general_stats['total_grades']); ?></h2>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-star fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">الطلاب المقيمون</h6>
                                <h2 class="mb-0"><?php echo number_format($general_stats['students_with_grades']); ?></h2>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-users fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">التكاليف</h6>
                                <h2 class="mb-0"><?php echo number_format($general_stats['total_assignments']); ?></h2>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-tasks fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">المعدل العام</h6>
                                <h2 class="mb-0"><?php echo round($general_stats['overall_average'] ?? 0, 1); ?>%</h2>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-chart-line fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- توزيع الدرجات -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    توزيع الدرجات
                </h6>
            </div>
            <div class="card-body">
                <?php if (!empty($grade_distribution)): ?>
                    <?php
                    $total_grades = array_sum(array_column($grade_distribution, 'count'));
                    $colors = ['success', 'info', 'warning', 'danger', 'secondary'];
                    ?>
                    
                    <?php foreach ($grade_distribution as $index => $range): ?>
                        <?php $percentage = $total_grades > 0 ? ($range['count'] / $total_grades) * 100 : 0; ?>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between mb-1">
                                <span><?php echo htmlspecialchars($range['grade_range']); ?></span>
                                <span>
                                    <strong><?php echo $range['count']; ?></strong>
                                    <small class="text-muted">(<?php echo round($percentage, 1); ?>%)</small>
                                </span>
                            </div>
                            <div class="progress" style="height: 8px;">
                                <div class="progress-bar bg-<?php echo $colors[$index] ?? 'primary'; ?>" 
                                     style="width: <?php echo $percentage; ?>%"></div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="text-center text-muted py-3">
                        <i class="fas fa-chart-pie fa-2x mb-2"></i>
                        <p class="mb-0">لا توجد درجات لعرض التوزيع</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- إحصائيات حسب نوع التقييم -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    إحصائيات حسب نوع التقييم
                </h6>
            </div>
            <div class="card-body">
                <?php if (!empty($type_stats)): ?>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>النوع</th>
                                    <th>العدد</th>
                                    <th>المعدل</th>
                                    <th>النطاق</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                $type_labels = [
                                    'assignment' => 'واجب',
                                    'quiz' => 'اختبار قصير',
                                    'exam' => 'امتحان',
                                    'project' => 'مشروع',
                                    'participation' => 'مشاركة',
                                    'attendance' => 'حضور'
                                ];
                                ?>
                                <?php foreach ($type_stats as $type): ?>
                                    <tr>
                                        <td>
                                            <span class="badge bg-primary">
                                                <?php echo $type_labels[$type['grade_type']] ?? $type['grade_type']; ?>
                                            </span>
                                        </td>
                                        <td><?php echo $type['count']; ?></td>
                                        <td><?php echo round($type['avg_percentage'], 1); ?>%</td>
                                        <td>
                                            <small class="text-muted">
                                                <?php echo round($type['min_percentage'], 1); ?>% - 
                                                <?php echo round($type['max_percentage'], 1); ?>%
                                            </small>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="text-center text-muted py-3">
                        <i class="fas fa-chart-bar fa-2x mb-2"></i>
                        <p class="mb-0">لا توجد درجات لعرض الإحصائيات</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- أفضل الطلاب -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-trophy me-2"></i>
                    أفضل الطلاب
                </h6>
            </div>
            <div class="card-body">
                <?php if (!empty($top_students)): ?>
                    <div class="list-group list-group-flush">
                        <?php foreach (array_slice($top_students, 0, 5) as $index => $student): ?>
                            <div class="list-group-item px-0 d-flex justify-content-between align-items-center">
                                <div>
                                    <div class="d-flex align-items-center">
                                        <span class="badge bg-warning me-2"><?php echo $index + 1; ?></span>
                                        <div>
                                            <h6 class="mb-0"><?php echo htmlspecialchars($student['student_name']); ?></h6>
                                            <small class="text-muted"><?php echo $student['total_grades']; ?> تقييم</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="text-end">
                                    <span class="badge bg-success fs-6">
                                        <?php echo round($student['avg_percentage'], 1); ?>%
                                    </span>
                                    <br>
                                    <small class="text-muted">
                                        <?php echo $student['total_points']; ?>/<?php echo $student['total_max_points']; ?>
                                    </small>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div class="text-center text-muted py-3">
                        <i class="fas fa-trophy fa-2x mb-2"></i>
                        <p class="mb-0">لا توجد بيانات كافية لعرض أفضل الطلاب</p>
                        <small>(يتطلب 3 تقييمات على الأقل)</small>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- إحصائيات التكاليف -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-clipboard-list me-2"></i>
                    إحصائيات التكاليف
                </h6>
            </div>
            <div class="card-body">
                <?php if (!empty($assignment_stats)): ?>
                    <div class="table-responsive" style="max-height: 300px; overflow-y: auto;">
                        <table class="table table-sm">
                            <thead class="sticky-top bg-light">
                                <tr>
                                    <th>التكليف</th>
                                    <th>المشاركات</th>
                                    <th>المعدل</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($assignment_stats as $assignment): ?>
                                    <tr>
                                        <td>
                                            <div>
                                                <strong><?php echo htmlspecialchars($assignment['assignment_name']); ?></strong>
                                                <br>
                                                <small class="text-muted">
                                                    <?php echo $type_labels[$assignment['grade_type']] ?? $assignment['grade_type']; ?>
                                                </small>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-info"><?php echo $assignment['submissions']; ?></span>
                                        </td>
                                        <td>
                                            <?php 
                                            $avg = $assignment['avg_percentage'];
                                            $color = $avg >= 80 ? 'success' : ($avg >= 60 ? 'warning' : 'danger');
                                            ?>
                                            <span class="badge bg-<?php echo $color; ?>">
                                                <?php echo round($avg, 1); ?>%
                                            </span>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="text-center text-muted py-3">
                        <i class="fas fa-clipboard-list fa-2x mb-2"></i>
                        <p class="mb-0">لا توجد تكاليف لعرض الإحصائيات</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- إجراءات سريعة -->
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-tools me-2"></i>
                    إجراءات سريعة
                </h6>
            </div>
            <div class="card-body">
                <div class="row g-2">
                    <div class="col-md-3">
                        <a href="export-grades.php?course_id=<?php echo $course_id; ?>" class="btn btn-success w-100">
                            <i class="fas fa-download me-2"></i>
                            تصدير الدرجات
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="bulk-grade.php?course_id=<?php echo $course_id; ?>" class="btn btn-primary w-100">
                            <i class="fas fa-users me-2"></i>
                            درجات جماعية
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="students.php?course_id=<?php echo $course_id; ?>" class="btn btn-info w-100">
                            <i class="fas fa-list me-2"></i>
                            عرض الطلاب
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="course-details.php?id=<?php echo $course_id; ?>" class="btn btn-secondary w-100">
                            <i class="fas fa-arrow-right me-2"></i>
                            العودة للكورس
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.card-header {
    background: var(--instructor-light);
    border-bottom: 1px solid var(--gray-200);
    border-radius: 12px 12px 0 0 !important;
}

.progress {
    border-radius: 10px;
}

.list-group-item {
    border: none;
    border-bottom: 1px solid var(--gray-100);
}

.list-group-item:last-child {
    border-bottom: none;
}

.sticky-top {
    top: 0;
    z-index: 1020;
}
</style>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

</body>
</html>
