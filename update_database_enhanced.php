<?php
/**
 * تحديث قاعدة البيانات إلى النسخة المحسنة
 * Enhanced Database Update Script
 */

// تفعيل عرض الأخطاء للتطوير
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// تضمين ملف الاتصال بقاعدة البيانات
require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>تحديث قاعدة البيانات</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<style>";
echo "body { font-family: 'Tahoma', sans-serif; background-color: #f8f9fa; }";
echo ".container { max-width: 800px; margin-top: 2rem; }";
echo ".step { margin-bottom: 1rem; padding: 1rem; border-radius: 8px; }";
echo ".step.success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }";
echo ".step.error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }";
echo ".step.info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }";
echo "</style>";
echo "</head>";
echo "<body>";
echo "<div class='container'>";
echo "<h1 class='text-center mb-4'>تحديث قاعدة البيانات إلى النسخة المحسنة</h1>";

try {
    // قراءة ملف SQL المحسن
    $sqlFile = 'database/enhanced_setup.sql';
    
    if (!file_exists($sqlFile)) {
        throw new Exception("ملف SQL غير موجود: $sqlFile");
    }
    
    $sql = file_get_contents($sqlFile);
    
    if ($sql === false) {
        throw new Exception("فشل في قراءة ملف SQL");
    }
    
    echo "<div class='step info'>";
    echo "<h5><i class='fas fa-info-circle'></i> بدء عملية التحديث</h5>";
    echo "<p>جاري قراءة ملف SQL وتطبيق التحديثات...</p>";
    echo "</div>";
    
    // تقسيم الاستعلامات
    $statements = explode(';', $sql);
    $successCount = 0;
    $errorCount = 0;
    $skippedCount = 0;
    
    foreach ($statements as $statement) {
        $statement = trim($statement);
        
        // تجاهل التعليقات والأسطر الفارغة
        if (empty($statement) || 
            strpos($statement, '--') === 0 || 
            strpos($statement, '/*') === 0) {
            $skippedCount++;
            continue;
        }
        
        try {
            $conn->exec($statement);
            $successCount++;
            
            // عرض الاستعلامات المهمة فقط
            if (stripos($statement, 'CREATE TABLE') !== false) {
                $tableName = '';
                if (preg_match('/CREATE TABLE.*?`?(\w+)`?/i', $statement, $matches)) {
                    $tableName = $matches[1];
                }
                echo "<div class='step success'>";
                echo "<small><i class='fas fa-check'></i> تم إنشاء/تحديث الجدول: $tableName</small>";
                echo "</div>";
            } elseif (stripos($statement, 'INSERT INTO') !== false) {
                if (preg_match('/INSERT INTO.*?`?(\w+)`?/i', $statement, $matches)) {
                    $tableName = $matches[1];
                    echo "<div class='step success'>";
                    echo "<small><i class='fas fa-plus'></i> تم إدراج بيانات في الجدول: $tableName</small>";
                    echo "</div>";
                }
            } elseif (stripos($statement, 'CREATE INDEX') !== false) {
                if (preg_match('/CREATE INDEX.*?`?(\w+)`?/i', $statement, $matches)) {
                    $indexName = $matches[1];
                    echo "<div class='step success'>";
                    echo "<small><i class='fas fa-database'></i> تم إنشاء المؤشر: $indexName</small>";
                    echo "</div>";
                }
            } elseif (stripos($statement, 'CREATE OR REPLACE VIEW') !== false) {
                if (preg_match('/CREATE OR REPLACE VIEW.*?`?(\w+)`?/i', $statement, $matches)) {
                    $viewName = $matches[1];
                    echo "<div class='step success'>";
                    echo "<small><i class='fas fa-eye'></i> تم إنشاء العرض: $viewName</small>";
                    echo "</div>";
                }
            }
            
        } catch (PDOException $e) {
            $errorCount++;
            
            // تجاهل بعض الأخطاء المتوقعة
            $ignorableErrors = [
                'Duplicate entry',
                'already exists',
                'Table already exists',
                'Duplicate key name'
            ];
            
            $shouldIgnore = false;
            foreach ($ignorableErrors as $ignorableError) {
                if (stripos($e->getMessage(), $ignorableError) !== false) {
                    $shouldIgnore = true;
                    break;
                }
            }
            
            if (!$shouldIgnore) {
                echo "<div class='step error'>";
                echo "<h6><i class='fas fa-exclamation-triangle'></i> خطأ في تنفيذ الاستعلام</h6>";
                echo "<p><strong>الاستعلام:</strong> " . htmlspecialchars(substr($statement, 0, 100)) . "...</p>";
                echo "<p><strong>الخطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
                echo "</div>";
            } else {
                // تحويل الخطأ إلى تحذير
                echo "<div class='step info'>";
                echo "<small><i class='fas fa-info'></i> تم تجاهل: " . htmlspecialchars($e->getMessage()) . "</small>";
                echo "</div>";
                $errorCount--;
                $successCount++;
            }
        }
    }
    
    echo "<div class='step success'>";
    echo "<h5><i class='fas fa-check-circle'></i> تم الانتهاء من التحديث</h5>";
    echo "<ul>";
    echo "<li>الاستعلامات الناجحة: $successCount</li>";
    echo "<li>الأخطاء: $errorCount</li>";
    echo "<li>المتجاهلة: $skippedCount</li>";
    echo "</ul>";
    echo "</div>";
    
    // التحقق من حالة قاعدة البيانات
    echo "<div class='step info'>";
    echo "<h5><i class='fas fa-database'></i> التحقق من حالة قاعدة البيانات</h5>";
    
    // فحص الجداول
    $stmt = $conn->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "<p><strong>عدد الجداول:</strong> " . count($tables) . "</p>";
    
    // فحص المستخدمين
    $stmt = $conn->query("SELECT COUNT(*) FROM users");
    $userCount = $stmt->fetchColumn();
    echo "<p><strong>عدد المستخدمين:</strong> $userCount</p>";
    
    // فحص الكورسات
    $stmt = $conn->query("SELECT COUNT(*) FROM courses");
    $courseCount = $stmt->fetchColumn();
    echo "<p><strong>عدد الكورسات:</strong> $courseCount</p>";
    
    // فحص الجلسات
    $stmt = $conn->query("SELECT COUNT(*) FROM sessions");
    $sessionCount = $stmt->fetchColumn();
    echo "<p><strong>عدد الجلسات:</strong> $sessionCount</p>";
    
    echo "</div>";
    
    // إنشاء ملف تكوين محدث
    $configContent = "<?php\n";
    $configContent .= "// تم تحديث قاعدة البيانات في: " . date('Y-m-d H:i:s') . "\n";
    $configContent .= "define('DB_VERSION', '2.0');\n";
    $configContent .= "define('DB_UPDATED', true);\n";
    $configContent .= "?>";
    
    file_put_contents('config/db_version.php', $configContent);
    
    echo "<div class='step success'>";
    echo "<h5><i class='fas fa-rocket'></i> التحديث مكتمل!</h5>";
    echo "<p>تم تحديث قاعدة البيانات بنجاح إلى النسخة المحسنة.</p>";
    echo "<p><strong>بيانات تسجيل الدخول التجريبية:</strong></p>";
    echo "<ul>";
    echo "<li><strong>المدير:</strong> <EMAIL> / password</li>";
    echo "<li><strong>المدرب:</strong> <EMAIL> / password</li>";
    echo "<li><strong>الطالب:</strong> <EMAIL> / password</li>";
    echo "</ul>";
    echo "<div class='mt-3'>";
    echo "<a href='login.php' class='btn btn-primary'>الذهاب إلى صفحة تسجيل الدخول</a>";
    echo "<a href='admin/dashboard.php' class='btn btn-success ms-2'>لوحة تحكم المدير</a>";
    echo "</div>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='step error'>";
    echo "<h5><i class='fas fa-times-circle'></i> فشل في التحديث</h5>";
    echo "<p><strong>الخطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>يرجى التحقق من:</p>";
    echo "<ul>";
    echo "<li>صحة إعدادات قاعدة البيانات في config/database.php</li>";
    echo "<li>صلاحيات قاعدة البيانات</li>";
    echo "<li>وجود ملف database/enhanced_setup.sql</li>";
    echo "</ul>";
    echo "</div>";
}

echo "</div>";
echo "<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js'></script>";
echo "</body>";
echo "</html>";
?>
