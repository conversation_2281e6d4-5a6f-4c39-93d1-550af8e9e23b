                </div> <!-- إنهاء main-content -->
            </div> <!-- إنهاء col-lg-10 -->
        </div> <!-- إنهاء row -->
    </div> <!-- إنهاء container-fluid -->

    <!-- شريط التنقل السفلي للهاتف المحمول -->
    <div class="mobile-bottom-nav d-lg-none">
        <div class="container-fluid">
            <div class="row g-0">
                <div class="col">
                    <a href="dashboard.php" class="mobile-nav-item <?php echo basename($_SERVER['PHP_SELF']) == 'dashboard.php' ? 'active' : ''; ?>">
                        <i class="fas fa-home"></i>
                        <span>الرئيسية</span>
                    </a>
                </div>
                <div class="col">
                    <a href="courses.php" class="mobile-nav-item <?php echo basename($_SERVER['PHP_SELF']) == 'courses.php' ? 'active' : ''; ?>">
                        <i class="fas fa-graduation-cap"></i>
                        <span>كورساتي</span>
                    </a>
                </div>
                <div class="col">
                    <a href="assignments.php" class="mobile-nav-item <?php echo basename($_SERVER['PHP_SELF']) == 'assignments.php' ? 'active' : ''; ?>">
                        <i class="fas fa-tasks"></i>
                        <span>الواجبات</span>
                    </a>
                </div>
                <div class="col">
                    <a href="grades.php" class="mobile-nav-item <?php echo basename($_SERVER['PHP_SELF']) == 'grades.php' ? 'active' : ''; ?>">
                        <i class="fas fa-chart-line"></i>
                        <span>الدرجات</span>
                    </a>
                </div>
                <div class="col">
                    <a href="profile.php" class="mobile-nav-item <?php echo basename($_SERVER['PHP_SELF']) == 'profile.php' ? 'active' : ''; ?>">
                        <i class="fas fa-user"></i>
                        <span>الملف</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- زر الإجراءات السريعة العائم -->
    <div class="floating-action-btn">
        <button class="fab-main" type="button" data-bs-toggle="dropdown" aria-expanded="false">
            <i class="fas fa-plus"></i>
        </button>
        <ul class="dropdown-menu dropdown-menu-end fab-menu">
            <li>
                <a class="dropdown-item fab-item" href="browse-courses.php">
                    <i class="fas fa-search text-primary"></i>
                    <div>
                        <strong>تصفح الكورسات</strong>
                        <small>ابحث عن كورسات جديدة</small>
                    </div>
                </a>
            </li>
            <li>
                <a class="dropdown-item fab-item" href="assignments.php">
                    <i class="fas fa-tasks text-warning"></i>
                    <div>
                        <strong>الواجبات</strong>
                        <small>عرض الواجبات المطلوبة</small>
                    </div>
                </a>
            </li>
            <li>
                <a class="dropdown-item fab-item" href="quizzes.php">
                    <i class="fas fa-question-circle text-info"></i>
                    <div>
                        <strong>الاختبارات</strong>
                        <small>الاختبارات المتاحة</small>
                    </div>
                </a>
            </li>
            <li>
                <a class="dropdown-item fab-item" href="schedule.php">
                    <i class="fas fa-calendar text-success"></i>
                    <div>
                        <strong>الجدول الزمني</strong>
                        <small>مواعيد المحاضرات</small>
                    </div>
                </a>
            </li>
            <li><hr class="dropdown-divider"></li>
            <li>
                <a class="dropdown-item fab-item" href="help.php">
                    <i class="fas fa-question text-secondary"></i>
                    <div>
                        <strong>المساعدة</strong>
                        <small>الحصول على المساعدة</small>
                    </div>
                </a>
            </li>
        </ul>
    </div>

    <!-- Footer المحسن -->
    <footer class="student-footer mt-5">
        <div class="container-fluid">
            <!-- معلومات سريعة -->
            <div class="footer-quick-info mb-4">
                <div class="row g-3">
                    <div class="col-lg-3 col-md-6">
                        <div class="quick-stat">
                            <div class="stat-icon">
                                <i class="fas fa-graduation-cap"></i>
                            </div>
                            <div class="stat-content">
                                <h6 class="mb-1">كورساتي</h6>
                                <p class="mb-0 text-muted">الكورسات المسجل بها</p>
                            </div>
                            <a href="courses.php" class="stat-link">
                                <i class="fas fa-arrow-left"></i>
                            </a>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="quick-stat">
                            <div class="stat-icon">
                                <i class="fas fa-tasks"></i>
                            </div>
                            <div class="stat-content">
                                <h6 class="mb-1">الواجبات</h6>
                                <p class="mb-0 text-muted">الواجبات والمهام</p>
                            </div>
                            <a href="assignments.php" class="stat-link">
                                <i class="fas fa-arrow-left"></i>
                            </a>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="quick-stat">
                            <div class="stat-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="stat-content">
                                <h6 class="mb-1">الدرجات</h6>
                                <p class="mb-0 text-muted">النتائج والتقييمات</p>
                            </div>
                            <a href="grades.php" class="stat-link">
                                <i class="fas fa-arrow-left"></i>
                            </a>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="quick-stat">
                            <div class="stat-icon">
                                <i class="fas fa-certificate"></i>
                            </div>
                            <div class="stat-content">
                                <h6 class="mb-1">الشهادات</h6>
                                <p class="mb-0 text-muted">الشهادات المحصلة</p>
                            </div>
                            <a href="certificates.php" class="stat-link">
                                <i class="fas fa-arrow-left"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- روابط سريعة -->
            <div class="footer-links mb-4">
                <div class="row">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <h6 class="footer-title">التعلم</h6>
                        <ul class="footer-menu">
                            <li><a href="courses.php"><i class="fas fa-graduation-cap me-2"></i>كورساتي</a></li>
                            <li><a href="browse-courses.php"><i class="fas fa-search me-2"></i>تصفح الكورسات</a></li>
                            <li><a href="library.php"><i class="fas fa-book-open me-2"></i>المكتبة</a></li>
                            <li><a href="schedule.php"><i class="fas fa-calendar me-2"></i>الجدول الزمني</a></li>
                        </ul>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <h6 class="footer-title">التقييم</h6>
                        <ul class="footer-menu">
                            <li><a href="assignments.php"><i class="fas fa-tasks me-2"></i>الواجبات</a></li>
                            <li><a href="quizzes.php"><i class="fas fa-question-circle me-2"></i>الاختبارات</a></li>
                            <li><a href="grades.php"><i class="fas fa-chart-line me-2"></i>الدرجات</a></li>
                            <li><a href="certificates.php"><i class="fas fa-certificate me-2"></i>الشهادات</a></li>
                        </ul>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <h6 class="footer-title">التفاعل</h6>
                        <ul class="footer-menu">
                            <li><a href="forums.php"><i class="fas fa-comments me-2"></i>المنتديات</a></li>
                            <li><a href="messages.php"><i class="fas fa-envelope me-2"></i>الرسائل</a></li>
                            <li><a href="notifications.php"><i class="fas fa-bell me-2"></i>الإشعارات</a></li>
                            <li><a href="study-groups.php"><i class="fas fa-users me-2"></i>مجموعات الدراسة</a></li>
                        </ul>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <h6 class="footer-title">الحساب</h6>
                        <ul class="footer-menu">
                            <li><a href="profile.php"><i class="fas fa-user me-2"></i>الملف الشخصي</a></li>
                            <li><a href="settings.php"><i class="fas fa-cog me-2"></i>الإعدادات</a></li>
                            <li><a href="help.php"><i class="fas fa-question me-2"></i>المساعدة</a></li>
                            <li><a href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- معلومات النظام -->
            <div class="footer-system-info">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="d-flex align-items-center">
                            <div class="system-status online me-3">
                                <span class="status-dot"></span>
                                <small>متصل</small>
                            </div>
                            <div class="last-update me-3">
                                <i class="fas fa-clock me-1"></i>
                                <small class="real-time"><?php echo date('Y-m-d H:i:s'); ?></small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <div class="footer-actions">
                            <button class="btn btn-sm btn-outline-primary me-2" onclick="refreshPage()">
                                <i class="fas fa-sync-alt me-1"></i>تحديث
                            </button>
                            <button class="btn btn-sm btn-outline-primary me-2" onclick="toggleTheme()">
                                <i class="fas fa-moon me-1"></i>الوضع المظلم
                            </button>
                            <button class="btn btn-sm btn-outline-primary" onclick="showHelp()">
                                <i class="fas fa-question me-1"></i>مساعدة
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- حقوق النشر -->
            <hr class="my-4">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0 text-muted">
                        &copy; <?php echo date('Y'); ?> منصة التعليم الإلكتروني - لوحة تحكم الطالب
                    </p>
                </div>
                <div class="col-md-6 text-md-end">
                    <div class="footer-social">
                        <a href="#" class="social-link" title="المساعدة">
                            <i class="fas fa-question-circle"></i>
                        </a>
                        <a href="#" class="social-link" title="الدعم الفني">
                            <i class="fas fa-headset"></i>
                        </a>
                        <a href="#" class="social-link" title="التوثيق">
                            <i class="fas fa-book"></i>
                        </a>
                        <a href="#" class="social-link" title="التحديثات">
                            <i class="fas fa-bell"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <style>
        /* شريط التنقل السفلي للهاتف */
        .mobile-bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-top: 1px solid #e2e8f0;
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            z-index: 1000;
            padding: 0.5rem 0;
        }

        .mobile-nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 0.5rem;
            color: var(--student-secondary);
            text-decoration: none;
            transition: all 0.3s ease;
            border-radius: 0.5rem;
            margin: 0 0.25rem;
        }

        .mobile-nav-item i {
            font-size: 1.2rem;
            margin-bottom: 0.25rem;
        }

        .mobile-nav-item span {
            font-size: 0.7rem;
            font-weight: 500;
        }

        .mobile-nav-item.active,
        .mobile-nav-item:hover {
            color: var(--student-primary);
            background-color: rgba(59, 130, 246, 0.1);
            transform: translateY(-2px);
        }

        /* زر الإجراءات العائم */
        .floating-action-btn {
            position: fixed;
            bottom: 80px;
            left: 20px;
            z-index: 1001;
        }

        .fab-main {
            width: 56px;
            height: 56px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--student-primary), #1d4ed8);
            border: none;
            color: white;
            font-size: 1.5rem;
            box-shadow: 0 4px 20px rgba(59, 130, 246, 0.3);
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .fab-main:hover {
            transform: scale(1.1) rotate(45deg);
            box-shadow: 0 6px 25px rgba(59, 130, 246, 0.4);
        }

        .fab-menu {
            bottom: 70px;
            left: 0;
            min-width: 280px;
            border: none;
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
            border-radius: 1rem;
            padding: 0.5rem;
        }

        .fab-item {
            display: flex;
            align-items: center;
            padding: 1rem;
            border-radius: 0.75rem;
            transition: all 0.3s ease;
            border: none;
        }

        .fab-item:hover {
            background-color: #f8fafc;
            transform: translateX(-5px);
        }

        .fab-item i {
            width: 40px;
            height: 40px;
            border-radius: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 1rem;
            font-size: 1.2rem;
        }

        .fab-item div {
            flex: 1;
        }

        .fab-item strong {
            display: block;
            font-weight: 600;
            color: var(--student-dark);
        }

        .fab-item small {
            color: var(--student-secondary);
            font-size: 0.8rem;
        }

        /* Footer المحسن */
        .student-footer {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-top: 1px solid #e2e8f0;
            padding: 3rem 0 1rem;
            margin-top: auto;
            margin-bottom: 70px;
        }

        .footer-quick-info {
            border-bottom: 1px solid #e2e8f0;
            padding-bottom: 2rem;
        }

        .quick-stat {
            background: white;
            border-radius: 1rem;
            padding: 1.5rem;
            display: flex;
            align-items: center;
            transition: all 0.3s ease;
            border: 1px solid #f1f5f9;
            position: relative;
            overflow: hidden;
        }

        .quick-stat::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: var(--student-primary);
            transform: scaleY(0);
            transition: transform 0.3s ease;
        }

        .quick-stat:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .quick-stat:hover::before {
            transform: scaleY(1);
        }

        .stat-icon {
            width: 50px;
            height: 50px;
            border-radius: 0.75rem;
            background: linear-gradient(135deg, var(--student-primary), #1d4ed8);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.3rem;
            margin-left: 1rem;
        }

        .stat-content {
            flex: 1;
        }

        .stat-content h6 {
            color: var(--student-dark);
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        .stat-link {
            width: 35px;
            height: 35px;
            border-radius: 0.5rem;
            background: #f1f5f9;
            color: var(--student-secondary);
            display: flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .stat-link:hover {
            background: var(--student-primary);
            color: white;
            transform: translateX(-3px);
        }

        .footer-title {
            color: var(--student-dark);
            font-weight: 600;
            margin-bottom: 1rem;
            position: relative;
            padding-bottom: 0.5rem;
        }

        .footer-title::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 30px;
            height: 2px;
            background: var(--student-primary);
        }

        .footer-menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .footer-menu li {
            margin-bottom: 0.5rem;
        }

        .footer-menu a {
            color: var(--student-secondary);
            text-decoration: none;
            display: flex;
            align-items: center;
            padding: 0.5rem 0;
            transition: all 0.3s ease;
            border-radius: 0.5rem;
        }

        .footer-menu a:hover {
            color: var(--student-primary);
            background-color: rgba(59, 130, 246, 0.05);
            transform: translateX(-5px);
            padding-left: 0.5rem;
        }

        .footer-system-info {
            background: white;
            border-radius: 1rem;
            padding: 1.5rem;
            margin-bottom: 1rem;
            border: 1px solid #f1f5f9;
        }

        .system-status {
            display: flex;
            align-items: center;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #10b981;
            margin-left: 0.5rem;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .last-update {
            display: flex;
            align-items: center;
            color: var(--student-secondary);
        }

        .footer-actions .btn {
            border-radius: 0.5rem;
            font-weight: 500;
        }

        .footer-social {
            display: flex;
            gap: 0.5rem;
        }

        .social-link {
            width: 40px;
            height: 40px;
            border-radius: 0.5rem;
            background: #f1f5f9;
            color: var(--student-secondary);
            display: flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .social-link:hover {
            background: var(--student-primary);
            color: white;
            transform: translateY(-2px);
        }

        @media (max-width: 768px) {
            .student-footer {
                text-align: center;
                margin-bottom: 80px;
                padding: 2rem 0 1rem;
            }
            
            .footer-quick-info .row {
                justify-content: center;
            }
            
            .quick-stat {
                margin-bottom: 1rem;
            }
            
            .footer-links .col-lg-3 {
                margin-bottom: 2rem;
            }
            
            .floating-action-btn {
                bottom: 90px;
                left: 50%;
                transform: translateX(-50%);
            }
            
            .fab-menu {
                left: 50%;
                transform: translateX(-50%);
                bottom: 80px;
            }
        }
    </style>

    <script>
        // دوال الفوتر المحسن
        function refreshPage() {
            location.reload();
        }

        function toggleTheme() {
            const body = document.body;
            const isDark = body.classList.contains('dark-theme');
            
            if (isDark) {
                body.classList.remove('dark-theme');
                localStorage.setItem('student_theme', 'light');
            } else {
                body.classList.add('dark-theme');
                localStorage.setItem('student_theme', 'dark');
            }
        }

        function showHelp() {
            window.open('help.php', '_blank');
        }

        // تحديث الوقت في الفوتر
        function updateFooterTime() {
            const timeElements = document.querySelectorAll('.real-time');
            const now = new Date();
            const timeString = now.toLocaleString('ar-SA', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            
            timeElements.forEach(element => {
                element.textContent = timeString;
            });
        }

        // تحديث الوقت كل ثانية
        setInterval(updateFooterTime, 1000);

        // تطبيق الثيم المحفوظ
        document.addEventListener('DOMContentLoaded', function() {
            const savedTheme = localStorage.getItem('student_theme') || 'light';
            if (savedTheme === 'dark') {
                document.body.classList.add('dark-theme');
            }
        });
    </script>
</body>
</html>
