<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات المدرب
if (!isLoggedIn() || !isInstructor()) {
    header('Location: ../login.php');
    exit;
}

$pageTitle = 'إنشاء اختبار جديد';
$breadcrumbs = [
    ['title' => 'الاختبارات', 'url' => 'quizzes.php'],
    ['title' => 'إنشاء اختبار جديد']
];

// معالجة إنشاء الاختبار
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $course_id = $_POST['course_id'] ?? 0;
    $title = trim($_POST['title'] ?? '');
    $description = trim($_POST['description'] ?? '');
    $time_limit = $_POST['time_limit'] ?? 0;
    $max_attempts = $_POST['max_attempts'] ?? 1;
    $passing_grade = $_POST['passing_grade'] ?? 60;
    $is_randomized = isset($_POST['is_randomized']) ? 1 : 0;
    $show_results = isset($_POST['show_results']) ? 1 : 0;
    $available_from = $_POST['available_from'] ?? '';
    $available_until = $_POST['available_until'] ?? '';
    
    $errors = [];
    
    if (empty($title)) {
        $errors[] = 'عنوان الاختبار مطلوب';
    }
    
    if (empty($course_id)) {
        $errors[] = 'يجب اختيار كورس';
    }
    
    if (empty($errors)) {
        try {
            // التحقق من أن الكورس ينتمي للمدرب
            $stmt = $conn->prepare("SELECT id FROM courses WHERE id = ? AND instructor_id = ?");
            $stmt->execute([$course_id, $_SESSION['user_id']]);
            
            if ($stmt->fetch()) {
                $stmt = $conn->prepare("
                    INSERT INTO quizzes (course_id, title, description, time_limit, max_attempts, passing_grade, 
                                       is_randomized, show_results, available_from, available_until, created_by) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ");
                $stmt->execute([$course_id, $title, $description, $time_limit, $max_attempts, $passing_grade, 
                              $is_randomized, $show_results, $available_from ?: null, $available_until ?: null, $_SESSION['user_id']]);
                
                $quiz_id = $conn->lastInsertId();
                
                // التوجيه المباشر لصفحة إضافة الأسئلة
                header("Location: add-quiz-questions.php?quiz_id=$quiz_id");
                exit;
            } else {
                $errors[] = 'الكورس المحدد غير صحيح';
            }
        } catch (PDOException $e) {
            $errors[] = 'حدث خطأ أثناء إنشاء الاختبار';
        }
    }
}

// جلب الكورسات الخاصة بالمدرب
try {
    $stmt = $conn->prepare("
        SELECT id, title 
        FROM courses 
        WHERE instructor_id = ? AND status = 'active'
        ORDER BY title
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $instructor_courses = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $instructor_courses = [];
}

include 'includes/header.php';
?>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="h3 mb-2">
                <i class="fas fa-plus-circle text-primary me-2"></i>
                إنشاء اختبار جديد
            </h2>
            <p class="text-muted mb-0">أنشئ اختباراً تفاعلياً لتقييم طلابك</p>
        </div>
        <div>
            <a href="quizzes.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-1"></i>العودة للاختبارات
            </a>
        </div>
    </div>

    <!-- رسائل الخطأ -->
    <?php if (!empty($errors)): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <ul class="mb-0">
            <?php foreach ($errors as $error): ?>
            <li><?php echo $error; ?></li>
            <?php endforeach; ?>
        </ul>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <!-- نموذج إنشاء الاختبار -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card border-0 shadow-lg">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-clipboard-list me-2"></i>
                        معلومات الاختبار
                    </h5>
                </div>
                <div class="card-body p-4">
                    <form method="POST" id="createQuizForm">
                        <!-- معلومات أساسية -->
                        <div class="row">
                            <div class="col-md-12 mb-4">
                                <label for="course_id" class="form-label fw-bold">
                                    <i class="fas fa-book text-primary me-1"></i>
                                    الكورس <span class="text-danger">*</span>
                                </label>
                                <select name="course_id" id="course_id" class="form-select form-select-lg" required>
                                    <option value="">-- اختر الكورس --</option>
                                    <?php foreach ($instructor_courses as $course): ?>
                                    <option value="<?php echo $course['id']; ?>" 
                                            <?php echo (isset($_POST['course_id']) && $_POST['course_id'] == $course['id']) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($course['title']); ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12 mb-4">
                                <label for="title" class="form-label fw-bold">
                                    <i class="fas fa-heading text-success me-1"></i>
                                    عنوان الاختبار <span class="text-danger">*</span>
                                </label>
                                <input type="text" name="title" id="title" class="form-control form-control-lg"
                                       placeholder="مثال: الاختبار الأول - مقدمة في البرمجة" 
                                       value="<?php echo htmlspecialchars($_POST['title'] ?? ''); ?>" required>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12 mb-4">
                                <label for="description" class="form-label fw-bold">
                                    <i class="fas fa-align-left text-info me-1"></i>
                                    وصف الاختبار
                                </label>
                                <textarea name="description" id="description" class="form-control" rows="4"
                                          placeholder="وصف مختصر عن الاختبار ومحتواه..."><?php echo htmlspecialchars($_POST['description'] ?? ''); ?></textarea>
                            </div>
                        </div>

                        <!-- إعدادات الاختبار -->
                        <div class="card bg-light border-0 mb-4">
                            <div class="card-header bg-transparent">
                                <h6 class="mb-0 text-primary">
                                    <i class="fas fa-cogs me-2"></i>
                                    إعدادات الاختبار
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label for="time_limit" class="form-label">
                                            <i class="fas fa-clock text-warning me-1"></i>
                                            المدة الزمنية (دقيقة)
                                        </label>
                                        <input type="number" name="time_limit" id="time_limit" class="form-control"
                                               value="<?php echo $_POST['time_limit'] ?? '60'; ?>" min="0" placeholder="0 = غير محدود">
                                        <small class="form-text text-muted">اتركه 0 للوقت غير المحدود</small>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label for="max_attempts" class="form-label">
                                            <i class="fas fa-redo text-primary me-1"></i>
                                            عدد المحاولات
                                        </label>
                                        <input type="number" name="max_attempts" id="max_attempts" class="form-control"
                                               value="<?php echo $_POST['max_attempts'] ?? '1'; ?>" min="1" max="10" required>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label for="passing_grade" class="form-label">
                                            <i class="fas fa-trophy text-success me-1"></i>
                                            درجة النجاح (%)
                                        </label>
                                        <input type="number" name="passing_grade" id="passing_grade" class="form-control"
                                               value="<?php echo $_POST['passing_grade'] ?? '60'; ?>" min="0" max="100" required>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- جدولة الاختبار -->
                        <div class="card bg-light border-0 mb-4">
                            <div class="card-header bg-transparent">
                                <h6 class="mb-0 text-info">
                                    <i class="fas fa-calendar-alt me-2"></i>
                                    جدولة الاختبار (اختياري)
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="available_from" class="form-label">متاح من</label>
                                        <input type="datetime-local" name="available_from" id="available_from" 
                                               class="form-control" value="<?php echo $_POST['available_from'] ?? ''; ?>">
                                        <small class="form-text text-muted">اتركه فارغاً ليكون متاحاً فوراً</small>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="available_until" class="form-label">متاح حتى</label>
                                        <input type="datetime-local" name="available_until" id="available_until" 
                                               class="form-control" value="<?php echo $_POST['available_until'] ?? ''; ?>">
                                        <small class="form-text text-muted">اتركه فارغاً ليبقى متاحاً دائماً</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- خيارات إضافية -->
                        <div class="card bg-light border-0 mb-4">
                            <div class="card-header bg-transparent">
                                <h6 class="mb-0 text-secondary">
                                    <i class="fas fa-sliders-h me-2"></i>
                                    خيارات إضافية
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" name="is_randomized" 
                                                   id="is_randomized" <?php echo (isset($_POST['is_randomized'])) ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="is_randomized">
                                                <i class="fas fa-random text-info me-1"></i>
                                                ترتيب عشوائي للأسئلة
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" name="show_results" 
                                                   id="show_results" <?php echo (!isset($_POST['show_results']) || isset($_POST['show_results'])) ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="show_results">
                                                <i class="fas fa-eye text-success me-1"></i>
                                                إظهار النتائج للطلاب
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div class="d-flex justify-content-between align-items-center">
                            <a href="quizzes.php" class="btn btn-outline-secondary btn-lg">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </a>
                           <A href="add-quiz-questions.php">
                            <button type="submit" class="btn btn-primary btn-lg px-5" id="submitBtn">
                                <i class="fas fa-rocket me-2"></i>إنشاء الاختبار وإضافة الأسئلة
                            </button>
                            </A>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border: none;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8, #6a4190);
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.btn-primary:active {
    transform: translateY(-1px) scale(0.98);
}

#submitBtn {
    position: relative;
    overflow: hidden;
}

#submitBtn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

#submitBtn:hover::before {
    left: 100%;
}

.btn-lg {
    font-weight: 600;
    letter-spacing: 0.5px;
}

.form-check-input:checked {
    background-color: #667eea;
    border-color: #667eea;
}

.card-header {
    border-radius: 0.5rem 0.5rem 0 0 !important;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes shimmer {
    0% { background-position: -200px 0; }
    100% { background-position: calc(200px + 100%) 0; }
}

.progress-bar-animated {
    animation: shimmer 1.5s infinite linear;
    background: linear-gradient(90deg, #667eea, #764ba2, #667eea);
    background-size: 200px 100%;
}

@media (max-width: 768px) {
    .btn-lg {
        font-size: 1rem;
        padding: 0.75rem 1.5rem;
    }

    .card-body {
        padding: 2rem 1rem;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // التركيز على أول حقل
    const firstInput = document.getElementById('course_id');
    if (firstInput) {
        firstInput.focus();
    }

    // تحسين تجربة الزر
    const submitBtn = document.getElementById('submitBtn');
    if (submitBtn) {
        // تأثير نبض للزر
        setInterval(() => {
            if (!submitBtn.disabled && !submitBtn.matches(':hover')) {
                submitBtn.style.animation = 'pulse 1.5s ease-in-out';
                setTimeout(() => {
                    submitBtn.style.animation = '';
                }, 1500);
            }
        }, 5000);

        // تأثير عند النقر
        submitBtn.addEventListener('click', function() {
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
        });
    }

    // تحسين تجربة الحقول
    const inputs = document.querySelectorAll('.form-control, .form-select');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentNode.style.transform = 'scale(1.02)';
            this.parentNode.style.transition = 'transform 0.2s ease';
        });

        input.addEventListener('blur', function() {
            this.parentNode.style.transform = 'scale(1)';
        });
    });
    
    // تحديث الحد الأدنى للتواريخ
    const availableFromInput = document.getElementById('available_from');
    const availableUntilInput = document.getElementById('available_until');
    
    if (availableFromInput && availableUntilInput) {
        const now = new Date();
        const nowString = now.toISOString().slice(0, 16);
        availableFromInput.min = nowString;
        availableUntilInput.min = nowString;
        
        availableFromInput.addEventListener('change', function() {
            if (this.value) {
                availableUntilInput.min = this.value;
                if (availableUntilInput.value && availableUntilInput.value < this.value) {
                    availableUntilInput.value = '';
                }
            }
        });
    }
    
    // التحقق من صحة النموذج
    document.getElementById('createQuizForm').addEventListener('submit', function(e) {
        const availableFrom = availableFromInput.value;
        const availableUntil = availableUntilInput.value;

        if (availableFrom && availableUntil && availableFrom >= availableUntil) {
            e.preventDefault();
            alert('تاريخ الانتهاء يجب أن يكون بعد تاريخ البداية');
            return false;
        }

        // التحقق من الحقول المطلوبة
        const courseId = document.getElementById('course_id').value;
        const title = document.getElementById('title').value.trim();

        if (!courseId) {
            e.preventDefault();
            alert('يرجى اختيار الكورس');
            document.getElementById('course_id').focus();
            return false;
        }

        if (!title) {
            e.preventDefault();
            alert('يرجى إدخال عنوان الاختبار');
            document.getElementById('title').focus();
            return false;
        }

        // إظهار مؤشر التحميل مع رسالة أكثر تفاعلية
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;

        // تأثير بصري فوري
        submitBtn.innerHTML = '<i class="fas fa-rocket fa-spin me-2"></i>جاري إنشاء الاختبار...';
        submitBtn.disabled = true;
        submitBtn.style.transform = 'scale(0.98)';

        // إضافة شريط تقدم
        const progressBar = document.createElement('div');
        progressBar.className = 'progress mt-3';
        progressBar.innerHTML = `
            <div class="progress-bar progress-bar-striped progress-bar-animated bg-primary"
                 role="progressbar" style="width: 0%"></div>
        `;
        submitBtn.parentNode.appendChild(progressBar);

        // تحريك شريط التقدم
        let progress = 0;
        const progressInterval = setInterval(() => {
            progress += Math.random() * 30;
            if (progress > 90) progress = 90;
            progressBar.querySelector('.progress-bar').style.width = progress + '%';
        }, 200);

        // في حالة حدوث خطأ، إعادة تعيين الزر
        setTimeout(() => {
            clearInterval(progressInterval);
            if (progressBar.parentNode) {
                progressBar.parentNode.removeChild(progressBar);
            }
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
            submitBtn.style.transform = '';
        }, 10000);
    });
});
</script>

<?php include 'includes/footer.php'; ?>
