<?php
require_once '../config/database.php';

echo "<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <title>اختبار المدربين</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
</head>
<body>
<div class='container mt-5'>
    <div class='card'>
        <div class='card-header bg-info text-white'>
            <h4>اختبار بيانات المدربين</h4>
        </div>
        <div class='card-body'>";

try {
    // جلب جميع المدربين
    $instructors = $conn->query("SELECT * FROM users WHERE role = 'instructor' ORDER BY id")->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h5>المدربين الموجودين:</h5>";
    
    if (empty($instructors)) {
        echo "<div class='alert alert-warning'>لا يوجد مدربين في النظام</div>";
        
        // إنشاء مدرب تجريبي
        $stmt = $conn->prepare("
            INSERT INTO users (name, email, password, role, phone, specialization, experience_years, status) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $test_instructors = [
            ['أحمد محمد', '<EMAIL>', password_hash('123456', PASSWORD_DEFAULT), 'instructor', '0501234567', 'تطوير الويب', 5, 'active'],
            ['فاطمة علي', '<EMAIL>', password_hash('123456', PASSWORD_DEFAULT), 'instructor', '0507654321', 'التسويق الرقمي', 3, 'active'],
            ['محمد سالم', '<EMAIL>', password_hash('123456', PASSWORD_DEFAULT), 'instructor', '0509876543', 'تصميم الجرافيك', 7, 'active']
        ];
        
        foreach ($test_instructors as $instructor) {
            $stmt->execute($instructor);
        }
        
        echo "<div class='alert alert-success'>تم إنشاء 3 مدربين تجريبيين</div>";
        
        // إعادة جلب المدربين
        $instructors = $conn->query("SELECT * FROM users WHERE role = 'instructor' ORDER BY id")->fetchAll(PDO::FETCH_ASSOC);
    }
    
    echo "<div class='table-responsive'>
            <table class='table table-striped'>
                <thead>
                    <tr>
                        <th>المعرف</th>
                        <th>الاسم</th>
                        <th>البريد الإلكتروني</th>
                        <th>الهاتف</th>
                        <th>التخصص</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>";
    
    foreach ($instructors as $instructor) {
        $status_color = $instructor['status'] === 'active' ? 'success' : 'secondary';
        echo "<tr>
                <td>{$instructor['id']}</td>
                <td>" . htmlspecialchars($instructor['name']) . "</td>
                <td>" . htmlspecialchars($instructor['email']) . "</td>
                <td>" . htmlspecialchars($instructor['phone'] ?? 'غير محدد') . "</td>
                <td>" . htmlspecialchars($instructor['specialization'] ?? 'غير محدد') . "</td>
                <td><span class='badge bg-{$status_color}'>{$instructor['status']}</span></td>
                <td>
                    <a href='instructor-details.php?id={$instructor['id']}' class='btn btn-primary btn-sm' target='_blank'>
                        <i class='fas fa-eye'></i> عرض التفاصيل
                    </a>
                    <a href='edit-instructor.php?id={$instructor['id']}' class='btn btn-warning btn-sm' target='_blank'>
                        <i class='fas fa-edit'></i> تعديل
                    </a>
                </td>
              </tr>";
    }
    
    echo "</tbody></table></div>";
    
    // إحصائيات سريعة
    $stats = [
        'total_instructors' => count($instructors),
        'active_instructors' => count(array_filter($instructors, fn($i) => $i['status'] === 'active')),
        'total_courses' => $conn->query("SELECT COUNT(*) FROM courses")->fetchColumn(),
        'total_students' => $conn->query("SELECT COUNT(*) FROM users WHERE role = 'student'")->fetchColumn()
    ];
    
    echo "<div class='row mt-4'>
            <div class='col-md-3'>
                <div class='card bg-primary text-white'>
                    <div class='card-body text-center'>
                        <h3>{$stats['total_instructors']}</h3>
                        <p>إجمالي المدربين</p>
                    </div>
                </div>
            </div>
            <div class='col-md-3'>
                <div class='card bg-success text-white'>
                    <div class='card-body text-center'>
                        <h3>{$stats['active_instructors']}</h3>
                        <p>المدربين النشطين</p>
                    </div>
                </div>
            </div>
            <div class='col-md-3'>
                <div class='card bg-info text-white'>
                    <div class='card-body text-center'>
                        <h3>{$stats['total_courses']}</h3>
                        <p>إجمالي الكورسات</p>
                    </div>
                </div>
            </div>
            <div class='col-md-3'>
                <div class='card bg-warning text-white'>
                    <div class='card-body text-center'>
                        <h3>{$stats['total_students']}</h3>
                        <p>إجمالي الطلاب</p>
                    </div>
                </div>
            </div>
          </div>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>خطأ: " . $e->getMessage() . "</div>";
}

echo "
        </div>
        <div class='card-footer'>
            <a href='manage-instructors.php' class='btn btn-primary'>إدارة المدربين</a>
            <a href='analytics.php' class='btn btn-info'>التحليلات</a>
        </div>
    </div>
</div>
</body>
</html>";
?>
