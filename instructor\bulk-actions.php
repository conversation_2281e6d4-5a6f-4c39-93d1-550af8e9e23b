<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات المدرب
if (!isLoggedIn() || !isInstructor()) {
    header('Location: ../login.php');
    exit;
}

$pageTitle = 'الإجراءات الجماعية';
$breadcrumbs = [
    ['title' => 'الطلاب', 'url' => 'students.php'],
    ['title' => 'الإجراءات الجماعية']
];

// معالجة الإجراءات الجماعية
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $selected_students = $_POST['selected_students'] ?? [];
    $course_id = $_POST['course_id'] ?? '';
    
    $errors = [];
    $success_count = 0;
    
    if (empty($selected_students)) {
        $errors[] = 'يرجى تحديد طلاب أولاً';
    }
    
    if (empty($errors)) {
        try {
            switch ($action) {
                case 'send_message':
                    $message_subject = trim($_POST['message_subject'] ?? '');
                    $message_content = trim($_POST['message_content'] ?? '');
                    
                    if (empty($message_subject) || empty($message_content)) {
                        $errors[] = 'عنوان الرسالة والمحتوى مطلوبان';
                        break;
                    }
                    
                    foreach ($selected_students as $student_id) {
                        // إرسال رسالة (يمكن تطوير هذا لاحقاً)
                        $success_count++;
                    }
                    $success_message = "تم إرسال الرسالة لـ $success_count طالب";
                    break;
                    
                case 'update_status':
                    $new_status = $_POST['new_status'] ?? '';
                    
                    if (empty($new_status)) {
                        $errors[] = 'يرجى اختيار الحالة الجديدة';
                        break;
                    }
                    
                    foreach ($selected_students as $student_id) {
                        $stmt = $conn->prepare("
                            UPDATE course_enrollments ce
                            INNER JOIN courses c ON ce.course_id = c.id
                            SET ce.status = ?
                            WHERE ce.student_id = ? AND c.instructor_id = ?
                        ");
                        if ($stmt->execute([$new_status, $student_id, $_SESSION['user_id']])) {
                            $success_count++;
                        }
                    }
                    $success_message = "تم تحديث حالة $success_count طالب";
                    break;
                    
                case 'assign_grade':
                    $assignment_name = trim($_POST['assignment_name'] ?? '');
                    $grade = $_POST['grade'] ?? 0;
                    $max_grade = $_POST['max_grade'] ?? 100;
                    $notes = trim($_POST['notes'] ?? '');
                    
                    if (empty($assignment_name) || empty($course_id)) {
                        $errors[] = 'اسم الواجب والكورس مطلوبان';
                        break;
                    }
                    
                    foreach ($selected_students as $student_id) {
                        $stmt = $conn->prepare("
                            INSERT INTO student_grades (course_id, student_id, assignment_name, grade, max_grade, notes, graded_by) 
                            VALUES (?, ?, ?, ?, ?, ?, ?)
                        ");
                        if ($stmt->execute([$course_id, $student_id, $assignment_name, $grade, $max_grade, $notes, $_SESSION['user_id']])) {
                            $success_count++;
                        }
                    }
                    $success_message = "تم إضافة درجة لـ $success_count طالب";
                    break;
                    
                case 'export_data':
                    $export_type = $_POST['export_type'] ?? 'basic';
                    
                    // إعداد التصدير
                    header('Content-Type: text/csv; charset=utf-8');
                    header('Content-Disposition: attachment; filename="students_data_' . date('Y-m-d') . '.csv"');
                    
                    $output = fopen('php://output', 'w');
                    
                    // كتابة العناوين
                    if ($export_type === 'detailed') {
                        fputcsv($output, ['الاسم', 'البريد الإلكتروني', 'الهاتف', 'الكورس', 'تاريخ التسجيل', 'الحالة', 'التقدم', 'متوسط الدرجات']);
                    } else {
                        fputcsv($output, ['الاسم', 'البريد الإلكتروني', 'الكورس', 'الحالة']);
                    }
                    
                    // جلب بيانات الطلاب المحددين
                    $placeholders = str_repeat('?,', count($selected_students) - 1) . '?';
                    $stmt = $conn->prepare("
                        SELECT 
                            u.name, u.email, u.phone,
                            c.title as course_title,
                            ce.enrolled_at, ce.status, ce.progress_percentage,
                            AVG(sg.grade) as avg_grade
                        FROM users u
                        INNER JOIN course_enrollments ce ON u.id = ce.student_id
                        INNER JOIN courses c ON ce.course_id = c.id
                        LEFT JOIN student_grades sg ON u.id = sg.student_id AND c.id = sg.course_id
                        WHERE u.id IN ($placeholders) AND c.instructor_id = ?
                        GROUP BY u.id, c.id
                    ");
                    $params = array_merge($selected_students, [$_SESSION['user_id']]);
                    $stmt->execute($params);
                    
                    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                        if ($export_type === 'detailed') {
                            fputcsv($output, [
                                $row['name'],
                                $row['email'],
                                $row['phone'],
                                $row['course_title'],
                                $row['enrolled_at'],
                                $row['status'],
                                $row['progress_percentage'] . '%',
                                number_format($row['avg_grade'], 2)
                            ]);
                        } else {
                            fputcsv($output, [
                                $row['name'],
                                $row['email'],
                                $row['course_title'],
                                $row['status']
                            ]);
                        }
                    }
                    
                    fclose($output);
                    exit;
                    break;
            }
            
        } catch (PDOException $e) {
            $errors[] = 'حدث خطأ أثناء تنفيذ العملية: ' . $e->getMessage();
        }
    }
}

// جلب الكورسات الخاصة بالمدرب
try {
    $stmt = $conn->prepare("
        SELECT id, title 
        FROM courses 
        WHERE instructor_id = ? AND status = 'active'
        ORDER BY title
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $instructor_courses = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $instructor_courses = [];
}

// جلب الطلاب للاختيار
$course_filter = $_GET['course_id'] ?? '';
try {
    $where_clause = "c.instructor_id = ?";
    $params = [$_SESSION['user_id']];
    
    if ($course_filter) {
        $where_clause .= " AND c.id = ?";
        $params[] = $course_filter;
    }
    
    $stmt = $conn->prepare("
        SELECT 
            u.id, u.name, u.email,
            c.id as course_id, c.title as course_title,
            ce.status, ce.progress_percentage,
            AVG(sg.grade) as avg_grade
        FROM users u
        INNER JOIN course_enrollments ce ON u.id = ce.student_id
        INNER JOIN courses c ON ce.course_id = c.id
        LEFT JOIN student_grades sg ON u.id = sg.student_id AND c.id = sg.course_id
        WHERE $where_clause
        GROUP BY u.id, c.id
        ORDER BY u.name, c.title
    ");
    $stmt->execute($params);
    $students = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $students = [];
}

include 'includes/header.php';
?>

<!-- رأس الصفحة -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="h3 mb-2">
            <i class="fas fa-tasks text-primary me-2"></i>
            الإجراءات الجماعية
        </h2>
        <p class="text-muted mb-0">تنفيذ إجراءات على مجموعة من الطلاب في نفس الوقت</p>
    </div>
    <div class="d-flex gap-2">
        <a href="students.php" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>العودة للطلاب
        </a>
        <button class="btn btn-info" onclick="showHelp()">
            <i class="fas fa-question-circle me-1"></i>مساعدة
        </button>
    </div>
</div>

<!-- رسائل النجاح والخطأ -->
<?php if (!empty($errors)): ?>
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <i class="fas fa-exclamation-triangle me-2"></i>
    <ul class="mb-0">
        <?php foreach ($errors as $error): ?>
        <li><?php echo htmlspecialchars($error); ?></li>
        <?php endforeach; ?>
    </ul>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<?php if (isset($success_message)): ?>
<div class="alert alert-success alert-dismissible fade show" role="alert">
    <i class="fas fa-check-circle me-2"></i>
    <?php echo $success_message; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<!-- فلتر الكورس -->
<div class="card border-0 shadow-sm mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3 align-items-end">
            <div class="col-md-6">
                <label for="course_id" class="form-label">فلترة حسب الكورس</label>
                <select name="course_id" id="course_id" class="form-select" onchange="this.form.submit()">
                    <option value="">جميع الكورسات</option>
                    <?php foreach ($instructor_courses as $course): ?>
                    <option value="<?php echo $course['id']; ?>" 
                            <?php echo $course_filter == $course['id'] ? 'selected' : ''; ?>>
                        <?php echo htmlspecialchars($course['title']); ?>
                    </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-6">
                <div class="d-flex gap-2">
                    <button type="button" class="btn btn-primary" onclick="selectAll()">
                        <i class="fas fa-check-square me-1"></i>تحديد الكل
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="clearSelection()">
                        <i class="fas fa-square me-1"></i>إلغاء التحديد
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- قائمة الطلاب -->
<div class="row g-4">
    <!-- قائمة الطلاب للاختيار -->
    <div class="col-lg-8">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-bottom">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-users me-2"></i>
                        اختيار الطلاب
                    </h5>
                    <span class="badge bg-primary" id="selectedCount">0 محدد</span>
                </div>
            </div>
            <div class="card-body p-0">
                <?php if (empty($students)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-user-graduate text-muted" style="font-size: 3rem;"></i>
                    <h5 class="mt-3 text-muted">لا توجد طلاب</h5>
                    <p class="text-muted">لا يوجد طلاب في الكورس المحدد</p>
                </div>
                <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>
                                    <input type="checkbox" class="form-check-input" id="selectAllCheckbox">
                                </th>
                                <th>الطالب</th>
                                <th>الكورس</th>
                                <th>الحالة</th>
                                <th>التقدم</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($students as $student): ?>
                            <tr>
                                <td>
                                    <input type="checkbox" class="form-check-input student-checkbox" 
                                           value="<?php echo $student['id']; ?>"
                                           data-course="<?php echo $student['course_id']; ?>">
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-sm bg-primary bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center me-2">
                                            <i class="fas fa-user text-primary"></i>
                                        </div>
                                        <div>
                                            <h6 class="mb-0"><?php echo htmlspecialchars($student['name']); ?></h6>
                                            <small class="text-muted"><?php echo htmlspecialchars($student['email']); ?></small>
                                        </div>
                                    </div>
                                </td>
                                <td><?php echo htmlspecialchars($student['course_title']); ?></td>
                                <td>
                                    <?php
                                    $status_classes = ['active' => 'success', 'completed' => 'primary', 'dropped' => 'danger'];
                                    $status_text = ['active' => 'نشط', 'completed' => 'مكتمل', 'dropped' => 'منسحب'];
                                    ?>
                                    <span class="badge bg-<?php echo $status_classes[$student['status']] ?? 'secondary'; ?>">
                                        <?php echo $status_text[$student['status']] ?? $student['status']; ?>
                                    </span>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <span class="me-2"><?php echo number_format($student['progress_percentage'], 1); ?>%</span>
                                        <div class="progress flex-grow-1" style="width: 60px; height: 6px;">
                                            <div class="progress-bar bg-success" 
                                                 style="width: <?php echo $student['progress_percentage']; ?>%"></div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- لوحة الإجراءات -->
    <div class="col-lg-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-light border-bottom">
                <h6 class="mb-0">
                    <i class="fas fa-cogs me-2"></i>
                    الإجراءات المتاحة
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-primary" onclick="showMessageModal()">
                        <i class="fas fa-envelope me-2"></i>إرسال رسالة جماعية
                    </button>
                    <button class="btn btn-warning" onclick="showStatusModal()">
                        <i class="fas fa-edit me-2"></i>تحديث الحالة
                    </button>
                    <button class="btn btn-success" onclick="showGradeModal()">
                        <i class="fas fa-star me-2"></i>إضافة درجة
                    </button>
                    <button class="btn btn-info" onclick="showExportModal()">
                        <i class="fas fa-download me-2"></i>تصدير البيانات
                    </button>
                </div>
                
                <hr>
                
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>تلميح:</strong> حدد الطلاب أولاً ثم اختر الإجراء المطلوب.
                </div>
            </div>
        </div>
        
        <!-- إحصائيات سريعة -->
        <div class="card border-0 shadow-sm mt-3">
            <div class="card-header bg-light border-bottom">
                <h6 class="mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    إحصائيات سريعة
                </h6>
            </div>
            <div class="card-body">
                <div class="row g-3 text-center">
                    <div class="col-6">
                        <h5 class="text-primary mb-1"><?php echo count($students); ?></h5>
                        <small class="text-muted">إجمالي الطلاب</small>
                    </div>
                    <div class="col-6">
                        <h5 class="text-success mb-1">
                            <?php echo count(array_filter($students, function($s) { return $s['status'] === 'active'; })); ?>
                        </h5>
                        <small class="text-muted">طلاب نشطون</small>
                    </div>
                    <div class="col-6">
                        <h5 class="text-info mb-1">
                            <?php 
                            $avg_progress = !empty($students) ? array_sum(array_column($students, 'progress_percentage')) / count($students) : 0;
                            echo number_format($avg_progress, 1); 
                            ?>%
                        </h5>
                        <small class="text-muted">متوسط التقدم</small>
                    </div>
                    <div class="col-6">
                        <h5 class="text-warning mb-1">
                            <?php 
                            $grades = array_filter(array_column($students, 'avg_grade'));
                            $avg_grade = !empty($grades) ? array_sum($grades) / count($grades) : 0;
                            echo number_format($avg_grade, 1); 
                            ?>
                        </h5>
                        <small class="text-muted">متوسط الدرجات</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal إرسال رسالة جماعية -->
<div class="modal fade" id="messageModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إرسال رسالة جماعية</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="send_message">
                    <input type="hidden" name="selected_students" id="messageStudents">

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        سيتم إرسال الرسالة لـ <span id="messageCount">0</span> طالب
                    </div>

                    <div class="mb-3">
                        <label for="message_subject" class="form-label">عنوان الرسالة</label>
                        <input type="text" name="message_subject" id="message_subject" class="form-control"
                               placeholder="عنوان الرسالة..." required>
                    </div>

                    <div class="mb-3">
                        <label for="message_content" class="form-label">محتوى الرسالة</label>
                        <textarea name="message_content" id="message_content" class="form-control" rows="6"
                                  placeholder="اكتب رسالتك هنا..." required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">إرسال الرسالة</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal تحديث الحالة -->
<div class="modal fade" id="statusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تحديث حالة الطلاب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="update_status">
                    <input type="hidden" name="selected_students" id="statusStudents">

                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        سيتم تحديث حالة <span id="statusCount">0</span> طالب
                    </div>

                    <div class="mb-3">
                        <label for="new_status" class="form-label">الحالة الجديدة</label>
                        <select name="new_status" id="new_status" class="form-select" required>
                            <option value="">-- اختر الحالة --</option>
                            <option value="active">نشط</option>
                            <option value="completed">مكتمل</option>
                            <option value="dropped">منسحب</option>
                            <option value="suspended">معلق</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-warning">تحديث الحالة</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal إضافة درجة -->
<div class="modal fade" id="gradeModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة درجة جماعية</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="assign_grade">
                    <input type="hidden" name="selected_students" id="gradeStudents">

                    <div class="alert alert-success">
                        <i class="fas fa-star me-2"></i>
                        سيتم إضافة درجة لـ <span id="gradeCount">0</span> طالب
                    </div>

                    <div class="mb-3">
                        <label for="grade_course_id" class="form-label">الكورس</label>
                        <select name="course_id" id="grade_course_id" class="form-select" required>
                            <option value="">-- اختر كورس --</option>
                            <?php foreach ($instructor_courses as $course): ?>
                            <option value="<?php echo $course['id']; ?>">
                                <?php echo htmlspecialchars($course['title']); ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="assignment_name" class="form-label">اسم الواجب/الاختبار</label>
                        <input type="text" name="assignment_name" id="assignment_name" class="form-control"
                               placeholder="مثال: الاختبار الأول" required>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="grade" class="form-label">الدرجة</label>
                                <input type="number" name="grade" id="grade" class="form-control"
                                       min="0" step="0.01" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="max_grade" class="form-label">الدرجة الكاملة</label>
                                <input type="number" name="max_grade" id="max_grade" class="form-control"
                                       min="1" step="0.01" value="100" required>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات (اختياري)</label>
                        <textarea name="notes" id="notes" class="form-control" rows="3"
                                  placeholder="ملاحظات إضافية..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-success">إضافة الدرجة</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal تصدير البيانات -->
<div class="modal fade" id="exportModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تصدير بيانات الطلاب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="export_data">
                    <input type="hidden" name="selected_students" id="exportStudents">

                    <div class="alert alert-info">
                        <i class="fas fa-download me-2"></i>
                        سيتم تصدير بيانات <span id="exportCount">0</span> طالب
                    </div>

                    <div class="mb-3">
                        <label for="export_type" class="form-label">نوع التصدير</label>
                        <select name="export_type" id="export_type" class="form-select" required>
                            <option value="basic">بيانات أساسية (الاسم، البريد، الكورس، الحالة)</option>
                            <option value="detailed">بيانات تفصيلية (تشمل التقدم والدرجات)</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">تنسيق الملف</label>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="export_format" id="csv_format" value="csv" checked>
                            <label class="form-check-label" for="csv_format">
                                CSV (Excel)
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="export_format" id="pdf_format" value="pdf">
                            <label class="form-check-label" for="pdf_format">
                                PDF (قريباً)
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-info">تصدير البيانات</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// متغيرات عامة
let selectedStudents = [];

// تحديث عداد الطلاب المحددين
function updateSelectedCount() {
    const count = selectedStudents.length;
    document.getElementById('selectedCount').textContent = count + ' محدد';

    // تحديث العدادات في النوافذ المنبثقة
    document.getElementById('messageCount').textContent = count;
    document.getElementById('statusCount').textContent = count;
    document.getElementById('gradeCount').textContent = count;
    document.getElementById('exportCount').textContent = count;
}

// تحديد/إلغاء تحديد جميع الطلاب
document.getElementById('selectAllCheckbox').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.student-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
    updateSelectedStudents();
});

// تحديث قائمة الطلاب المحددين
function updateSelectedStudents() {
    const checkboxes = document.querySelectorAll('.student-checkbox:checked');
    selectedStudents = Array.from(checkboxes).map(cb => cb.value);
    updateSelectedCount();
}

// مراقبة تغيير اختيار الطلاب
document.addEventListener('change', function(e) {
    if (e.target.classList.contains('student-checkbox')) {
        updateSelectedStudents();
    }
});

// دوال الإجراءات
function selectAll() {
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');
    selectAllCheckbox.checked = true;
    selectAllCheckbox.dispatchEvent(new Event('change'));
}

function clearSelection() {
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');
    selectAllCheckbox.checked = false;
    selectAllCheckbox.dispatchEvent(new Event('change'));
}

function showMessageModal() {
    if (selectedStudents.length === 0) {
        alert('يرجى تحديد طلاب أولاً');
        return;
    }

    document.getElementById('messageStudents').value = JSON.stringify(selectedStudents);
    new bootstrap.Modal(document.getElementById('messageModal')).show();
}

function showStatusModal() {
    if (selectedStudents.length === 0) {
        alert('يرجى تحديد طلاب أولاً');
        return;
    }

    document.getElementById('statusStudents').value = JSON.stringify(selectedStudents);
    new bootstrap.Modal(document.getElementById('statusModal')).show();
}

function showGradeModal() {
    if (selectedStudents.length === 0) {
        alert('يرجى تحديد طلاب أولاً');
        return;
    }

    document.getElementById('gradeStudents').value = JSON.stringify(selectedStudents);
    new bootstrap.Modal(document.getElementById('gradeModal')).show();
}

function showExportModal() {
    if (selectedStudents.length === 0) {
        alert('يرجى تحديد طلاب أولاً');
        return;
    }

    document.getElementById('exportStudents').value = JSON.stringify(selectedStudents);
    new bootstrap.Modal(document.getElementById('exportModal')).show();
}

function showHelp() {
    alert(`مساعدة الإجراءات الجماعية:

1. حدد الطلاب المطلوبين من القائمة
2. اختر الإجراء المطلوب من اللوحة الجانبية
3. املأ البيانات المطلوبة
4. اضغط تأكيد لتنفيذ الإجراء

الإجراءات المتاحة:
- إرسال رسالة جماعية
- تحديث حالة الطلاب
- إضافة درجة لجميع الطلاب
- تصدير بيانات الطلاب`);
}

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    updateSelectedStudents();

    // إضافة تأثيرات بصرية للصفوف
    const rows = document.querySelectorAll('tbody tr');
    rows.forEach(row => {
        row.addEventListener('mouseenter', function() {
            this.style.backgroundColor = 'rgba(0,123,255,0.05)';
        });

        row.addEventListener('mouseleave', function() {
            this.style.backgroundColor = '';
        });
    });
});

// تحديث النماذج قبل الإرسال
document.querySelectorAll('form').forEach(form => {
    form.addEventListener('submit', function(e) {
        const hiddenInput = this.querySelector('input[name="selected_students"]');
        if (hiddenInput) {
            hiddenInput.value = JSON.stringify(selectedStudents);
        }
    });
});
</script>

<style>
.avatar-sm {
    width: 32px;
    height: 32px;
}

.progress {
    border-radius: 10px;
}

.progress-bar {
    border-radius: 10px;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: var(--bs-gray-700);
}

.card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1) !important;
}

.btn {
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
}

@media (max-width: 768px) {
    .d-grid {
        gap: 0.5rem !important;
    }

    .btn {
        font-size: 0.875rem;
    }
}
</style>

<?php include 'includes/footer.php'; ?>
