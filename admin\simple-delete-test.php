<?php
require_once '../config/database.php';
require_once '../includes/functions.php';

// محاكاة جلسة المدير
session_start();
$_SESSION['user_id'] = 1;
$_SESSION['user_role'] = 'admin';

echo "<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <title>اختبار الحذف المبسط</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
</head>
<body>
<div class='container mt-5'>
    <div class='card'>
        <div class='card-header bg-danger text-white'>
            <h4>اختبار حذف الكورس</h4>
        </div>
        <div class='card-body'>";

try {
    // إنشاء كورس تجريبي
    $stmt = $conn->prepare("
        INSERT INTO courses (title, description, instructor_id, start_date, end_date, max_students, course_type, price, status) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");
    
    $stmt->execute([
        'كورس تجريبي للحذف المبسط',
        'وصف الكورس التجريبي',
        1,
        date('Y-m-d'),
        date('Y-m-d', strtotime('+30 days')),
        50,
        'free',
        0,
        'active'
    ]);
    
    $test_course_id = $conn->lastInsertId();
    echo "<div class='alert alert-success'>✓ تم إنشاء كورس تجريبي بمعرف: $test_course_id</div>";
    
    // محاكاة طلب الحذف
    $_POST['course_id'] = $test_course_id;
    $_SERVER['REQUEST_METHOD'] = 'POST';
    
    // تشغيل كود الحذف
    ob_start();
    
    // نسخ كود الحذف هنا مباشرة
    $course_id = $_POST['course_id'];
    
    // بدء المعاملة
    $conn->beginTransaction();
    
    // جلب معلومات الكورس قبل الحذف
    $stmt = $conn->prepare("SELECT title, image FROM courses WHERE id = ?");
    $stmt->execute([$course_id]);
    $course = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$course) {
        throw new Exception('الكورس غير موجود');
    }
    
    echo "<div class='alert alert-info'>📋 الكورس الموجود: " . $course['title'] . "</div>";
    
    // حذف صورة الكورس إذا كانت موجودة
    if ($course['image'] && file_exists('../' . $course['image'])) {
        unlink('../' . $course['image']);
        echo "<div class='alert alert-warning'>🗑️ تم حذف صورة الكورس</div>";
    }
    
    // 1. حذف سجلات الحضور للجلسات المرتبطة بالكورس
    $stmt = $conn->prepare("
        DELETE sa FROM session_attendance sa
        JOIN sessions s ON sa.session_id = s.id
        WHERE s.course_id = ?
    ");
    $stmt->execute([$course_id]);
    $deleted_attendance = $stmt->rowCount();
    echo "<div class='alert alert-info'>🗑️ تم حذف $deleted_attendance سجل حضور</div>";
    
    // 2. حذف الجلسات
    $stmt = $conn->prepare("DELETE FROM sessions WHERE course_id = ?");
    $stmt->execute([$course_id]);
    $deleted_sessions = $stmt->rowCount();
    echo "<div class='alert alert-info'>🗑️ تم حذف $deleted_sessions جلسة</div>";
    
    // 3. حذف تقييمات الكورس
    $stmt = $conn->prepare("DELETE FROM course_reviews WHERE course_id = ?");
    $stmt->execute([$course_id]);
    $deleted_reviews = $stmt->rowCount();
    echo "<div class='alert alert-info'>🗑️ تم حذف $deleted_reviews تقييم</div>";
    
    // 4. حذف تسجيلات الطلاب
    $stmt = $conn->prepare("DELETE FROM course_enrollments WHERE course_id = ?");
    $stmt->execute([$course_id]);
    $deleted_enrollments = $stmt->rowCount();
    echo "<div class='alert alert-info'>🗑️ تم حذف $deleted_enrollments تسجيل طالب</div>";
    
    // 5. حذف الكورس نفسه
    $stmt = $conn->prepare("DELETE FROM courses WHERE id = ?");
    $stmt->execute([$course_id]);
    
    // التحقق من نجاح الحذف
    if ($stmt->rowCount() === 0) {
        throw new Exception('فشل في حذف الكورس');
    }
    
    echo "<div class='alert alert-success'>✅ تم حذف الكورس نفسه</div>";
    
    // تسجيل النشاط
    logUserActivity($_SESSION['user_id'], 'حذف كورس', "تم حذف الكورس: " . $course['title']);
    echo "<div class='alert alert-info'>📝 تم تسجيل النشاط</div>";
    
    // تأكيد المعاملة
    $conn->commit();
    echo "<div class='alert alert-success'>✅ تم تأكيد جميع العمليات</div>";
    
    // التحقق من الحذف
    $stmt = $conn->prepare("SELECT COUNT(*) FROM courses WHERE id = ?");
    $stmt->execute([$course_id]);
    $course_exists = $stmt->fetchColumn();
    
    if ($course_exists == 0) {
        echo "<div class='alert alert-success'>🎉 تم التأكد من حذف الكورس نهائياً!</div>";
    } else {
        echo "<div class='alert alert-danger'>❌ الكورس ما زال موجود!</div>";
    }
    
} catch (Exception $e) {
    // التراجع عن المعاملة في حالة الخطأ (إذا كانت نشطة)
    if ($conn->inTransaction()) {
        $conn->rollBack();
    }
    echo "<div class='alert alert-danger'>❌ خطأ: " . $e->getMessage() . "</div>";
}

echo "
        </div>
        <div class='card-footer'>
            <a href='manage-courses.php' class='btn btn-primary'>العودة لإدارة الكورسات</a>
        </div>
    </div>
</div>
</body>
</html>";
?>
