<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات المدرب
if (!isLoggedIn() || !isInstructor()) {
    header('Location: ../login.php');
    exit;
}

$assignment_id = $_GET['id'] ?? 0;

// جلب معلومات الواجب
try {
    $stmt = $conn->prepare("
        SELECT a.*, c.title as course_title
        FROM assignments a
        INNER JOIN courses c ON a.course_id = c.id
        WHERE a.id = ? AND c.instructor_id = ?
    ");
    $stmt->execute([$assignment_id, $_SESSION['user_id']]);
    $assignment = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$assignment) {
        header('Location: assignments.php');
        exit;
    }
} catch (PDOException $e) {
    header('Location: assignments.php');
    exit;
}

// معالجة تحديث الواجب
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $title = trim($_POST['title'] ?? '');
    $description = trim($_POST['description'] ?? '');
    $instructions = trim($_POST['instructions'] ?? '');
    $due_date = $_POST['due_date'] ?? '';
    $max_grade = $_POST['max_grade'] ?? 100;
    $is_required = isset($_POST['is_required']) ? 1 : 0;
    
    $errors = [];
    
    if (empty($title)) {
        $errors[] = 'عنوان الواجب مطلوب';
    }
    
    if (empty($due_date)) {
        $errors[] = 'تاريخ التسليم مطلوب';
    }
    
    if (empty($errors)) {
        try {
            $stmt = $conn->prepare("
                UPDATE assignments 
                SET title = ?, description = ?, instructions = ?, due_date = ?, max_grade = ?, is_required = ?, updated_at = NOW()
                WHERE id = ?
            ");
            $stmt->execute([$title, $description, $instructions, $due_date, $max_grade, $is_required, $assignment_id]);
            
            $success_message = 'تم تحديث الواجب بنجاح';
            
            // تحديث البيانات المحلية
            $assignment['title'] = $title;
            $assignment['description'] = $description;
            $assignment['instructions'] = $instructions;
            $assignment['due_date'] = $due_date;
            $assignment['max_grade'] = $max_grade;
            $assignment['is_required'] = $is_required;
            
        } catch (PDOException $e) {
            $error_message = 'حدث خطأ أثناء تحديث الواجب';
        }
    } else {
        $error_message = implode('<br>', $errors);
    }
}

$pageTitle = 'تعديل الواجب - ' . $assignment['title'];
$breadcrumbs = [
    ['title' => 'إدارة الواجبات', 'url' => 'assignments.php'],
    ['title' => 'تعديل الواجب']
];

include 'includes/header.php';
?>

<!-- رأس الصفحة -->
<div class="d-flex justify-content-between align-items-start mb-4">
    <div>
        <h2 class="h3 mb-2">
            <i class="fas fa-edit text-primary me-2"></i>
            تعديل الواجب
        </h2>
        <p class="text-muted mb-0">
            <i class="fas fa-book me-1"></i><?php echo htmlspecialchars($assignment['course_title']); ?>
        </p>
    </div>
    <div class="d-flex gap-2">
        <a href="assignments.php" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>العودة للواجبات
        </a>
        <a href="assignment-submissions.php?id=<?php echo $assignment_id; ?>" class="btn btn-outline-info">
            <i class="fas fa-file-upload me-1"></i>عرض التسليمات
        </a>
    </div>
</div>

<!-- رسائل النجاح والخطأ -->
<?php if (isset($success_message)): ?>
<div class="alert alert-success alert-dismissible fade show" role="alert">
    <i class="fas fa-check-circle me-2"></i>
    <?php echo $success_message; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<?php if (isset($error_message)): ?>
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <i class="fas fa-exclamation-triangle me-2"></i>
    <?php echo $error_message; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<!-- نموذج تعديل الواجب -->
<div class="row">
    <div class="col-lg-8">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-bottom">
                <h5 class="mb-0">
                    <i class="fas fa-edit me-2"></i>
                    تعديل معلومات الواجب
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="title" class="form-label">عنوان الواجب <span class="text-danger">*</span></label>
                            <input type="text" name="title" id="title" class="form-control"
                                   value="<?php echo htmlspecialchars($assignment['title']); ?>" required>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="description" class="form-label">وصف الواجب</label>
                            <textarea name="description" id="description" class="form-control" rows="3"
                                      placeholder="وصف مختصر عن الواجب وأهدافه..."><?php echo htmlspecialchars($assignment['description']); ?></textarea>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="due_date" class="form-label">تاريخ ووقت التسليم <span class="text-danger">*</span></label>
                            <input type="datetime-local" name="due_date" id="due_date" class="form-control"
                                   value="<?php echo date('Y-m-d\TH:i', strtotime($assignment['due_date'])); ?>" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="max_grade" class="form-label">الدرجة الكاملة</label>
                            <input type="number" name="max_grade" id="max_grade" class="form-control"
                                   value="<?php echo $assignment['max_grade']; ?>" min="1" step="0.01" required>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="instructions" class="form-label">تعليمات الواجب</label>
                            <textarea name="instructions" id="instructions" class="form-control" rows="6"
                                      placeholder="تعليمات مفصلة للطلاب حول كيفية إنجاز الواجب..."><?php echo htmlspecialchars($assignment['instructions']); ?></textarea>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="is_required" id="is_required"
                                       <?php echo $assignment['is_required'] ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="is_required">
                                    <i class="fas fa-exclamation-circle text-warning me-1"></i>
                                    واجب مطلوب (إجباري)
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>حفظ التغييرات
                        </button>
                        <a href="assignments.php" class="btn btn-secondary">
                            <i class="fas fa-times me-1"></i>إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- معلومات الواجب -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white border-bottom">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات الواجب
                </h6>
            </div>
            <div class="card-body">
                <div class="small">
                    <div class="d-flex justify-content-between mb-2">
                        <span class="text-muted">الكورس:</span>
                        <span><?php echo htmlspecialchars($assignment['course_title']); ?></span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span class="text-muted">تاريخ الإنشاء:</span>
                        <span><?php echo date('Y-m-d', strtotime($assignment['created_at'])); ?></span>
                    </div>
                    <?php if ($assignment['updated_at'] && $assignment['updated_at'] !== $assignment['created_at']): ?>
                    <div class="d-flex justify-content-between mb-2">
                        <span class="text-muted">آخر تحديث:</span>
                        <span><?php echo date('Y-m-d H:i', strtotime($assignment['updated_at'])); ?></span>
                    </div>
                    <?php endif; ?>
                    <div class="d-flex justify-content-between mb-2">
                        <span class="text-muted">الحالة:</span>
                        <span>
                            <?php if ($assignment['status'] === 'active'): ?>
                            <span class="badge bg-success">نشط</span>
                            <?php else: ?>
                            <span class="badge bg-secondary"><?php echo $assignment['status']; ?></span>
                            <?php endif; ?>
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- إجراءات سريعة -->
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-bottom">
                <h6 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="assignment-submissions.php?id=<?php echo $assignment_id; ?>" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-file-upload me-2"></i>
                        عرض التسليمات
                    </a>
                    <button class="btn btn-outline-info btn-sm" onclick="previewAssignment()">
                        <i class="fas fa-eye me-2"></i>
                        معاينة الواجب
                    </button>
                    <button class="btn btn-outline-warning btn-sm" onclick="duplicateAssignment()">
                        <i class="fas fa-copy me-2"></i>
                        نسخ الواجب
                    </button>
                    <hr>
                    <button class="btn btn-outline-danger btn-sm" onclick="deleteAssignment()">
                        <i class="fas fa-trash me-2"></i>
                        حذف الواجب
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// معاينة الواجب
function previewAssignment() {
    alert('ميزة المعاينة قيد التطوير');
}

// نسخ الواجب
function duplicateAssignment() {
    if (confirm('هل تريد إنشاء نسخة من هذا الواجب؟')) {
        window.location.href = `duplicate-assignment.php?id=<?php echo $assignment_id; ?>`;
    }
}

// حذف الواجب
function deleteAssignment() {
    if (confirm('هل أنت متأكد من حذف هذا الواجب؟\n\nسيتم حذف جميع التسليمات والدرجات المرتبطة به نهائياً.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = 'assignments.php';
        
        const actionInput = document.createElement('input');
        actionInput.type = 'hidden';
        actionInput.name = 'action';
        actionInput.value = 'delete_assignment';
        
        const idInput = document.createElement('input');
        idInput.type = 'hidden';
        idInput.name = 'assignment_id';
        idInput.value = '<?php echo $assignment_id; ?>';
        
        form.appendChild(actionInput);
        form.appendChild(idInput);
        document.body.appendChild(form);
        form.submit();
    }
}

// التحقق من صحة النموذج
document.querySelector('form').addEventListener('submit', function(e) {
    const dueDate = new Date(document.getElementById('due_date').value);
    const now = new Date();
    
    // السماح بالتواريخ الماضية للواجبات الموجودة
    if (dueDate <= now) {
        const confirmPast = confirm('تاريخ التسليم في الماضي. هل تريد المتابعة؟');
        if (!confirmPast) {
            e.preventDefault();
            return false;
        }
    }
});
</script>

<style>
.card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.form-label {
    font-weight: 600;
    color: var(--bs-gray-700);
}

.btn:hover {
    transform: translateY(-1px);
}

@media (max-width: 768px) {
    .d-flex.gap-2 {
        flex-direction: column;
    }
    
    .d-flex.gap-2 .btn {
        margin-bottom: 0.5rem;
    }
}
</style>

<?php include 'includes/footer.php'; ?>
