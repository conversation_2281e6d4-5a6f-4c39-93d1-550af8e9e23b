<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';
require_once '../includes/activity_logger.php';

// التحقق من صلاحيات المدرب
if (!isLoggedIn() || !isInstructor()) {
    header('Location: ../login.php');
    exit;
}

$pageTitle = 'إدارة الطلاب';
require_once '../includes/header.php';

// معالجة إضافة طالب جديد
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_student'])) {
    $name = trim($_POST['name']);
    $email = trim($_POST['email']);
    $username = trim($_POST['username']);
    $password = $_POST['password'];
    $course_id = (int)$_POST['course_id'];

    if (empty($name) || empty($email) || empty($username) || empty($password) || empty($course_id)) {
        $error = 'يرجى ملء جميع الحقول المطلوبة';
    } elseif (strlen($name) < 3) {
        $error = 'يجب أن يكون الاسم الكامل 3 أحرف على الأقل';
    } elseif (strlen($username) < 4) {
        $error = 'يجب أن يكون اسم المستخدم 4 أحرف على الأقل';
    } elseif (!preg_match('/^[a-zA-Z0-9_]+$/', $username)) {
        $error = 'اسم المستخدم يجب أن يحتوي على أحرف إنجليزية وأرقام وشرطة سفلية فقط';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = 'يرجى إدخال بريد إلكتروني صحيح';
    } elseif (strlen($password) < 8) {
        $error = 'يجب أن تكون كلمة المرور 8 أحرف على الأقل';
    } else {
        try {
            // التحقق من عدم وجود البريد الإلكتروني أو اسم المستخدم مسبقاً
            $stmt = $conn->prepare("SELECT id, email, username FROM users WHERE email = ? OR username = ?");
            $stmt->execute([$email, $username]);
            $existingUser = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($existingUser) {
                if ($existingUser['email'] === $email) {
                    $error = 'البريد الإلكتروني مستخدم بالفعل';
                } else {
                    $error = 'اسم المستخدم مستخدم بالفعل';
                }
            } else {
                // إنشاء حساب الطالب
                $conn->beginTransaction();

                $stmt = $conn->prepare("
                    INSERT INTO users (name, email, username, password, role, instructor_id)
                    VALUES (?, ?, ?, ?, 'student', ?)
                ");
                $stmt->execute([
                    $name,
                    $email,
                    $username,
                    password_hash($password, PASSWORD_DEFAULT),
                    $_SESSION['user_id']
                ]);
                
                $student_id = $conn->lastInsertId();

                // تسجيل الطالب في الكورس
                $stmt = $conn->prepare("
                    INSERT INTO course_enrollments (course_id, student_id)
                    VALUES (?, ?)
                ");
                $stmt->execute([$course_id, $student_id]);

                $conn->commit();
                $success = 'تم إضافة الطالب بنجاح';
                
                // تسجيل النشاط
                logUserActivity('إضافة طالب', "تم إضافة طالب جديد: $name");
                
                // إرسال بريد إلكتروني للطالب (يمكن إضافة هذه الوظيفة لاحقاً)
            }
        } catch (PDOException $e) {
            $conn->rollBack();
            $error = 'حدث خطأ أثناء إضافة الطالب';
        }
    }
}

// جلب قائمة الكورسات الخاصة بالمدرب
try {
    $stmt = $conn->prepare("
        SELECT id, title 
        FROM courses 
        WHERE instructor_id = ? AND status = 'active'
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $courses = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء جلب قائمة الكورسات';
}

// جلب قائمة الطلاب
try {
    $stmt = $conn->prepare("
        SELECT DISTINCT u.id, u.name, u.email, u.phone, u.created_at, u.last_login,
        COALESCE(u.username, SUBSTRING(u.email, 1, LOCATE('@', u.email) - 1)) as username,
        (SELECT COUNT(DISTINCT course_id)
         FROM course_enrollments
         WHERE student_id = u.id) as enrolled_courses,
        (SELECT COUNT(DISTINCT sa.session_id)
         FROM session_attendance sa
         JOIN sessions s ON sa.session_id = s.id
         JOIN courses c ON s.course_id = c.id
         WHERE sa.student_id = u.id
         AND c.instructor_id = ?) as attended_sessions
        FROM users u
        JOIN course_enrollments ce ON u.id = ce.student_id
        JOIN courses c ON ce.course_id = c.id
        WHERE c.instructor_id = ? AND u.role = 'student'
        ORDER BY u.created_at DESC
    ");
    $stmt->execute([$_SESSION['user_id'], $_SESSION['user_id']]);
    $students = $stmt->fetchAll(PDO::FETCH_ASSOC);
    if (!$students) {
        $students = [];
    }
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء جلب قائمة الطلاب';
    $students = [];
}
?>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">إضافة طالب جديد</h5>
                </div>
                <div class="card-body">
                    <?php if (isset($error)): ?>
                        <div class="alert alert-danger"><?php echo $error; ?></div>
                    <?php endif; ?>
                    <?php if (isset($success)): ?>
                        <div class="alert alert-success"><?php echo $success; ?></div>
                    <?php endif; ?>

                    <form method="POST" class="row g-3">
                        <div class="col-md-6">
                            <label for="name" class="form-label">الاسم الكامل</label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>
                        <div class="col-md-6">
                            <label for="email" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="email" name="email" required>
                        </div>
                        <div class="col-md-6">
                            <label for="username" class="form-label">اسم المستخدم</label>
                            <input type="text" class="form-control" id="username" name="username" required>
                        </div>
                        <div class="col-md-6">
                            <label for="password" class="form-label">كلمة المرور</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="password" name="password" required>
                                <button type="button" class="btn btn-outline-secondary" onclick="generatePassword()">
                                    <i class="fas fa-random"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-12">
                            <label for="course_id" class="form-label">الكورس</label>
                            <select class="form-select" id="course_id" name="course_id" required>
                                <option value="">اختر الكورس</option>
                                <?php foreach ($courses as $course): ?>
                                    <option value="<?php echo $course['id']; ?>">
                                        <?php echo htmlspecialchars($course['title']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-12">
                            <button type="submit" name="add_student" class="btn btn-primary">إضافة الطالب</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">قائمة الطلاب</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>الاسم</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>اسم المستخدم</th>
                                    <th>الكورسات المسجلة</th>
                                    <th>الجلسات المحضورة</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>آخر تسجيل دخول</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($students as $student): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($student['name']); ?></td>
                                        <td><?php echo htmlspecialchars($student['email']); ?></td>
                                        <td><?php echo htmlspecialchars($student['username'] ?? substr($student['email'], 0, strpos($student['email'], '@'))); ?></td>
                                        <td><?php echo $student['enrolled_courses']; ?></td>
                                        <td><?php echo $student['attended_sessions']; ?></td>
                                        <td><?php echo date('Y-m-d', strtotime($student['created_at'])); ?></td>
                                        <td>
                                            <?php 
                                            echo $student['last_login'] 
                                                ? date('Y-m-d H:i', strtotime($student['last_login']))
                                                : 'لم يسجل الدخول بعد';
                                            ?>
                                        </td>
                                        <td>
                                            <a href="student-details.php?id=<?php echo $student['id']; ?>" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <button class="btn btn-sm btn-warning" onclick="resetPassword(<?php echo $student['id']; ?>)">
                                                <i class="fas fa-key"></i>
                                            </button>
                                            <button class="btn btn-sm btn-danger" onclick="removeStudent(<?php echo $student['id']; ?>)">
                                                <i class="fas fa-user-minus"></i>
                                            </button>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة DataTables
    const table = document.querySelector('table');
    if (table) {
        new DataTable(table, {
            order: [[5, 'desc']],
            pageLength: 25,
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/ar.json'
            }
        });
    }
});

// توليد كلمة مرور عشوائية
function generatePassword() {
    const length = 12;
    const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*";
    let password = "";
    for (let i = 0; i < length; i++) {
        password += charset.charAt(Math.floor(Math.random() * charset.length));
    }
    document.getElementById('password').value = password;
}

function resetPassword(studentId) {
    if (confirm('هل أنت متأكد من إعادة تعيين كلمة المرور؟')) {
        const newPassword = generatePassword();
        fetch('../ajax/reset_student_password.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                student_id: studentId,
                new_password: newPassword
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`تم إعادة تعيين كلمة المرور بنجاح. كلمة المرور الجديدة هي: ${newPassword}`);
            } else {
                alert('حدث خطأ أثناء إعادة تعيين كلمة المرور');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء إعادة تعيين كلمة المرور');
        });
    }
}

function removeStudent(studentId) {
    if (confirm('هل أنت متأكد من إزالة هذا الطالب؟')) {
        fetch('../ajax/remove_student.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                student_id: studentId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.location.reload();
            } else {
                alert('حدث خطأ أثناء إزالة الطالب');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء إزالة الطالب');
        });
    }
}
</script>

<?php require_once '../includes/footer.php'; ?>
