<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات المدرب
if (!isLoggedIn() || !isInstructor()) {
    header('Location: ../login.php');
    exit;
}

$lesson_id = isset($_GET['lesson_id']) ? (int)$_GET['lesson_id'] : 0;
$error = '';

// جلب معلومات الدرس والتحقق من الملكية
try {
    $stmt = $conn->prepare("
        SELECT cl.*, ch.title as chapter_title, c.title as course_title, c.instructor_id
        FROM course_lessons cl
        JOIN course_chapters ch ON cl.chapter_id = ch.id
        JOIN courses c ON cl.course_id = c.id
        WHERE cl.id = ? AND c.instructor_id = ?
    ");
    $stmt->execute([$lesson_id, $_SESSION['user_id']]);
    $lesson = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$lesson) {
        header('Location: dashboard.php');
        exit;
    }
} catch (PDOException $e) {
    $error = 'حدث خطأ في جلب معلومات الدرس';
}

$pageTitle = 'عرض الدرس';
include 'includes/header.php';
?>

<style>
.video-container {
    background: #000;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.video-player {
    width: 100%;
    height: 500px;
}

.lesson-info {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    padding: 25px;
}

.stats-card {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    margin-bottom: 20px;
}

.btn-action {
    border-radius: 25px;
    padding: 10px 25px;
    font-weight: 600;
    margin: 5px;
}

</style>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2>عرض الدرس</h2>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item">
                                <a href="dashboard.php">لوحة التحكم</a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="manage-course-content.php?course_id=<?php echo $lesson['course_id']; ?>">
                                    <?php echo htmlspecialchars($lesson['course_title']); ?>
                                </a>
                            </li>
                            <li class="breadcrumb-item active">
                                <?php echo htmlspecialchars($lesson['title']); ?>
                            </li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="manage-course-content.php?course_id=<?php echo $lesson['course_id']; ?>" 
                       class="btn btn-secondary btn-action">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للكورس
                    </a>
                </div>
            </div>

            <?php if ($error): ?>
                <div class="alert alert-danger"><?php echo $error; ?></div>
            <?php endif; ?>

            <div class="row">
                <!-- مشغل الفيديو -->
                <div class="col-lg-8">
                    <div class="video-container mb-4">
                        <?php if ($lesson['video_file_path'] && file_exists($lesson['video_file_path'])): ?>
                            <video class="video-player" controls>
                                <source src="<?php echo htmlspecialchars($lesson['video_file_path']); ?>" type="video/mp4">
                                متصفحك لا يدعم تشغيل الفيديو
                            </video>
                        <?php else: ?>
                            <div class="video-player d-flex align-items-center justify-content-center">
                                <div class="text-center text-white">
                                    <i class="fas fa-video fa-5x mb-3"></i>
                                    <h4>لم يتم رفع فيديو لهذا الدرس</h4>
                                    <p>يمكنك رفع فيديو من صفحة إدارة المحتوى</p>
                                    <a href="manage-course-content.php?course_id=<?php echo $lesson['course_id']; ?>" 
                                       class="btn btn-primary">
                                        <i class="fas fa-upload me-2"></i>
                                        رفع فيديو
                                    </a>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- معلومات الدرس -->
                    <div class="lesson-info">
                        <h3><?php echo htmlspecialchars($lesson['title']); ?></h3>
                        <p class="text-muted mb-3">
                            <i class="fas fa-book me-2"></i>
                            <?php echo htmlspecialchars($lesson['chapter_title']); ?>
                        </p>
                        
                        <?php if ($lesson['description']): ?>
                            <div class="mb-4">
                                <h5>وصف الدرس:</h5>
                                <p><?php echo nl2br(htmlspecialchars($lesson['description'])); ?></p>
                            </div>
                        <?php endif; ?>

                        <div class="row">
                            <div class="col-md-6">
                                <h6>معلومات الدرس:</h6>
                                <ul class="list-unstyled">
                                    <li><strong>الترتيب:</strong> <?php echo $lesson['lesson_order']; ?></li>
                                    <li><strong>النوع:</strong> 
                                        <?php if ($lesson['is_free']): ?>
                                            <span class="badge bg-success">مجاني</span>
                                        <?php else: ?>
                                            <span class="badge bg-primary">مدفوع</span>
                                        <?php endif; ?>
                                    </li>
                                    <li><strong>الحالة:</strong> 
                                        <?php if ($lesson['status'] === 'active'): ?>
                                            <span class="badge bg-success">نشط</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">غير نشط</span>
                                        <?php endif; ?>
                                    </li>
                                    <li><strong>تاريخ الإنشاء:</strong> <?php echo date('Y-m-d H:i', strtotime($lesson['created_at'])); ?></li>
                                </ul>
                            </div>
                            
                            <?php if ($lesson['video_file_path']): ?>
                                <div class="col-md-6">
                                    <h6>معلومات الفيديو:</h6>
                                    <ul class="list-unstyled">
                                        <li><strong>حجم الملف:</strong> <?php echo formatFileSize($lesson['video_size']); ?></li>
                                        <li><strong>مدة الفيديو:</strong> 
                                            <?php if ($lesson['video_duration']): ?>
                                                <?php echo gmdate("H:i:s", $lesson['video_duration']); ?>
                                            <?php else: ?>
                                                غير محدد
                                            <?php endif; ?>
                                        </li>
                                        <li><strong>مسار الملف:</strong> 
                                            <small class="text-muted"><?php echo htmlspecialchars($lesson['video_file_path']); ?></small>
                                        </li>
                                    </ul>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div class="mt-4">
                            <a href="edit-lesson.php?lesson_id=<?php echo $lesson['id']; ?>" 
                               class="btn btn-primary btn-action">
                                <i class="fas fa-edit me-2"></i>
                                تعديل الدرس
                            </a>
                            
                            <?php if ($lesson['video_file_path']): ?>
                                <a href="<?php echo htmlspecialchars($lesson['video_file_path']); ?>" 
                                   class="btn btn-success btn-action" download>
                                    <i class="fas fa-download me-2"></i>
                                    تحميل الفيديو
                                </a>
                            <?php endif; ?>
                            
                            <button class="btn btn-warning btn-action" onclick="uploadNewVideo()">
                                <i class="fas fa-upload me-2"></i>
                                <?php echo $lesson['video_file_path'] ? 'تغيير الفيديو' : 'رفع فيديو'; ?>
                            </button>
                            
                            <button class="btn btn-danger btn-action" onclick="deleteLesson()">
                                <i class="fas fa-trash me-2"></i>
                                حذف الدرس
                            </button>
                        </div>
                    </div>
                </div>

                <!-- الشريط الجانبي -->
                <div class="col-lg-4">
                    <!-- إحصائيات الدرس -->
                    <div class="stats-card">
                        <h5>إحصائيات الدرس</h5>
                        <?php
                        try {
                            // جلب عدد المشاهدات
                            $stmt = $conn->prepare("
                                SELECT COUNT(*) as views,
                                       COUNT(CASE WHEN is_completed = 1 THEN 1 END) as completed
                                FROM student_lesson_progress 
                                WHERE lesson_id = ?
                            ");
                            $stmt->execute([$lesson_id]);
                            $stats = $stmt->fetch(PDO::FETCH_ASSOC);
                        } catch (PDOException $e) {
                            $stats = ['views' => 0, 'completed' => 0];
                        }
                        ?>
                        <div class="row text-center">
                            <div class="col-6">
                                <h3><?php echo $stats['views']; ?></h3>
                                <p class="mb-0">مشاهدة</p>
                            </div>
                            <div class="col-6">
                                <h3><?php echo $stats['completed']; ?></h3>
                                <p class="mb-0">مكتمل</p>
                            </div>
                        </div>
                    </div>

                    <!-- دروس أخرى في نفس الفصل -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">دروس أخرى في هذا الفصل</h6>
                        </div>
                        <div class="card-body">
                            <?php
                            try {
                                $stmt = $conn->prepare("
                                    SELECT * FROM course_lessons 
                                    WHERE chapter_id = ? AND id != ?
                                    ORDER BY lesson_order
                                ");
                                $stmt->execute([$lesson['chapter_id'], $lesson_id]);
                                $other_lessons = $stmt->fetchAll(PDO::FETCH_ASSOC);
                            } catch (PDOException $e) {
                                $other_lessons = [];
                            }
                            ?>
                            
                            <?php if (empty($other_lessons)): ?>
                                <p class="text-muted">لا توجد دروس أخرى في هذا الفصل</p>
                            <?php else: ?>
                                <?php foreach ($other_lessons as $other_lesson): ?>
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="fas fa-play-circle me-2 text-primary"></i>
                                        <a href="view-lesson.php?lesson_id=<?php echo $other_lesson['id']; ?>" 
                                           class="text-decoration-none">
                                            <?php echo htmlspecialchars($other_lesson['title']); ?>
                                        </a>
                                        <?php if ($other_lesson['is_free']): ?>
                                            <span class="badge bg-success ms-auto">مجاني</span>
                                        <?php endif; ?>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- معاينة للطالب -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h6 class="mb-0">معاينة للطالب</h6>
                        </div>
                        <div class="card-body text-center">
                            <p class="text-muted">شاهد كيف يبدو الدرس للطلاب</p>
                            <a href="../student/course-content.php?course_id=<?php echo $lesson['course_id']; ?>&lesson_id=<?php echo $lesson_id; ?>" 
                               class="btn btn-outline-primary" target="_blank">
                                <i class="fas fa-external-link-alt me-2"></i>
                                معاينة
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function uploadNewVideo() {
    window.location.href = 'manage-course-content.php?course_id=<?php echo $lesson['course_id']; ?>#upload-video-<?php echo $lesson_id; ?>';
}

function deleteLesson() {
    if (confirm('هل أنت متأكد من حذف هذا الدرس؟ لا يمكن التراجع عن هذا الإجراء.')) {
        // يمكن إضافة منطق الحذف هنا
        alert('سيتم تطوير وظيفة الحذف قريباً');
    }
}

// تسجيل إحصائيات المشاهدة
const video = document.querySelector('video');
if (video) {
    video.addEventListener('loadedmetadata', function() {
        console.log('مدة الفيديو:', video.duration);
    });
    
    video.addEventListener('ended', function() {
        console.log('انتهى تشغيل الفيديو');
    });
}
</script>

<?php
function formatFileSize($bytes) {
    if ($bytes >= 1073741824) {
        return number_format($bytes / 1073741824, 2) . ' GB';
    } elseif ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' bytes';
    }
}

require_once '../includes/footer.php';
?>
