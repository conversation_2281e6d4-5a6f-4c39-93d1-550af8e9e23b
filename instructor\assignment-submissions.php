<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات المدرب
if (!isLoggedIn() || !isInstructor()) {
    header('Location: ../login.php');
    exit;
}

$assignment_id = $_GET['id'] ?? 0;

// جلب معلومات الواجب
try {
    $stmt = $conn->prepare("
        SELECT a.*, c.title as course_title
        FROM assignments a
        INNER JOIN courses c ON a.course_id = c.id
        WHERE a.id = ? AND c.instructor_id = ?
    ");
    $stmt->execute([$assignment_id, $_SESSION['user_id']]);
    $assignment = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$assignment) {
        header('Location: assignments.php');
        exit;
    }
} catch (PDOException $e) {
    header('Location: assignments.php');
    exit;
}

// معالجة تقييم التسليمات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'grade_submission') {
        $submission_id = $_POST['submission_id'] ?? 0;
        $grade = $_POST['grade'] ?? 0;
        $feedback = $_POST['feedback'] ?? '';
        
        try {
            $stmt = $conn->prepare("
                UPDATE assignment_submissions 
                SET grade = ?, feedback = ?, status = 'graded', graded_by = ?, graded_at = NOW()
                WHERE id = ? AND assignment_id = ?
            ");
            $stmt->execute([$grade, $feedback, $_SESSION['user_id'], $submission_id, $assignment_id]);
            
            $success_message = 'تم تقييم التسليم بنجاح';
        } catch (PDOException $e) {
            $error_message = 'حدث خطأ أثناء تقييم التسليم';
        }
    }
}

// جلب التسليمات
try {
    $stmt = $conn->prepare("
        SELECT 
            asub.*,
            u.name as student_name,
            u.email as student_email,
            CASE 
                WHEN asub.submission_date > a.due_date THEN 1 
                ELSE 0 
            END as is_late
        FROM assignment_submissions asub
        INNER JOIN users u ON asub.student_id = u.id
        INNER JOIN assignments a ON asub.assignment_id = a.id
        WHERE asub.assignment_id = ?
        ORDER BY asub.submission_date DESC
    ");
    $stmt->execute([$assignment_id]);
    $submissions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (PDOException $e) {
    $submissions = [];
    $error_message = 'حدث خطأ أثناء جلب التسليمات';
}

// إحصائيات التسليمات
$total_submissions = count($submissions);
$graded_submissions = count(array_filter($submissions, function($s) { return $s['status'] === 'graded'; }));
$late_submissions = count(array_filter($submissions, function($s) { return $s['is_late']; }));
$avg_grade = 0;

if ($graded_submissions > 0) {
    $total_grades = array_sum(array_map(function($s) { 
        return $s['status'] === 'graded' ? $s['grade'] : 0; 
    }, $submissions));
    $avg_grade = $total_grades / $graded_submissions;
}

$pageTitle = 'تسليمات الواجب - ' . $assignment['title'];
$breadcrumbs = [
    ['title' => 'إدارة الواجبات', 'url' => 'assignments.php'],
    ['title' => 'تسليمات الواجب']
];

include 'includes/header.php';
?>

<!-- رأس الصفحة -->
<div class="d-flex justify-content-between align-items-start mb-4">
    <div>
        <h2 class="h3 mb-2">
            <i class="fas fa-file-upload text-primary me-2"></i>
            تسليمات الواجب
        </h2>
        <h4 class="text-muted mb-1"><?php echo htmlspecialchars($assignment['title']); ?></h4>
        <p class="text-muted mb-0">
            <i class="fas fa-book me-1"></i><?php echo htmlspecialchars($assignment['course_title']); ?>
            <span class="mx-2">•</span>
            <i class="fas fa-calendar me-1"></i>موعد التسليم: <?php echo date('Y-m-d H:i', strtotime($assignment['due_date'])); ?>
        </p>
    </div>
    <div class="d-flex gap-2">
        <a href="assignments.php" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>العودة للواجبات
        </a>
        <a href="edit-assignment.php?id=<?php echo $assignment_id; ?>" class="btn btn-outline-primary">
            <i class="fas fa-edit me-1"></i>تعديل الواجب
        </a>
    </div>
</div>

<!-- رسائل النجاح والخطأ -->
<?php if (isset($success_message)): ?>
<div class="alert alert-success alert-dismissible fade show" role="alert">
    <i class="fas fa-check-circle me-2"></i>
    <?php echo $success_message; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<?php if (isset($error_message)): ?>
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <i class="fas fa-exclamation-triangle me-2"></i>
    <?php echo $error_message; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<!-- بطاقات الإحصائيات -->
<div class="row g-4 mb-4">
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-primary bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-file-upload text-primary fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h5 class="mb-1"><?php echo $total_submissions; ?></h5>
                        <p class="text-muted mb-0">إجمالي التسليمات</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-success bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-check-circle text-success fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h5 class="mb-1"><?php echo $graded_submissions; ?></h5>
                        <p class="text-muted mb-0">تم تقييمها</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-warning bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-clock text-warning fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h5 class="mb-1"><?php echo $late_submissions; ?></h5>
                        <p class="text-muted mb-0">تسليمات متأخرة</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-info bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-chart-line text-info fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h5 class="mb-1"><?php echo number_format($avg_grade, 1); ?>%</h5>
                        <p class="text-muted mb-0">متوسط الدرجات</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- قائمة التسليمات -->
<div class="card border-0 shadow-sm">
    <div class="card-header bg-white border-bottom">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>
            قائمة التسليمات
        </h5>
    </div>
    <div class="card-body p-0">
        <?php if (empty($submissions)): ?>
        <div class="text-center py-5">
            <i class="fas fa-file-upload text-muted" style="font-size: 3rem;"></i>
            <h5 class="mt-3 text-muted">لا توجد تسليمات</h5>
            <p class="text-muted">لم يقم أي طالب بتسليم هذا الواجب بعد</p>
        </div>
        <?php else: ?>
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>الطالب</th>
                        <th>تاريخ التسليم</th>
                        <th>الحالة</th>
                        <th>الدرجة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($submissions as $submission): ?>
                    <tr>
                        <td>
                            <div>
                                <h6 class="mb-1"><?php echo htmlspecialchars($submission['student_name']); ?></h6>
                                <small class="text-muted"><?php echo htmlspecialchars($submission['student_email']); ?></small>
                            </div>
                        </td>
                        <td>
                            <span class="<?php echo $submission['is_late'] ? 'text-danger' : 'text-muted'; ?>">
                                <?php echo date('Y-m-d H:i', strtotime($submission['submission_date'])); ?>
                            </span>
                            <?php if ($submission['is_late']): ?>
                            <br><small class="text-danger">
                                <i class="fas fa-exclamation-triangle me-1"></i>متأخر
                            </small>
                            <?php endif; ?>
                        </td>
                        <td>
                            <?php if ($submission['status'] === 'graded'): ?>
                            <span class="badge bg-success">تم التقييم</span>
                            <?php elseif ($submission['status'] === 'submitted'): ?>
                            <span class="badge bg-warning">في انتظار التقييم</span>
                            <?php else: ?>
                            <span class="badge bg-secondary"><?php echo $submission['status']; ?></span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <?php if ($submission['status'] === 'graded' && $submission['grade'] !== null): ?>
                            <div class="text-center">
                                <strong class="text-success"><?php echo number_format($submission['grade'], 1); ?></strong>
                                <br><small class="text-muted">من <?php echo $assignment['max_grade']; ?></small>
                            </div>
                            <?php else: ?>
                            <span class="text-muted">لم يتم التقييم</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <button class="btn btn-sm btn-outline-primary" 
                                        onclick="viewSubmission(<?php echo $submission['id']; ?>)"
                                        title="عرض التسليم">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-success" 
                                        onclick="gradeSubmission(<?php echo $submission['id']; ?>, '<?php echo htmlspecialchars($submission['student_name'], ENT_QUOTES); ?>', <?php echo $submission['grade'] ?? 0; ?>, '<?php echo htmlspecialchars($submission['feedback'] ?? '', ENT_QUOTES); ?>')"
                                        title="تقييم">
                                    <i class="fas fa-star"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- Modal عرض التسليم -->
<div class="modal fade" id="viewSubmissionModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">عرض التسليم</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="submissionContent">
                    <!-- سيتم تحميل المحتوى هنا -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal تقييم التسليم -->
<div class="modal fade" id="gradeSubmissionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تقييم التسليم</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="grade_submission">
                    <input type="hidden" name="submission_id" id="gradeSubmissionId">

                    <div class="mb-3">
                        <label class="form-label">الطالب</label>
                        <p class="form-control-plaintext" id="gradeStudentName"></p>
                    </div>

                    <div class="mb-3">
                        <label for="grade" class="form-label">الدرجة <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <input type="number" name="grade" id="grade" class="form-control"
                                   min="0" max="<?php echo $assignment['max_grade']; ?>" step="0.01" required>
                            <span class="input-group-text">من <?php echo $assignment['max_grade']; ?></span>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="feedback" class="form-label">التعليقات والملاحظات</label>
                        <textarea name="feedback" id="feedback" class="form-control" rows="4"
                                  placeholder="اكتب تعليقاتك وملاحظاتك على التسليم..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-success">حفظ التقييم</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// عرض التسليم
function viewSubmission(submissionId) {
    const modal = new bootstrap.Modal(document.getElementById('viewSubmissionModal'));
    const content = document.getElementById('submissionContent');

    content.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> جاري التحميل...</div>';
    modal.show();

    // جلب محتوى التسليم
    fetch(`get-submission-details.php?id=${submissionId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                content.innerHTML = `
                    <div class="row">
                        <div class="col-md-6">
                            <h6>معلومات التسليم</h6>
                            <table class="table table-sm">
                                <tr><td><strong>الطالب:</strong></td><td>${data.submission.student_name}</td></tr>
                                <tr><td><strong>تاريخ التسليم:</strong></td><td>${data.submission.submission_date}</td></tr>
                                <tr><td><strong>الحالة:</strong></td><td>${data.submission.status}</td></tr>
                                ${data.submission.is_late ? '<tr><td><strong>متأخر:</strong></td><td><span class="text-danger">نعم</span></td></tr>' : ''}
                            </table>
                        </div>
                        <div class="col-md-6">
                            ${data.submission.grade ? `
                                <h6>التقييم</h6>
                                <table class="table table-sm">
                                    <tr><td><strong>الدرجة:</strong></td><td>${data.submission.grade} من ${data.assignment.max_grade}</td></tr>
                                    <tr><td><strong>النسبة:</strong></td><td>${((data.submission.grade / data.assignment.max_grade) * 100).toFixed(1)}%</td></tr>
                                    <tr><td><strong>تاريخ التقييم:</strong></td><td>${data.submission.graded_at || 'غير محدد'}</td></tr>
                                </table>
                            ` : '<p class="text-muted">لم يتم التقييم بعد</p>'}
                        </div>
                    </div>

                    ${data.submission.submission_text ? `
                        <hr>
                        <h6>نص التسليم</h6>
                        <div class="bg-light p-3 rounded">
                            <pre style="white-space: pre-wrap;">${data.submission.submission_text}</pre>
                        </div>
                    ` : ''}

                    ${data.submission.file_path ? `
                        <hr>
                        <h6>الملف المرفق</h6>
                        <div class="d-flex align-items-center">
                            <i class="fas fa-file me-2"></i>
                            <a href="${data.submission.file_path}" target="_blank" class="btn btn-outline-primary btn-sm">
                                تحميل الملف (${data.submission.original_filename})
                            </a>
                        </div>
                    ` : ''}

                    ${data.submission.feedback ? `
                        <hr>
                        <h6>تعليقات المدرب</h6>
                        <div class="bg-info bg-opacity-10 p-3 rounded">
                            ${data.submission.feedback}
                        </div>
                    ` : ''}
                `;
            } else {
                content.innerHTML = '<div class="alert alert-danger">حدث خطأ أثناء تحميل التسليم</div>';
            }
        })
        .catch(error => {
            console.error('خطأ:', error);
            content.innerHTML = '<div class="alert alert-danger">حدث خطأ أثناء تحميل التسليم</div>';
        });
}

// تقييم التسليم
function gradeSubmission(submissionId, studentName, currentGrade, currentFeedback) {
    document.getElementById('gradeSubmissionId').value = submissionId;
    document.getElementById('gradeStudentName').textContent = studentName;
    document.getElementById('grade').value = currentGrade || '';
    document.getElementById('feedback').value = currentFeedback || '';

    new bootstrap.Modal(document.getElementById('gradeSubmissionModal')).show();
}

// تهيئة DataTables
document.addEventListener('DOMContentLoaded', function() {
    if (typeof DataTable !== 'undefined' && document.querySelector('table')) {
        new DataTable('table', {
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/ar.json'
            },
            pageLength: 25,
            order: [[1, 'desc']], // ترتيب حسب تاريخ التسليم
            columnDefs: [
                { orderable: false, targets: [4] } // عدم ترتيب عمود الإجراءات
            ]
        });
    }
});
</script>

<style>
.table th {
    border-top: none;
    font-weight: 600;
    color: var(--bs-gray-700);
}

.btn-group .btn {
    border-radius: 0.375rem;
}

.btn-group .btn:not(:last-child) {
    margin-right: 0.25rem;
}

.badge {
    font-size: 0.75rem;
}

.card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1) !important;
}

pre {
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    line-height: 1.4;
}

@media (max-width: 768px) {
    .btn-group {
        display: flex;
        flex-direction: column;
        width: 100%;
    }

    .btn-group .btn {
        margin-right: 0;
        margin-bottom: 0.25rem;
    }

    .table-responsive {
        font-size: 0.875rem;
    }
}
</style>

<?php include 'includes/footer.php'; ?>
