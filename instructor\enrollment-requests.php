<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات المدرب
if (!isLoggedIn() || !isInstructor()) {
    header('Location: ../login.php');
    exit;
}

$pageTitle = 'طلبات الانضمام';
$breadcrumbs = [
    ['title' => 'طلبات الانضمام']
];

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $student_id = (int)($_POST['student_id'] ?? 0);
    $course_id = (int)($_POST['course_id'] ?? 0);
    $action = $_POST['action'] ?? '';

    // تسجيل محاولة المعالجة للتشخيص
    error_log("Processing request: action=$action, student_id=$student_id, course_id=$course_id");

    if ($action === 'approve' && $student_id && $course_id) {
        try {
            $conn->beginTransaction();

            // التحقق من أن الكورس ينتمي للمدرب
            $stmt = $conn->prepare("SELECT id, title FROM courses WHERE id = ? AND instructor_id = ?");
            $stmt->execute([$course_id, $_SESSION['user_id']]);
            $course = $stmt->fetch();

            if ($course) {
                // التحقق من وجود الطلب
                $stmt = $conn->prepare("SELECT id FROM join_requests WHERE student_id = ? AND course_id = ? AND status = 'pending'");
                $stmt->execute([$student_id, $course_id]);
                $request = $stmt->fetch();

                if ($request) {
                    // التحقق من عدم وجود تسجيل مسبق
                    $stmt = $conn->prepare("SELECT id FROM course_enrollments WHERE student_id = ? AND course_id = ?");
                    $stmt->execute([$student_id, $course_id]);
                    $existing_enrollment = $stmt->fetch();

                    if (!$existing_enrollment) {
                        // تحديث حالة الطلب إلى مقبول
                        $stmt = $conn->prepare("
                            UPDATE join_requests
                            SET status = 'approved', processed_at = NOW()
                            WHERE student_id = ? AND course_id = ? AND status = 'pending'
                        ");
                        $stmt->execute([$student_id, $course_id]);

                        // إضافة سجل في course_enrollments
                        $stmt = $conn->prepare("
                            INSERT INTO course_enrollments (course_id, student_id, status, enrolled_at, progress_percentage)
                            VALUES (?, ?, 'active', NOW(), 0)
                        ");
                        $stmt->execute([$course_id, $student_id]);

                        // جلب اسم الطالب
                        $stmt = $conn->prepare("SELECT name FROM users WHERE id = ?");
                        $stmt->execute([$student_id]);
                        $student = $stmt->fetch();

                        $conn->commit();
                        $success_message = "تم قبول الطالب {$student['name']} في كورس {$course['title']} بنجاح وتم تسجيله في الكورس";
                    } else {
                        $error_message = 'الطالب مسجل بالفعل في هذا الكورس';
                    }
                } else {
                    $error_message = 'طلب الانضمام غير موجود أو تم التعامل معه مسبقاً';
                }
            } else {
                $error_message = 'الكورس غير موجود أو لا تملك صلاحية للوصول إليه';
            }
        } catch (PDOException $e) {
            $conn->rollBack();
            $error_message = 'حدث خطأ أثناء قبول الطالب: ' . $e->getMessage();
        }
    }

    if ($action === 'reject' && $student_id && $course_id) {
        try {
            // التحقق من أن الكورس ينتمي للمدرب
            $stmt = $conn->prepare("SELECT id, title FROM courses WHERE id = ? AND instructor_id = ?");
            $stmt->execute([$course_id, $_SESSION['user_id']]);
            $course = $stmt->fetch();

            if ($course) {
                // تحديث حالة الطلب إلى مرفوض
                $stmt = $conn->prepare("
                    UPDATE join_requests
                    SET status = 'rejected', processed_at = NOW()
                    WHERE student_id = ? AND course_id = ? AND status = 'pending'
                ");
                $result = $stmt->execute([$student_id, $course_id]);

                if ($result && $stmt->rowCount() > 0) {
                    // جلب اسم الطالب
                    $stmt = $conn->prepare("SELECT name FROM users WHERE id = ?");
                    $stmt->execute([$student_id]);
                    $student = $stmt->fetch();

                    $success_message = "تم رفض طلب الطالب {$student['name']} للانضمام إلى كورس {$course['title']}";
                } else {
                    $error_message = 'طلب الانضمام غير موجود أو تم التعامل معه مسبقاً';
                }
            } else {
                $error_message = 'الكورس غير موجود أو لا تملك صلاحية للوصول إليه';
            }
        } catch (PDOException $e) {
            $error_message = 'حدث خطأ أثناء رفض الطالب: ' . $e->getMessage();
        }
    }

    if ($action === 'approve_all') {
        try {
            $conn->beginTransaction();

            // جلب جميع الطلبات المعلقة للمدرب
            $stmt = $conn->prepare("
                SELECT jr.student_id, jr.course_id, u.name as student_name, c.title as course_title
                FROM join_requests jr
                INNER JOIN users u ON jr.student_id = u.id
                INNER JOIN courses c ON jr.course_id = c.id
                WHERE c.instructor_id = ? AND jr.status = 'pending'
            ");
            $stmt->execute([$_SESSION['user_id']]);
            $all_requests = $stmt->fetchAll();

            $approved_count = 0;
            foreach ($all_requests as $request) {
                // التحقق من عدم وجود تسجيل مسبق
                $stmt = $conn->prepare("SELECT id FROM course_enrollments WHERE student_id = ? AND course_id = ?");
                $stmt->execute([$request['student_id'], $request['course_id']]);

                if (!$stmt->fetch()) {
                    // قبول الطلب
                    $stmt = $conn->prepare("
                        UPDATE join_requests
                        SET status = 'approved', processed_at = NOW()
                        WHERE student_id = ? AND course_id = ? AND status = 'pending'
                    ");
                    $stmt->execute([$request['student_id'], $request['course_id']]);

                    // إضافة التسجيل
                    $stmt = $conn->prepare("
                        INSERT INTO course_enrollments (course_id, student_id, status, enrolled_at, progress_percentage)
                        VALUES (?, ?, 'active', NOW(), 0)
                    ");
                    $stmt->execute([$request['course_id'], $request['student_id']]);

                    $approved_count++;
                }
            }

            $conn->commit();
            $success_message = "تم قبول {$approved_count} طلب انضمام بنجاح";

        } catch (PDOException $e) {
            $conn->rollBack();
            $error_message = 'حدث خطأ أثناء قبول الطلبات: ' . $e->getMessage();
        }
    }
}

// جلب طلبات الانضمام المعلقة
try {
    $stmt = $conn->prepare("
        SELECT
            jr.id as enrollment_id,
            jr.student_id,
            jr.course_id,
            jr.requested_at as request_date,
            jr.message,
            u.name as student_name,
            u.email as student_email,
            u.phone as student_phone,
            c.title as course_title,
            c.max_students,
            (SELECT COUNT(*) FROM course_enrollments WHERE course_id = c.id AND status = 'active') as current_students
        FROM join_requests jr
        INNER JOIN users u ON jr.student_id = u.id
        INNER JOIN courses c ON jr.course_id = c.id
        WHERE c.instructor_id = ? AND jr.status = 'pending'
        ORDER BY jr.requested_at DESC
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $pending_requests = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // إحصائيات شاملة
    $stmt = $conn->prepare("
        SELECT
            COUNT(CASE WHEN jr.status = 'pending' THEN 1 END) as total_pending,
            COUNT(CASE WHEN jr.status = 'approved' THEN 1 END) as total_approved,
            COUNT(CASE WHEN jr.status = 'rejected' THEN 1 END) as total_rejected,
            COUNT(CASE WHEN DATE(jr.requested_at) = CURDATE() AND jr.status = 'pending' THEN 1 END) as today_requests,
            COUNT(CASE WHEN DATE(jr.requested_at) = CURDATE() AND jr.status = 'approved' THEN 1 END) as today_approved
        FROM join_requests jr
        INNER JOIN courses c ON jr.course_id = c.id
        WHERE c.instructor_id = ?
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $stats = $stmt->fetch(PDO::FETCH_ASSOC);

    // إحصائيات الكورسات
    $stmt = $conn->prepare("
        SELECT
            COUNT(DISTINCT c.id) as total_courses,
            COUNT(DISTINCT ce.student_id) as total_enrolled_students
        FROM courses c
        LEFT JOIN course_enrollments ce ON c.id = ce.course_id AND ce.status = 'active'
        WHERE c.instructor_id = ?
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $course_stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
} catch (PDOException $e) {
    $error_message = 'حدث خطأ أثناء جلب البيانات: ' . $e->getMessage();
    $pending_requests = [];
    $stats = ['total_pending' => 0, 'today_requests' => 0];
}

include 'includes/header.php';
?>

<!-- رأس الصفحة -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="h3 mb-2">
            <i class="fas fa-user-clock text-warning me-2"></i>
            طلبات الانضمام
        </h2>
        <p class="text-muted mb-0">إدارة طلبات انضمام الطلاب للكورسات</p>
    </div>
    <div class="d-flex gap-2">
        <button class="btn btn-outline-primary" onclick="refreshPage()">
            <i class="fas fa-sync-alt me-1"></i>تحديث
        </button>
        <?php if ($stats['total_pending'] > 0): ?>
        <form method="POST" class="d-inline">
            <button type="submit" name="action" value="approve_all" class="btn btn-success"
                    onclick="return confirm('هل أنت متأكد من قبول جميع الطلبات المعلقة (<?php echo $stats['total_pending']; ?> طلب)؟')">
                <i class="fas fa-check-double me-1"></i>قبول الكل (<?php echo $stats['total_pending']; ?>)
            </button>
        </form>
        <?php endif; ?>
        <a href="students.php" class="btn btn-info">
            <i class="fas fa-users me-1"></i>عرض الطلاب
        </a>
    </div>
</div>

<!-- رسائل النجاح والخطأ -->
<?php if (isset($success_message)): ?>
<div class="alert alert-success alert-dismissible fade show" role="alert">
    <i class="fas fa-check-circle me-2"></i>
    <?php echo $success_message; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<?php if (isset($error_message)): ?>
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <i class="fas fa-exclamation-triangle me-2"></i>
    <?php echo $error_message; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<!-- بطاقات الإحصائيات -->
<div class="row g-4 mb-4">
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm stats-card pending">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-warning bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-clock text-warning fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h4 class="mb-1 fw-bold text-warning"><?php echo $stats['total_pending']; ?></h4>
                        <p class="text-muted mb-0 fw-medium">طلبات معلقة</p>
                        <?php if ($stats['today_requests'] > 0): ?>
                            <small class="text-success">+<?php echo $stats['today_requests']; ?> اليوم</small>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm stats-card approved">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-success bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-check-circle text-success fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h4 class="mb-1 fw-bold text-success"><?php echo $stats['total_approved']; ?></h4>
                        <p class="text-muted mb-0 fw-medium">طلبات مقبولة</p>
                        <?php if ($stats['today_approved'] > 0): ?>
                            <small class="text-success">+<?php echo $stats['today_approved']; ?> اليوم</small>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm stats-card rejected">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-danger bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-times-circle text-danger fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h4 class="mb-1 fw-bold text-danger"><?php echo $stats['total_rejected']; ?></h4>
                        <p class="text-muted mb-0 fw-medium">طلبات مرفوضة</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm stats-card students">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-primary bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-users text-primary fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h4 class="mb-1 fw-bold text-primary"><?php echo $course_stats['total_enrolled_students']; ?></h4>
                        <p class="text-muted mb-0 fw-medium">طلاب مسجلون</p>
                        <small class="text-muted"><?php echo $course_stats['total_courses']; ?> كورس</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- جدول طلبات الانضمام -->
<div class="card border-0 shadow-sm">
    <div class="card-header bg-white border-bottom">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>
                طلبات الانضمام المعلقة
            </h5>
            <div class="d-flex gap-2">
                <input type="text" class="form-control form-control-sm" placeholder="البحث في الأسماء والإيميلات..." id="searchInput" style="width: 250px;">
                <select class="form-select form-select-sm" id="courseFilter" style="width: 200px;">
                    <option value="">جميع الكورسات</option>
                    <?php
                    // جلب كورسات المدرب للفلتر
                    $stmt = $conn->prepare("
                        SELECT DISTINCT c.id, c.title
                        FROM courses c
                        INNER JOIN join_requests jr ON c.id = jr.course_id
                        WHERE c.instructor_id = ? AND jr.status = 'pending'
                        ORDER BY c.title
                    ");
                    $stmt->execute([$_SESSION['user_id']]);
                    $filter_courses = $stmt->fetchAll();

                    foreach ($filter_courses as $course):
                    ?>
                    <option value="<?php echo $course['id']; ?>"><?php echo htmlspecialchars($course['title']); ?></option>
                    <?php endforeach; ?>
                </select>
                <button class="btn btn-outline-secondary btn-sm" onclick="clearFilters()">
                    <i class="fas fa-times me-1"></i>مسح الفلاتر
                </button>
            </div>
        </div>
    </div>
    <div class="card-body p-0">
        <?php if (empty($pending_requests)): ?>
        <div class="text-center py-5">
            <i class="fas fa-inbox text-muted" style="font-size: 3rem;"></i>
            <h5 class="mt-3 text-muted">لا توجد طلبات انضمام معلقة</h5>
            <p class="text-muted">جميع طلبات الانضمام تم التعامل معها</p>
        </div>
        <?php else: ?>
        <div class="table-responsive">
            <table class="table table-hover mb-0" id="requestsTable">
                <thead class="table-light">
                    <tr>
                        <th>
                            <input type="checkbox" class="form-check-input" id="selectAll">
                        </th>
                        <th>الطالب</th>
                        <th>الكورس</th>
                        <th>الرسالة</th>
                        <th>تاريخ الطلب</th>
                        <th>معلومات الاتصال</th>
                        <th>حالة الكورس</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($pending_requests as $request): ?>
                    <tr>
                        <td>
                            <input type="checkbox" class="form-check-input request-checkbox" 
                                   value="<?php echo $request['enrollment_id']; ?>">
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar-sm bg-primary bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center me-2">
                                    <i class="fas fa-user text-primary"></i>
                                </div>
                                <div>
                                    <h6 class="mb-0"><?php echo htmlspecialchars($request['student_name']); ?></h6>
                                    <small class="text-muted"><?php echo htmlspecialchars($request['student_email']); ?></small>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="fw-medium"><?php echo htmlspecialchars($request['course_title']); ?></span>
                        </td>
                        <td>
                            <?php if (!empty($request['message'])): ?>
                                <small class="text-muted"><?php echo htmlspecialchars(substr($request['message'], 0, 50)); ?><?php echo strlen($request['message']) > 50 ? '...' : ''; ?></small>
                            <?php else: ?>
                                <span class="text-muted">لا توجد رسالة</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <span class="text-muted"><?php echo date('Y-m-d H:i', strtotime($request['request_date'])); ?></span>
                        </td>
                        <td>
                            <?php if ($request['student_phone']): ?>
                            <a href="tel:<?php echo $request['student_phone']; ?>" class="text-decoration-none">
                                <i class="fas fa-phone text-success me-1"></i>
                                <?php echo htmlspecialchars($request['student_phone']); ?>
                            </a>
                            <?php else: ?>
                            <span class="text-muted">غير متوفر</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <?php 
                            $capacity_percentage = ($request['current_students'] / $request['max_students']) * 100;
                            $capacity_class = $capacity_percentage >= 90 ? 'danger' : ($capacity_percentage >= 70 ? 'warning' : 'success');
                            ?>
                            <div class="d-flex align-items-center">
                                <small class="text-<?php echo $capacity_class; ?> me-2">
                                    <?php echo $request['current_students']; ?>/<?php echo $request['max_students']; ?>
                                </small>
                                <div class="progress" style="width: 60px; height: 6px;">
                                    <div class="progress-bar bg-<?php echo $capacity_class; ?>" 
                                         style="width: <?php echo $capacity_percentage; ?>%"></div>
                                </div>
                            </div>
                        </td>
                        <td>
                            <div class="d-flex gap-1">
                                <form method="POST" class="d-inline" id="approveForm_<?php echo $request['enrollment_id']; ?>">
                                    <input type="hidden" name="student_id" value="<?php echo $request['student_id']; ?>">
                                    <input type="hidden" name="course_id" value="<?php echo $request['course_id']; ?>">
                                    <input type="hidden" name="action" value="approve">
                                    <button type="button"
                                            class="btn btn-sm btn-success approve-btn"
                                            data-student-name="<?php echo htmlspecialchars($request['student_name']); ?>"
                                            data-form-id="approveForm_<?php echo $request['enrollment_id']; ?>"
                                            title="قبول الطلب - سيتم تسجيل الطالب في الكورس">
                                        <i class="fas fa-check me-1"></i>قبول
                                    </button>
                                </form>
                                <form method="POST" class="d-inline" id="rejectForm_<?php echo $request['enrollment_id']; ?>">
                                    <input type="hidden" name="student_id" value="<?php echo $request['student_id']; ?>">
                                    <input type="hidden" name="course_id" value="<?php echo $request['course_id']; ?>">
                                    <input type="hidden" name="action" value="reject">
                                    <button type="button"
                                            class="btn btn-sm btn-danger reject-btn"
                                            data-student-name="<?php echo htmlspecialchars($request['student_name']); ?>"
                                            data-form-id="rejectForm_<?php echo $request['enrollment_id']; ?>"
                                            title="رفض الطلب">
                                        <i class="fas fa-times me-1"></i>رفض
                                    </button>
                                </form>
                                <button class="btn btn-sm btn-outline-info"
                                        onclick="showRequestDetails(<?php echo $request['enrollment_id']; ?>, '<?php echo htmlspecialchars($request['student_name']); ?>', '<?php echo htmlspecialchars($request['course_title']); ?>', '<?php echo htmlspecialchars($request['message']); ?>', '<?php echo $request['request_date']; ?>')"
                                        title="عرض تفاصيل الطلب">
                                    <i class="fas fa-info-circle"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- Modal تفاصيل الطلب -->
<div class="modal fade" id="requestDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-info-circle text-primary me-2"></i>
                    تفاصيل طلب الانضمام
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-muted">معلومات الطالب</h6>
                        <p><strong>الاسم:</strong> <span id="modalStudentName"></span></p>
                        <p><strong>البريد الإلكتروني:</strong> <span id="modalStudentEmail"></span></p>
                        <p><strong>رقم الهاتف:</strong> <span id="modalStudentPhone"></span></p>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-muted">معلومات الكورس</h6>
                        <p><strong>اسم الكورس:</strong> <span id="modalCourseTitle"></span></p>
                        <p><strong>تاريخ الطلب:</strong> <span id="modalRequestDate"></span></p>
                        <p><strong>حالة الكورس:</strong> <span id="modalCourseCapacity"></span></p>
                    </div>
                </div>
                <div class="mt-3">
                    <h6 class="text-muted">رسالة الطالب</h6>
                    <div class="bg-light p-3 rounded">
                        <p id="modalMessage" class="mb-0"></p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-success" id="modalApproveBtn">
                    <i class="fas fa-check me-1"></i>قبول الطلب
                </button>
                <button type="button" class="btn btn-danger" id="modalRejectBtn">
                    <i class="fas fa-times me-1"></i>رفض الطلب
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.stats-card {
    transition: all 0.3s ease;
    border-left: 4px solid transparent;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

.stats-card.pending {
    border-left-color: #ffc107;
}

.stats-card.approved {
    border-left-color: #28a745;
}

.stats-card.rejected {
    border-left-color: #dc3545;
}

.stats-card.students {
    border-left-color: #007bff;
}

.table-responsive {
    border-radius: 10px;
}

.btn-group-actions {
    display: flex;
    gap: 5px;
}

.request-row {
    transition: all 0.2s ease;
}

.request-row:hover {
    background-color: #f8f9fa;
}

.avatar-sm {
    width: 40px;
    height: 40px;
}

.progress {
    border-radius: 10px;
}

.alert {
    border-radius: 10px;
    border: none;
}
</style>

<script>
// تحديث الصفحة
function refreshPage() {
    // إضافة تأثير تحميل
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري التحديث...';
    btn.disabled = true;

    setTimeout(() => {
        location.reload();
    }, 500);
}

// عرض تفاصيل الطلب
function showRequestDetails(requestId, studentName, courseTitle, message, requestDate) {
    // تعبئة البيانات في Modal
    document.getElementById('modalStudentName').textContent = studentName;
    document.getElementById('modalCourseTitle').textContent = courseTitle;
    document.getElementById('modalMessage').textContent = message || 'لا توجد رسالة';
    document.getElementById('modalRequestDate').textContent = new Date(requestDate).toLocaleDateString('ar-SA');

    // إظهار Modal
    const modal = new bootstrap.Modal(document.getElementById('requestDetailsModal'));
    modal.show();
}

// تحديد/إلغاء تحديد الكل
document.getElementById('selectAll').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.request-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
    updateSelectedCount();
});

// تحديث عدد المحدد
function updateSelectedCount() {
    const selected = document.querySelectorAll('.request-checkbox:checked').length;
    const selectAllBtn = document.getElementById('selectAll');

    if (selected > 0) {
        selectAllBtn.indeterminate = selected < document.querySelectorAll('.request-checkbox').length;
    } else {
        selectAllBtn.indeterminate = false;
    }
}

// البحث في الجدول
document.getElementById('searchInput').addEventListener('input', function() {
    const searchTerm = this.value.toLowerCase();
    const rows = document.querySelectorAll('#requestsTable tbody tr');
    let visibleCount = 0;

    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        const isVisible = text.includes(searchTerm);
        row.style.display = isVisible ? '' : 'none';
        if (isVisible) visibleCount++;
    });

    // إظهار رسالة إذا لم توجد نتائج
    const noResults = document.getElementById('noSearchResults');
    if (noResults) noResults.remove();

    if (visibleCount === 0 && searchTerm.length > 0) {
        const tbody = document.querySelector('#requestsTable tbody');
        const noResultsRow = document.createElement('tr');
        noResultsRow.id = 'noSearchResults';
        noResultsRow.innerHTML = `
            <td colspan="8" class="text-center py-4">
                <i class="fas fa-search text-muted fa-2x mb-2"></i>
                <p class="text-muted mb-0">لا توجد نتائج للبحث عن "${searchTerm}"</p>
            </td>
        `;
        tbody.appendChild(noResultsRow);
    }
});

// فلتر الكورسات
document.getElementById('courseFilter').addEventListener('change', function() {
    const selectedCourse = this.value;
    const rows = document.querySelectorAll('#requestsTable tbody tr');
    let visibleCount = 0;

    rows.forEach(row => {
        if (row.id === 'noSearchResults') return;

        const courseCell = row.cells[2]; // عمود الكورس
        const courseText = courseCell.textContent;

        const isVisible = selectedCourse === '' || courseText.includes(selectedCourse);
        row.style.display = isVisible ? '' : 'none';
        if (isVisible) visibleCount++;
    });

    updateFilterResults(visibleCount, 'الكورس المحدد');
});

// مسح الفلاتر
function clearFilters() {
    document.getElementById('searchInput').value = '';
    document.getElementById('courseFilter').value = '';

    const rows = document.querySelectorAll('#requestsTable tbody tr');
    rows.forEach(row => {
        if (row.id !== 'noSearchResults') {
            row.style.display = '';
        }
    });

    // إزالة رسالة عدم وجود نتائج
    const noResults = document.getElementById('noSearchResults');
    if (noResults) noResults.remove();
}

// تحديث نتائج الفلتر
function updateFilterResults(count, filterType) {
    const noResults = document.getElementById('noSearchResults');
    if (noResults) noResults.remove();

    if (count === 0) {
        const tbody = document.querySelector('#requestsTable tbody');
        const noResultsRow = document.createElement('tr');
        noResultsRow.id = 'noSearchResults';
        noResultsRow.innerHTML = `
            <td colspan="8" class="text-center py-4">
                <i class="fas fa-filter text-muted fa-2x mb-2"></i>
                <p class="text-muted mb-0">لا توجد طلبات تطابق ${filterType}</p>
                <button class="btn btn-sm btn-outline-primary mt-2" onclick="clearFilters()">
                    <i class="fas fa-times me-1"></i>مسح الفلاتر
                </button>
            </td>
        `;
        tbody.appendChild(noResultsRow);
    }
}

// معالجة أزرار القبول والرفض
document.addEventListener('DOMContentLoaded', function() {
    // معالجة أزرار القبول
    document.querySelectorAll('.approve-btn').forEach(button => {
        button.addEventListener('click', function() {
            const studentName = this.getAttribute('data-student-name');
            const formId = this.getAttribute('data-form-id');

            if (confirm(`هل أنت متأكد من قبول طلب ${studentName}؟\n\nسيتم:\n- قبول الطلب\n- تسجيل الطالب في الكورس\n- إرسال إشعار للطالب`)) {
                // تغيير نص الزر
                this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري المعالجة...';
                this.disabled = true;

                // إرسال النموذج
                document.getElementById(formId).submit();
            }
        });
    });

    // معالجة أزرار الرفض
    document.querySelectorAll('.reject-btn').forEach(button => {
        button.addEventListener('click', function() {
            const studentName = this.getAttribute('data-student-name');
            const formId = this.getAttribute('data-form-id');

            if (confirm(`هل أنت متأكد من رفض طلب ${studentName}؟`)) {
                // تغيير نص الزر
                this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري المعالجة...';
                this.disabled = true;

                // إرسال النموذج
                document.getElementById(formId).submit();
            }
        });
    });

    // إضافة تأثيرات للبطاقات
    const statsCards = document.querySelectorAll('.stats-card');
    statsCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // تحديث عدد المحدد عند تغيير الاختيار
    document.querySelectorAll('.request-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', updateSelectedCount);
    });
});

// تهيئة DataTables إذا كان متوفراً
document.addEventListener('DOMContentLoaded', function() {
    if (typeof DataTable !== 'undefined') {
        new DataTable('#requestsTable', {
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/ar.json'
            },
            pageLength: 25,
            order: [[3, 'desc']], // ترتيب حسب تاريخ الطلب
            columnDefs: [
                { orderable: false, targets: [0, 6] } // عدم ترتيب عمود الاختيار والإجراءات
            ]
        });
    }
});
</script>

<?php include 'includes/footer.php'; ?>
