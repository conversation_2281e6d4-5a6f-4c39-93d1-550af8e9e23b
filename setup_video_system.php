<?php
/**
 * إعداد نظام الفيديوهات والفصول
 * Setup video and chapters system
 */

require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>إعداد نظام الفيديوهات والفصول</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "</head>";
echo "<body>";
echo "<div class='container mt-4'>";
echo "<h2>🎥 إعداد نظام الفيديوهات والفصول</h2>";

try {
    // 1. إنشاء جدول الفصول
    echo "<h4>📚 إنشاء جدول الفصول</h4>";
    
    $stmt = $conn->query("SHOW TABLES LIKE 'course_chapters'");
    if ($stmt->rowCount() == 0) {
        $conn->exec("
            CREATE TABLE course_chapters (
                id INT AUTO_INCREMENT PRIMARY KEY,
                course_id INT NOT NULL,
                title VARCHAR(255) NOT NULL,
                description TEXT,
                chapter_order INT NOT NULL DEFAULT 1,
                status ENUM('active', 'inactive') DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                
                INDEX idx_course_id (course_id),
                INDEX idx_order (chapter_order),
                INDEX idx_status (status),
                
                FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        echo "<div class='alert alert-success'>✅ تم إنشاء جدول الفصول</div>";
    } else {
        echo "<div class='alert alert-info'>ℹ️ جدول الفصول موجود</div>";
    }

    // 2. إنشاء جدول الدروس/المحاضرات
    echo "<h4>🎬 إنشاء جدول الدروس</h4>";
    
    $stmt = $conn->query("SHOW TABLES LIKE 'course_lessons'");
    if ($stmt->rowCount() == 0) {
        $conn->exec("
            CREATE TABLE course_lessons (
                id INT AUTO_INCREMENT PRIMARY KEY,
                course_id INT NOT NULL,
                chapter_id INT NOT NULL,
                title VARCHAR(255) NOT NULL,
                description TEXT,
                video_url VARCHAR(500),
                video_file_path VARCHAR(500),
                video_duration INT DEFAULT 0,
                video_size BIGINT DEFAULT 0,
                thumbnail_path VARCHAR(500),
                lesson_order INT NOT NULL DEFAULT 1,
                is_free BOOLEAN DEFAULT FALSE,
                status ENUM('active', 'inactive') DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                
                INDEX idx_course_id (course_id),
                INDEX idx_chapter_id (chapter_id),
                INDEX idx_order (lesson_order),
                INDEX idx_status (status),
                INDEX idx_free (is_free),
                
                FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
                FOREIGN KEY (chapter_id) REFERENCES course_chapters(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        echo "<div class='alert alert-success'>✅ تم إنشاء جدول الدروس</div>";
    } else {
        echo "<div class='alert alert-info'>ℹ️ جدول الدروس موجود</div>";
    }

    // 3. إنشاء جدول تقدم الطلاب
    echo "<h4>📈 إنشاء جدول تقدم الطلاب</h4>";
    
    $stmt = $conn->query("SHOW TABLES LIKE 'student_lesson_progress'");
    if ($stmt->rowCount() == 0) {
        $conn->exec("
            CREATE TABLE student_lesson_progress (
                id INT AUTO_INCREMENT PRIMARY KEY,
                student_id INT NOT NULL,
                course_id INT NOT NULL,
                lesson_id INT NOT NULL,
                watch_time INT DEFAULT 0,
                total_duration INT DEFAULT 0,
                completion_percentage DECIMAL(5,2) DEFAULT 0.00,
                is_completed BOOLEAN DEFAULT FALSE,
                last_watched_at TIMESTAMP NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                
                INDEX idx_student_id (student_id),
                INDEX idx_course_id (course_id),
                INDEX idx_lesson_id (lesson_id),
                INDEX idx_completed (is_completed),
                
                FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
                FOREIGN KEY (lesson_id) REFERENCES course_lessons(id) ON DELETE CASCADE,
                
                UNIQUE KEY unique_student_lesson (student_id, lesson_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        echo "<div class='alert alert-success'>✅ تم إنشاء جدول تقدم الطلاب</div>";
    } else {
        echo "<div class='alert alert-info'>ℹ️ جدول تقدم الطلاب موجود</div>";
    }

    // 4. إنشاء مجلدات التحميل
    echo "<h4>📁 إنشاء مجلدات التحميل</h4>";
    
    $upload_dirs = [
        'uploads/videos',
        'uploads/thumbnails',
        'uploads/materials'
    ];
    
    foreach ($upload_dirs as $dir) {
        if (!file_exists($dir)) {
            mkdir($dir, 0777, true);
            echo "<div class='alert alert-success'>✅ تم إنشاء مجلد: $dir</div>";
        } else {
            echo "<div class='alert alert-info'>ℹ️ مجلد موجود: $dir</div>";
        }
    }

    // 5. إضافة بيانات تجريبية للكورس رقم 15
    echo "<h4>📚 إضافة فصول ودروس تجريبية للكورس رقم 15</h4>";
    
    $course_id = 15;
    
    // التحقق من وجود فصول
    $stmt = $conn->prepare("SELECT COUNT(*) FROM course_chapters WHERE course_id = ?");
    $stmt->execute([$course_id]);
    $chapters_count = $stmt->fetchColumn();
    
    if ($chapters_count == 0) {
        // إنشاء فصول تجريبية
        $sample_chapters = [
            [
                'title' => 'الفصل الأول: مقدمة في البرمجة المتقدمة',
                'description' => 'نظرة عامة على مفاهيم البرمجة المتقدمة والأدوات المطلوبة',
                'order' => 1
            ],
            [
                'title' => 'الفصل الثاني: هياكل البيانات المتقدمة',
                'description' => 'تعلم هياكل البيانات المعقدة وتطبيقاتها العملية',
                'order' => 2
            ],
            [
                'title' => 'الفصل الثالث: خوارزميات التحسين',
                'description' => 'تحسين الأداء وكتابة كود فعال ومحسن',
                'order' => 3
            ],
            [
                'title' => 'الفصل الرابع: مشاريع تطبيقية',
                'description' => 'تطبيق عملي شامل لجميع المفاهيم المتعلمة',
                'order' => 4
            ]
        ];
        
        $chapter_ids = [];
        foreach ($sample_chapters as $chapter) {
            $stmt = $conn->prepare("
                INSERT INTO course_chapters (course_id, title, description, chapter_order, status, created_at)
                VALUES (?, ?, ?, ?, 'active', NOW())
            ");
            $stmt->execute([$course_id, $chapter['title'], $chapter['description'], $chapter['order']]);
            $chapter_ids[] = $conn->lastInsertId();
        }
        
        echo "<div class='alert alert-success'>✅ تم إنشاء " . count($sample_chapters) . " فصول</div>";
        
        // إنشاء دروس لكل فصل
        $lessons_created = 0;
        foreach ($chapter_ids as $index => $chapter_id) {
            $chapter_lessons = [
                // الفصل الأول
                0 => [
                    ['title' => 'مرحباً بك في الكورس', 'description' => 'مقدمة ترحيبية وشرح محتوى الكورس', 'is_free' => true],
                    ['title' => 'إعداد بيئة التطوير', 'description' => 'تثبيت وإعداد الأدوات المطلوبة للبرمجة'],
                    ['title' => 'مراجعة المفاهيم الأساسية', 'description' => 'مراجعة سريعة للمفاهيم الأساسية في البرمجة'],
                    ['title' => 'أول برنامج متقدم', 'description' => 'كتابة أول برنامج باستخدام المفاهيم المتقدمة']
                ],
                // الفصل الثاني
                1 => [
                    ['title' => 'مقدمة في هياكل البيانات', 'description' => 'نظرة عامة على هياكل البيانات المختلفة'],
                    ['title' => 'القوائم المترابطة', 'description' => 'تطبيق وفهم القوائم المترابطة'],
                    ['title' => 'الأشجار والرسوم البيانية', 'description' => 'العمل مع الأشجار والرسوم البيانية'],
                    ['title' => 'جداول التجميع', 'description' => 'استخدام جداول التجميع لتحسين الأداء']
                ],
                // الفصل الثالث
                2 => [
                    ['title' => 'مقدمة في الخوارزميات', 'description' => 'أساسيات تحليل وتصميم الخوارزميات'],
                    ['title' => 'خوارزميات الترتيب المتقدمة', 'description' => 'خوارزميات ترتيب فعالة ومحسنة'],
                    ['title' => 'خوارزميات البحث', 'description' => 'تقنيات البحث السريع والفعال'],
                    ['title' => 'البرمجة الديناميكية', 'description' => 'حل المشاكل المعقدة باستخدام البرمجة الديناميكية']
                ],
                // الفصل الرابع
                3 => [
                    ['title' => 'تخطيط المشروع', 'description' => 'كيفية تخطيط وتصميم مشروع برمجي كبير'],
                    ['title' => 'تطبيق نظام إدارة المهام', 'description' => 'بناء نظام إدارة مهام متكامل'],
                    ['title' => 'اختبار وتحسين الأداء', 'description' => 'اختبار المشروع وتحسين أدائه'],
                    ['title' => 'نشر المشروع', 'description' => 'كيفية نشر المشروع وجعله متاحاً للمستخدمين']
                ]
            ];
            
            if (isset($chapter_lessons[$index])) {
                foreach ($chapter_lessons[$index] as $lesson_order => $lesson) {
                    $stmt = $conn->prepare("
                        INSERT INTO course_lessons (course_id, chapter_id, title, description, lesson_order, is_free, status, created_at)
                        VALUES (?, ?, ?, ?, ?, ?, 'active', NOW())
                    ");
                    $stmt->execute([
                        $course_id,
                        $chapter_id,
                        $lesson['title'],
                        $lesson['description'],
                        $lesson_order + 1,
                        $lesson['is_free'] ?? false
                    ]);
                    $lessons_created++;
                }
            }
        }
        
        echo "<div class='alert alert-success'>✅ تم إنشاء $lessons_created درس</div>";
        
    } else {
        echo "<div class='alert alert-info'>ℹ️ الكورس يحتوي على فصول ودروس</div>";
    }

    // 6. عرض إحصائيات النظام
    echo "<h4>📊 إحصائيات النظام</h4>";
    
    $stmt = $conn->query("SELECT COUNT(*) FROM course_chapters");
    $total_chapters = $stmt->fetchColumn();
    
    $stmt = $conn->query("SELECT COUNT(*) FROM course_lessons");
    $total_lessons = $stmt->fetchColumn();
    
    $stmt = $conn->query("SELECT COUNT(*) FROM course_lessons WHERE is_free = 1");
    $free_lessons = $stmt->fetchColumn();
    
    $stmt = $conn->query("SELECT COUNT(*) FROM student_lesson_progress");
    $total_progress = $stmt->fetchColumn();
    
    echo "<div class='row'>";
    
    echo "<div class='col-md-3'>";
    echo "<div class='card bg-primary text-white'>";
    echo "<div class='card-body text-center'>";
    echo "<h3>$total_chapters</h3>";
    echo "<p class='mb-0'>إجمالي الفصول</p>";
    echo "</div></div></div>";
    
    echo "<div class='col-md-3'>";
    echo "<div class='card bg-success text-white'>";
    echo "<div class='card-body text-center'>";
    echo "<h3>$total_lessons</h3>";
    echo "<p class='mb-0'>إجمالي الدروس</p>";
    echo "</div></div></div>";
    
    echo "<div class='col-md-3'>";
    echo "<div class='card bg-info text-white'>";
    echo "<div class='card-body text-center'>";
    echo "<h3>$free_lessons</h3>";
    echo "<p class='mb-0'>دروس مجانية</p>";
    echo "</div></div></div>";
    
    echo "<div class='col-md-3'>";
    echo "<div class='card bg-warning text-white'>";
    echo "<div class='card-body text-center'>";
    echo "<h3>$total_progress</h3>";
    echo "<p class='mb-0'>سجلات التقدم</p>";
    echo "</div></div></div>";
    
    echo "</div>";

    // 7. عرض هيكل الكورس رقم 15
    echo "<h4>🏗️ هيكل الكورس رقم 15</h4>";
    
    $stmt = $conn->prepare("
        SELECT ch.*, 
               (SELECT COUNT(*) FROM course_lessons WHERE chapter_id = ch.id) as lessons_count
        FROM course_chapters ch
        WHERE ch.course_id = ?
        ORDER BY ch.chapter_order
    ");
    $stmt->execute([$course_id]);
    $chapters = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($chapters)) {
        foreach ($chapters as $chapter) {
            echo "<div class='card mb-3'>";
            echo "<div class='card-header bg-primary text-white'>";
            echo "<h6 class='mb-0'>" . htmlspecialchars($chapter['title']) . " ({$chapter['lessons_count']} دروس)</h6>";
            echo "</div>";
            echo "<div class='card-body'>";
            echo "<p>" . htmlspecialchars($chapter['description']) . "</p>";
            
            // جلب دروس هذا الفصل
            $stmt = $conn->prepare("
                SELECT * FROM course_lessons 
                WHERE chapter_id = ? 
                ORDER BY lesson_order
            ");
            $stmt->execute([$chapter['id']]);
            $lessons = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if (!empty($lessons)) {
                echo "<ol>";
                foreach ($lessons as $lesson) {
                    echo "<li>";
                    echo htmlspecialchars($lesson['title']);
                    if ($lesson['is_free']) {
                        echo " <span class='badge bg-success'>مجاني</span>";
                    }
                    echo "<br><small class='text-muted'>" . htmlspecialchars($lesson['description']) . "</small>";
                    echo "</li>";
                }
                echo "</ol>";
            }
            
            echo "</div></div>";
        }
    }

    echo "<div class='alert alert-success mt-4'>";
    echo "<h5>🎉 تم إعداد نظام الفيديوهات والفصول بنجاح!</h5>";
    echo "<ul class='mb-0'>";
    echo "<li>✅ تم إنشاء جداول الفصول والدروس</li>";
    echo "<li>✅ تم إنشاء جدول تقدم الطلاب</li>";
    echo "<li>✅ تم إنشاء مجلدات التحميل</li>";
    echo "<li>✅ تم إضافة 4 فصول و16 درس للكورس</li>";
    echo "<li>✅ النظام جاهز لرفع الفيديوهات</li>";
    echo "</ul>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div class='alert alert-danger'>❌ خطأ: " . htmlspecialchars($e->getMessage()) . "</div>";
}

echo "<div class='text-center mt-4'>";
echo "<a href='instructor/manage-course-content.php?course_id=15' class='btn btn-primary btn-lg me-2'>🎬 إدارة محتوى الكورس</a>";
echo "<a href='student/course-content.php?course_id=15' class='btn btn-success btn-lg me-2'>👨‍🎓 عرض الكورس للطالب</a>";
echo "<a href='course-details.php?id=15' class='btn btn-info btn-lg'>📚 تفاصيل الكورس</a>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
