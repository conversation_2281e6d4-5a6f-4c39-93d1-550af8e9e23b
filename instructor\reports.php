<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات المدرب
if (!isLoggedIn() || !isInstructor()) {
    header('Location: ../login.php');
    exit;
}

$pageTitle = 'التقارير والإحصائيات';
include 'includes/header.php';


// Initialize variables with default empty arrays
$session_attendance = [];
$course_activity = [];
$error = '';

try {
    // إحصائيات عامة
    $stmt = $conn->prepare("SELECT COUNT(*) as total_courses FROM courses WHERE instructor_id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $total_courses = $stmt->fetch(PDO::FETCH_ASSOC)['total_courses'];

    $stmt = $conn->prepare("SELECT COUNT(DISTINCT student_id) as total_students FROM course_enrollments ce JOIN courses c ON ce.course_id = c.id WHERE c.instructor_id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $total_students = $stmt->fetch(PDO::FETCH_ASSOC)['total_students'];

    $stmt = $conn->prepare("SELECT COUNT(*) as total_sessions FROM sessions s JOIN courses c ON s.course_id = c.id WHERE c.instructor_id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $total_sessions = $stmt->fetch(PDO::FETCH_ASSOC)['total_sessions'];

    // تقرير حضور الجلسات
    $stmt = $conn->prepare("SELECT 
        c.title as course_title,
        s.title as session_title,
        s.start_time,
        COUNT(DISTINCT sa.student_id) as attendees_count,
        (SELECT COUNT(*) FROM course_enrollments WHERE course_id = c.id) as total_enrolled
    FROM sessions s
    JOIN courses c ON s.course_id = c.id
    LEFT JOIN session_attendance sa ON s.id = sa.session_id
    WHERE c.instructor_id = ?
    GROUP BY s.id
    ORDER BY s.start_time DESC
    LIMIT 10");
    $stmt->execute([$_SESSION['user_id']]);
    $session_attendance = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // تقرير نشاط الكورسات
    $stmt = $conn->prepare("SELECT 
        c.title,
        c.created_at,
        COUNT(DISTINCT ce.student_id) as enrolled_students,
        COUNT(DISTINCT s.id) as total_sessions,
        (SELECT COUNT(DISTINCT sa.student_id) 
         FROM session_attendance sa 
         JOIN sessions ss ON sa.session_id = ss.id 
         WHERE ss.course_id = c.id) as total_attendance
    FROM courses c
    LEFT JOIN course_enrollments ce ON c.id = ce.course_id
    LEFT JOIN sessions s ON c.id = s.course_id
    WHERE c.instructor_id = ?
    GROUP BY c.id
    ORDER BY c.created_at DESC");
    $stmt->execute([$_SESSION['user_id']]);
    $course_activity = $stmt->fetchAll(PDO::FETCH_ASSOC);

} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء جلب البيانات';
    error_log($e->getMessage());
}
?>

<div class="container-fluid py-4">
    <!-- الإحصائيات العامة -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <h5 class="card-title">إجمالي الكورسات</h5>
                    <h2 class="display-4"><?php echo $total_courses; ?></h2>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <h5 class="card-title">إجمالي الطلاب</h5>
                    <h2 class="display-4"><?php echo $total_students; ?></h2>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <h5 class="card-title">إجمالي الجلسات</h5>
                    <h2 class="display-4"><?php echo $total_sessions; ?></h2>
                </div>
            </div>
        </div>
    </div>

    <!-- تقرير حضور الجلسات -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">تقرير حضور الجلسات</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>الكورس</th>
                            <th>الجلسة</th>
                            <th>تاريخ الجلسة</th>
                            <th>عدد الحضور</th>
                            <th>نسبة الحضور</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($session_attendance as $session): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($session['course_title']); ?></td>
                                <td><?php echo htmlspecialchars($session['session_title']); ?></td>
                                <td><?php echo date('Y-m-d H:i', strtotime($session['start_time'])); ?></td>
                                <td><?php echo $session['attendees_count']; ?></td>
                                <td>
                                    <?php 
                                    $attendance_rate = $session['total_enrolled'] > 0 
                                        ? round(($session['attendees_count'] / $session['total_enrolled']) * 100) 
                                        : 0;
                                    echo $attendance_rate . '%';
                                    ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- تقرير نشاط الكورسات -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">تقرير نشاط الكورسات</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>اسم الكورس</th>
                            <th>تاريخ الإنشاء</th>
                            <th>عدد الطلاب</th>
                            <th>عدد الجلسات</th>
                            <th>إجمالي الحضور</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($course_activity as $course): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($course['title']); ?></td>
                                <td><?php echo date('Y-m-d', strtotime($course['created_at'])); ?></td>
                                <td><?php echo $course['enrolled_students']; ?></td>
                                <td><?php echo $course['total_sessions']; ?></td>
                                <td><?php echo $course['total_attendance']; ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<?php require_once '../includes/footer.php'; ?>