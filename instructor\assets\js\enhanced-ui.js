/**
 * ملف الوظائف المتقدمة لواجهة المدرب
 * Enhanced UI Functions for Instructor Dashboard
 */

class InstructorUI {
    constructor() {
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.initializeComponents();
        this.setupAnimations();
        this.setupTheme();
    }

    // إعداد مستمعي الأحداث
    setupEventListeners() {
        document.addEventListener('DOMContentLoaded', () => {
            this.enhanceCards();
            this.enhanceButtons();
            this.enhanceForms();
            this.enhanceTables();
            this.setupTooltips();
            this.setupModals();
        });

        // مراقبة تغيير حجم النافذة
        window.addEventListener('resize', this.debounce(() => {
            this.handleResize();
        }, 250));

        // مراقبة التمرير
        window.addEventListener('scroll', this.throttle(() => {
            this.handleScroll();
        }, 16));
    }

    // تحسين البطاقات
    enhanceCards() {
        const cards = document.querySelectorAll('.card:not(.card-enhanced)');
        cards.forEach(card => {
            card.classList.add('card-enhanced');
            
            // إضافة تأثير الحركة عند الدخول للعرض
            this.observeElement(card, () => {
                card.classList.add('animate-fade-in');
            });

            // إضافة تأثير التمرير
            card.addEventListener('mouseenter', () => {
                card.style.transform = 'translateY(-4px)';
            });

            card.addEventListener('mouseleave', () => {
                card.style.transform = '';
            });
        });
    }

    // تحسين الأزرار
    enhanceButtons() {
        const buttons = document.querySelectorAll('.btn:not(.btn-enhanced)');
        buttons.forEach(button => {
            button.classList.add('btn-enhanced');

            // إضافة تأثير الموجة
            button.addEventListener('click', (e) => {
                this.createRippleEffect(e, button);
            });

            // إضافة تأثير التحميل
            if (button.type === 'submit') {
                button.addEventListener('click', () => {
                    this.showButtonLoading(button);
                });
            }
        });
    }

    // تحسين النماذج
    enhanceForms() {
        const formControls = document.querySelectorAll('.form-control:not(.form-control-enhanced)');
        formControls.forEach(control => {
            control.classList.add('form-control-enhanced');

            // إضافة تأثير التركيز
            control.addEventListener('focus', () => {
                control.parentElement.classList.add('focused');
            });

            control.addEventListener('blur', () => {
                control.parentElement.classList.remove('focused');
            });

            // التحقق من صحة البيانات في الوقت الفعلي
            control.addEventListener('input', () => {
                this.validateField(control);
            });
        });

        // تحسين التسميات
        const labels = document.querySelectorAll('.form-label:not(.form-label-enhanced)');
        labels.forEach(label => {
            label.classList.add('form-label-enhanced');
        });
    }

    // تحسين الجداول
    enhanceTables() {
        const tables = document.querySelectorAll('.table:not(.table-enhanced)');
        tables.forEach(table => {
            table.classList.add('table-enhanced');

            // إضافة تأثير التمرير على الصفوف
            const rows = table.querySelectorAll('tbody tr');
            rows.forEach(row => {
                row.addEventListener('mouseenter', () => {
                    row.style.transform = 'scale(1.01)';
                });

                row.addEventListener('mouseleave', () => {
                    row.style.transform = '';
                });
            });
        });
    }

    // إعداد التلميحات
    setupTooltips() {
        const tooltipElements = document.querySelectorAll('[data-bs-toggle="tooltip"]');
        tooltipElements.forEach(element => {
            new bootstrap.Tooltip(element, {
                animation: true,
                delay: { show: 500, hide: 100 }
            });
        });
    }

    // إعداد النوافذ المنبثقة
    setupModals() {
        const modals = document.querySelectorAll('.modal:not(.modal-enhanced)');
        modals.forEach(modal => {
            modal.classList.add('modal-enhanced');

            modal.addEventListener('show.bs.modal', () => {
                modal.querySelector('.modal-content').classList.add('animate-scale-in');
            });
        });
    }

    // إنشاء تأثير الموجة
    createRippleEffect(event, button) {
        const ripple = document.createElement('span');
        const rect = button.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = event.clientX - rect.left - size / 2;
        const y = event.clientY - rect.top - size / 2;

        ripple.style.width = ripple.style.height = size + 'px';
        ripple.style.left = x + 'px';
        ripple.style.top = y + 'px';
        ripple.classList.add('ripple');

        button.appendChild(ripple);

        setTimeout(() => {
            ripple.remove();
        }, 600);
    }

    // عرض حالة التحميل للزر
    showButtonLoading(button) {
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري المعالجة...';
        button.disabled = true;

        // استعادة الحالة الأصلية بعد 3 ثوان (يمكن تخصيصها)
        setTimeout(() => {
            button.innerHTML = originalText;
            button.disabled = false;
        }, 3000);
    }

    // التحقق من صحة الحقل
    validateField(field) {
        const value = field.value.trim();
        const type = field.type;
        let isValid = true;
        let message = '';

        // التحقق حسب نوع الحقل
        switch (type) {
            case 'email':
                isValid = this.isValidEmail(value);
                message = isValid ? '' : 'البريد الإلكتروني غير صحيح';
                break;
            case 'tel':
                isValid = this.isValidPhone(value);
                message = isValid ? '' : 'رقم الهاتف غير صحيح';
                break;
            case 'url':
                isValid = this.isValidURL(value);
                message = isValid ? '' : 'الرابط غير صحيح';
                break;
            default:
                if (field.required && !value) {
                    isValid = false;
                    message = 'هذا الحقل مطلوب';
                }
        }

        this.showFieldValidation(field, isValid, message);
    }

    // عرض نتيجة التحقق من الحقل
    showFieldValidation(field, isValid, message) {
        // إزالة الرسائل السابقة
        const existingFeedback = field.parentElement.querySelector('.invalid-feedback');
        if (existingFeedback) {
            existingFeedback.remove();
        }

        // إضافة أو إزالة الفئات
        field.classList.remove('is-valid', 'is-invalid');
        if (field.value.trim()) {
            field.classList.add(isValid ? 'is-valid' : 'is-invalid');

            // إضافة رسالة الخطأ
            if (!isValid && message) {
                const feedback = document.createElement('div');
                feedback.className = 'invalid-feedback';
                feedback.textContent = message;
                field.parentElement.appendChild(feedback);
            }
        }
    }

    // مراقبة العناصر عند دخولها للعرض
    observeElement(element, callback) {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    callback();
                    observer.unobserve(entry.target);
                }
            });
        }, {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        });

        observer.observe(element);
    }

    // معالجة تغيير حجم النافذة
    handleResize() {
        // إعادة حساب أحجام العناصر
        this.recalculateLayout();
    }

    // معالجة التمرير
    handleScroll() {
        const scrolled = window.pageYOffset;
        
        // إخفاء/إظهار عناصر معينة حسب التمرير
        this.toggleScrollElements(scrolled);
    }

    // إعادة حساب التخطيط
    recalculateLayout() {
        // يمكن إضافة منطق إعادة حساب التخطيط هنا
    }

    // تبديل عناصر التمرير
    toggleScrollElements(scrolled) {
        const threshold = 100;
        const scrollToTop = document.querySelector('.scroll-to-top');
        
        if (scrollToTop) {
            if (scrolled > threshold) {
                scrollToTop.style.opacity = '1';
                scrollToTop.style.pointerEvents = 'auto';
            } else {
                scrollToTop.style.opacity = '0';
                scrollToTop.style.pointerEvents = 'none';
            }
        }
    }

    // إعداد الحركات
    setupAnimations() {
        // تفعيل الحركات للعناصر الجديدة
        const animatedElements = document.querySelectorAll('[data-animate]');
        animatedElements.forEach(element => {
            this.observeElement(element, () => {
                const animation = element.dataset.animate;
                element.classList.add(`animate-${animation}`);
            });
        });
    }

    // إعداد الثيم
    setupTheme() {
        // تطبيق الثيم المحفوظ
        const savedTheme = localStorage.getItem('instructor_theme') || 'light';
        this.applyTheme(savedTheme);
    }

    // تطبيق الثيم
    applyTheme(theme) {
        document.body.classList.remove('light-theme', 'dark-theme');
        document.body.classList.add(`${theme}-theme`);
        localStorage.setItem('instructor_theme', theme);
    }

    // تبديل الثيم
    toggleTheme() {
        const currentTheme = document.body.classList.contains('dark-theme') ? 'dark' : 'light';
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
        this.applyTheme(newTheme);
        
        this.showToast(`تم التبديل إلى الوضع ${newTheme === 'dark' ? 'المظلم' : 'العادي'}`, 'success');
    }

    // عرض رسالة منبثقة
    showToast(message, type = 'info', duration = 3000) {
        const toastContainer = this.getToastContainer();
        const toast = this.createToast(message, type);
        
        toastContainer.appendChild(toast);
        
        // إظهار التوست
        setTimeout(() => {
            toast.classList.add('show');
        }, 100);
        
        // إخفاء التوست
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => {
                toast.remove();
            }, 300);
        }, duration);
    }

    // الحصول على حاوية التوست
    getToastContainer() {
        let container = document.getElementById('toast-container');
        if (!container) {
            container = document.createElement('div');
            container.id = 'toast-container';
            container.className = 'toast-container position-fixed top-0 end-0 p-3';
            container.style.zIndex = '9999';
            document.body.appendChild(container);
        }
        return container;
    }

    // إنشاء توست
    createToast(message, type) {
        const toast = document.createElement('div');
        toast.className = `toast align-items-center text-white bg-${type} border-0`;
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas fa-${this.getToastIcon(type)} me-2"></i>
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" onclick="this.parentElement.parentElement.remove()"></button>
            </div>
        `;
        return toast;
    }

    // الحصول على أيقونة التوست
    getToastIcon(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-triangle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    }

    // دوال التحقق من صحة البيانات
    isValidEmail(email) {
        const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return regex.test(email);
    }

    isValidPhone(phone) {
        const regex = /^[\+]?[1-9][\d]{0,15}$/;
        return regex.test(phone.replace(/\s/g, ''));
    }

    isValidURL(url) {
        try {
            new URL(url);
            return true;
        } catch {
            return false;
        }
    }

    // دوال المساعدة
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }
}

// تهيئة الواجهة المحسنة
const instructorUI = new InstructorUI();

// تصدير الفئة للاستخدام العام
window.InstructorUI = InstructorUI;
