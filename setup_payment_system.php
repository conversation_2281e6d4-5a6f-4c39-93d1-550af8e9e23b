<?php
/**
 * إعداد نظام الدفع والتسجيل
 * Setup payment and enrollment system
 */

require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>إعداد نظام الدفع والتسجيل</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "</head>";
echo "<body>";
echo "<div class='container mt-4'>";
echo "<h2>💳 إعداد نظام الدفع والتسجيل</h2>";

try {
    // 1. إنشاء جدول المدفوعات
    echo "<h4>💰 إنشاء جدول المدفوعات</h4>";
    
    $stmt = $conn->query("SHOW TABLES LIKE 'course_payments'");
    if ($stmt->rowCount() == 0) {
        $conn->exec("
            CREATE TABLE course_payments (
                id INT AUTO_INCREMENT PRIMARY KEY,
                student_id INT NOT NULL,
                course_id INT NOT NULL,
                amount DECIMAL(10,2) NOT NULL,
                currency VARCHAR(10) DEFAULT 'SAR',
                payment_method ENUM('credit_card', 'bank_transfer', 'paypal', 'cash') DEFAULT 'credit_card',
                payment_status ENUM('pending', 'completed', 'failed', 'refunded') DEFAULT 'pending',
                transaction_id VARCHAR(255),
                payment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                
                INDEX idx_student_id (student_id),
                INDEX idx_course_id (course_id),
                INDEX idx_status (payment_status),
                INDEX idx_transaction (transaction_id),
                
                FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        echo "<div class='alert alert-success'>✅ تم إنشاء جدول المدفوعات</div>";
    } else {
        echo "<div class='alert alert-info'>ℹ️ جدول المدفوعات موجود</div>";
    }

    // 2. إنشاء جدول طلبات التسجيل
    echo "<h4>📝 إنشاء جدول طلبات التسجيل</h4>";
    
    $stmt = $conn->query("SHOW TABLES LIKE 'enrollment_requests'");
    if ($stmt->rowCount() == 0) {
        $conn->exec("
            CREATE TABLE enrollment_requests (
                id INT AUTO_INCREMENT PRIMARY KEY,
                student_id INT NOT NULL,
                course_id INT NOT NULL,
                request_status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
                payment_required BOOLEAN DEFAULT FALSE,
                payment_id INT NULL,
                request_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                processed_date TIMESTAMP NULL,
                processed_by INT NULL,
                notes TEXT,
                
                INDEX idx_student_id (student_id),
                INDEX idx_course_id (course_id),
                INDEX idx_status (request_status),
                
                FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
                FOREIGN KEY (payment_id) REFERENCES course_payments(id) ON DELETE SET NULL,
                FOREIGN KEY (processed_by) REFERENCES users(id) ON DELETE SET NULL,
                
                UNIQUE KEY unique_student_course (student_id, course_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        echo "<div class='alert alert-success'>✅ تم إنشاء جدول طلبات التسجيل</div>";
    } else {
        echo "<div class='alert alert-info'>ℹ️ جدول طلبات التسجيل موجود</div>";
    }

    // 3. تحديث جدول التسجيلات
    echo "<h4>🔄 تحديث جدول التسجيلات</h4>";
    
    $stmt = $conn->query("SHOW COLUMNS FROM course_enrollments LIKE 'payment_id'");
    if ($stmt->rowCount() == 0) {
        $conn->exec("ALTER TABLE course_enrollments ADD COLUMN payment_id INT NULL AFTER status");
        $conn->exec("ALTER TABLE course_enrollments ADD FOREIGN KEY (payment_id) REFERENCES course_payments(id) ON DELETE SET NULL");
        echo "<div class='alert alert-success'>✅ تم إضافة عمود payment_id لجدول التسجيلات</div>";
    } else {
        echo "<div class='alert alert-info'>ℹ️ جدول التسجيلات محدث</div>";
    }

    // 4. إنشاء جدول سلة التسوق
    echo "<h4>🛒 إنشاء جدول سلة التسوق</h4>";
    
    $stmt = $conn->query("SHOW TABLES LIKE 'shopping_cart'");
    if ($stmt->rowCount() == 0) {
        $conn->exec("
            CREATE TABLE shopping_cart (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                course_id INT NOT NULL,
                added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                
                INDEX idx_user_id (user_id),
                INDEX idx_course_id (course_id),
                
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
                
                UNIQUE KEY unique_user_course (user_id, course_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        echo "<div class='alert alert-success'>✅ تم إنشاء جدول سلة التسوق</div>";
    } else {
        echo "<div class='alert alert-info'>ℹ️ جدول سلة التسوق موجود</div>";
    }

    // 5. إضافة بيانات تجريبية للمدفوعات
    echo "<h4>💳 إضافة مدفوعات تجريبية</h4>";
    
    $stmt = $conn->query("SELECT COUNT(*) FROM course_payments");
    $payments_count = $stmt->fetchColumn();
    
    if ($payments_count == 0) {
        // جلب طلاب وكورسات مدفوعة
        $stmt = $conn->query("SELECT id FROM users WHERE role = 'student' LIMIT 5");
        $students = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        $stmt = $conn->query("SELECT id, price FROM courses WHERE course_type = 'paid' LIMIT 3");
        $paid_courses = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($students) && !empty($paid_courses)) {
            foreach ($students as $index => $student_id) {
                if (isset($paid_courses[$index % count($paid_courses)])) {
                    $course = $paid_courses[$index % count($paid_courses)];
                    
                    // إنشاء دفعة
                    $stmt = $conn->prepare("
                        INSERT INTO course_payments (student_id, course_id, amount, payment_status, transaction_id, payment_date)
                        VALUES (?, ?, ?, 'completed', ?, NOW())
                    ");
                    $transaction_id = 'TXN_' . time() . '_' . $student_id . '_' . $course['id'];
                    $stmt->execute([$student_id, $course['id'], $course['price'], $transaction_id]);
                    $payment_id = $conn->lastInsertId();
                    
                    // إنشاء تسجيل في الكورس
                    $stmt = $conn->prepare("
                        INSERT INTO course_enrollments (student_id, course_id, enrollment_date, status, payment_id)
                        VALUES (?, ?, NOW(), 'active', ?)
                        ON DUPLICATE KEY UPDATE status = 'active', payment_id = VALUES(payment_id)
                    ");
                    $stmt->execute([$student_id, $course['id'], $payment_id]);
                }
            }
            
            echo "<div class='alert alert-success'>✅ تم إنشاء " . count($students) . " دفعة تجريبية</div>";
        }
    } else {
        echo "<div class='alert alert-info'>ℹ️ توجد مدفوعات في النظام: $payments_count</div>";
    }

    // 6. إحصائيات النظام
    echo "<h4>📊 إحصائيات النظام</h4>";
    
    $stmt = $conn->query("SELECT COUNT(*) FROM course_payments WHERE payment_status = 'completed'");
    $completed_payments = $stmt->fetchColumn();
    
    $stmt = $conn->query("SELECT SUM(amount) FROM course_payments WHERE payment_status = 'completed'");
    $total_revenue = $stmt->fetchColumn() ?: 0;
    
    $stmt = $conn->query("SELECT COUNT(*) FROM course_enrollments WHERE status = 'active'");
    $active_enrollments = $stmt->fetchColumn();
    
    $stmt = $conn->query("SELECT COUNT(*) FROM enrollment_requests WHERE request_status = 'pending'");
    $pending_requests = $stmt->fetchColumn();
    
    echo "<div class='row'>";
    
    echo "<div class='col-md-3'>";
    echo "<div class='card bg-success text-white'>";
    echo "<div class='card-body text-center'>";
    echo "<h3>$completed_payments</h3>";
    echo "<p class='mb-0'>دفعات مكتملة</p>";
    echo "</div></div></div>";
    
    echo "<div class='col-md-3'>";
    echo "<div class='card bg-primary text-white'>";
    echo "<div class='card-body text-center'>";
    echo "<h3>" . number_format($total_revenue, 0) . "</h3>";
    echo "<p class='mb-0'>إجمالي الإيرادات (ريال)</p>";
    echo "</div></div></div>";
    
    echo "<div class='col-md-3'>";
    echo "<div class='card bg-info text-white'>";
    echo "<div class='card-body text-center'>";
    echo "<h3>$active_enrollments</h3>";
    echo "<p class='mb-0'>تسجيلات نشطة</p>";
    echo "</div></div></div>";
    
    echo "<div class='col-md-3'>";
    echo "<div class='card bg-warning text-white'>";
    echo "<div class='card-body text-center'>";
    echo "<h3>$pending_requests</h3>";
    echo "<p class='mb-0'>طلبات معلقة</p>";
    echo "</div></div></div>";
    
    echo "</div>";

    // 7. عرض المدفوعات الأخيرة
    echo "<h4>💰 المدفوعات الأخيرة</h4>";
    
    $stmt = $conn->query("
        SELECT cp.*, u.name as student_name, c.title as course_title
        FROM course_payments cp
        JOIN users u ON cp.student_id = u.id
        JOIN courses c ON cp.course_id = c.id
        ORDER BY cp.payment_date DESC
        LIMIT 10
    ");
    $recent_payments = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($recent_payments)) {
        echo "<div class='table-responsive'>";
        echo "<table class='table table-striped'>";
        echo "<thead class='table-dark'>";
        echo "<tr>";
        echo "<th>الطالب</th>";
        echo "<th>الكورس</th>";
        echo "<th>المبلغ</th>";
        echo "<th>الحالة</th>";
        echo "<th>التاريخ</th>";
        echo "</tr>";
        echo "</thead>";
        echo "<tbody>";
        
        foreach ($recent_payments as $payment) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($payment['student_name']) . "</td>";
            echo "<td>" . htmlspecialchars($payment['course_title']) . "</td>";
            echo "<td>" . number_format($payment['amount'], 0) . " " . $payment['currency'] . "</td>";
            echo "<td>";
            switch ($payment['payment_status']) {
                case 'completed':
                    echo "<span class='badge bg-success'>مكتمل</span>";
                    break;
                case 'pending':
                    echo "<span class='badge bg-warning'>معلق</span>";
                    break;
                case 'failed':
                    echo "<span class='badge bg-danger'>فاشل</span>";
                    break;
                default:
                    echo "<span class='badge bg-secondary'>" . $payment['payment_status'] . "</span>";
            }
            echo "</td>";
            echo "<td>" . date('Y-m-d H:i', strtotime($payment['payment_date'])) . "</td>";
            echo "</tr>";
        }
        
        echo "</tbody>";
        echo "</table>";
        echo "</div>";
    }

    echo "<div class='alert alert-success mt-4'>";
    echo "<h5>🎉 تم إعداد نظام الدفع والتسجيل بنجاح!</h5>";
    echo "<ul class='mb-0'>";
    echo "<li>✅ تم إنشاء جداول المدفوعات وطلبات التسجيل</li>";
    echo "<li>✅ تم إنشاء جدول سلة التسوق</li>";
    echo "<li>✅ تم تحديث جدول التسجيلات</li>";
    echo "<li>✅ تم إضافة مدفوعات تجريبية</li>";
    echo "<li>✅ النظام جاهز للاستخدام</li>";
    echo "</ul>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div class='alert alert-danger'>❌ خطأ: " . htmlspecialchars($e->getMessage()) . "</div>";
}

echo "<div class='text-center mt-4'>";
echo "<a href='course-enrollment.php?course_id=15' class='btn btn-primary btn-lg me-2'>📝 تجربة التسجيل</a>";
echo "<a href='student/my-courses.php' class='btn btn-success btn-lg me-2'>📚 كورساتي</a>";
echo "<a href='payment-gateway.php' class='btn btn-info btn-lg'>💳 بوابة الدفع</a>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
