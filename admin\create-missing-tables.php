<?php
require_once '../config/database.php';

echo "<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>إنشاء الجداول المفقودة</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css' rel='stylesheet'>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f8f9fa; }
        .container { max-width: 800px; margin: 50px auto; }
        .log-item { padding: 10px; margin: 5px 0; border-radius: 5px; }
        .log-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .log-error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .log-info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
<div class='container'>
    <div class='card'>
        <div class='card-header bg-primary text-white'>
            <h4 class='mb-0'><i class='fas fa-database me-2'></i>إنشاء الجداول المفقودة</h4>
        </div>
        <div class='card-body'>
            <div id='logContainer'>";

function logMessage($message, $type = 'info') {
    $icon = $type === 'success' ? 'check-circle' : ($type === 'error' ? 'exclamation-circle' : 'info-circle');
    echo "<div class='log-item log-$type'><i class='fas fa-$icon me-2'></i>$message</div>";
    flush();
    ob_flush();
}

try {
    $conn->beginTransaction();
    
    // جدول النسخ الاحتياطية
    logMessage('إنشاء جدول النسخ الاحتياطية...', 'info');
    $conn->exec("
        CREATE TABLE IF NOT EXISTS backups (
            id INT AUTO_INCREMENT PRIMARY KEY,
            filename VARCHAR(255) NOT NULL,
            file_path VARCHAR(500) NOT NULL,
            file_size BIGINT DEFAULT NULL,
            backup_type ENUM('full', 'partial', 'structure', 'data') DEFAULT 'full',
            status ENUM('in_progress', 'completed', 'failed', 'cancelled') DEFAULT 'in_progress',
            progress_percentage TINYINT DEFAULT 0,
            error_message TEXT DEFAULT NULL,
            tables_included JSON DEFAULT NULL,
            compression_type ENUM('none', 'gzip', 'zip') DEFAULT 'gzip',
            created_by INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            completed_at TIMESTAMP NULL,
            FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
            INDEX idx_status (status),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    logMessage('✓ تم إنشاء جدول backups', 'success');
    
    // جدول سجل الأمان
    logMessage('إنشاء جدول سجل الأمان...', 'info');
    $conn->exec("
        CREATE TABLE IF NOT EXISTS security_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT DEFAULT NULL,
            event_type ENUM('login', 'logout', 'failed_login', 'password_change', 'permission_change', 'data_access', 'system_change') NOT NULL,
            ip_address VARCHAR(45),
            user_agent TEXT,
            details JSON DEFAULT NULL,
            risk_level ENUM('low', 'medium', 'high', 'critical') DEFAULT 'low',
            location_country VARCHAR(100) DEFAULT NULL,
            location_city VARCHAR(100) DEFAULT NULL,
            is_suspicious BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
            INDEX idx_user_id (user_id),
            INDEX idx_event_type (event_type),
            INDEX idx_risk_level (risk_level),
            INDEX idx_created_at (created_at),
            INDEX idx_suspicious (is_suspicious)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    logMessage('✓ تم إنشاء جدول security_logs', 'success');
    
    // جدول الصلاحيات
    logMessage('إنشاء جدول الصلاحيات...', 'info');
    $conn->exec("
        CREATE TABLE IF NOT EXISTS permissions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) UNIQUE NOT NULL,
            display_name VARCHAR(255) NOT NULL,
            description TEXT,
            category VARCHAR(50) DEFAULT 'general',
            resource VARCHAR(100) DEFAULT NULL,
            action VARCHAR(100) DEFAULT NULL,
            is_system BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_category (category),
            INDEX idx_resource (resource)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    logMessage('✓ تم إنشاء جدول permissions', 'success');
    
    // جدول صلاحيات الأدوار
    logMessage('إنشاء جدول صلاحيات الأدوار...', 'info');
    $conn->exec("
        CREATE TABLE IF NOT EXISTS role_permissions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            role ENUM('admin', 'instructor', 'student') NOT NULL,
            permission_id INT NOT NULL,
            granted BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE,
            UNIQUE KEY unique_role_permission (role, permission_id),
            INDEX idx_role (role)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    logMessage('✓ تم إنشاء جدول role_permissions', 'success');
    
    // جدول إحصائيات النظام
    logMessage('إنشاء جدول إحصائيات النظام...', 'info');
    $conn->exec("
        CREATE TABLE IF NOT EXISTS system_stats (
            id INT AUTO_INCREMENT PRIMARY KEY,
            stat_date DATE NOT NULL,
            total_users INT DEFAULT 0,
            active_users INT DEFAULT 0,
            new_users INT DEFAULT 0,
            total_students INT DEFAULT 0,
            total_instructors INT DEFAULT 0,
            total_courses INT DEFAULT 0,
            active_courses INT DEFAULT 0,
            new_courses INT DEFAULT 0,
            total_sessions INT DEFAULT 0,
            completed_sessions INT DEFAULT 0,
            live_sessions INT DEFAULT 0,
            total_enrollments INT DEFAULT 0,
            new_enrollments INT DEFAULT 0,
            total_assignments INT DEFAULT 0,
            submitted_assignments INT DEFAULT 0,
            total_quizzes INT DEFAULT 0,
            completed_quizzes INT DEFAULT 0,
            total_certificates INT DEFAULT 0,
            new_certificates INT DEFAULT 0,
            total_revenue DECIMAL(12,2) DEFAULT 0,
            platform_commission DECIMAL(12,2) DEFAULT 0,
            instructor_earnings DECIMAL(12,2) DEFAULT 0,
            pending_payments DECIMAL(12,2) DEFAULT 0,
            storage_used BIGINT DEFAULT 0,
            bandwidth_used BIGINT DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_date (stat_date),
            INDEX idx_date (stat_date)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    logMessage('✓ تم إنشاء جدول system_stats', 'success');
    
    // جدول تتبع الملفات
    logMessage('إنشاء جدول تتبع الملفات...', 'info');
    $conn->exec("
        CREATE TABLE IF NOT EXISTS file_uploads (
            id INT AUTO_INCREMENT PRIMARY KEY,
            original_name VARCHAR(255) NOT NULL,
            stored_name VARCHAR(255) NOT NULL,
            file_path VARCHAR(500) NOT NULL,
            file_size BIGINT NOT NULL,
            mime_type VARCHAR(100) NOT NULL,
            file_type ENUM('image', 'video', 'audio', 'document', 'archive', 'other') NOT NULL,
            uploaded_by INT NOT NULL,
            related_type ENUM('course', 'session', 'assignment', 'quiz', 'user', 'library', 'system') DEFAULT NULL,
            related_id INT DEFAULT NULL,
            is_public BOOLEAN DEFAULT FALSE,
            download_count INT DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (uploaded_by) REFERENCES users(id) ON DELETE CASCADE,
            INDEX idx_uploaded_by (uploaded_by),
            INDEX idx_related (related_type, related_id),
            INDEX idx_file_type (file_type),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    logMessage('✓ تم إنشاء جدول file_uploads', 'success');
    
    // جدول قوالب الإشعارات
    logMessage('إنشاء جدول قوالب الإشعارات...', 'info');
    $conn->exec("
        CREATE TABLE IF NOT EXISTS notification_templates (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) UNIQUE NOT NULL,
            title VARCHAR(255) NOT NULL,
            content TEXT NOT NULL,
            type ENUM('email', 'sms', 'push', 'system') NOT NULL,
            category VARCHAR(50) DEFAULT 'general',
            variables JSON DEFAULT NULL,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_type (type),
            INDEX idx_category (category),
            INDEX idx_active (is_active)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    logMessage('✓ تم إنشاء جدول notification_templates', 'success');
    
    // جدول سجل إرسال الإشعارات
    logMessage('إنشاء جدول سجل إرسال الإشعارات...', 'info');
    $conn->exec("
        CREATE TABLE IF NOT EXISTS notification_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            notification_id INT DEFAULT NULL,
            template_id INT DEFAULT NULL,
            user_id INT NOT NULL,
            type ENUM('email', 'sms', 'push', 'system') NOT NULL,
            status ENUM('pending', 'sent', 'failed', 'delivered', 'read') DEFAULT 'pending',
            recipient VARCHAR(255) NOT NULL,
            subject VARCHAR(255) DEFAULT NULL,
            content TEXT NOT NULL,
            error_message TEXT DEFAULT NULL,
            sent_at TIMESTAMP NULL,
            delivered_at TIMESTAMP NULL,
            read_at TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (notification_id) REFERENCES notifications(id) ON DELETE SET NULL,
            FOREIGN KEY (template_id) REFERENCES notification_templates(id) ON DELETE SET NULL,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            INDEX idx_user_id (user_id),
            INDEX idx_type (type),
            INDEX idx_status (status),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    logMessage('✓ تم إنشاء جدول notification_logs', 'success');
    
    // جدول جلسات المستخدمين
    logMessage('إنشاء جدول جلسات المستخدمين...', 'info');
    $conn->exec("
        CREATE TABLE IF NOT EXISTS user_sessions (
            id VARCHAR(128) PRIMARY KEY,
            user_id INT NOT NULL,
            ip_address VARCHAR(45) NOT NULL,
            user_agent TEXT,
            last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            expires_at TIMESTAMP NOT NULL,
            is_active BOOLEAN DEFAULT TRUE,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            INDEX idx_user_id (user_id),
            INDEX idx_last_activity (last_activity),
            INDEX idx_expires_at (expires_at),
            INDEX idx_active (is_active)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    logMessage('✓ تم إنشاء جدول user_sessions', 'success');
    
    // إدراج الصلاحيات الأساسية
    logMessage('إدراج الصلاحيات الأساسية...', 'info');
    $permissions = [
        ['users.view', 'عرض المستخدمين', 'عرض قائمة المستخدمين', 'users', 'users', 'view'],
        ['users.create', 'إنشاء مستخدمين', 'إنشاء مستخدمين جدد', 'users', 'users', 'create'],
        ['users.edit', 'تعديل المستخدمين', 'تعديل بيانات المستخدمين', 'users', 'users', 'edit'],
        ['users.delete', 'حذف المستخدمين', 'حذف المستخدمين', 'users', 'users', 'delete'],
        ['courses.view', 'عرض الكورسات', 'عرض قائمة الكورسات', 'courses', 'courses', 'view'],
        ['courses.create', 'إنشاء كورسات', 'إنشاء كورسات جديدة', 'courses', 'courses', 'create'],
        ['courses.edit', 'تعديل الكورسات', 'تعديل بيانات الكورسات', 'courses', 'courses', 'edit'],
        ['courses.delete', 'حذف الكورسات', 'حذف الكورسات', 'courses', 'courses', 'delete'],
        ['reports.view', 'عرض التقارير', 'عرض التقارير والإحصائيات', 'reports', 'reports', 'view'],
        ['reports.export', 'تصدير التقارير', 'تصدير التقارير', 'reports', 'reports', 'export'],
        ['system.settings', 'إعدادات النظام', 'تعديل إعدادات النظام', 'system', 'system', 'settings'],
        ['system.backup', 'النسخ الاحتياطي', 'إنشاء واستعادة النسخ الاحتياطية', 'system', 'system', 'backup']
    ];
    
    $stmt = $conn->prepare("
        INSERT IGNORE INTO permissions (name, display_name, description, category, resource, action) 
        VALUES (?, ?, ?, ?, ?, ?)
    ");
    
    foreach ($permissions as $permission) {
        $stmt->execute($permission);
    }
    logMessage('✓ تم إدراج الصلاحيات الأساسية', 'success');
    
    // إدراج صلاحيات المدير
    logMessage('إدراج صلاحيات المدير...', 'info');
    $conn->exec("
        INSERT IGNORE INTO role_permissions (role, permission_id, granted) 
        SELECT 'admin', id, TRUE FROM permissions
    ");
    logMessage('✓ تم إدراج صلاحيات المدير', 'success');
    
    // إدراج الإعدادات الأساسية
    logMessage('إدراج الإعدادات الأساسية...', 'info');
    $settings = [
        ['site_name', 'نظام إدارة التعلم الإلكتروني', 'string', 'اسم الموقع', 'general'],
        ['site_description', 'منصة تعليمية متقدمة للتعلم عن بعد', 'string', 'وصف الموقع', 'general'],
        ['admin_email', '<EMAIL>', 'string', 'بريد المدير الإلكتروني', 'general'],
        ['timezone', 'Asia/Riyadh', 'string', 'المنطقة الزمنية', 'general'],
        ['language', 'ar', 'string', 'اللغة الافتراضية', 'general'],
        ['currency', 'SAR', 'string', 'العملة الافتراضية', 'financial'],
        ['platform_commission', '30', 'number', 'عمولة المنصة بالنسبة المئوية', 'financial'],
        ['auto_approve_instructors', 'false', 'boolean', 'الموافقة التلقائية على المدربين', 'users'],
        ['auto_approve_courses', 'false', 'boolean', 'الموافقة التلقائية على الكورسات', 'courses'],
        ['max_file_size', '100', 'number', 'أقصى حجم ملف بالميجابايت', 'uploads'],
        ['maintenance_mode', 'false', 'boolean', 'وضع الصيانة', 'system'],
        ['registration_enabled', 'true', 'boolean', 'تفعيل التسجيل', 'users'],
        ['email_verification', 'true', 'boolean', 'تفعيل التحقق من البريد الإلكتروني', 'users']
    ];
    
    $stmt = $conn->prepare("
        INSERT IGNORE INTO system_settings (setting_key, setting_value, setting_type, description, category) 
        VALUES (?, ?, ?, ?, ?)
    ");
    
    foreach ($settings as $setting) {
        $stmt->execute($setting);
    }
    logMessage('✓ تم إدراج الإعدادات الأساسية', 'success');
    
    $conn->commit();
    
    logMessage('تم إنشاء جميع الجداول بنجاح!', 'success');
    
    // التحقق من الجداول
    logMessage('التحقق من الجداول المنشأة:', 'info');
    $tables = ['notifications', 'system_settings', 'backups', 'security_logs', 'permissions', 'role_permissions', 'system_stats', 'file_uploads', 'notification_templates', 'notification_logs', 'user_sessions'];
    
    foreach ($tables as $table) {
        try {
            $stmt = $conn->query("SELECT COUNT(*) FROM $table");
            $count = $stmt->fetchColumn();
            logMessage("✓ جدول $table: $count سجل", 'success');
        } catch (Exception $e) {
            logMessage("✗ جدول $table: غير موجود", 'error');
        }
    }
    
} catch (Exception $e) {
    $conn->rollBack();
    logMessage('خطأ: ' . $e->getMessage(), 'error');
}

echo "</div>
        <div class='mt-4 text-center'>
            <div class='alert alert-success'>
                <h5><i class='fas fa-check-circle me-2'></i>تم إنشاء الجداول بنجاح!</h5>
            </div>
            <a href='test-functions.php' class='btn btn-primary me-2'>
                <i class='fas fa-vial me-2'></i>اختبار الدوال
            </a>
            <a href='dashboard.php' class='btn btn-success'>
                <i class='fas fa-tachometer-alt me-2'></i>لوحة التحكم
            </a>
        </div>
    </div>
</div>
</body>
</html>";
?>
