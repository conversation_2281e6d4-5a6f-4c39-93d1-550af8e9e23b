<?php
require_once '../config/database.php';

// التحقق من الاتصال بقاعدة البيانات
if (!$conn) {
    die('خطأ في الاتصال بقاعدة البيانات');
}

echo "<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>إعداد جداول المدير</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css' rel='stylesheet'>
    <style>
        body { font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif; background: #f8f9fa; }
        .container { max-width: 800px; margin: 50px auto; }
        .card { border: none; box-shadow: 0 4px 20px rgba(0,0,0,0.1); }
        .progress { height: 25px; }
        .log-item { padding: 10px; margin: 5px 0; border-radius: 5px; }
        .log-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .log-error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .log-info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
<div class='container'>
    <div class='card'>
        <div class='card-header bg-primary text-white'>
            <h4 class='mb-0'><i class='fas fa-database me-2'></i>إعداد جداول المدير</h4>
        </div>
        <div class='card-body'>
            <div class='progress mb-4'>
                <div class='progress-bar progress-bar-striped progress-bar-animated' id='progressBar' style='width: 0%'></div>
            </div>
            <div id='logContainer'>";

$steps = [
    'إنشاء جدول الإشعارات',
    'إنشاء جدول إعدادات النظام',
    'إنشاء جدول النسخ الاحتياطية',
    'إنشاء جدول سجل الأمان',
    'إنشاء جدول الصلاحيات',
    'إنشاء جدول صلاحيات الأدوار',
    'إنشاء جدول إحصائيات النظام',
    'إنشاء جدول تتبع الملفات',
    'إنشاء جدول قوالب الإشعارات',
    'إنشاء جدول سجل إرسال الإشعارات',
    'إنشاء جدول جلسات المستخدمين',
    'إدراج الصلاحيات الأساسية',
    'إدراج صلاحيات المدير',
    'إدراج الإعدادات الأساسية'
];

$total_steps = count($steps);
$current_step = 0;

function logMessage($message, $type = 'info') {
    $icon = $type === 'success' ? 'check-circle' : ($type === 'error' ? 'exclamation-circle' : 'info-circle');
    echo "<div class='log-item log-$type'><i class='fas fa-$icon me-2'></i>$message</div>";
    echo "<script>document.getElementById('logContainer').scrollTop = document.getElementById('logContainer').scrollHeight;</script>";
    flush();
    ob_flush();
}

function updateProgress($current, $total) {
    $percentage = round(($current / $total) * 100);
    echo "<script>document.getElementById('progressBar').style.width = '$percentage%'; document.getElementById('progressBar').textContent = '$percentage%';</script>";
    flush();
    ob_flush();
}

try {
    // قراءة ملف SQL
    $sql_file = '../database/admin_tables.sql';
    if (!file_exists($sql_file)) {
        throw new Exception('ملف SQL غير موجود: ' . $sql_file);
    }
    
    $sql_content = file_get_contents($sql_file);
    if ($sql_content === false) {
        throw new Exception('فشل في قراءة ملف SQL');
    }
    
    logMessage('تم قراءة ملف SQL بنجاح', 'success');
    updateProgress(++$current_step, $total_steps);
    
    // تقسيم الاستعلامات
    $statements = explode(';', $sql_content);
    $statements = array_filter($statements, function($stmt) {
        $stmt = trim($stmt);
        return !empty($stmt) && !preg_match('/^--/', $stmt);
    });
    
    logMessage('تم تحليل ' . count($statements) . ' استعلام', 'info');
    
    // تنفيذ الاستعلامات
    $conn->beginTransaction();
    
    $executed_count = 0;
    foreach ($statements as $statement) {
        $statement = trim($statement);
        if (empty($statement)) continue;
        
        try {
            $conn->exec($statement);
            $executed_count++;
            
            // تحديد نوع الاستعلام للرسالة
            if (preg_match('/CREATE TABLE.*?(\w+)/i', $statement, $matches)) {
                logMessage("تم إنشاء جدول: {$matches[1]}", 'success');
            } elseif (preg_match('/INSERT INTO.*?(\w+)/i', $statement, $matches)) {
                logMessage("تم إدراج بيانات في جدول: {$matches[1]}", 'success');
            } else {
                logMessage("تم تنفيذ استعلام بنجاح", 'success');
            }
            
            updateProgress($current_step + ($executed_count / count($statements)), $total_steps);
            
        } catch (PDOException $e) {
            // تجاهل أخطاء الجداول الموجودة بالفعل
            if (strpos($e->getMessage(), 'already exists') !== false || 
                strpos($e->getMessage(), 'Duplicate entry') !== false) {
                logMessage("تم تخطي: " . substr($statement, 0, 50) . "... (موجود بالفعل)", 'info');
            } else {
                logMessage("خطأ في الاستعلام: " . $e->getMessage(), 'error');
                logMessage("الاستعلام: " . substr($statement, 0, 100) . "...", 'error');
            }
        }
        
        // توقف قصير لإظهار التقدم
        usleep(100000); // 0.1 ثانية
    }
    
    $conn->commit();
    updateProgress($total_steps, $total_steps);
    
    logMessage("تم تنفيذ $executed_count استعلام بنجاح", 'success');
    logMessage('تم إعداد جداول المدير بنجاح!', 'success');
    
    // التحقق من الجداول المنشأة
    $tables_check = [
        'notifications',
        'system_settings', 
        'backups',
        'security_logs',
        'permissions',
        'role_permissions',
        'system_stats',
        'file_uploads',
        'notification_templates',
        'notification_logs',
        'user_sessions'
    ];
    
    logMessage('التحقق من الجداول المنشأة:', 'info');
    foreach ($tables_check as $table) {
        try {
            $stmt = $conn->query("SELECT COUNT(*) FROM $table");
            $count = $stmt->fetchColumn();
            logMessage("✓ جدول $table: $count سجل", 'success');
        } catch (PDOException $e) {
            logMessage("✗ جدول $table: غير موجود", 'error');
        }
    }
    
    // إنشاء مستخدم مدير افتراضي إذا لم يكن موجوداً
    try {
        $stmt = $conn->query("SELECT COUNT(*) FROM users WHERE role = 'admin'");
        $admin_count = $stmt->fetchColumn();
        
        if ($admin_count == 0) {
            $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
            $stmt = $conn->prepare("
                INSERT INTO users (name, email, password, role, status, email_verified, created_at) 
                VALUES (?, ?, ?, 'admin', 'active', TRUE, NOW())
            ");
            $stmt->execute(['مدير النظام', '<EMAIL>', $admin_password]);
            
            logMessage('تم إنشاء حساب المدير الافتراضي', 'success');
            logMessage('البريد الإلكتروني: <EMAIL>', 'info');
            logMessage('كلمة المرور: admin123', 'info');
        } else {
            logMessage("يوجد $admin_count حساب مدير في النظام", 'info');
        }
    } catch (PDOException $e) {
        logMessage('خطأ في إنشاء حساب المدير: ' . $e->getMessage(), 'error');
    }
    
    echo "</div>
        <div class='mt-4 text-center'>
            <div class='alert alert-success'>
                <h5><i class='fas fa-check-circle me-2'></i>تم الإعداد بنجاح!</h5>
                <p class='mb-0'>يمكنك الآن الوصول إلى لوحة تحكم المدير</p>
            </div>
            <a href='dashboard.php' class='btn btn-primary btn-lg'>
                <i class='fas fa-tachometer-alt me-2'></i>
                الذهاب إلى لوحة التحكم
            </a>
        </div>";
    
} catch (Exception $e) {
    $conn->rollBack();
    logMessage('خطأ عام: ' . $e->getMessage(), 'error');
    
    echo "</div>
        <div class='mt-4 text-center'>
            <div class='alert alert-danger'>
                <h5><i class='fas fa-exclamation-circle me-2'></i>فشل في الإعداد</h5>
                <p class='mb-0'>حدث خطأ أثناء إعداد قاعدة البيانات</p>
            </div>
            <button onclick='location.reload()' class='btn btn-warning'>
                <i class='fas fa-redo me-2'></i>
                إعادة المحاولة
            </button>
        </div>";
}

echo "
        </div>
    </div>
</div>

<script>
// تحديث تلقائي للصفحة كل 30 ثانية إذا كانت العملية قيد التنفيذ
setTimeout(function() {
    const progressBar = document.getElementById('progressBar');
    const progress = parseInt(progressBar.style.width);
    if (progress < 100) {
        location.reload();
    }
}, 30000);

// تمرير تلقائي لسجل العمليات
setInterval(function() {
    const logContainer = document.getElementById('logContainer');
    logContainer.scrollTop = logContainer.scrollHeight;
}, 1000);
</script>

</body>
</html>";
?>
