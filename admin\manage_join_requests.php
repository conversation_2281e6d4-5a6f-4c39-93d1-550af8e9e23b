<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/activity_logger.php';

// التحقق من تسجيل الدخول كمدير
if (!isLoggedIn() || !isAdmin()) {
    header('Location: ../login.php');
    exit();
}

// إنشاء جدول join_requests إذا لم يكن موجوداً
try {
    $conn->exec("
        CREATE TABLE IF NOT EXISTS join_requests (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            email VARCHAR(255) NOT NULL,
            phone VARCHAR(20),
            course_interest TEXT,
            message TEXT,
            status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
            assigned_course_id INT DEFAULT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

            INDEX idx_status (status),
            INDEX idx_email (email),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
} catch (Exception $e) {
    // تجاهل الخطأ إذا كان الجدول موجود
}

$success = '';
$error = '';

// معالجة طلبات الموافقة/الرفض
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $requestId = (int)$_POST['request_id'];
    $action = $_POST['action'];
    $courseId = isset($_POST['course_id']) ? (int)$_POST['course_id'] : null;

    try {
        if ($action === 'approve' && $courseId) {
            // الموافقة على الطلب
            $stmt = $conn->prepare("
                UPDATE join_requests
                SET status = 'approved', assigned_course_id = ?, updated_at = NOW()
                WHERE id = ?
            ");
            $stmt->execute([$courseId, $requestId]);

            // الحصول على بيانات الطلب
            $stmt = $conn->prepare("SELECT * FROM join_requests WHERE id = ?");
            $stmt->execute([$requestId]);
            $request = $stmt->fetch();

            if ($request) {
                // إنشاء حساب للطالب
                $password = 'student123'; // كلمة مرور افتراضية
                $hashedPassword = password_hash($password, PASSWORD_DEFAULT);

                $stmt = $conn->prepare("
                    INSERT INTO users (name, email, password, role, phone, status)
                    VALUES (?, ?, ?, 'student', ?, 'active')
                    ON DUPLICATE KEY UPDATE
                    name = VALUES(name), phone = VALUES(phone), status = 'active'
                ");
                $stmt->execute([$request['name'], $request['email'], $hashedPassword, $request['phone']]);

                // تسجيل النشاط
                logUserActivity('join_request_approved', "تمت الموافقة على طلب انضمام: {$request['name']} - {$request['email']}");

                $success = "تمت الموافقة على الطلب وإنشاء حساب للطالب. كلمة المرور: student123";
            }

        } elseif ($action === 'reject') {
            // رفض الطلب
            $stmt = $conn->prepare("
                UPDATE join_requests
                SET status = 'rejected', updated_at = NOW()
                WHERE id = ?
            ");
            $stmt->execute([$requestId]);

            // الحصول على بيانات الطلب
            $stmt = $conn->prepare("SELECT * FROM join_requests WHERE id = ?");
            $stmt->execute([$requestId]);
            $request = $stmt->fetch();

            if ($request) {
                // تسجيل النشاط
                logUserActivity('join_request_rejected', "تم رفض طلب انضمام: {$request['name']} - {$request['email']}");
            }

            $success = "تم رفض الطلب";
        }

    } catch (Exception $e) {
        $error = "حدث خطأ: " . $e->getMessage();
    }
}

// الحصول على طلبات الانضمام
$filter = isset($_GET['filter']) ? $_GET['filter'] : 'all';
$query = "SELECT jr.* FROM join_requests jr";

if ($filter !== 'all') {
    $query .= " WHERE jr.status = :status";
}

$query .= " ORDER BY jr.created_at DESC";

try {
    $stmt = $conn->prepare($query);
    if ($filter !== 'all') {
        $stmt->bindParam(':status', $filter);
    }
    $stmt->execute();
    $requests = $stmt->fetchAll();
} catch (Exception $e) {
    $error = 'حدث خطأ في جلب طلبات الانضمام: ' . $e->getMessage();
    $requests = [];
}

// الحصول على الكورسات للاختيار
$stmt = $conn->query("SELECT id, title FROM courses WHERE status = 'active'");
$courses = $stmt->fetchAll();

// إحصائيات
$stats = [];
$stmt = $conn->query("SELECT COUNT(*) FROM join_requests WHERE status = 'pending'");
$stats['pending'] = $stmt->fetchColumn();

$stmt = $conn->query("SELECT COUNT(*) FROM join_requests WHERE status = 'approved'");
$stats['approved'] = $stmt->fetchColumn();

$stmt = $conn->query("SELECT COUNT(*) FROM join_requests WHERE status = 'rejected'");
$stats['rejected'] = $stmt->fetchColumn();
?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة طلبات الانضمام</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid mt-4">
        <h2 class="mb-4">📝 إدارة طلبات الانضمام</h2>

        <?php if ($success): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo $success; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?php echo $error; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <h6 class="card-title">طلبات معلقة</h6>
                        <h2><?php echo $stats['pending']; ?></h2>
                        <i class="fas fa-clock fa-2x position-absolute end-0 bottom-0 mb-3 me-3 opacity-50"></i>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <h6 class="card-title">طلبات مقبولة</h6>
                        <h2><?php echo $stats['approved']; ?></h2>
                        <i class="fas fa-check fa-2x position-absolute end-0 bottom-0 mb-3 me-3 opacity-50"></i>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-danger text-white">
                    <div class="card-body">
                        <h6 class="card-title">طلبات مرفوضة</h6>
                        <h2><?php echo $stats['rejected']; ?></h2>
                        <i class="fas fa-times fa-2x position-absolute end-0 bottom-0 mb-3 me-3 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filter Buttons -->
        <div class="mb-3">
            <a href="?filter=all" class="btn btn-outline-primary <?php echo $filter === 'all' ? 'active' : ''; ?>">الكل</a>
            <a href="?filter=pending" class="btn btn-outline-warning <?php echo $filter === 'pending' ? 'active' : ''; ?>">معلقة</a>
            <a href="?filter=approved" class="btn btn-outline-success <?php echo $filter === 'approved' ? 'active' : ''; ?>">مقبولة</a>
            <a href="?filter=rejected" class="btn btn-outline-danger <?php echo $filter === 'rejected' ? 'active' : ''; ?>">مرفوضة</a>
        </div>

        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">طلبات الانضمام</h5>
            </div>
            <div class="card-body">
                <?php if (empty($requests)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد طلبات انضمام</h5>
                        <p class="text-muted">سيتم عرض الطلبات الجديدة هنا عند وصولها</p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover requests-table" id="requestsTable">
                            <thead>
                                <tr>
                                    <th>الاسم</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>الهاتف</th>
                                    <th>الكورس المطلوب</th>
                                    <th>الرسالة</th>
                                    <th>الحالة</th>
                                    <th>تاريخ الطلب</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($requests as $request): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($request['name']); ?></td>
                                        <td><?php echo htmlspecialchars($request['email']); ?></td>
                                        <td><?php echo htmlspecialchars($request['phone'] ?? 'غير محدد'); ?></td>
                                        <td><?php echo htmlspecialchars($request['course_interest']); ?></td>
                                        <td>
                                            <small><?php echo htmlspecialchars(substr($request['message'], 0, 50)); ?>
                                            <?php if (strlen($request['message']) > 50) echo '...'; ?></small>
                                        </td>
                                        <td>
                                            <?php
                                            $statusClass = [
                                                'pending' => 'warning',
                                                'approved' => 'success',
                                                'rejected' => 'danger'
                                            ];
                                            $statusText = [
                                                'pending' => 'معلق',
                                                'approved' => 'مقبول',
                                                'rejected' => 'مرفوض'
                                            ];
                                            ?>
                                            <span class="badge bg-<?php echo $statusClass[$request['status']]; ?>">
                                                <?php echo $statusText[$request['status']]; ?>
                                            </span>
                                        </td>
                                        <td><?php echo date('Y-m-d H:i', strtotime($request['created_at'])); ?></td>
                                        <td>
                                            <?php if ($request['status'] === 'pending'): ?>
                                                <button class="btn btn-sm btn-success me-1"
                                                        onclick="approveRequest(<?php echo $request['id']; ?>, '<?php echo htmlspecialchars($request['name']); ?>')">
                                                    <i class="fas fa-check"></i> موافقة
                                                </button>
                                                <button class="btn btn-sm btn-danger"
                                                        onclick="rejectRequest(<?php echo $request['id']; ?>, '<?php echo htmlspecialchars($request['name']); ?>')">
                                                    <i class="fas fa-times"></i> رفض
                                                </button>
                                            <?php else: ?>
                                                <span class="text-muted">تم المعالجة</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <div class="mt-3">
            <a href="dashboard.php" class="btn btn-secondary">العودة للوحة التحكم</a>
            <a href="activity-logs.php" class="btn btn-primary">سجل الأنشطة</a>
        </div>
    </div>

    <!-- Approval Modal -->
    <div class="modal fade" id="approvalModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">الموافقة على طلب الانضمام</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="request_id" id="approve_request_id">
                        <input type="hidden" name="action" value="approve">

                        <p>هل أنت متأكد من الموافقة على طلب <strong id="approve_student_name"></strong>؟</p>

                        <div class="mb-3">
                            <label class="form-label">اختر الكورس:</label>
                            <select name="course_id" class="form-select" required>
                                <option value="">اختر كورس...</option>
                                <?php foreach ($courses as $course): ?>
                                    <option value="<?php echo $course['id']; ?>">
                                        <?php echo htmlspecialchars($course['title']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-success">موافقة</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Rejection Modal -->
    <div class="modal fade" id="rejectionModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">رفض طلب الانضمام</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="request_id" id="reject_request_id">
                        <input type="hidden" name="action" value="reject">

                        <p>هل أنت متأكد من رفض طلب <strong id="reject_student_name"></strong>؟</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-danger">رفض</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <script src="assets/js/datatables-fix.js"></script>
    <script>
        $(document).ready(function() {
            // استخدام الدالة المحسنة لتهيئة DataTable
            initDataTable('#requestsTable', {
                "order": [[6, "desc"]],
                "columnDefs": [
                    { "orderable": false, "targets": [7] }
                ]
            });
        });

        function approveRequest(id, name) {
            document.getElementById('approve_request_id').value = id;
            document.getElementById('approve_student_name').textContent = name;
            new bootstrap.Modal(document.getElementById('approvalModal')).show();
        }

        function rejectRequest(id, name) {
            document.getElementById('reject_request_id').value = id;
            document.getElementById('reject_student_name').textContent = name;
            new bootstrap.Modal(document.getElementById('rejectionModal')).show();
        }
    </script>
</body>
</html>
