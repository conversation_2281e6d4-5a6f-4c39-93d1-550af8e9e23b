
/**
 * إصلاح شامل لمشاكل DataTables
 * يحل جميع المشاكل الشائعة مع DataTables
 */

// متغير عام لتتبع الجداول المهيأة
window.initializedTables = window.initializedTables || {};

// دالة آمنة لتهيئة DataTable
function safeInitDataTable(selector, options = {}) {
    const tableId = typeof selector === 'string' ? selector : '#' + $(selector).attr('id');
    
    // التحقق من وجود العنصر
    if (!$(tableId).length) {
        console.warn('الجدول غير موجود: ' + tableId);
        return null;
    }
    
    // التحقق من التهيئة السابقة
    if (window.initializedTables[tableId]) {
        console.log('الجدول مهيأ مسبقاً: ' + tableId);
        return window.initializedTables[tableId];
    }
    
    try {
        // تدمير أي DataTable موجود
        if ($.fn.DataTable.isDataTable(tableId)) {
            $(tableId).DataTable().destroy();
            console.log('تم تدمير DataTable الموجود: ' + tableId);
        }
        
        // الإعدادات الافتراضية
        const defaultOptions = {
            language: {
                url: '//cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json',
                emptyTable: 'لا توجد بيانات متاحة في الجدول',
                info: 'عرض _START_ إلى _END_ من أصل _TOTAL_ مدخل',
                infoEmpty: 'عرض 0 إلى 0 من أصل 0 مدخل',
                infoFiltered: '(مرشح من _MAX_ إجمالي المدخلات)',
                lengthMenu: 'عرض _MENU_ مدخلات',
                loadingRecords: 'جاري التحميل...',
                processing: 'جاري المعالجة...',
                search: 'البحث:',
                zeroRecords: 'لم يتم العثور على نتائج مطابقة',
                paginate: {
                    first: 'الأول',
                    last: 'الأخير',
                    next: 'التالي',
                    previous: 'السابق'
                }
            },
            pageLength: 10,
            responsive: true,
            autoWidth: false,
            processing: false,
            serverSide: false,
            stateSave: false,
            order: [[0, 'desc']],
            columnDefs: []
        };
        
        // دمج الإعدادات
        const finalOptions = $.extend(true, {}, defaultOptions, options);
        
        // إنشاء DataTable
        const table = $(tableId).DataTable(finalOptions);
        
        // حفظ المرجع
        window.initializedTables[tableId] = table;
        
        console.log('تم إنشاء DataTable بنجاح: ' + tableId);
        return table;
        
    } catch (error) {
        console.error('خطأ في إنشاء DataTable:', error);
        return null;
    }
}

// تنظيف عند مغادرة الصفحة
$(window).on('beforeunload', function() {
    Object.keys(window.initializedTables).forEach(function(tableId) {
        try {
            if ($.fn.DataTable.isDataTable(tableId)) {
                $(tableId).DataTable().destroy();
            }
        } catch (e) {
            console.warn('خطأ في تدمير الجدول:', e);
        }
    });
    window.initializedTables = {};
});

// تهيئة تلقائية عند تحميل الصفحة
$(document).ready(function() {
    // تأخير قصير للتأكد من تحميل جميع العناصر
    setTimeout(function() {
        // تهيئة جدول طلبات الانضمام
        if ($('#requestsTable').length) {
            safeInitDataTable('#requestsTable', {
                order: [[6, 'desc']],
                columnDefs: [
                    { orderable: false, targets: [7] }
                ]
            });
        }
        
        // تهيئة جدول المستخدمين
        if ($('#usersTable').length) {
            safeInitDataTable('#usersTable', {
                order: [[4, 'desc']],
                columnDefs: [
                    { orderable: false, targets: [-1] }
                ]
            });
        }
        
        // تهيئة جدول الكورسات
        if ($('#coursesTable').length) {
            safeInitDataTable('#coursesTable', {
                order: [[5, 'desc']],
                columnDefs: [
                    { orderable: false, targets: [-1] }
                ]
            });
        }
        
        // تهيئة أي جداول أخرى
        $('.data-table').each(function() {
            const tableId = '#' + $(this).attr('id');
            if (!window.initializedTables[tableId]) {
                safeInitDataTable(tableId);
            }
        });
        
    }, 200);
});
