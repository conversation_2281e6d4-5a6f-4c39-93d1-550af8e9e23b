<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات المدير
if (!isLoggedIn() || !isAdmin()) {
    header('Location: ../login.php');
    exit;
}

$success_message = '';
$error_message = '';

// معالجة تحديث إعدادات العمولة
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $action = $_POST['action'];
    
    if ($action === 'update_commission_rate') {
        $commission_rate = (float)($_POST['commission_rate'] ?? 30);
        
        // التحقق من صحة النسبة
        if ($commission_rate < 15 || $commission_rate > 50) {
            $error_message = 'نسبة العمولة يجب أن تكون بين 15% و 50%';
        } else {
            try {
                // تحديث إعداد العمولة
                updateSystemSetting('commission_rate', $commission_rate, $_SESSION['user_id']);
                
                logUserActivity($_SESSION['user_id'], 'تحديث نسبة العمولة', "تم تحديث نسبة العمولة إلى $commission_rate%");
                $success_message = 'تم تحديث نسبة العمولة بنجاح';
            } catch (Exception $e) {
                $error_message = 'حدث خطأ أثناء تحديث نسبة العمولة: ' . $e->getMessage();
            }
        }
    }
}

// جلب نسبة العمولة الحالية
$commission_rate = getSystemSetting('commission_rate', 30);

// جلب إحصائيات العمولات
try {
    // إنشاء جدول العمولات إذا لم يكن موجود
    $conn->exec("CREATE TABLE IF NOT EXISTS commissions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        instructor_id INT NOT NULL,
        course_id INT NOT NULL,
        payment_id INT NOT NULL,
        course_price DECIMAL(10,2) NOT NULL,
        commission_rate DECIMAL(5,2) NOT NULL,
        commission_amount DECIMAL(10,2) NOT NULL,
        instructor_amount DECIMAL(10,2) NOT NULL,
        status ENUM('pending', 'paid') DEFAULT 'pending',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        paid_at TIMESTAMP NULL,
        FOREIGN KEY (instructor_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
        FOREIGN KEY (payment_id) REFERENCES payments(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    
    // جلب العمولات
    $stmt = $conn->prepare("
        SELECT c.*, u.name as instructor_name, u.email as instructor_email,
               co.title as course_title, p.amount as payment_amount,
               s.name as student_name
        FROM commissions c
        LEFT JOIN users u ON c.instructor_id = u.id
        LEFT JOIN courses co ON c.course_id = co.id
        LEFT JOIN payments p ON c.payment_id = p.id
        LEFT JOIN users s ON p.student_id = s.id
        ORDER BY c.created_at DESC
    ");
    $stmt->execute();
    $commissions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // إحصائيات العمولات
    $stats = [
        'total_commissions' => $conn->query("SELECT COALESCE(SUM(commission_amount), 0) FROM commissions")->fetchColumn(),
        'total_instructor_earnings' => $conn->query("SELECT COALESCE(SUM(instructor_amount), 0) FROM commissions")->fetchColumn(),
        'pending_commissions' => $conn->query("SELECT COALESCE(SUM(commission_amount), 0) FROM commissions WHERE status = 'pending'")->fetchColumn(),
        'paid_commissions' => $conn->query("SELECT COALESCE(SUM(commission_amount), 0) FROM commissions WHERE status = 'paid'")->fetchColumn()
    ];
    
} catch (Exception $e) {
    $error_message = 'حدث خطأ أثناء جلب بيانات العمولات: ' . $e->getMessage();
    $commissions = [];
    $stats = ['total_commissions' => 0, 'total_instructor_earnings' => 0, 'pending_commissions' => 0, 'paid_commissions' => 0];
}

$pageTitle = 'إدارة العمولات';
$pageSubtitle = 'مراقبة ومتابعة عمولات المنصة وأرباح المدربين';
require_once 'includes/admin-header.php';
?>

<div class="container-fluid py-4">
    <!-- رسائل النجاح والخطأ -->
    <?php if ($success_message): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>
    
    <?php if ($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- إعدادات العمولة -->
    <div class="admin-card mb-4">
        <div class="card-header">
            <h6 class="mb-0"><i class="fas fa-cog me-2"></i>إعدادات العمولة</h6>
        </div>
        <div class="card-body">
            <form method="POST" class="row g-3">
                <input type="hidden" name="action" value="update_commission_rate">
                <div class="col-md-6">
                    <label class="form-label">نسبة عمولة المنصة (%)</label>
                    <div class="input-group">
                        <input type="number" class="form-control" name="commission_rate" 
                               value="<?php echo $commission_rate; ?>" min="15" max="50" step="0.1" required>
                        <span class="input-group-text">%</span>
                    </div>
                    <small class="text-muted">النسبة يجب أن تكون بين 15% و 50%</small>
                </div>
                <div class="col-md-6 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i>حفظ التغييرات
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- الإحصائيات -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="admin-card">
                <div class="card-body text-center">
                    <div class="stat-icon bg-primary text-white mb-3">
                        <i class="fas fa-percentage"></i>
                    </div>
                    <h3 class="mb-1"><?php echo number_format($stats['total_commissions'], 2); ?> ر.س</h3>
                    <p class="text-muted mb-0">إجمالي العمولات</p>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="admin-card">
                <div class="card-body text-center">
                    <div class="stat-icon bg-success text-white mb-3">
                        <i class="fas fa-user-tie"></i>
                    </div>
                    <h3 class="mb-1"><?php echo number_format($stats['total_instructor_earnings'], 2); ?> ر.س</h3>
                    <p class="text-muted mb-0">أرباح المدربين</p>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="admin-card">
                <div class="card-body text-center">
                    <div class="stat-icon bg-warning text-white mb-3">
                        <i class="fas fa-clock"></i>
                    </div>
                    <h3 class="mb-1"><?php echo number_format($stats['pending_commissions'], 2); ?> ر.س</h3>
                    <p class="text-muted mb-0">عمولات معلقة</p>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="admin-card">
                <div class="card-body text-center">
                    <div class="stat-icon bg-info text-white mb-3">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <h3 class="mb-1"><?php echo number_format($stats['paid_commissions'], 2); ?> ر.س</h3>
                    <p class="text-muted mb-0">عمولات مدفوعة</p>
                </div>
            </div>
        </div>
    </div>

    <!-- قائمة العمولات -->
    <div class="admin-card">
        <div class="card-header">
            <h6 class="mb-0"><i class="fas fa-list me-2"></i>تفاصيل العمولات (<?php echo count($commissions); ?>)</h6>
        </div>
        <div class="card-body">
            <?php if (empty($commissions)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-percentage fa-4x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد عمولات</h5>
                    <p class="text-muted">لم يتم تسجيل أي عمولات حتى الآن</p>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover" id="commissionsTable">
                        <thead>
                            <tr>
                                <th>المدرب</th>
                                <th>الدورة</th>
                                <th>الطالب</th>
                                <th>سعر الدورة</th>
                                <th>نسبة العمولة</th>
                                <th>عمولة المنصة</th>
                                <th>ربح المدرب</th>
                                <th>الحالة</th>
                                <th>التاريخ</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($commissions as $commission): ?>
                                <tr>
                                    <td>
                                        <div>
                                            <strong><?php echo htmlspecialchars($commission['instructor_name'] ?? 'غير محدد'); ?></strong>
                                            <br>
                                            <small class="text-muted"><?php echo htmlspecialchars($commission['instructor_email'] ?? ''); ?></small>
                                        </div>
                                    </td>
                                    <td>
                                        <strong><?php echo htmlspecialchars($commission['course_title'] ?? 'غير محدد'); ?></strong>
                                    </td>
                                    <td>
                                        <?php echo htmlspecialchars($commission['student_name'] ?? 'غير محدد'); ?>
                                    </td>
                                    <td>
                                        <strong><?php echo number_format($commission['course_price'], 2); ?> ر.س</strong>
                                    </td>
                                    <td>
                                        <span class="badge bg-info"><?php echo $commission['commission_rate']; ?>%</span>
                                    </td>
                                    <td>
                                        <strong class="text-primary"><?php echo number_format($commission['commission_amount'], 2); ?> ر.س</strong>
                                    </td>
                                    <td>
                                        <strong class="text-success"><?php echo number_format($commission['instructor_amount'], 2); ?> ر.س</strong>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php echo $commission['status'] === 'paid' ? 'success' : 'warning'; ?>">
                                            <?php echo $commission['status'] === 'paid' ? 'مدفوعة' : 'معلقة'; ?>
                                        </span>
                                    </td>
                                    <td><?php echo date('Y-m-d H:i', strtotime($commission['created_at'])); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    font-size: 24px;
}
</style>

<script>
// تهيئة DataTable
$(document).ready(function() {
    $('#commissionsTable').DataTable({
        order: [[8, 'desc']],
        pageLength: 25,
        responsive: true,
        language: {
            url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
        }
    });
});
</script>

<?php require_once 'includes/admin-footer.php'; ?>
