-- إضا<PERSON>ة الأعمدة المطلوبة لجدول join_requests
ALTER TABLE join_requests 
ADD COLUMN processed_by INT NULL,
ADD COLUMN processed_at TIMESTAMP NULL,
ADD COLUMN rejection_reason TEXT NULL;

-- إضا<PERSON>ة مفتاح خارجي للمستخدم الذي عالج الطلب
ALTER TABLE join_requests 
ADD FOREIGN KEY (processed_by) REFERENCES users(id);

-- إنشاء جدول session_files إذا لم يكن موجوداً
CREATE TABLE IF NOT EXISTS session_files (
    id INT AUTO_INCREMENT PRIMARY KEY,
    session_id INT NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INT NOT NULL,
    file_type VARCHAR(100) NOT NULL,
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOR<PERSON>GN KEY (session_id) REFERENCES sessions(id) ON DELETE CASCADE
);
