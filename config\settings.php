<?php
/**
 * إعدادات الموقع المتقدمة
 * Advanced Site Settings
 */

// إعدادات الموقع الأساسية
define('SITE_NAME', 'منصة التعلم الإلكتروني');
define('SITE_DESCRIPTION', 'منصة التعلم الإلكتروني الرائدة - تعلم مهارات جديدة مع أفضل المدربين');
define('SITE_KEYWORDS', 'تعلم, كورسات, تدريب, مهارات, تعليم إلكتروني, منصة تعليمية');
define('SITE_AUTHOR', 'فريق منصة التعلم');
define('SITE_VERSION', '2.0.0');

// إعدادات قاعدة البيانات
define('DB_CHARSET', 'utf8mb4');
define('DB_COLLATE', 'utf8mb4_unicode_ci');

// إعدادات الأمان
define('SECURITY_SALT', 'your-unique-security-salt-here-change-this');
define('SESSION_LIFETIME', 3600 * 24 * 7); // أسبوع
define('PASSWORD_MIN_LENGTH', 8);
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_LOCKOUT_TIME', 900); // 15 دقيقة

// إعدادات الملفات
define('MAX_FILE_SIZE', 50 * 1024 * 1024); // 50 MB
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'webp']);
define('ALLOWED_DOCUMENT_TYPES', ['pdf', 'doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx']);
define('UPLOAD_PATH', 'uploads/');

// إعدادات البريد الإلكتروني
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-app-password');
define('SMTP_ENCRYPTION', 'tls');
define('FROM_EMAIL', '<EMAIL>');
define('FROM_NAME', 'منصة التعلم');

// إعدادات الدفع
define('PLATFORM_COMMISSION_RATE', 0.30); // 30%
define('MIN_COMMISSION_RATE', 0.15); // 15%
define('MAX_COMMISSION_RATE', 0.50); // 50%
define('DEFAULT_CURRENCY', 'SAR');
define('CURRENCY_SYMBOL', 'ر.س');

// إعدادات الكورسات
define('MAX_COURSES_PER_INSTRUCTOR', 50);
define('MAX_SESSIONS_PER_COURSE', 100);
define('MAX_STUDENTS_PER_COURSE', 1000);
define('DEFAULT_SESSION_DURATION', 60); // دقيقة

// إعدادات الإشعارات
define('ENABLE_EMAIL_NOTIFICATIONS', true);
define('ENABLE_SMS_NOTIFICATIONS', false);
define('ENABLE_PUSH_NOTIFICATIONS', true);

// إعدادات التخزين المؤقت
define('CACHE_ENABLED', true);
define('CACHE_LIFETIME', 3600); // ساعة
define('CACHE_PREFIX', 'lms_');

// إعدادات التحليلات
define('GOOGLE_ANALYTICS_ID', 'GA_MEASUREMENT_ID');
define('FACEBOOK_PIXEL_ID', 'FB_PIXEL_ID');
define('ENABLE_ANALYTICS', true);

// إعدادات وسائل التواصل الاجتماعي
define('FACEBOOK_URL', 'https://facebook.com/learningplatform');
define('TWITTER_URL', 'https://twitter.com/learningplatform');
define('INSTAGRAM_URL', 'https://instagram.com/learningplatform');
define('LINKEDIN_URL', 'https://linkedin.com/company/learningplatform');
define('YOUTUBE_URL', 'https://youtube.com/c/learningplatform');

// إعدادات API
define('API_VERSION', 'v1');
define('API_RATE_LIMIT', 1000); // طلب في الساعة
define('API_KEY_LENGTH', 32);

// إعدادات الصور
define('PROFILE_IMAGE_MAX_SIZE', 5 * 1024 * 1024); // 5 MB
define('COURSE_IMAGE_MAX_SIZE', 10 * 1024 * 1024); // 10 MB
define('PROFILE_IMAGE_DIMENSIONS', ['width' => 300, 'height' => 300]);
define('COURSE_IMAGE_DIMENSIONS', ['width' => 800, 'height' => 450]);

// إعدادات الفيديو
define('MAX_VIDEO_SIZE', 500 * 1024 * 1024); // 500 MB
define('ALLOWED_VIDEO_TYPES', ['mp4', 'avi', 'mov', 'wmv', 'flv']);
define('VIDEO_QUALITY_OPTIONS', ['720p', '1080p', '4K']);

// إعدادات الجلسات المباشرة
define('ZOOM_API_KEY', 'your-zoom-api-key');
define('ZOOM_API_SECRET', 'your-zoom-api-secret');
define('DEFAULT_MEETING_DURATION', 60); // دقيقة
define('MAX_MEETING_PARTICIPANTS', 100);

// إعدادات التقييمات
define('MIN_RATING', 1);
define('MAX_RATING', 5);
define('ENABLE_ANONYMOUS_REVIEWS', false);
define('REVIEW_MODERATION', true);

// إعدادات البحث
define('SEARCH_RESULTS_PER_PAGE', 12);
define('ENABLE_SEARCH_SUGGESTIONS', true);
define('SEARCH_MIN_CHARACTERS', 3);

// إعدادات الصفحات
define('COURSES_PER_PAGE', 12);
define('STUDENTS_PER_PAGE', 20);
define('INSTRUCTORS_PER_PAGE', 15);
define('REVIEWS_PER_PAGE', 10);

// إعدادات الأداء
define('ENABLE_GZIP_COMPRESSION', true);
define('ENABLE_BROWSER_CACHING', true);
define('CACHE_STATIC_FILES', true);
define('MINIFY_CSS', true);
define('MINIFY_JS', true);

// إعدادات الصيانة
define('MAINTENANCE_MODE', false);
define('MAINTENANCE_MESSAGE', 'الموقع تحت الصيانة. سنعود قريباً!');
define('MAINTENANCE_ALLOWED_IPS', ['127.0.0.1', '::1']);

// إعدادات اللغة والمنطقة الزمنية
define('DEFAULT_LANGUAGE', 'ar');
define('SUPPORTED_LANGUAGES', ['ar', 'en']);
define('DEFAULT_TIMEZONE', 'Asia/Riyadh');
define('DATE_FORMAT', 'Y-m-d');
define('TIME_FORMAT', 'H:i');
define('DATETIME_FORMAT', 'Y-m-d H:i:s');

// إعدادات التسجيل والمراقبة
define('ENABLE_ERROR_LOGGING', true);
define('ENABLE_ACCESS_LOGGING', true);
define('LOG_LEVEL', 'INFO'); // DEBUG, INFO, WARNING, ERROR
define('LOG_RETENTION_DAYS', 30);

// إعدادات النسخ الاحتياطي
define('ENABLE_AUTO_BACKUP', true);
define('BACKUP_FREQUENCY', 'daily'); // hourly, daily, weekly
define('BACKUP_RETENTION_DAYS', 30);
define('BACKUP_PATH', 'backups/');

// إعدادات الشهادات
define('ENABLE_CERTIFICATES', true);
define('CERTIFICATE_TEMPLATE_PATH', 'templates/certificate.html');
define('CERTIFICATE_SIGNATURE_PATH', 'assets/images/signature.png');

// إعدادات التكامل مع الخدمات الخارجية
define('ENABLE_GOOGLE_LOGIN', false);
define('ENABLE_FACEBOOK_LOGIN', false);
define('ENABLE_TWITTER_LOGIN', false);

define('GOOGLE_CLIENT_ID', 'your-google-client-id');
define('GOOGLE_CLIENT_SECRET', 'your-google-client-secret');

define('FACEBOOK_APP_ID', 'your-facebook-app-id');
define('FACEBOOK_APP_SECRET', 'your-facebook-app-secret');

// إعدادات CDN
define('ENABLE_CDN', false);
define('CDN_URL', 'https://cdn.yoursite.com');
define('CDN_ASSETS_PATH', 'assets/');

// إعدادات الأمان المتقدمة
define('ENABLE_2FA', false);
define('ENABLE_CAPTCHA', true);
define('CAPTCHA_SITE_KEY', 'your-recaptcha-site-key');
define('CAPTCHA_SECRET_KEY', 'your-recaptcha-secret-key');

// إعدادات الإحصائيات
define('ENABLE_VISITOR_TRACKING', true);
define('TRACK_USER_ACTIVITY', true);
define('ANONYMIZE_IP_ADDRESSES', true);

// إعدادات التخصيص
define('ENABLE_DARK_MODE', true);
define('ENABLE_RTL_SUPPORT', true);
define('ENABLE_CUSTOM_THEMES', false);

// إعدادات الموبايل
define('ENABLE_PWA', true);
define('PWA_NAME', 'منصة التعلم');
define('PWA_SHORT_NAME', 'تعلم');
define('PWA_THEME_COLOR', '#667eea');
define('PWA_BACKGROUND_COLOR', '#ffffff');

// إعدادات التحديثات
define('CHECK_UPDATES', true);
define('AUTO_UPDATE', false);
define('UPDATE_CHANNEL', 'stable'); // stable, beta, alpha

// إعدادات المطورين
define('DEBUG_MODE', false);
define('SHOW_ERRORS', false);
define('ENABLE_PROFILER', false);
define('ENABLE_QUERY_LOG', false);

/**
 * دالة للحصول على إعداد معين
 */
function getSetting($key, $default = null) {
    return defined($key) ? constant($key) : $default;
}

/**
 * دالة للتحقق من تفعيل ميزة معينة
 */
function isFeatureEnabled($feature) {
    $key = 'ENABLE_' . strtoupper($feature);
    return getSetting($key, false);
}

/**
 * دالة لتحديد المسار الكامل للملف
 */
function getFullPath($relativePath) {
    return rtrim($_SERVER['DOCUMENT_ROOT'], '/') . '/' . ltrim($relativePath, '/');
}

/**
 * دالة للحصول على URL الأساسي للموقع
 */
function getBaseUrl() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $path = dirname($_SERVER['SCRIPT_NAME']);
    return $protocol . '://' . $host . rtrim($path, '/') . '/';
}

/**
 * دالة لتحويل الحجم إلى تنسيق قابل للقراءة
 */
function formatFileSize($bytes) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);
    $bytes /= pow(1024, $pow);
    return round($bytes, 2) . ' ' . $units[$pow];
}

/**
 * دالة للتحقق من صحة نوع الملف
 */
function isValidFileType($filename, $allowedTypes) {
    $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
    return in_array($extension, $allowedTypes);
}

/**
 * دالة لإنشاء رمز عشوائي آمن
 */
function generateSecureToken($length = 32) {
    return bin2hex(random_bytes($length / 2));
}

/**
 * دالة للتحقق من وضع الصيانة
 */
function isMaintenanceMode() {
    if (!getSetting('MAINTENANCE_MODE', false)) {
        return false;
    }
    
    $allowedIPs = getSetting('MAINTENANCE_ALLOWED_IPS', []);
    $clientIP = $_SERVER['REMOTE_ADDR'] ?? '';
    
    return !in_array($clientIP, $allowedIPs);
}

/**
 * دالة لتسجيل الأخطاء
 */
function logError($message, $context = []) {
    if (!getSetting('ENABLE_ERROR_LOGGING', false)) {
        return;
    }
    
    $logEntry = [
        'timestamp' => date('Y-m-d H:i:s'),
        'message' => $message,
        'context' => $context,
        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
    ];
    
    $logFile = 'logs/error_' . date('Y-m-d') . '.log';
    file_put_contents($logFile, json_encode($logEntry) . PHP_EOL, FILE_APPEND | LOCK_EX);
}

/**
 * دالة لتسجيل النشاطات
 */
function logActivity($action, $userId = null, $details = []) {
    if (!getSetting('ENABLE_ACCESS_LOGGING', false)) {
        return;
    }
    
    $logEntry = [
        'timestamp' => date('Y-m-d H:i:s'),
        'action' => $action,
        'user_id' => $userId,
        'details' => $details,
        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
    ];
    
    $logFile = 'logs/activity_' . date('Y-m-d') . '.log';
    file_put_contents($logFile, json_encode($logEntry) . PHP_EOL, FILE_APPEND | LOCK_EX);
}
?>
