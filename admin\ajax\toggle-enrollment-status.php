<?php
require_once '../../includes/session_config.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';
require_once '../../includes/security.php';

// التحقق من صلاحيات المدير
if (!isLoggedIn() || !isAdmin()) {
    echo json_encode(['success' => false, 'message' => 'غير مصرح لك بهذا الإجراء']);
    exit;
}

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'طريقة طلب غير صحيحة']);
    exit;
}

$student_id = $_POST['student_id'] ?? 0;
$course_id = $_POST['course_id'] ?? 0;
$status = $_POST['status'] ?? '';

if (!$student_id || !$course_id) {
    echo json_encode(['success' => false, 'message' => 'معرف الطالب والكورس مطلوبان']);
    exit;
}

$allowed_statuses = ['active', 'inactive', 'completed'];
if (!in_array($status, $allowed_statuses)) {
    echo json_encode(['success' => false, 'message' => 'حالة غير صحيحة']);
    exit;
}

try {
    // جلب معلومات الطالب والكورس
    $stmt = $conn->prepare("
        SELECT u.name as student_name, c.title as course_title
        FROM course_enrollments e
        JOIN users u ON e.student_id = u.id
        JOIN courses c ON e.course_id = c.id
        WHERE e.student_id = ? AND e.course_id = ?
    ");
    $stmt->execute([$student_id, $course_id]);
    $enrollment = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$enrollment) {
        throw new Exception('التسجيل غير موجود');
    }
    
    // تحديث حالة التسجيل
    $stmt = $conn->prepare("
        UPDATE course_enrollments 
        SET status = ?
        WHERE student_id = ? AND course_id = ?
    ");
    $stmt->execute([$status, $student_id, $course_id]);
    
    // تسجيل النشاط
    $status_text = [
        'active' => 'نشط',
        'inactive' => 'غير نشط',
        'completed' => 'مكتمل'
    ];
    
    logUserActivity($_SESSION['user_id'], 'تحديث حالة تسجيل', "تم تحديث حالة تسجيل الطالب '{$enrollment['student_name']}' في الكورس '{$enrollment['course_title']}' إلى: " . $status_text[$status]);
    
    echo json_encode([
        'success' => true, 
        'message' => 'تم تحديث حالة التسجيل بنجاح'
    ]);
    
} catch (Exception $e) {
    error_log("Error updating enrollment status: " . $e->getMessage());
    echo json_encode([
        'success' => false, 
        'message' => 'حدث خطأ أثناء تحديث حالة التسجيل: ' . $e->getMessage()
    ]);
}
?>
