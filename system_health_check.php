<?php
/**
 * فحص شامل لسلامة النظام وقاعدة البيانات
 * Comprehensive system and database health check
 */

require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>فحص سلامة النظام</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>";
echo "<style>";
echo ".status-good { color: #28a745; }";
echo ".status-warning { color: #ffc107; }";
echo ".status-error { color: #dc3545; }";
echo ".test-item { margin-bottom: 10px; padding: 10px; border-left: 4px solid #ddd; }";
echo ".test-pass { border-left-color: #28a745; background-color: #f8fff9; }";
echo ".test-warning { border-left-color: #ffc107; background-color: #fffdf5; }";
echo ".test-fail { border-left-color: #dc3545; background-color: #fff5f5; }";
echo "</style>";
echo "</head>";
echo "<body>";
echo "<div class='container mt-4'>";
echo "<h1>🔍 فحص شامل لسلامة النظام</h1>";
echo "<p class='text-muted'>فحص قاعدة البيانات، الجداول، البيانات، والاتصالات</p>";

$total_tests = 0;
$passed_tests = 0;
$warnings = 0;
$errors = 0;

function runTest($name, $test_function, $description = '') {
    global $total_tests, $passed_tests, $warnings, $errors;
    $total_tests++;
    
    try {
        $result = $test_function();
        
        if ($result['status'] === 'pass') {
            $passed_tests++;
            $class = 'test-pass';
            $icon = '<i class="fas fa-check-circle status-good"></i>';
        } elseif ($result['status'] === 'warning') {
            $warnings++;
            $class = 'test-warning';
            $icon = '<i class="fas fa-exclamation-triangle status-warning"></i>';
        } else {
            $errors++;
            $class = 'test-fail';
            $icon = '<i class="fas fa-times-circle status-error"></i>';
        }
        
        echo "<div class='test-item $class'>";
        echo "<strong>$icon $name</strong>";
        if ($description) echo "<br><small class='text-muted'>$description</small>";
        echo "<br>" . $result['message'];
        if (isset($result['details'])) {
            echo "<br><small>" . $result['details'] . "</small>";
        }
        echo "</div>";
        
    } catch (Exception $e) {
        $errors++;
        echo "<div class='test-item test-fail'>";
        echo "<strong><i class='fas fa-times-circle status-error'></i> $name</strong>";
        echo "<br>خطأ: " . htmlspecialchars($e->getMessage());
        echo "</div>";
    }
}

// 1. فحص الاتصال بقاعدة البيانات
echo "<h3>🔌 فحص الاتصال بقاعدة البيانات</h3>";

runTest('اتصال قاعدة البيانات', function() use ($conn) {
    if ($conn) {
        $stmt = $conn->query("SELECT 1");
        return [
            'status' => 'pass',
            'message' => 'الاتصال بقاعدة البيانات يعمل بشكل صحيح',
            'details' => 'تم تنفيذ استعلام تجريبي بنجاح'
        ];
    } else {
        return [
            'status' => 'fail',
            'message' => 'فشل في الاتصال بقاعدة البيانات'
        ];
    }
});

runTest('معلومات قاعدة البيانات', function() use ($conn) {
    $stmt = $conn->query("SELECT DATABASE() as db_name, VERSION() as version");
    $info = $stmt->fetch(PDO::FETCH_ASSOC);
    return [
        'status' => 'pass',
        'message' => "قاعدة البيانات: {$info['db_name']} | إصدار MySQL: {$info['version']}"
    ];
});

// 2. فحص الجداول المطلوبة
echo "<h3>📋 فحص الجداول المطلوبة</h3>";

$required_tables = [
    'users' => 'جدول المستخدمين',
    'courses' => 'جدول الكورسات', 
    'sessions' => 'جدول الجلسات',
    'join_requests' => 'جدول طلبات الانضمام',
    'course_enrollments' => 'جدول تسجيل الطلاب',
    'student_grades' => 'جدول الدرجات',
    'session_attendance' => 'جدول الحضور',
    'activity_logs' => 'جدول سجل الأنشطة',
    'categories' => 'جدول التصنيفات'
];

foreach ($required_tables as $table => $description) {
    runTest("جدول $table", function() use ($conn, $table) {
        $stmt = $conn->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            // فحص عدد الصفوف
            $stmt = $conn->query("SELECT COUNT(*) as count FROM $table");
            $count = $stmt->fetchColumn();
            return [
                'status' => 'pass',
                'message' => "الجدول موجود ويحتوي على $count صف",
                'details' => "جدول $table متوفر وجاهز للاستخدام"
            ];
        } else {
            return [
                'status' => 'fail',
                'message' => "الجدول غير موجود"
            ];
        }
    }, $description);
}

// 3. فحص الأعمدة المهمة
echo "<h3>🏗️ فحص الأعمدة المهمة</h3>";

$critical_columns = [
    'courses' => ['id', 'title', 'instructor_id', 'image_path', 'category_id'],
    'sessions' => ['id', 'course_id', 'title', 'session_date', 'start_time', 'end_time'],
    'join_requests' => ['id', 'course_id', 'student_name', 'student_email', 'request_date'],
    'users' => ['id', 'name', 'email', 'password', 'role']
];

foreach ($critical_columns as $table => $columns) {
    runTest("أعمدة جدول $table", function() use ($conn, $table, $columns) {
        try {
            $stmt = $conn->query("DESCRIBE $table");
            $existing_columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            $missing_columns = array_diff($columns, $existing_columns);
            
            if (empty($missing_columns)) {
                return [
                    'status' => 'pass',
                    'message' => 'جميع الأعمدة المطلوبة موجودة',
                    'details' => 'الأعمدة: ' . implode(', ', $columns)
                ];
            } else {
                return [
                    'status' => 'warning',
                    'message' => 'بعض الأعمدة مفقودة: ' . implode(', ', $missing_columns)
                ];
            }
        } catch (Exception $e) {
            return [
                'status' => 'fail',
                'message' => 'لا يمكن فحص الأعمدة - الجدول غير موجود'
            ];
        }
    });
}

// 4. فحص البيانات الأساسية
echo "<h3>📊 فحص البيانات الأساسية</h3>";

runTest('المستخدمين', function() use ($conn) {
    try {
        $stmt = $conn->query("SELECT COUNT(*) as total, 
                             COUNT(CASE WHEN role = 'admin' THEN 1 END) as admins,
                             COUNT(CASE WHEN role = 'instructor' THEN 1 END) as instructors,
                             COUNT(CASE WHEN role = 'student' THEN 1 END) as students
                             FROM users");
        $data = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($data['total'] > 0) {
            return [
                'status' => 'pass',
                'message' => "إجمالي: {$data['total']} | مدراء: {$data['admins']} | مدربين: {$data['instructors']} | طلاب: {$data['students']}"
            ];
        } else {
            return [
                'status' => 'warning',
                'message' => 'لا يوجد مستخدمين في النظام'
            ];
        }
    } catch (Exception $e) {
        return ['status' => 'fail', 'message' => 'خطأ في فحص المستخدمين'];
    }
});

runTest('الكورسات', function() use ($conn) {
    try {
        $stmt = $conn->query("SELECT COUNT(*) as total,
                             COUNT(CASE WHEN status = 'active' THEN 1 END) as active
                             FROM courses");
        $data = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($data['total'] > 0) {
            return [
                'status' => 'pass',
                'message' => "إجمالي الكورسات: {$data['total']} | النشطة: {$data['active']}"
            ];
        } else {
            return [
                'status' => 'warning',
                'message' => 'لا توجد كورسات في النظام'
            ];
        }
    } catch (Exception $e) {
        return ['status' => 'fail', 'message' => 'خطأ في فحص الكورسات'];
    }
});

runTest('التصنيفات', function() use ($conn) {
    try {
        $stmt = $conn->query("SELECT COUNT(*) as count FROM categories");
        $count = $stmt->fetchColumn();
        
        if ($count >= 5) {
            return [
                'status' => 'pass',
                'message' => "يوجد $count تصنيف"
            ];
        } elseif ($count > 0) {
            return [
                'status' => 'warning',
                'message' => "يوجد $count تصنيف فقط (يُنصح بـ 5 على الأقل)"
            ];
        } else {
            return [
                'status' => 'fail',
                'message' => 'لا توجد تصنيفات'
            ];
        }
    } catch (Exception $e) {
        return ['status' => 'fail', 'message' => 'خطأ في فحص التصنيفات'];
    }
});

// 5. فحص الملفات والمجلدات
echo "<h3>📁 فحص الملفات والمجلدات</h3>";

$required_directories = [
    'uploads/courses' => 'مجلد صور الكورسات',
    'uploads/sessions' => 'مجلد ملفات الجلسات',
    'uploads/assignments' => 'مجلد الواجبات',
    'uploads/materials' => 'مجلد المواد التعليمية'
];

foreach ($required_directories as $dir => $description) {
    runTest("مجلد $dir", function() use ($dir) {
        if (file_exists($dir)) {
            if (is_writable($dir)) {
                $files = glob($dir . '/*');
                $file_count = count($files);
                return [
                    'status' => 'pass',
                    'message' => "المجلد موجود وقابل للكتابة | يحتوي على $file_count ملف"
                ];
            } else {
                return [
                    'status' => 'warning',
                    'message' => 'المجلد موجود لكنه غير قابل للكتابة'
                ];
            }
        } else {
            return [
                'status' => 'fail',
                'message' => 'المجلد غير موجود'
            ];
        }
    }, $description);
}

// 6. فحص الملفات المهمة
$important_files = [
    'config/database.php' => 'ملف إعدادات قاعدة البيانات',
    'includes/session_config.php' => 'ملف إعدادات الجلسة',
    'includes/functions.php' => 'ملف الدوال العامة',
    'includes/security.php' => 'ملف الأمان',
    'includes/activity_logger.php' => 'ملف تسجيل الأنشطة'
];

foreach ($important_files as $file => $description) {
    runTest("ملف $file", function() use ($file) {
        if (file_exists($file)) {
            if (is_readable($file)) {
                $size = filesize($file);
                return [
                    'status' => 'pass',
                    'message' => "الملف موجود وقابل للقراءة | الحجم: " . round($size/1024, 2) . " KB"
                ];
            } else {
                return [
                    'status' => 'warning',
                    'message' => 'الملف موجود لكنه غير قابل للقراءة'
                ];
            }
        } else {
            return [
                'status' => 'fail',
                'message' => 'الملف غير موجود'
            ];
        }
    }, $description);
}

// 7. فحص العلاقات بين الجداول
echo "<h3>🔗 فحص العلاقات بين الجداول</h3>";

runTest('علاقة الكورسات والمدربين', function() use ($conn) {
    try {
        $stmt = $conn->query("
            SELECT COUNT(*) as courses_with_instructors
            FROM courses c
            JOIN users u ON c.instructor_id = u.id
            WHERE u.role = 'instructor'
        ");
        $count = $stmt->fetchColumn();
        
        $stmt = $conn->query("SELECT COUNT(*) as total_courses FROM courses");
        $total = $stmt->fetchColumn();
        
        if ($count == $total && $total > 0) {
            return [
                'status' => 'pass',
                'message' => "جميع الكورسات ($total) مرتبطة بمدربين صحيحين"
            ];
        } elseif ($count > 0) {
            return [
                'status' => 'warning',
                'message' => "$count من $total كورس مرتبط بمدربين صحيحين"
            ];
        } else {
            return [
                'status' => 'warning',
                'message' => 'لا توجد كورسات مرتبطة بمدربين'
            ];
        }
    } catch (Exception $e) {
        return ['status' => 'fail', 'message' => 'خطأ في فحص العلاقة'];
    }
});

runTest('علاقة الجلسات والكورسات', function() use ($conn) {
    try {
        $stmt = $conn->query("
            SELECT COUNT(*) as sessions_with_courses
            FROM sessions s
            JOIN courses c ON s.course_id = c.id
        ");
        $count = $stmt->fetchColumn();
        
        $stmt = $conn->query("SELECT COUNT(*) as total_sessions FROM sessions");
        $total = $stmt->fetchColumn();
        
        if ($count == $total && $total > 0) {
            return [
                'status' => 'pass',
                'message' => "جميع الجلسات ($total) مرتبطة بكورسات صحيحة"
            ];
        } elseif ($count > 0) {
            return [
                'status' => 'warning',
                'message' => "$count من $total جلسة مرتبطة بكورسات صحيحة"
            ];
        } else {
            return [
                'status' => 'warning',
                'message' => 'لا توجد جلسات مرتبطة بكورسات'
            ];
        }
    } catch (Exception $e) {
        return ['status' => 'fail', 'message' => 'خطأ في فحص العلاقة'];
    }
});

// 8. فحص الأداء
echo "<h3>⚡ فحص الأداء</h3>";

runTest('سرعة الاستعلامات', function() use ($conn) {
    $start_time = microtime(true);
    
    // تنفيذ عدة استعلامات
    $conn->query("SELECT COUNT(*) FROM users");
    $conn->query("SELECT COUNT(*) FROM courses");
    $conn->query("SELECT COUNT(*) FROM sessions");
    
    $end_time = microtime(true);
    $execution_time = ($end_time - $start_time) * 1000; // بالميلي ثانية
    
    if ($execution_time < 100) {
        return [
            'status' => 'pass',
            'message' => "سرعة ممتازة: " . round($execution_time, 2) . " ميلي ثانية"
        ];
    } elseif ($execution_time < 500) {
        return [
            'status' => 'warning',
            'message' => "سرعة مقبولة: " . round($execution_time, 2) . " ميلي ثانية"
        ];
    } else {
        return [
            'status' => 'fail',
            'message' => "بطء في الاستعلامات: " . round($execution_time, 2) . " ميلي ثانية"
        ];
    }
});

// ملخص النتائج
echo "<h3>📊 ملخص النتائج</h3>";
echo "<div class='row'>";

echo "<div class='col-md-3'>";
echo "<div class='card bg-primary text-white'>";
echo "<div class='card-body text-center'>";
echo "<h3>$total_tests</h3>";
echo "<p class='mb-0'>إجمالي الاختبارات</p>";
echo "</div></div></div>";

echo "<div class='col-md-3'>";
echo "<div class='card bg-success text-white'>";
echo "<div class='card-body text-center'>";
echo "<h3>$passed_tests</h3>";
echo "<p class='mb-0'>اختبارات ناجحة</p>";
echo "</div></div></div>";

echo "<div class='col-md-3'>";
echo "<div class='card bg-warning text-white'>";
echo "<div class='card-body text-center'>";
echo "<h3>$warnings</h3>";
echo "<p class='mb-0'>تحذيرات</p>";
echo "</div></div></div>";

echo "<div class='col-md-3'>";
echo "<div class='card bg-danger text-white'>";
echo "<div class='card-body text-center'>";
echo "<h3>$errors</h3>";
echo "<p class='mb-0'>أخطاء</p>";
echo "</div></div></div>";

echo "</div>";

// تقييم عام للنظام
$success_rate = ($total_tests > 0) ? round(($passed_tests / $total_tests) * 100, 1) : 0;

echo "<div class='mt-4'>";
if ($success_rate >= 90) {
    echo "<div class='alert alert-success'>";
    echo "<h5><i class='fas fa-check-circle'></i> النظام يعمل بشكل ممتاز!</h5>";
    echo "<p>معدل النجاح: $success_rate% - النظام جاهز للاستخدام الكامل.</p>";
} elseif ($success_rate >= 70) {
    echo "<div class='alert alert-warning'>";
    echo "<h5><i class='fas fa-exclamation-triangle'></i> النظام يعمل بشكل جيد مع بعض التحذيرات</h5>";
    echo "<p>معدل النجاح: $success_rate% - يُنصح بمعالجة التحذيرات.</p>";
} else {
    echo "<div class='alert alert-danger'>";
    echo "<h5><i class='fas fa-times-circle'></i> النظام يحتاج إلى إصلاحات</h5>";
    echo "<p>معدل النجاح: $success_rate% - يجب معالجة الأخطاء قبل الاستخدام.</p>";
}
echo "</div>";

// روابط الإصلاح
echo "<div class='mt-4'>";
echo "<h5>🔧 أدوات الإصلاح:</h5>";
echo "<div class='btn-group-vertical d-grid gap-2 d-md-flex'>";
echo "<a href='fix_foreign_keys.php' class='btn btn-warning'>إصلاح قاعدة البيانات</a>";
echo "<a href='fix_all_tables.php' class='btn btn-info'>إصلاح الجداول</a>";
echo "<a href='quick_fix_join_requests.php' class='btn btn-secondary'>إصلاح طلبات الانضمام</a>";
echo "<a href='instructor/courses.php' class='btn btn-primary'>عرض الكورسات</a>";
echo "<a href='admin/dashboard.php' class='btn btn-success'>لوحة التحكم</a>";
echo "</div>";
echo "</div>";

echo "<div class='mt-4'>";
echo "<small class='text-muted'>تم إجراء الفحص في: " . date('Y-m-d H:i:s') . "</small>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
