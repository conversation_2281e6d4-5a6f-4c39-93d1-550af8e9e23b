<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات المدرب
if (!isLoggedIn() || !isInstructor()) {
    header('Location: ../login.php');
    exit;
}

$pageTitle = 'إدارة الكورسات';
include 'includes/header.php';


// جلب قائمة الكورسات الخاصة بالمدرب
try {
    $stmt = $conn->prepare("
        SELECT c.*, 
        (SELECT COUNT(DISTINCT ce.student_id) 
         FROM course_enrollments ce 
         WHERE ce.course_id = c.id) as enrolled_students,
        (SELECT COUNT(DISTINCT s.id) 
         FROM sessions s 
         WHERE s.course_id = c.id) as total_sessions
        FROM courses c
        WHERE c.instructor_id = ?
        ORDER BY c.created_at DESC
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $courses = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء جلب قائمة الكورسات';
    $courses = [];
}
?>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">قائمة الكورسات</h5>
                    <a href="add-course.php" class="btn btn-primary">
                        <i class="fas fa-plus"></i> إضافة كورس جديد
                    </a>
                </div>
                <div class="card-body">
                    <?php if (isset($error)): ?>
                        <div class="alert alert-danger"><?php echo $error; ?></div>
                    <?php endif; ?>

                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>عنوان الكورس</th>
                                    <th>الوصف</th>
                                    <th>الطلاب المسجلين</th>
                                    <th>عدد الجلسات</th>
                                    <th>الحالة</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($courses as $course): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($course['title']); ?></td>
                                        <td><?php echo htmlspecialchars(substr($course['description'], 0, 100)) . '...'; ?></td>
                                        <td><?php echo $course['enrolled_students']; ?></td>
                                        <td><?php echo $course['total_sessions']; ?></td>
                                        <td>
                                            <?php if ($course['status'] === 'active'): ?>
                                                <span class="badge bg-success">نشط</span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary">غير نشط</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo date('Y-m-d', strtotime($course['created_at'])); ?></td>
                                        <td>
                                            <a href="course-details.php?id=<?php echo $course['id']; ?>" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="edit-course.php?id=<?php echo $course['id']; ?>" class="btn btn-sm btn-warning">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button class="btn btn-sm btn-danger" onclick="toggleCourseStatus(<?php echo $course['id']; ?>, '<?php echo $course['status']; ?>')">
                                                <i class="fas <?php echo $course['status'] === 'active' ? 'fa-ban' : 'fa-check'; ?>"></i>
                                            </button>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة DataTables
    const table = document.querySelector('table');
    if (table) {
        new DataTable(table, {
            order: [[5, 'desc']],
            pageLength: 25,
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/ar.json'
            }
        });
    }
});

function toggleCourseStatus(courseId, currentStatus) {
    const newStatus = currentStatus === 'active' ? 'inactive' : 'active';
    const confirmMessage = currentStatus === 'active' 
        ? 'هل أنت متأكد من تعطيل هذا الكورس؟'
        : 'هل أنت متأكد من تفعيل هذا الكورس؟';

    if (confirm(confirmMessage)) {
        fetch('../ajax/toggle_course_status.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                course_id: courseId,
                status: newStatus
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.location.reload();
            } else {
                alert('حدث خطأ أثناء تحديث حالة الكورس');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء تحديث حالة الكورس');
        });
    }
}
</script>

<?php require_once '../includes/footer.php'; ?>