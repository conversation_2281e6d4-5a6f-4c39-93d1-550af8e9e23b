<?php
/**
 * معاينة الاختبار للمدرب
 * Quiz Preview for Instructor
 */

require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات المدرب
if (!isLoggedIn() || !isInstructor()) {
    header('Location: ../login.php');
    exit;
}

$quiz_id = $_GET['id'] ?? 0;

// التحقق من وجود الاختبار وأنه ينتمي للمدرب
try {
    $stmt = $conn->prepare("
        SELECT q.*, c.title as course_title
        FROM quizzes q
        INNER JOIN courses c ON q.course_id = c.id
        WHERE q.id = ? AND c.instructor_id = ?
    ");
    $stmt->execute([$quiz_id, $_SESSION['user_id']]);
    $quiz = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$quiz) {
        header('Location: quizzes.php');
        exit;
    }
} catch (PDOException $e) {
    header('Location: quizzes.php');
    exit;
}

// جلب أسئلة الاختبار
try {
    $stmt = $conn->prepare("
        SELECT qq.*, qqo.id as option_id, qqo.option_text, qqo.is_correct
        FROM quiz_questions qq
        LEFT JOIN quiz_question_options qqo ON qq.id = qqo.question_id
        WHERE qq.quiz_id = ?
        ORDER BY qq.question_order ASC, qq.id ASC, qqo.option_order ASC
    ");
    $stmt->execute([$quiz_id]);
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // تنظيم البيانات
    $questions = [];
    foreach ($results as $row) {
        $question_id = $row['id'];
        
        if (!isset($questions[$question_id])) {
            $questions[$question_id] = [
                'id' => $row['id'],
                'question_text' => $row['question_text'],
                'question_type' => $row['question_type'],
                'points' => $row['points'],
                'explanation' => $row['explanation'],
                'options' => []
            ];
        }
        
        if ($row['option_id']) {
            $questions[$question_id]['options'][] = [
                'id' => $row['option_id'],
                'text' => $row['option_text'],
                'is_correct' => $row['is_correct']
            ];
        }
    }
    
} catch (PDOException $e) {
    $questions = [];
}

$pageTitle = 'معاينة الاختبار - ' . $quiz['title'];
?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($pageTitle); ?></title>
    
    <!-- الخطوط العربية -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Bootstrap و Font Awesome -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }
        
        .quiz-container {
            max-width: 900px;
            margin: 2rem auto;
            padding: 0 1rem;
        }
        
        .quiz-header {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            padding: 2rem;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .question-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            overflow: hidden;
            transition: transform 0.3s ease;
        }
        
        .question-card:hover {
            transform: translateY(-2px);
        }
        
        .question-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 1.5rem;
        }
        
        .question-body {
            padding: 1.5rem;
        }
        
        .option-item {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 0.75rem;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .option-item:hover {
            border-color: #007bff;
            background: #e3f2fd;
        }
        
        .option-item.correct {
            background: #d4edda;
            border-color: #28a745;
        }
        
        .explanation-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 1rem;
            margin-top: 1rem;
        }
        
        .quiz-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .info-card {
            background: white;
            border-radius: 10px;
            padding: 1rem;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .preview-badge {
            position: fixed;
            top: 20px;
            left: 20px;
            background: #dc3545;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-weight: 600;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        }
        
        .action-buttons {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
        }
        
        @media (max-width: 768px) {
            .quiz-container {
                margin: 1rem auto;
                padding: 0 0.5rem;
            }
            
            .quiz-header {
                padding: 1.5rem;
            }
            
            .question-body {
                padding: 1rem;
            }
            
            .preview-badge {
                position: relative;
                top: auto;
                left: auto;
                margin-bottom: 1rem;
                display: inline-block;
            }
            
            .action-buttons {
                position: relative;
                bottom: auto;
                right: auto;
                text-align: center;
                margin-top: 2rem;
            }
        }
    </style>
</head>
<body>
    <!-- شارة المعاينة -->
    <div class="preview-badge">
        <i class="fas fa-eye me-1"></i>
        وضع المعاينة
    </div>
    
    <div class="quiz-container">
        <!-- رأس الاختبار -->
        <div class="quiz-header">
            <h1 class="mb-3"><?php echo htmlspecialchars($quiz['title']); ?></h1>
            <p class="text-muted mb-3"><?php echo htmlspecialchars($quiz['course_title']); ?></p>
            
            <?php if ($quiz['description']): ?>
                <div class="alert alert-info">
                    <?php echo nl2br(htmlspecialchars($quiz['description'])); ?>
                </div>
            <?php endif; ?>
            
            <!-- معلومات الاختبار -->
            <div class="quiz-info">
                <div class="info-card">
                    <h5 class="text-primary"><?php echo count($questions); ?></h5>
                    <small class="text-muted">عدد الأسئلة</small>
                </div>
                <div class="info-card">
                    <h5 class="text-success"><?php echo array_sum(array_column($questions, 'points')); ?></h5>
                    <small class="text-muted">إجمالي النقاط</small>
                </div>
                <div class="info-card">
                    <h5 class="text-warning">
                        <?php echo $quiz['time_limit'] ? $quiz['time_limit'] . ' دقيقة' : 'غير محدود'; ?>
                    </h5>
                    <small class="text-muted">المدة الزمنية</small>
                </div>
                <div class="info-card">
                    <h5 class="text-info"><?php echo $quiz['passing_grade']; ?>%</h5>
                    <small class="text-muted">درجة النجاح</small>
                </div>
            </div>
        </div>
        
        <!-- الأسئلة -->
        <?php if (empty($questions)): ?>
            <div class="question-card">
                <div class="question-body text-center">
                    <i class="fas fa-question-circle text-muted" style="font-size: 3rem;"></i>
                    <h5 class="mt-3 text-muted">لا توجد أسئلة</h5>
                    <p class="text-muted">لم يتم إضافة أسئلة لهذا الاختبار بعد</p>
                    <a href="quiz-questions.php?quiz_id=<?php echo $quiz_id; ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>إضافة أسئلة
                    </a>
                </div>
            </div>
        <?php else: ?>
            <?php foreach ($questions as $index => $question): ?>
                <div class="question-card">
                    <div class="question-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">السؤال <?php echo $index + 1; ?></h6>
                            <div>
                                <span class="badge bg-light text-dark me-2">
                                    <?php echo $question['points']; ?> نقطة
                                </span>
                                <span class="badge bg-light text-dark">
                                    <?php 
                                    $types = [
                                        'multiple_choice' => 'اختيار متعدد',
                                        'true_false' => 'صح/خطأ',
                                        'short_answer' => 'إجابة قصيرة',
                                        'essay' => 'مقال'
                                    ];
                                    echo $types[$question['question_type']] ?? $question['question_type'];
                                    ?>
                                </span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="question-body">
                        <h6 class="mb-4"><?php echo nl2br(htmlspecialchars($question['question_text'])); ?></h6>
                        
                        <?php if ($question['question_type'] === 'multiple_choice' && !empty($question['options'])): ?>
                            <div class="options-list">
                                <?php foreach ($question['options'] as $option_index => $option): ?>
                                    <div class="option-item <?php echo $option['is_correct'] ? 'correct' : ''; ?>">
                                        <div class="d-flex align-items-center">
                                            <div class="form-check me-3">
                                                <input class="form-check-input" type="radio" 
                                                       name="question_<?php echo $question['id']; ?>" 
                                                       id="option_<?php echo $option['id']; ?>"
                                                       <?php echo $option['is_correct'] ? 'checked' : ''; ?> disabled>
                                            </div>
                                            <label class="form-check-label flex-grow-1" 
                                                   for="option_<?php echo $option['id']; ?>">
                                                <?php echo htmlspecialchars($option['text']); ?>
                                                <?php if ($option['is_correct']): ?>
                                                    <i class="fas fa-check text-success ms-2"></i>
                                                <?php endif; ?>
                                            </label>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php elseif ($question['question_type'] === 'true_false'): ?>
                            <div class="options-list">
                                <div class="option-item">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" 
                                               name="question_<?php echo $question['id']; ?>" disabled>
                                        <label class="form-check-label">صحيح</label>
                                    </div>
                                </div>
                                <div class="option-item">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" 
                                               name="question_<?php echo $question['id']; ?>" disabled>
                                        <label class="form-check-label">خطأ</label>
                                    </div>
                                </div>
                            </div>
                        <?php elseif ($question['question_type'] === 'short_answer'): ?>
                            <div class="mb-3">
                                <input type="text" class="form-control" placeholder="اكتب إجابتك هنا..." disabled>
                            </div>
                        <?php elseif ($question['question_type'] === 'essay'): ?>
                            <div class="mb-3">
                                <textarea class="form-control" rows="5" placeholder="اكتب إجابتك المفصلة هنا..." disabled></textarea>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($question['explanation']): ?>
                            <div class="explanation-box">
                                <h6 class="text-warning mb-2">
                                    <i class="fas fa-lightbulb me-1"></i>
                                    التفسير:
                                </h6>
                                <p class="mb-0"><?php echo nl2br(htmlspecialchars($question['explanation'])); ?></p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>
    
    <!-- أزرار الإجراءات -->
    <div class="action-buttons">
        <div class="btn-group-vertical" role="group">
            <a href="quiz-questions.php?quiz_id=<?php echo $quiz_id; ?>" class="btn btn-primary">
                <i class="fas fa-edit me-1"></i>
                تعديل الأسئلة
            </a>
            <a href="quizzes.php" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-1"></i>
                العودة للاختبارات
            </a>
            <button onclick="window.print()" class="btn btn-info">
                <i class="fas fa-print me-1"></i>
                طباعة
            </button>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // إضافة تأثيرات تفاعلية
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير النقر على الخيارات
            const options = document.querySelectorAll('.option-item');
            options.forEach(option => {
                option.addEventListener('click', function() {
                    const radio = this.querySelector('input[type="radio"]');
                    if (radio && !radio.disabled) {
                        radio.checked = true;
                    }
                });
            });
            
            // تأثير التمرير السلس
            const questionCards = document.querySelectorAll('.question-card');
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            });
            
            questionCards.forEach(card => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                observer.observe(card);
            });
        });
        
        // طباعة مخصصة
        window.addEventListener('beforeprint', function() {
            document.querySelector('.preview-badge').style.display = 'none';
            document.querySelector('.action-buttons').style.display = 'none';
        });
        
        window.addEventListener('afterprint', function() {
            document.querySelector('.preview-badge').style.display = 'block';
            document.querySelector('.action-buttons').style.display = 'block';
        });
    </script>
</body>
</html>
