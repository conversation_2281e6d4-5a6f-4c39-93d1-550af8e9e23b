<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات المدرب
if (!isLoggedIn() || !isInstructor()) {
    header('Location: ../login.php');
    exit;
}

$student_id = $_GET['id'] ?? 0;
$course_id = $_GET['course_id'] ?? 0;

if (!$student_id || !$course_id) {
    header('Location: students.php');
    exit;
}

// جلب بيانات الطالب والكورس
try {
    $stmt = $conn->prepare("
        SELECT
            u.*,
            c.title as course_title,
            c.instructor_id,
            ce.enrolled_at,
            ce.status as enrollment_status,
            COALESCE(ce.progress_percentage, 0) as progress_percentage,
            ce.final_grade,
            ce.completion_date,
            ce.notes
        FROM users u
        INNER JOIN course_enrollments ce ON u.id = ce.student_id
        INNER JOIN courses c ON ce.course_id = c.id
        WHERE u.id = ? AND c.id = ? AND c.instructor_id = ?
    ");
    $stmt->execute([$student_id, $course_id, $_SESSION['user_id']]);
    $student = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$student) {
        echo "<script>alert('الطالب غير موجود أو ليس لديك صلاحية للوصول'); window.close();</script>";
        exit;
    }

    // جلب الدرجات
    $stmt = $conn->prepare("
        SELECT sg.*,
               COALESCE(sg.graded_at, sg.created_at) as graded_at,
               cs.title as session_title
        FROM student_grades sg
        LEFT JOIN course_sessions cs ON sg.session_id = cs.id
        WHERE sg.student_id = ? AND sg.course_id = ?
        ORDER BY COALESCE(sg.graded_at, sg.created_at) DESC
    ");
    $stmt->execute([$student_id, $course_id]);
    $grades = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // جلب سجل الحضور
    $stmt = $conn->prepare("
        SELECT
            cs.title as session_title,
            cs.session_date,
            sa.join_time,
            sa.leave_time,
            COALESCE(sa.status, 'absent') as attendance_status
        FROM course_sessions cs
        LEFT JOIN session_attendance sa ON cs.id = sa.session_id AND sa.student_id = ?
        WHERE cs.course_id = ?
        ORDER BY cs.session_date DESC
    ");
    $stmt->execute([$student_id, $course_id]);
    $attendance = $stmt->fetchAll(PDO::FETCH_ASSOC);

} catch (PDOException $e) {
    echo "<script>alert('حدث خطأ في جلب البيانات'); window.close();</script>";
    exit;
}

?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تفاصيل الطالب - <?php echo htmlspecialchars($student['name']); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .student-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
        }
        .student-avatar {
            width: 100px;
            height: 100px;
            background: rgba(255,255,255,0.2);
            border: 3px solid rgba(255,255,255,0.3);
        }
        .info-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        .info-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0,0,0,0.2);
        }
        .grade-badge {
            font-size: 1.1rem;
            padding: 0.5rem 1rem;
        }
        .attendance-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
        }
        .present { background-color: #28a745; }
        .absent { background-color: #dc3545; }
        .late { background-color: #ffc107; }
    </style>
</head>
<body>
    <!-- رأس الطالب -->
    <div class="student-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-auto">
                    <div class="student-avatar rounded-circle d-flex align-items-center justify-content-center">
                        <i class="fas fa-user fa-3x"></i>
                    </div>
                </div>
                <div class="col">
                    <h2 class="mb-1"><?php echo htmlspecialchars($student['name']); ?></h2>
                    <p class="mb-1 opacity-75"><?php echo htmlspecialchars($student['email']); ?></p>
                    <p class="mb-0 opacity-75">
                        <i class="fas fa-book me-2"></i><?php echo htmlspecialchars($student['course_title']); ?>
                    </p>
                </div>
                <div class="col-auto">
                    <button class="btn btn-light" onclick="window.close()">
                        <i class="fas fa-times me-1"></i>إغلاق
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="container my-4">
        <!-- معلومات أساسية -->
        <div class="row g-4 mb-4">
            <div class="col-md-3">
                <div class="info-card card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-calendar-alt text-primary fs-2 mb-3"></i>
                        <h6 class="text-muted">تاريخ التسجيل</h6>
                        <p class="fw-bold"><?php echo date('d/m/Y', strtotime($student['enrolled_at'])); ?></p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="info-card card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-chart-line text-success fs-2 mb-3"></i>
                        <h6 class="text-muted">نسبة التقدم</h6>
                        <p class="fw-bold"><?php echo number_format($student['progress_percentage'], 1); ?>%</p>
                        <div class="progress">
                            <div class="progress-bar bg-success" style="width: <?php echo $student['progress_percentage']; ?>%"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="info-card card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-star text-warning fs-2 mb-3"></i>
                        <h6 class="text-muted">المعدل العام</h6>
                        <?php if (!empty($grades)): ?>
                            <?php $avg_grade = array_sum(array_column($grades, 'grade')) / count($grades); ?>
                            <span class="grade-badge badge bg-<?php echo $avg_grade >= 85 ? 'success' : ($avg_grade >= 70 ? 'warning' : 'danger'); ?>">
                                <?php echo number_format($avg_grade, 1); ?>
                            </span>
                        <?php else: ?>
                            <span class="badge bg-secondary">لا توجد درجات</span>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="info-card card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-user-check text-info fs-2 mb-3"></i>
                        <h6 class="text-muted">حالة التسجيل</h6>
                        <?php
                        $status_classes = [
                            'active' => 'success',
                            'completed' => 'primary',
                            'dropped' => 'danger',
                            'suspended' => 'warning'
                        ];
                        $status_text = [
                            'active' => 'نشط',
                            'completed' => 'مكتمل',
                            'dropped' => 'منسحب',
                            'suspended' => 'معلق'
                        ];
                        ?>
                        <span class="badge bg-<?php echo $status_classes[$student['enrollment_status']] ?? 'secondary'; ?> grade-badge">
                            <?php echo $status_text[$student['enrollment_status']] ?? $student['enrollment_status']; ?>
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- تبويبات التفاصيل -->
        <div class="card info-card">
            <div class="card-header">
                <ul class="nav nav-tabs card-header-tabs" role="tablist">
                    <li class="nav-item">
                        <a class="nav-link active" data-bs-toggle="tab" href="#grades">
                            <i class="fas fa-star me-1"></i>الدرجات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#attendance">
                            <i class="fas fa-calendar-check me-1"></i>الحضور
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#info">
                            <i class="fas fa-info-circle me-1"></i>معلومات إضافية
                        </a>
                    </li>
                </ul>
            </div>
            <div class="card-body">
                <div class="tab-content">
                    <!-- تبويب الدرجات -->
                    <div class="tab-pane fade show active" id="grades">
                        <?php if (!empty($grades)): ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>التقييم</th>
                                            <th>الدرجة</th>
                                            <th>النهاية العظمى</th>
                                            <th>النسبة المئوية</th>
                                            <th>تاريخ التقييم</th>
                                            <th>ملاحظات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($grades as $grade): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($grade['assignment_name'] ?? $grade['session_title'] ?? 'تقييم عام'); ?></td>
                                            <td>
                                                <span class="fw-bold text-primary"><?php echo $grade['grade']; ?></span>
                                            </td>
                                            <td><?php echo $grade['max_grade'] ?? 100; ?></td>
                                            <td>
                                                <?php
                                                $max_grade = $grade['max_grade'] ?? 100;
                                                $percentage = ($grade['grade'] / $max_grade) * 100;
                                                $color = $percentage >= 85 ? 'success' : ($percentage >= 70 ? 'warning' : 'danger');
                                                ?>
                                                <span class="badge bg-<?php echo $color; ?>">
                                                    <?php echo number_format($percentage, 1); ?>%
                                                </span>
                                            </td>
                                            <td><?php echo date('d/m/Y', strtotime($grade['graded_at'])); ?></td>
                                            <td><?php echo htmlspecialchars($grade['notes'] ?? '-'); ?></td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-4">
                                <i class="fas fa-star text-muted" style="font-size: 3rem;"></i>
                                <h5 class="mt-3 text-muted">لا توجد درجات</h5>
                                <p class="text-muted">لم يتم تقييم هذا الطالب بعد</p>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- تبويب الحضور -->
                    <div class="tab-pane fade" id="attendance">
                        <?php if (!empty($attendance)): ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>الجلسة</th>
                                            <th>التاريخ</th>
                                            <th>الحالة</th>
                                            <th>وقت الدخول</th>
                                            <th>وقت الخروج</th>
                                            <th>مدة الحضور</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($attendance as $session): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($session['session_title']); ?></td>
                                            <td><?php echo date('d/m/Y', strtotime($session['session_date'])); ?></td>
                                            <td>
                                                <?php
                                                $status = $session['attendance_status'];
                                                if ($status === 'present' || $session['join_time']): ?>
                                                    <span class="attendance-indicator present"></span>
                                                    <span class="text-success">حاضر</span>
                                                <?php elseif ($status === 'late'): ?>
                                                    <span class="attendance-indicator late"></span>
                                                    <span class="text-warning">متأخر</span>
                                                <?php else: ?>
                                                    <span class="attendance-indicator absent"></span>
                                                    <span class="text-danger">غائب</span>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo $session['join_time'] ? date('H:i', strtotime($session['join_time'])) : '-'; ?></td>
                                            <td><?php echo $session['leave_time'] ? date('H:i', strtotime($session['leave_time'])) : '-'; ?></td>
                                            <td>
                                                <?php
                                                if ($session['join_time'] && $session['leave_time']) {
                                                    $duration = strtotime($session['leave_time']) - strtotime($session['join_time']);
                                                    echo gmdate('H:i', $duration);
                                                } else {
                                                    echo '-';
                                                }
                                                ?>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-4">
                                <i class="fas fa-calendar-check text-muted" style="font-size: 3rem;"></i>
                                <h5 class="mt-3 text-muted">لا توجد جلسات</h5>
                                <p class="text-muted">لم يتم إنشاء جلسات لهذا الكورس بعد</p>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- تبويب المعلومات الإضافية -->
                    <div class="tab-pane fade" id="info">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-muted mb-3">معلومات الاتصال</h6>
                                <p><strong>الهاتف:</strong> <?php echo htmlspecialchars($student['phone'] ?? 'غير محدد'); ?></p>
                                <p><strong>تاريخ التسجيل في النظام:</strong> <?php echo date('d/m/Y', strtotime($student['created_at'])); ?></p>
                                <p><strong>آخر تسجيل دخول:</strong> <?php echo $student['last_login'] ? date('d/m/Y H:i', strtotime($student['last_login'])) : 'لم يسجل دخول'; ?></p>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-muted mb-3">ملاحظات المدرب</h6>
                                <div class="border rounded p-3 bg-light">
                                    <?php echo $student['notes'] ? nl2br(htmlspecialchars($student['notes'])) : 'لا توجد ملاحظات'; ?>
                                </div>
                                <button class="btn btn-sm btn-outline-primary mt-2" onclick="editNotes()">
                                    <i class="fas fa-edit me-1"></i>تعديل الملاحظات
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function editNotes() {
            const notes = prompt('ملاحظات المدرب:', '<?php echo addslashes($student['notes'] ?? ''); ?>');
            if (notes !== null) {
                // يمكن إرسال الملاحظات عبر AJAX
                alert('سيتم حفظ الملاحظات قريباً');
            }
        }
    </script>
</body>
</html>