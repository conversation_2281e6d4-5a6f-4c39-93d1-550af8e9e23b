<?php
// إصلاح بسيط لقاعدة البيانات
echo "<h1>إصلاح قاعدة البيانات</h1>";

// اختبار الاتصال الأساسي
echo "<h2>1. اختبار الاتصال بـ MySQL:</h2>";
try {
    $pdo = new PDO("mysql:host=localhost", "root", "");
    echo "✅ نجح الاتصال بـ MySQL<br>";
    
    // إنشاء قاعدة البيانات
    echo "<h2>2. إنشاء قاعدة البيانات:</h2>";
    $pdo->exec("CREATE DATABASE IF NOT EXISTS zoom_learning_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "✅ تم إنشاء قاعدة البيانات zoom_learning_system<br>";
    
    // الاتصال بقاعدة البيانات
    $pdo = new PDO("mysql:host=localhost;dbname=zoom_learning_system;charset=utf8mb4", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ نجح الاتصال بقاعدة البيانات<br>";
    
    // إنشاء جدول المستخدمين
    echo "<h2>3. إنشاء جدول المستخدمين:</h2>";
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            email VARCHAR(255) NOT NULL UNIQUE,
            username VARCHAR(50) NULL,
            phone VARCHAR(20) NULL,
            password VARCHAR(255) NOT NULL,
            role ENUM('admin', 'instructor', 'student') NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_login TIMESTAMP NULL,
            status ENUM('active', 'inactive') DEFAULT 'active'
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✅ تم إنشاء جدول المستخدمين<br>";
    
    // إنشاء مستخدم تجريبي
    echo "<h2>4. إنشاء مستخدم تجريبي:</h2>";
    $email = '<EMAIL>';
    $password = password_hash('12345678', PASSWORD_DEFAULT);
    
    // التحقق من وجود المستخدم
    $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
    $stmt->execute([$email]);
    
    if (!$stmt->fetch()) {
        $stmt = $pdo->prepare("INSERT INTO users (name, email, username, password, role, status) VALUES (?, ?, ?, ?, ?, ?)");
        $stmt->execute(['مستخدم تجريبي', $email, 'wow11', $password, 'instructor', 'active']);
        echo "✅ تم إنشاء المستخدم: $email<br>";
    } else {
        echo "ℹ️ المستخدم موجود مسبقاً: $email<br>";
    }
    
    // إنشاء جدول الكورسات
    echo "<h2>5. إنشاء جدول الكورسات:</h2>";
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS courses (
            id INT AUTO_INCREMENT PRIMARY KEY,
            title VARCHAR(255) NOT NULL,
            description TEXT,
            instructor_id INT,
            max_students INT DEFAULT 50,
            price DECIMAL(10,2) DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            status ENUM('active', 'inactive') DEFAULT 'active',
            FOREIGN KEY (instructor_id) REFERENCES users(id) ON DELETE SET NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✅ تم إنشاء جدول الكورسات<br>";
    
    // إنشاء جدول الواجبات
    echo "<h2>6. إنشاء جدول الواجبات:</h2>";
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS assignments (
            id INT AUTO_INCREMENT PRIMARY KEY,
            course_id INT NOT NULL,
            instructor_id INT NOT NULL,
            title VARCHAR(255) NOT NULL,
            description TEXT,
            instructions TEXT,
            due_date DATETIME NOT NULL,
            max_grade INT DEFAULT 100,
            is_required TINYINT(1) DEFAULT 0,
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_by INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
            FOREIGN KEY (instructor_id) REFERENCES users(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✅ تم إنشاء جدول الواجبات<br>";
    
    echo "<h2>✅ تم الإصلاح بنجاح!</h2>";
    echo "<p><strong>بيانات تسجيل الدخول:</strong></p>";
    echo "<ul>";
    echo "<li>البريد الإلكتروني: <EMAIL></li>";
    echo "<li>كلمة المرور: 12345678</li>";
    echo "</ul>";
    
    echo "<p><a href='login.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>تسجيل الدخول الآن</a></p>";
    
} catch (PDOException $e) {
    echo "<h2 style='color: red;'>❌ خطأ في قاعدة البيانات:</h2>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
    
    echo "<h3>الحلول المقترحة:</h3>";
    echo "<ol>";
    echo "<li><strong>تأكد من تشغيل XAMPP:</strong> افتح XAMPP Control Panel وتأكد من تشغيل Apache و MySQL</li>";
    echo "<li><strong>أعد تشغيل MySQL:</strong> في XAMPP، انقر على Stop ثم Start لـ MySQL</li>";
    echo "<li><strong>تحقق من المنفذ:</strong> تأكد من أن المنفذ 3306 غير مستخدم من برنامج آخر</li>";
    echo "<li><strong>أعد تشغيل الكمبيوتر:</strong> أحياناً يحل هذا المشكلة</li>";
    echo "</ol>";
    
    echo "<h3>اختبار phpMyAdmin:</h3>";
    echo "<p>جرب فتح: <a href='http://localhost/phpmyadmin' target='_blank'>http://localhost/phpmyadmin</a></p>";
}
?>
