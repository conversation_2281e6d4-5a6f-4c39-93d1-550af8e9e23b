<?php
// اختبار قاعدة البيانات
require_once 'config/database.php';

echo "<h2>اختبار قاعدة البيانات</h2>";

try {
    // عرض الجداول الموجودة
    echo "<h3>الجداول الموجودة:</h3>";
    $stmt = $conn->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    foreach ($tables as $table) {
        echo "- $table<br>";
    }
    
    // عرض هيكل جدول courses
    echo "<h3>هيكل جدول courses:</h3>";
    try {
        $stmt = $conn->query("DESCRIBE courses");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table border='1'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>" . $column['Field'] . "</td>";
            echo "<td>" . $column['Type'] . "</td>";
            echo "<td>" . $column['Null'] . "</td>";
            echo "<td>" . $column['Key'] . "</td>";
            echo "<td>" . $column['Default'] . "</td>";
            echo "<td>" . $column['Extra'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } catch (PDOException $e) {
        echo "خطأ في جدول courses: " . $e->getMessage();
    }
    
    // عرض البيانات الموجودة
    echo "<h3>المستخدمون:</h3>";
    try {
        $stmt = $conn->query("SELECT id, name, email, role FROM users");
        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if ($users) {
            echo "<table border='1'>";
            echo "<tr><th>ID</th><th>Name</th><th>Email</th><th>Role</th></tr>";
            foreach ($users as $user) {
                echo "<tr>";
                echo "<td>" . $user['id'] . "</td>";
                echo "<td>" . $user['name'] . "</td>";
                echo "<td>" . $user['email'] . "</td>";
                echo "<td>" . $user['role'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "لا توجد مستخدمون";
        }
    } catch (PDOException $e) {
        echo "خطأ في جدول المستخدمين: " . $e->getMessage();
    }
    
    echo "<h3>الكورسات:</h3>";
    try {
        $stmt = $conn->query("SELECT * FROM courses");
        $courses = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if ($courses) {
            echo "<table border='1'>";
            echo "<tr><th>ID</th><th>Title</th><th>Description</th><th>Instructor ID</th><th>Price</th></tr>";
            foreach ($courses as $course) {
                echo "<tr>";
                echo "<td>" . $course['id'] . "</td>";
                echo "<td>" . $course['title'] . "</td>";
                echo "<td>" . substr($course['description'], 0, 50) . "...</td>";
                echo "<td>" . ($course['instructor_id'] ?? 'NULL') . "</td>";
                echo "<td>" . ($course['price'] ?? 'NULL') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "لا توجد كورسات";
        }
    } catch (PDOException $e) {
        echo "خطأ في جدول الكورسات: " . $e->getMessage();
    }
    
    echo "<h3>الواجبات:</h3>";
    try {
        $stmt = $conn->query("SELECT * FROM assignments");
        $assignments = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if ($assignments) {
            echo "<table border='1'>";
            echo "<tr><th>ID</th><th>Title</th><th>Course ID</th><th>Instructor ID</th><th>Due Date</th></tr>";
            foreach ($assignments as $assignment) {
                echo "<tr>";
                echo "<td>" . $assignment['id'] . "</td>";
                echo "<td>" . $assignment['title'] . "</td>";
                echo "<td>" . $assignment['course_id'] . "</td>";
                echo "<td>" . $assignment['instructor_id'] . "</td>";
                echo "<td>" . $assignment['due_date'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "لا توجد واجبات";
        }
    } catch (PDOException $e) {
        echo "خطأ في جدول الواجبات: " . $e->getMessage();
    }
    
} catch (PDOException $e) {
    echo "<h3 style='color: red;'>خطأ عام: " . $e->getMessage() . "</h3>";
}

echo "<br><br>";
echo "<a href='setup_database.php'>إعادة إعداد قاعدة البيانات</a><br>";
echo "<a href='instructor/assignments.php'>صفحة الواجبات</a><br>";
echo "<a href='instructor/quick-login.php'>تسجيل دخول سريع</a>";
?>
