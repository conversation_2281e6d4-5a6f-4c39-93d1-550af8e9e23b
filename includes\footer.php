        </div> <!-- end main-content -->
    </div> <!-- end container-fluid -->

<!-- Footer محسن -->
<footer class="footer mt-5 py-4" style="background: linear-gradient(135deg, var(--primary-color), var(--primary-dark)); color: white;">
    <div class="container">
        <div class="row">
            <div class="col-md-6">
                <h5><i class="fas fa-graduation-cap me-2"></i>نظام التعلم عن بعد</h5>
                <p class="mb-2">منصة تعليمية متطورة لإدارة الدورات والجلسات التعليمية عن بعد</p>
                <div class="social-links">
                    <a href="#" class="text-white me-3"><i class="fab fa-facebook-f"></i></a>
                    <a href="#" class="text-white me-3"><i class="fab fa-twitter"></i></a>
                    <a href="#" class="text-white me-3"><i class="fab fa-linkedin-in"></i></a>
                    <a href="#" class="text-white"><i class="fab fa-youtube"></i></a>
                </div>
            </div>
            <div class="col-md-3">
                <h6>روابط سريعة</h6>
                <ul class="list-unstyled">
                    <li><a href="#" class="text-white-50">الرئيسية</a></li>
                    <li><a href="#" class="text-white-50">الدورات</a></li>
                    <li><a href="#" class="text-white-50">المدربين</a></li>
                    <li><a href="#" class="text-white-50">اتصل بنا</a></li>
                </ul>
            </div>
            <div class="col-md-3">
                <h6>معلومات الاتصال</h6>
                <p class="text-white-50 mb-1"><i class="fas fa-envelope me-2"></i><EMAIL></p>
                <p class="text-white-50 mb-1"><i class="fas fa-phone me-2"></i>+966 50 123 4567</p>
                <p class="text-white-50"><i class="fas fa-map-marker-alt me-2"></i>الرياض، المملكة العربية السعودية</p>
            </div>
        </div>
        <hr class="my-4" style="border-color: rgba(255,255,255,0.2);">
        <div class="row align-items-center">
            <div class="col-md-6">
                <p class="mb-0 text-white-50">جميع الحقوق محفوظة © <?php echo date('Y'); ?> نظام التعلم عن بعد</p>
            </div>
            <div class="col-md-6 text-end">
                <small class="text-white-50">تم التطوير بواسطة فريق التطوير المتخصص</small>
            </div>
        </div>
    </div>
</footer>

<!-- مؤشر التحميل العام -->
<div class="loading-overlay" id="globalLoader">
    <div class="loading-spinner-large"></div>
</div>

<!-- JavaScript Libraries -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<!-- ملف JavaScript المخصص -->
<script src="<?php echo '/Zoom/assets/js/main.js'; ?>"></script>

<script>
// إعدادات إضافية للنظام
document.addEventListener('DOMContentLoaded', function() {
    // تفعيل الرسوم المتحركة للعناصر
    const animatedElements = document.querySelectorAll('.card, .stats-card, .table-enhanced');
    animatedElements.forEach((el, index) => {
        el.style.animationDelay = `${index * 0.1}s`;
        el.classList.add('fade-in-up');
    });

    // تحسين تجربة المستخدم للنماذج
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function() {
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn && !form.classList.contains('ajax-form')) {
                submitBtn.innerHTML = '<span class="loading-spinner me-2"></span>جاري المعالجة...';
                submitBtn.disabled = true;
            }
        });
    });

    // إخفاء التنبيهات تلقائياً
    setTimeout(function() {
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(alert => {
            alert.style.transition = 'opacity 0.5s ease';
            alert.style.opacity = '0';
            setTimeout(() => alert.remove(), 500);
        });
    }, 5000);
});
</script>

</body>
</html>
