<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات المدير
if (!isLoggedIn() || !isAdmin()) {
    header('Location: ../login.php');
    exit;
}

$pageTitle = 'إدارة الإشعارات';
$breadcrumbs = [
    ['title' => 'إدارة الإشعارات']
];

$success = '';
$error = '';

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    try {
        switch ($action) {
            case 'send_notification':
                $title = trim($_POST['title']);
                $message = trim($_POST['message']);
                $type = $_POST['type'];
                $category = $_POST['category'];
                $recipients = $_POST['recipients'] ?? [];
                $send_to_all = isset($_POST['send_to_all']);
                
                if (empty($title) || empty($message)) {
                    throw new Exception('العنوان والرسالة مطلوبان');
                }
                
                // تحديد المستلمين
                if ($send_to_all) {
                    $stmt = $conn->query("SELECT id FROM users WHERE role != 'admin'");
                    $recipients = $stmt->fetchAll(PDO::FETCH_COLUMN);
                }
                
                if (empty($recipients)) {
                    throw new Exception('يجب اختيار مستلمين للإشعار');
                }
                
                // إرسال الإشعارات
                $stmt = $conn->prepare("
                    INSERT INTO notifications (user_id, title, message, type, category) 
                    VALUES (?, ?, ?, ?, ?)
                ");
                
                $sent_count = 0;
                foreach ($recipients as $user_id) {
                    $stmt->execute([$user_id, $title, $message, $type, $category]);
                    $sent_count++;
                }
                
                $success = "تم إرسال الإشعار إلى $sent_count مستخدم";
                logActivity($_SESSION['user_id'], 'notification_management', "تم إرسال إشعار جماعي: $title");
                break;
                
            case 'mark_as_read':
                $notification_id = (int)$_POST['notification_id'];
                
                $stmt = $conn->prepare("
                    UPDATE notifications 
                    SET is_read = TRUE, read_at = NOW() 
                    WHERE id = ?
                ");
                $stmt->execute([$notification_id]);
                
                $success = 'تم تحديد الإشعار كمقروء';
                break;
                
            case 'delete_notification':
                $notification_id = (int)$_POST['notification_id'];
                
                $stmt = $conn->prepare("DELETE FROM notifications WHERE id = ?");
                $stmt->execute([$notification_id]);
                
                $success = 'تم حذف الإشعار بنجاح';
                logActivity($_SESSION['user_id'], 'notification_management', "تم حذف إشعار");
                break;
                
            case 'bulk_action':
                $bulk_action = $_POST['bulk_action'];
                $selected_notifications = $_POST['selected_notifications'] ?? [];
                
                if (!empty($selected_notifications)) {
                    $placeholders = str_repeat('?,', count($selected_notifications) - 1) . '?';
                    
                    switch ($bulk_action) {
                        case 'mark_read':
                            $stmt = $conn->prepare("
                                UPDATE notifications 
                                SET is_read = TRUE, read_at = NOW() 
                                WHERE id IN ($placeholders)
                            ");
                            $stmt->execute($selected_notifications);
                            $success = 'تم تحديد الإشعارات المحددة كمقروءة';
                            break;
                            
                        case 'delete':
                            $stmt = $conn->prepare("DELETE FROM notifications WHERE id IN ($placeholders)");
                            $stmt->execute($selected_notifications);
                            $success = 'تم حذف الإشعارات المحددة';
                            break;
                    }
                }
                break;
        }
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// معاملات البحث والفلترة
$search = $_GET['search'] ?? '';
$type_filter = $_GET['type'] ?? '';
$category_filter = $_GET['category'] ?? '';
$status_filter = $_GET['status'] ?? '';
$user_filter = $_GET['user'] ?? '';
$page = (int)($_GET['page'] ?? 1);
$per_page = 20;
$offset = ($page - 1) * $per_page;

// بناء استعلام البحث
$where_conditions = [];
$params = [];

if ($search) {
    $where_conditions[] = "(n.title LIKE ? OR n.message LIKE ?)";
    $search_term = "%$search%";
    $params = array_merge($params, [$search_term, $search_term]);
}

if ($type_filter) {
    $where_conditions[] = "n.type = ?";
    $params[] = $type_filter;
}

if ($category_filter) {
    $where_conditions[] = "n.category = ?";
    $params[] = $category_filter;
}

if ($status_filter) {
    if ($status_filter === 'read') {
        $where_conditions[] = "n.is_read = TRUE";
    } else {
        $where_conditions[] = "n.is_read = FALSE";
    }
}

if ($user_filter) {
    $where_conditions[] = "n.user_id = ?";
    $params[] = $user_filter;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

try {
    // جلب إجمالي عدد الإشعارات
    $count_stmt = $conn->prepare("
        SELECT COUNT(*) 
        FROM notifications n
        LEFT JOIN users u ON n.user_id = u.id
        $where_clause
    ");
    $count_stmt->execute($params);
    $total_notifications = $count_stmt->fetchColumn();
    $total_pages = ceil($total_notifications / $per_page);
    
    // جلب الإشعارات
    $stmt = $conn->prepare("
        SELECT n.*, u.name as user_name, u.email as user_email
        FROM notifications n
        LEFT JOIN users u ON n.user_id = u.id
        $where_clause
        ORDER BY n.created_at DESC
        LIMIT $per_page OFFSET $offset
    ");
    $stmt->execute($params);
    $notifications = $stmt->fetchAll();
    
    // إحصائيات الإشعارات
    $stats_stmt = $conn->query("
        SELECT 
            COUNT(*) as total_notifications,
            COUNT(CASE WHEN is_read = FALSE THEN 1 END) as unread_notifications,
            COUNT(CASE WHEN is_read = TRUE THEN 1 END) as read_notifications,
            COUNT(CASE WHEN type = 'info' THEN 1 END) as info_notifications,
            COUNT(CASE WHEN type = 'success' THEN 1 END) as success_notifications,
            COUNT(CASE WHEN type = 'warning' THEN 1 END) as warning_notifications,
            COUNT(CASE WHEN type = 'error' THEN 1 END) as error_notifications,
            COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 END) as today_notifications
        FROM notifications
    ");
    $stats = $stats_stmt->fetch();
    
    // جلب قائمة المستخدمين للفلترة
    $users_stmt = $conn->query("
        SELECT u.id, u.name, u.role
        FROM users u 
        WHERE u.role != 'admin'
        ORDER BY u.name
    ");
    $users = $users_stmt->fetchAll();
    
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء جلب البيانات';
}

include 'includes/header.php';
?>

<!-- الإحصائيات السريعة -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3" data-aos="fade-up">
        <div class="stats-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3 class="text-primary mb-1"><?php echo number_format($stats['total_notifications']); ?></h3>
                    <p class="text-muted mb-0">إجمالي الإشعارات</p>
                    <small class="text-success">
                        <i class="fas fa-plus me-1"></i>
                        +<?php echo $stats['today_notifications']; ?> اليوم
                    </small>
                </div>
                <div class="text-primary">
                    <i class="fas fa-bell fa-2x"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3" data-aos="fade-up" data-aos-delay="100">
        <div class="stats-card warning">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3 class="text-warning mb-1"><?php echo number_format($stats['unread_notifications']); ?></h3>
                    <p class="text-muted mb-0">غير مقروءة</p>
                    <small class="text-muted">
                        <i class="fas fa-envelope me-1"></i>
                        تحتاج مراجعة
                    </small>
                </div>
                <div class="text-warning">
                    <i class="fas fa-envelope fa-2x"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3" data-aos="fade-up" data-aos-delay="200">
        <div class="stats-card success">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3 class="text-success mb-1"><?php echo number_format($stats['read_notifications']); ?></h3>
                    <p class="text-muted mb-0">مقروءة</p>
                    <small class="text-muted">
                        <i class="fas fa-check-circle me-1"></i>
                        تم الاطلاع عليها
                    </small>
                </div>
                <div class="text-success">
                    <i class="fas fa-check-circle fa-2x"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3" data-aos="fade-up" data-aos-delay="300">
        <div class="stats-card danger">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3 class="text-danger mb-1"><?php echo number_format($stats['error_notifications']); ?></h3>
                    <p class="text-muted mb-0">إشعارات خطأ</p>
                    <small class="text-muted">
                        <i class="fas fa-exclamation-triangle me-1"></i>
                        تحتاج انتباه
                    </small>
                </div>
                <div class="text-danger">
                    <i class="fas fa-exclamation-triangle fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<?php if ($success): ?>
<div class="alert alert-success alert-dismissible fade show" data-aos="fade-down">
    <i class="fas fa-check-circle me-2"></i>
    <?php echo htmlspecialchars($success); ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<?php if ($error): ?>
<div class="alert alert-danger alert-dismissible fade show" data-aos="fade-down">
    <i class="fas fa-exclamation-circle me-2"></i>
    <?php echo htmlspecialchars($error); ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<!-- أدوات البحث والفلترة -->
<div class="card-admin mb-4" data-aos="fade-up">
    <div class="card-body">
        <div class="row g-3">
            <div class="col-md-3">
                <form method="GET" class="d-flex">
                    <input type="text" name="search" class="form-control" 
                           placeholder="ابحث في الإشعارات..." 
                           value="<?php echo htmlspecialchars($search); ?>">
                    <button type="submit" class="btn btn-admin-primary ms-2">
                        <i class="fas fa-search"></i>
                    </button>
                </form>
            </div>
            <div class="col-md-2">
                <select name="type" class="form-select" onchange="applyFilters()">
                    <option value="">جميع الأنواع</option>
                    <option value="info" <?php echo $type_filter === 'info' ? 'selected' : ''; ?>>معلومات</option>
                    <option value="success" <?php echo $type_filter === 'success' ? 'selected' : ''; ?>>نجاح</option>
                    <option value="warning" <?php echo $type_filter === 'warning' ? 'selected' : ''; ?>>تحذير</option>
                    <option value="error" <?php echo $type_filter === 'error' ? 'selected' : ''; ?>>خطأ</option>
                </select>
            </div>
            <div class="col-md-2">
                <select name="category" class="form-select" onchange="applyFilters()">
                    <option value="">جميع الفئات</option>
                    <option value="system" <?php echo $category_filter === 'system' ? 'selected' : ''; ?>>نظام</option>
                    <option value="course" <?php echo $category_filter === 'course' ? 'selected' : ''; ?>>كورس</option>
                    <option value="assignment" <?php echo $category_filter === 'assignment' ? 'selected' : ''; ?>>واجب</option>
                    <option value="quiz" <?php echo $category_filter === 'quiz' ? 'selected' : ''; ?>>اختبار</option>
                    <option value="payment" <?php echo $category_filter === 'payment' ? 'selected' : ''; ?>>دفع</option>
                    <option value="user" <?php echo $category_filter === 'user' ? 'selected' : ''; ?>>مستخدم</option>
                </select>
            </div>
            <div class="col-md-2">
                <select name="status" class="form-select" onchange="applyFilters()">
                    <option value="">جميع الحالات</option>
                    <option value="unread" <?php echo $status_filter === 'unread' ? 'selected' : ''; ?>>غير مقروءة</option>
                    <option value="read" <?php echo $status_filter === 'read' ? 'selected' : ''; ?>>مقروءة</option>
                </select>
            </div>
            <div class="col-md-3">
                <div class="d-flex gap-2">
                    <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#sendNotificationModal">
                        <i class="fas fa-plus me-1"></i>
                        إرسال إشعار
                    </button>
                    <a href="notifications.php" class="btn btn-outline-secondary">
                        <i class="fas fa-redo me-1"></i>
                        إعادة تعيين
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- جدول الإشعارات -->
<div class="card-admin" data-aos="fade-up">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="fas fa-bell me-2"></i>
                قائمة الإشعارات (<?php echo number_format($total_notifications); ?>)
            </h5>
            <div class="d-flex gap-2">
                <button class="btn btn-info btn-sm" onclick="exportData('excel', 'export-notifications.php', 'notifications.xlsx')">
                    <i class="fas fa-file-excel me-1"></i>
                    تصدير Excel
                </button>
            </div>
        </div>
    </div>
    <div class="card-body p-0">
        <?php if (empty($notifications)): ?>
        <div class="text-center py-5">
            <i class="fas fa-bell fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد إشعارات</h5>
            <p class="text-muted">لم يتم العثور على إشعارات تطابق معايير البحث</p>
        </div>
        <?php else: ?>
        <form id="bulkActionForm" method="POST">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th width="50">
                                <input type="checkbox" id="selectAll" class="form-check-input">
                            </th>
                            <th>الإشعار</th>
                            <th>المستلم</th>
                            <th>النوع</th>
                            <th>الفئة</th>
                            <th>الحالة</th>
                            <th>التاريخ</th>
                            <th width="150">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($notifications as $notification): ?>
                        <tr class="<?php echo !$notification['is_read'] ? 'table-warning' : ''; ?>">
                            <td>
                                <input type="checkbox" name="selected_notifications[]" value="<?php echo $notification['id']; ?>" class="form-check-input item-checkbox">
                            </td>
                            <td>
                                <div>
                                    <div class="fw-bold"><?php echo htmlspecialchars($notification['title']); ?></div>
                                    <small class="text-muted">
                                        <?php echo htmlspecialchars(substr($notification['message'], 0, 100)); ?>
                                        <?php if (strlen($notification['message']) > 100): ?>...<?php endif; ?>
                                    </small>
                                </div>
                            </td>
                            <td>
                                <div>
                                    <div class="fw-bold"><?php echo htmlspecialchars($notification['user_name']); ?></div>
                                    <small class="text-muted"><?php echo htmlspecialchars($notification['user_email']); ?></small>
                                </div>
                            </td>
                            <td>
                                <?php
                                $type_badges = [
                                    'info' => 'bg-info',
                                    'success' => 'bg-success',
                                    'warning' => 'bg-warning',
                                    'error' => 'bg-danger'
                                ];
                                $type_names = [
                                    'info' => 'معلومات',
                                    'success' => 'نجاح',
                                    'warning' => 'تحذير',
                                    'error' => 'خطأ'
                                ];
                                ?>
                                <span class="badge <?php echo $type_badges[$notification['type']] ?? 'bg-secondary'; ?>">
                                    <?php echo $type_names[$notification['type']] ?? $notification['type']; ?>
                                </span>
                            </td>
                            <td>
                                <?php
                                $category_names = [
                                    'system' => 'نظام',
                                    'course' => 'كورس',
                                    'assignment' => 'واجب',
                                    'quiz' => 'اختبار',
                                    'payment' => 'دفع',
                                    'user' => 'مستخدم'
                                ];
                                ?>
                                <span class="badge bg-secondary">
                                    <?php echo $category_names[$notification['category']] ?? $notification['category']; ?>
                                </span>
                            </td>
                            <td>
                                <?php if ($notification['is_read']): ?>
                                <span class="badge bg-success">
                                    <i class="fas fa-check me-1"></i>مقروء
                                </span>
                                <br>
                                <small class="text-muted">
                                    <?php echo date('Y-m-d H:i', strtotime($notification['read_at'])); ?>
                                </small>
                                <?php else: ?>
                                <span class="badge bg-warning">
                                    <i class="fas fa-envelope me-1"></i>غير مقروء
                                </span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <small class="text-muted">
                                    <?php echo date('Y-m-d H:i', strtotime($notification['created_at'])); ?>
                                </small>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button class="btn btn-sm btn-outline-primary" 
                                            data-bs-toggle="modal" 
                                            data-bs-target="#viewNotificationModal<?php echo $notification['id']; ?>">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    
                                    <?php if (!$notification['is_read']): ?>
                                    <form method="POST" class="d-inline">
                                        <input type="hidden" name="action" value="mark_as_read">
                                        <input type="hidden" name="notification_id" value="<?php echo $notification['id']; ?>">
                                        <button type="submit" class="btn btn-sm btn-outline-success" title="تحديد كمقروء">
                                            <i class="fas fa-check"></i>
                                        </button>
                                    </form>
                                    <?php endif; ?>
                                    
                                    <form method="POST" class="d-inline">
                                        <input type="hidden" name="action" value="delete_notification">
                                        <input type="hidden" name="notification_id" value="<?php echo $notification['id']; ?>">
                                        <button type="submit" class="btn btn-sm btn-outline-danger"
                                                onclick="return confirm('هل أنت متأكد من حذف هذا الإشعار؟')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            
            <!-- الإجراءات المجمعة -->
            <div class="card-footer" id="bulkActionBtn" style="display: none;">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <select name="bulk_action" class="form-select d-inline-block w-auto">
                            <option value="">اختر إجراء...</option>
                            <option value="mark_read">تحديد كمقروء</option>
                            <option value="delete">حذف المحدد</option>
                        </select>
                    </div>
                    <button type="submit" name="action" value="bulk_action" class="btn btn-primary"
                            onclick="return confirm('هل أنت متأكد من تنفيذ هذا الإجراء على الإشعارات المحددة؟')">
                        تنفيذ الإجراء
                    </button>
                </div>
            </div>
        </form>
        <?php endif; ?>
    </div>
</div>

<!-- التصفح -->
<?php if ($total_pages > 1): ?>
<nav class="mt-4" data-aos="fade-up">
    <ul class="pagination justify-content-center">
        <?php if ($page > 1): ?>
        <li class="page-item">
            <a class="page-link" href="?page=<?php echo $page-1; ?>&<?php echo http_build_query($_GET); ?>">السابق</a>
        </li>
        <?php endif; ?>
        
        <?php for ($i = max(1, $page-2); $i <= min($total_pages, $page+2); $i++): ?>
        <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
            <a class="page-link" href="?page=<?php echo $i; ?>&<?php echo http_build_query($_GET); ?>"><?php echo $i; ?></a>
        </li>
        <?php endfor; ?>
        
        <?php if ($page < $total_pages): ?>
        <li class="page-item">
            <a class="page-link" href="?page=<?php echo $page+1; ?>&<?php echo http_build_query($_GET); ?>">التالي</a>
        </li>
        <?php endif; ?>
    </ul>
</nav>
<?php endif; ?>

<script>
function applyFilters() {
    const form = document.createElement('form');
    form.method = 'GET';
    
    const params = ['type', 'category', 'status', 'user'];
    params.forEach(param => {
        const select = document.querySelector(`select[name="${param}"]`);
        if (select && select.value) {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = param;
            input.value = select.value;
            form.appendChild(input);
        }
    });
    
    document.body.appendChild(form);
    form.submit();
}
</script>

<?php include 'includes/footer.php'; ?>
