<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'includes/activity_logger.php';

$error = '';

// تسجيل الخروج إذا تم طلبه
if (isset($_GET['logout'])) {
    session_destroy();
    header('Location: login.php');
    exit();
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = filter_var(trim($_POST['email']), FILTER_SANITIZE_EMAIL);
    $password = $_POST['password'];
    $role = isset($_POST['role']) ? $_POST['role'] : 'student';

    error_log("Login attempt - Email: {$email}, Role: {$role}");

    if (empty($email) || empty($password)) {
        $error = 'جميع الحقول مطلوبة.';
    } else {
        try {
            $stmt = $conn->prepare("SELECT id, name, email, password, role, status, failed_attempts, lock_expires FROM users WHERE email = ?");
            $stmt->execute([$email]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($user) {
                error_log("User found: {$user['email']}. Role: {$user['role']}, Status: {$user['status']}, Failed Attempts: {$user['failed_attempts']}, Lock Expires: {$user['lock_expires']}");

                // التحقق مما إذا كان الحساب مقفلاً
                if ($user['lock_expires'] && strtotime($user['lock_expires']) > time()) {
                    $error = 'تم قفل الحساب مؤقتًا بسبب محاولات تسجيل دخول فاشلة متعددة. يرجى المحاولة مرة أخرى لاحقًا.';
                    error_log("Login failed: Account locked for user {$user['email']}. Lock expires at {$user['lock_expires']}");
                } elseif ($user['role'] !== $role) {
                    $error = 'البريد الإلكتروني أو كلمة المرور غير صحيحة.'; // رسالة عامة
                    error_log("Login failed: Invalid role for user {$user['email']}. Expected: {$role}, Found: {$user['role']}");
                    // لا نزيد محاولات الفشل هنا لأن الدور خطأ وليس كلمة المرور
                } elseif ($user['status'] === 'pending') {
                    $error = 'حسابك قيد المراجعة. يرجى انتظار موافقة الإدارة وستتلقى إشعاراً عند تفعيل حسابك.';
                    error_log("Login failed: Account pending approval for user {$user['email']}");
                } elseif ($user['status'] !== 'active') {
                    $error = 'حسابك غير مفعل. يرجى التواصل مع الإدارة.';
                    error_log("Login failed: Account not active for user {$user['email']}. Status: {$user['status']}");
                } elseif (!password_verify($password, $user['password'])) {
                    // كلمة المرور غير صحيحة
                    $failedAttempts = ($user['failed_attempts'] ?? 0) + 1;
                    $lockExpires = null;
                    $maxLoginAttempts = 5; // يمكن جعل هذا قابل للتكوين

                    if ($failedAttempts >= $maxLoginAttempts) {
                        $lockExpires = date('Y-m-d H:i:s', strtotime('+30 minutes')); // قفل لمدة 30 دقيقة
                        $error = 'تم قفل الحساب مؤقتًا بسبب محاولات تسجيل دخول فاشلة متعددة. يرجى المحاولة مرة أخرى لاحقًا.';
                        error_log("Login failed: Account locked for user {$user['email']} due to {$failedAttempts} failed attempts. Lock expires at {$lockExpires}");
                    } else {
                        $error = 'البريد الإلكتروني أو كلمة المرور غير صحيحة.';
                        error_log("Login failed: Invalid password for user {$user['email']}. Attempt {$failedAttempts} of {$maxLoginAttempts}.");
                    }

                    $updateStmt = $conn->prepare("UPDATE users SET failed_attempts = ?, lock_expires = ? WHERE id = ?");
                    $updateStmt->execute([$failedAttempts, $lockExpires, $user['id']]);

                    // تسجيل محاولة تسجيل دخول فاشلة
                    logLoginAttempt($email, false, 'كلمة مرور خاطئة');

                } else {
                    // تسجيل الدخول ناجح
                    error_log("Login successful - User: {$user['email']}, Role: {$user['role']}");

                    // إعادة تعيين محاولات تسجيل الدخول الفاشلة عند النجاح
                    $updateStmt = $conn->prepare("UPDATE users SET failed_attempts = 0, lock_expires = NULL WHERE id = ?");
                    $updateStmt->execute([$user['id']]);

                    $_SESSION['user_id'] = $user['id'];
                    $_SESSION['name'] = $user['name'];
                    $_SESSION['email'] = $user['email'];
                    $_SESSION['role'] = $user['role'];

                    // تسجيل محاولة تسجيل دخول ناجحة
                    logLoginAttempt($email, true);
                    logUserActivity('login', "تسجيل دخول ناجح - {$user['name']} ({$user['role']})");

                    // إعادة التوجيه حسب الرابط المحدد أو نوع المستخدم
                    $redirect = isset($_GET['redirect']) ? $_GET['redirect'] : '';
                    if (!empty($redirect)) {
                        header('Location: ' . $redirect);
                    } else {
                        // توجيه المستخدم حسب نوعه
                        switch ($user['role']) {
                            case 'admin':
                                header('Location: admin/dashboard.php');
                                break;
                            case 'instructor':
                                header('Location: instructor/dashboard.php');
                                break;
                            case 'student':
                                header('Location: student/dashboard.php');
                                break;
                            default:
                                header('Location: dashboard.php');
                                break;
                        }
                    }
                    exit();
                }
            } else {
                // المستخدم غير موجود
                $error = 'البريد الإلكتروني أو كلمة المرور غير صحيحة.'; // رسالة عامة
                error_log("Login failed: User not found with email {$email}");

                // تسجيل محاولة تسجيل دخول لمستخدم غير موجود
                logLoginAttempt($email, false, 'مستخدم غير موجود');
            }
        } catch (PDOException $e) {
            $error = 'حدث خطأ في الاتصال بقاعدة البيانات. يرجى المحاولة مرة أخرى لاحقًا.';
            error_log("Database error during login: " . $e->getMessage());
        }
    }
}
?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول</title>
    <!-- الخطوط العربية -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <!-- Bootstrap و Font Awesome -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- SweetAlert2 للرسائل التفاعلية -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">

    <!-- ملفات CSS المخصصة -->
    <link href="assets/css/main.css" rel="stylesheet">
    <link href="assets/css/components.css" rel="stylesheet">

    <style>
        body {
            background: var(--gradient-primary);
            min-height: 100vh;
            font-family: var(--font-family-arabic);
            position: relative;
            overflow-x: hidden;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(255,255,255,0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255,255,255,0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(255,255,255,0.05) 0%, transparent 50%);
            animation: particleFloat 8s ease-in-out infinite;
            z-index: -1;
        }

        .login-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem 1rem;
        }

        .login-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 450px;
            width: 100%;
            position: relative;
        }

        .login-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 5px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
        }

        .login-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            padding: 2rem;
            text-align: center;
            position: relative;
        }

        .login-header::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: 15px solid transparent;
            border-right: 15px solid transparent;
            border-top: 10px solid var(--primary-dark);
        }

        .login-body {
            padding: 2.5rem;
        }

        .google-btn {
            background: white;
            border: 2px solid #e0e0e0;
            color: #666;
            transition: all 0.3s ease;
            border-radius: 50px;
            padding: 0.75rem 1.5rem;
        }

        .google-btn:hover {
            border-color: #4285f4;
            color: #4285f4;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(66, 133, 244, 0.2);
        }

        .or-divider {
            position: relative;
            text-align: center;
            margin: 2rem 0;
            color: #999;
        }

        .or-divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            width: 45%;
            height: 1px;
            background: linear-gradient(to right, transparent, #ddd);
        }

        .or-divider::after {
            content: '';
            position: absolute;
            top: 50%;
            right: 0;
            width: 45%;
            height: 1px;
            background: linear-gradient(to left, transparent, #ddd);
        }

        .or-divider span {
            background: white;
            padding: 0 1rem;
            font-weight: 500;
        }

        .form-floating {
            margin-bottom: 1.5rem;
        }

        .form-floating .form-control {
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            padding: 1rem 0.75rem;
            height: auto;
            transition: all 0.3s ease;
        }

        .form-floating .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(33, 150, 243, 0.25);
            transform: translateY(-2px);
        }

        .form-floating label {
            padding: 1rem 0.75rem;
            color: #999;
        }

        .btn-login {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            border: none;
            border-radius: 50px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(33, 150, 243, 0.3);
        }

        .btn-login::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255,255,255,0.3);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: all 0.3s ease;
        }

        .btn-login:hover::before {
            width: 300px;
            height: 300px;
        }

        .alert-custom {
            border: none;
            border-radius: 15px;
            padding: 1rem 1.5rem;
            margin-bottom: 1.5rem;
            border-left: 4px solid;
        }

        .alert-danger-custom {
            background: #ffebee;
            color: #c62828;
            border-left-color: #f44336;
        }

        .role-selector {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 1rem;
            margin-bottom: 1.5rem;
        }

        .role-option {
            display: flex;
            align-items: center;
            padding: 0.75rem;
            margin: 0.25rem 0;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .role-option:hover {
            background: white;
            transform: translateX(5px);
        }

        .role-option.selected {
            background: var(--primary-light);
            border-color: var(--primary-color);
        }

        .role-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 1rem;
            color: white;
        }

        .floating-shapes {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .shape {
            position: absolute;
            opacity: 0.1;
            animation: float 6s ease-in-out infinite;
        }

        .shape:nth-child(1) {
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }

        .shape:nth-child(2) {
            top: 60%;
            right: 10%;
            animation-delay: 2s;
        }

        .shape:nth-child(3) {
            bottom: 20%;
            left: 20%;
            animation-delay: 4s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        @media (max-width: 768px) {
            .login-body {
                padding: 1.5rem;
            }

            .login-header {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- الأشكال العائمة في الخلفية -->
    <div class="floating-shapes">
        <div class="shape">
            <i class="fas fa-graduation-cap fa-3x"></i>
        </div>
        <div class="shape">
            <i class="fas fa-book fa-2x"></i>
        </div>
        <div class="shape">
            <i class="fas fa-laptop fa-2x"></i>
        </div>
    </div>

    <div class="login-container">
        <div class="login-card fade-in">
            <div class="login-header">
                <h2 class="mb-2">
                    <i class="fas fa-graduation-cap me-2"></i>
                    مرحباً بك
                </h2>
                <p class="mb-0 opacity-75">سجل دخولك للوصول إلى منصة التعلم</p>
            </div>

            <div class="login-body">
                <?php if ($error): ?>
                    <div class="alert-custom alert-danger-custom" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?php echo htmlspecialchars($error, ENT_QUOTES, 'UTF-8'); ?>
                    </div>
                <?php endif; ?>

                <!-- تسجيل الدخول عبر Google -->
                <a href="google-login.php" class="btn google-btn w-100 mb-3 d-flex align-items-center justify-content-center">
                    <img src="https://upload.wikimedia.org/wikipedia/commons/5/53/Google_%22G%22_Logo.svg" alt="Google" style="height: 20px; margin-left: 10px;">
                    <span>تسجيل الدخول باستخدام Google</span>
                </a>

                <div class="or-divider">
                    <span>أو</span>
                </div>

                <!-- نموذج تسجيل الدخول -->
                <form method="POST" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" class="needs-validation" novalidate>
                    <!-- اختيار نوع المستخدم -->
                    <div class="role-selector">
                        <label class="form-label fw-bold mb-3">
                            <i class="fas fa-users me-2"></i>
                            اختر نوع حسابك:
                        </label>
                        <div class="role-options">
                            <div class="role-option <?php echo (!isset($_POST['role']) || $_POST['role'] === 'student') ? 'selected' : ''; ?>" data-role="student">
                                <div class="role-icon bg-success">
                                    <i class="fas fa-user-graduate"></i>
                                </div>
                                <div>
                                    <div class="fw-bold">طالب</div>
                                    <small class="text-muted">الوصول للكورسات والجلسات</small>
                                </div>
                                <input type="radio" name="role" value="student" <?php echo (!isset($_POST['role']) || $_POST['role'] === 'student') ? 'checked' : ''; ?> style="display: none;">
                            </div>

                            <div class="role-option <?php echo (isset($_POST['role']) && $_POST['role'] === 'instructor') ? 'selected' : ''; ?>" data-role="instructor">
                                <div class="role-icon bg-info">
                                    <i class="fas fa-chalkboard-teacher"></i>
                                </div>
                                <div>
                                    <div class="fw-bold">مدرب</div>
                                    <small class="text-muted">إدارة الكورسات والطلاب</small>
                                </div>
                                <input type="radio" name="role" value="instructor" <?php echo (isset($_POST['role']) && $_POST['role'] === 'instructor') ? 'checked' : ''; ?> style="display: none;">
                            </div>

                            <div class="role-option <?php echo (isset($_POST['role']) && $_POST['role'] === 'admin') ? 'selected' : ''; ?>" data-role="admin">
                                <div class="role-icon bg-danger">
                                    <i class="fas fa-user-shield"></i>
                                </div>
                                <div>
                                    <div class="fw-bold">مدير</div>
                                    <small class="text-muted">إدارة النظام بالكامل</small>
                                </div>
                                <input type="radio" name="role" value="admin" <?php echo (isset($_POST['role']) && $_POST['role'] === 'admin') ? 'checked' : ''; ?> style="display: none;">
                            </div>
                        </div>
                    </div>

                    <!-- البريد الإلكتروني -->
                    <div class="form-floating">
                        <input type="email" class="form-control" id="email" name="email"
                               value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email'], ENT_QUOTES, 'UTF-8') : ''; ?>"
                               placeholder="البريد الإلكتروني" required>
                        <label for="email">
                            <i class="fas fa-envelope me-2"></i>
                            البريد الإلكتروني
                        </label>
                        <div class="invalid-feedback">
                            يرجى إدخال بريد إلكتروني صحيح
                        </div>
                    </div>

                    <!-- كلمة المرور -->
                    <div class="form-floating">
                        <input type="password" class="form-control" id="password" name="password"
                               placeholder="كلمة المرور" required>
                        <label for="password">
                            <i class="fas fa-lock me-2"></i>
                            كلمة المرور
                        </label>
                        <div class="invalid-feedback">
                            يرجى إدخال كلمة المرور
                        </div>
                    </div>

                    <!-- نسيت كلمة المرور -->
                    <div class="text-end mb-3">
                        <a href="forgot-password.php" class="text-decoration-none text-primary">
                            <i class="fas fa-key me-1"></i>
                            نسيت كلمة المرور؟
                        </a>
                    </div>

                    <!-- زر تسجيل الدخول -->
                    <button type="submit" class="btn btn-login w-100 text-white mb-3">
                        <i class="fas fa-sign-in-alt me-2"></i>
                        <span>تسجيل الدخول</span>
                    </button>
                </form>

                <!-- روابط إضافية -->
                <div class="text-center">
                    <p class="text-muted mb-2">ليس لديك حساب؟</p>
                    <div class="d-flex gap-2 justify-content-center flex-wrap">
                        <a href="register.php<?php echo isset($_GET['redirect']) ? '?redirect=' . urlencode($_GET['redirect']) : ''; ?>" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-user-plus me-1"></i>
                            إنشاء حساب جديد
                        </a>
                        <a href="registration-status.php" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-search me-1"></i>
                            حالة التسجيل
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="assets/js/main.js"></script>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // تفعيل اختيار نوع المستخدم
        const roleOptions = document.querySelectorAll('.role-option');
        roleOptions.forEach(option => {
            option.addEventListener('click', function() {
                // إزالة التحديد من جميع الخيارات
                roleOptions.forEach(opt => opt.classList.remove('selected'));

                // إضافة التحديد للخيار المختار
                this.classList.add('selected');

                // تحديث radio button
                const radio = this.querySelector('input[type="radio"]');
                radio.checked = true;

                // تأثير بصري
                this.style.transform = 'scale(1.02)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 200);
            });
        });

        // تحسين تجربة النموذج
        const form = document.querySelector('form');
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalBtnText = submitBtn.innerHTML;

        form.addEventListener('submit', function(e) {
            // التحقق من صحة النموذج
            if (!form.checkValidity()) {
                e.preventDefault();
                e.stopPropagation();

                // إظهار رسالة خطأ
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ في البيانات',
                    text: 'يرجى التأكد من صحة جميع البيانات المدخلة',
                    confirmButtonText: 'حسناً'
                });
            } else {
                // إظهار حالة التحميل
                submitBtn.innerHTML = '<span class="loading-spinner me-2"></span>جاري تسجيل الدخول...';
                submitBtn.disabled = true;

                // إضافة تأخير بسيط لتحسين تجربة المستخدم
                setTimeout(() => {
                    // السماح بإرسال النموذج
                }, 500);
            }

            form.classList.add('was-validated');
        });

        // تأثيرات بصرية للحقول
        const inputs = document.querySelectorAll('.form-control');
        inputs.forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.style.transform = 'translateY(-2px)';
            });

            input.addEventListener('blur', function() {
                this.parentElement.style.transform = '';
            });
        });

        // تأثير الكتابة للعنوان
        const title = document.querySelector('.login-header h2');
        const originalTitle = title.textContent;
        title.textContent = '';

        let i = 0;
        function typeWriter() {
            if (i < originalTitle.length) {
                title.textContent += originalTitle.charAt(i);
                i++;
                setTimeout(typeWriter, 100);
            }
        }

        setTimeout(typeWriter, 500);

        // تأثير الظهور التدريجي للعناصر
        const elements = document.querySelectorAll('.role-option, .form-floating, .btn');
        elements.forEach((element, index) => {
            element.style.opacity = '0';
            element.style.transform = 'translateY(20px)';

            setTimeout(() => {
                element.style.transition = 'all 0.5s ease';
                element.style.opacity = '1';
                element.style.transform = 'translateY(0)';
            }, 200 + (index * 100));
        });

        // إضافة تأثيرات للأشكال العائمة
        const shapes = document.querySelectorAll('.shape');
        shapes.forEach((shape, index) => {
            shape.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.2) rotate(10deg)';
                this.style.opacity = '0.3';
            });

            shape.addEventListener('mouseleave', function() {
                this.style.transform = '';
                this.style.opacity = '0.1';
            });
        });

        // تحسين أداء الرسوم المتحركة
        let ticking = false;
        function updateShapes() {
            shapes.forEach((shape, index) => {
                const speed = 0.5 + (index * 0.2);
                const currentTransform = shape.style.transform || '';
                const yOffset = Math.sin(Date.now() * 0.001 * speed) * 10;
                shape.style.transform = `translateY(${yOffset}px)`;
            });
            ticking = false;
        }

        function requestTick() {
            if (!ticking) {
                requestAnimationFrame(updateShapes);
                ticking = true;
            }
        }

        // تشغيل الرسوم المتحركة
        setInterval(requestTick, 16); // ~60fps

        // إضافة تأثير للزر عند التمرير
        submitBtn.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px) scale(1.02)';
        });

        submitBtn.addEventListener('mouseleave', function() {
            this.style.transform = '';
        });

        // تحسين إمكانية الوصول
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && document.activeElement.classList.contains('role-option')) {
                document.activeElement.click();
            }
        });

        // إضافة خاصية التذكر للدور المختار
        const savedRole = localStorage.getItem('selectedRole');
        if (savedRole) {
            const savedRoleOption = document.querySelector(`[data-role="${savedRole}"]`);
            if (savedRoleOption) {
                savedRoleOption.click();
            }
        }

        // حفظ الدور المختار
        roleOptions.forEach(option => {
            option.addEventListener('click', function() {
                const role = this.getAttribute('data-role');
                localStorage.setItem('selectedRole', role);
            });
        });
    });
    </script>
</body>
</html>