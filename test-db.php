<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>اختبار قاعدة البيانات</h2>";

try {
    require_once 'config/database.php';
    echo "<p>✅ تم الاتصال بقاعدة البيانات بنجاح</p>";
    
    // اختبار جدول المستخدمين
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM users");
    $stmt->execute();
    $userCount = $stmt->fetchColumn();
    echo "<p>عدد المستخدمين: " . $userCount . "</p>";
    
    // عرض بنية جدول المستخدمين
    $stmt = $conn->prepare("DESCRIBE users");
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>بنية جدول المستخدمين:</h3>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // اختبار إدراج مستخدم تجريبي
    if (isset($_GET['test_insert'])) {
        $testName = "مستخدم تجريبي";
        $testEmail = "test" . time() . "@example.com";
        $testPhone = "0501234567";
        $testUsername = "test" . time();
        $testPassword = password_hash("12345678", PASSWORD_DEFAULT);
        
        try {
            $stmt = $conn->prepare("INSERT INTO users (name, email, phone, username, password, role, status) VALUES (?, ?, ?, ?, ?, 'student', 'pending')");
            $result = $stmt->execute([$testName, $testEmail, $testPhone, $testUsername, $testPassword]);
            
            if ($result) {
                $userId = $conn->lastInsertId();
                echo "<p style='color: green;'>✅ تم إنشاء مستخدم تجريبي بنجاح! معرف المستخدم: " . $userId . "</p>";
            } else {
                echo "<p style='color: red;'>❌ فشل في إنشاء المستخدم التجريبي</p>";
            }
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ خطأ في إنشاء المستخدم: " . $e->getMessage() . "</p>";
        }
    }
    
    // عرض آخر 5 مستخدمين
    $stmt = $conn->prepare("SELECT id, name, email, username, role, status, created_at FROM users ORDER BY id DESC LIMIT 5");
    $stmt->execute();
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($users) {
        echo "<h3>آخر 5 مستخدمين:</h3>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>الاسم</th><th>البريد</th><th>اسم المستخدم</th><th>الدور</th><th>الحالة</th><th>تاريخ الإنشاء</th></tr>";
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td>" . $user['id'] . "</td>";
            echo "<td>" . htmlspecialchars($user['name']) . "</td>";
            echo "<td>" . htmlspecialchars($user['email']) . "</td>";
            echo "<td>" . htmlspecialchars($user['username']) . "</td>";
            echo "<td>" . $user['role'] . "</td>";
            echo "<td>" . $user['status'] . "</td>";
            echo "<td>" . $user['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
}
?>

<p><a href="?test_insert=1">اختبار إدراج مستخدم تجريبي</a></p>
<p><a href="register.php">اختبار صفحة التسجيل</a></p>
<p><a href="minimal-register.php">اختبار صفحة التسجيل المبسطة</a></p>
