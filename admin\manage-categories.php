<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات المدير
if (!isLoggedIn() || !isAdmin()) {
    header('Location: ../login.php');
    exit;
}

$pageTitle = 'إدارة التصنيفات';
$breadcrumbs = [
    ['title' => 'إدارة التصنيفات']
];

$success = '';
$error = '';

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    try {
        switch ($action) {
            case 'add_category':
                $name = trim($_POST['name']);
                $description = trim($_POST['description']);
                $icon = $_POST['icon'] ?? 'fas fa-tag';
                $color = $_POST['color'] ?? '#007bff';
                $slug = strtolower(str_replace(' ', '-', $name));
                $slug = preg_replace('/[^a-z0-9\-]/', '', $slug);

                if (empty($name)) {
                    throw new Exception('اسم التصنيف مطلوب');
                }

                // التحقق من عدم تكرار الاسم
                $stmt = $conn->prepare("SELECT COUNT(*) FROM categories WHERE name = ? OR slug = ?");
                $stmt->execute([$name, $slug]);
                if ($stmt->fetchColumn() > 0) {
                    throw new Exception('التصنيف موجود بالفعل');
                }

                // الحصول على أعلى ترتيب وإضافة 1
                $sort_order_stmt = $conn->query("SELECT COALESCE(MAX(sort_order), 0) + 1 FROM categories");
                $sort_order = $sort_order_stmt->fetchColumn();

                $stmt = $conn->prepare("INSERT INTO categories (name, slug, description, icon, color, sort_order) VALUES (?, ?, ?, ?, ?, ?)");
                $stmt->execute([$name, $slug, $description, $icon, $color, $sort_order]);

                $success = 'تم إضافة التصنيف بنجاح';
                logUserActivity($_SESSION['user_id'], 'إضافة تصنيف', "تم إضافة تصنيف جديد: $name");
                break;
                
            case 'edit_category':
                $id = (int)$_POST['category_id'];
                $name = trim($_POST['name']);
                $description = trim($_POST['description']);
                $icon = $_POST['icon'] ?? 'fas fa-tag';
                $color = $_POST['color'] ?? '#007bff';
                $slug = strtolower(str_replace(' ', '-', $name));
                $slug = preg_replace('/[^a-z0-9\-]/', '', $slug);

                if (empty($name)) {
                    throw new Exception('اسم التصنيف مطلوب');
                }

                // التحقق من عدم تكرار الاسم (باستثناء التصنيف الحالي)
                $stmt = $conn->prepare("SELECT COUNT(*) FROM categories WHERE (name = ? OR slug = ?) AND id != ?");
                $stmt->execute([$name, $slug, $id]);
                if ($stmt->fetchColumn() > 0) {
                    throw new Exception('التصنيف موجود بالفعل');
                }

                $stmt = $conn->prepare("UPDATE categories SET name = ?, slug = ?, description = ?, icon = ?, color = ? WHERE id = ?");
                $stmt->execute([$name, $slug, $description, $icon, $color, $id]);

                $success = 'تم تحديث التصنيف بنجاح';
                logUserActivity($_SESSION['user_id'], 'تحديث تصنيف', "تم تحديث التصنيف: $name");
                break;
                
            case 'delete_category':
                $id = (int)$_POST['category_id'];
                
                // التحقق من وجود كورسات مرتبطة
                $stmt = $conn->prepare("SELECT COUNT(*) FROM course_categories WHERE category_id = ?");
                $stmt->execute([$id]);
                $courses_count = $stmt->fetchColumn();
                
                if ($courses_count > 0) {
                    throw new Exception("لا يمكن حذف التصنيف لأنه مرتبط بـ $courses_count كورس");
                }
                
                $stmt = $conn->prepare("DELETE FROM categories WHERE id = ?");
                $stmt->execute([$id]);
                
                $success = 'تم حذف التصنيف بنجاح';
                logUserActivity($_SESSION['user_id'], 'حذف تصنيف', "تم حذف تصنيف");
                break;
                
            case 'bulk_delete':
                $selected_categories = $_POST['selected_categories'] ?? [];
                
                if (!empty($selected_categories)) {
                    $placeholders = str_repeat('?,', count($selected_categories) - 1) . '?';
                    
                    // التحقق من وجود كورسات مرتبطة
                    $stmt = $conn->prepare("SELECT COUNT(*) FROM course_categories WHERE category_id IN ($placeholders)");
                    $stmt->execute($selected_categories);
                    $courses_count = $stmt->fetchColumn();
                    
                    if ($courses_count > 0) {
                        throw new Exception("لا يمكن حذف التصنيفات المحددة لأنها مرتبطة بـ $courses_count كورس");
                    }
                    
                    $stmt = $conn->prepare("DELETE FROM categories WHERE id IN ($placeholders)");
                    $stmt->execute($selected_categories);
                    
                    $success = 'تم حذف التصنيفات المحددة بنجاح';
                    logUserActivity($_SESSION['user_id'], 'حذف تصنيفات', "تم حذف " . count($selected_categories) . " تصنيف");
                }
                break;
        }
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// جلب التصنيفات مع إحصائيات
try {
    $search = $_GET['search'] ?? '';
    $sort = $_GET['sort'] ?? 'sort_order';
    $order = $_GET['order'] ?? 'ASC';
    $status_filter = $_GET['status'] ?? 'all';

    $where_conditions = [];
    $params = [];

    if ($search) {
        $where_conditions[] = "(c.name LIKE ? OR c.description LIKE ?)";
        $search_term = "%$search%";
        $params[] = $search_term;
        $params[] = $search_term;
    }

    if ($status_filter !== 'all') {
        $where_conditions[] = "c.status = ?";
        $params[] = $status_filter;
    }

    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

    // التأكد من أن العمود المطلوب للترتيب موجود
    $allowed_sort_columns = ['name', 'sort_order', 'created_at', 'courses_count'];
    if (!in_array($sort, $allowed_sort_columns)) {
        $sort = 'sort_order';
    }

    $stmt = $conn->prepare("
        SELECT c.*,
               COUNT(cc.course_id) as courses_count,
               COUNT(CASE WHEN co.status = 'active' THEN 1 END) as active_courses_count
        FROM categories c
        LEFT JOIN course_categories cc ON c.id = cc.category_id
        LEFT JOIN courses co ON cc.course_id = co.id
        $where_clause
        GROUP BY c.id, c.name, c.slug, c.description, c.icon, c.color, c.sort_order, c.status, c.created_at, c.updated_at
        ORDER BY $sort $order
    ");
    $stmt->execute($params);
    $categories = $stmt->fetchAll();

    // إحصائيات عامة
    $stats_stmt = $conn->query("
        SELECT
            COUNT(*) as total_categories,
            COUNT(CASE WHEN status = 'active' THEN 1 END) as active_categories,
            COUNT(CASE WHEN status = 'inactive' THEN 1 END) as inactive_categories,
            COUNT(CASE WHEN EXISTS(SELECT 1 FROM course_categories WHERE category_id = categories.id) THEN 1 END) as used_categories,
            (SELECT COUNT(*) FROM course_categories) as total_course_category_relations
        FROM categories
    ");
    $stats = $stats_stmt->fetch();

} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء جلب البيانات: ' . $e->getMessage();
    $categories = [];
    $stats = ['total_categories' => 0, 'active_categories' => 0, 'inactive_categories' => 0, 'used_categories' => 0, 'total_course_category_relations' => 0];
}

$pageTitle = 'إدارة التصنيفات';
$pageSubtitle = 'إدارة وتنظيم تصنيفات الكورسات';
require_once 'includes/admin-header.php';
?>

<div class="container-fluid py-4">
    <!-- الإحصائيات السريعة -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="admin-card">
                <div class="card-body text-center">
                    <div class="stat-icon bg-primary text-white mb-3">
                        <i class="fas fa-tags"></i>
                    </div>
                    <h3 class="mb-1"><?php echo number_format($stats['total_categories']); ?></h3>
                    <p class="text-muted mb-0">إجمالي التصنيفات</p>
                    <small class="text-success">
                        <i class="fas fa-check-circle me-1"></i>
                        <?php echo $stats['active_categories']; ?> نشط
                    </small>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="admin-card">
                <div class="card-body text-center">
                    <div class="stat-icon bg-success text-white mb-3">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <h3 class="mb-1"><?php echo number_format($stats['active_categories']); ?></h3>
                    <p class="text-muted mb-0">تصنيفات نشطة</p>
                    <small class="text-muted">
                        <i class="fas fa-eye me-1"></i>
                        ظاهرة للمستخدمين
                    </small>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="admin-card">
                <div class="card-body text-center">
                    <div class="stat-icon bg-warning text-white mb-3">
                        <i class="fas fa-book"></i>
                    </div>
                    <h3 class="mb-1"><?php echo number_format($stats['used_categories']); ?></h3>
                    <p class="text-muted mb-0">تصنيفات مستخدمة</p>
                    <small class="text-muted">
                        <i class="fas fa-link me-1"></i>
                        مرتبطة بكورسات
                    </small>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="admin-card">
                <div class="card-body text-center">
                    <div class="stat-icon bg-info text-white mb-3">
                        <i class="fas fa-link"></i>
                    </div>
                    <h3 class="mb-1"><?php echo number_format($stats['total_course_category_relations']); ?></h3>
                    <p class="text-muted mb-0">ارتباطات الكورسات</p>
                    <small class="text-muted">
                        <i class="fas fa-chart-line me-1"></i>
                        إجمالي الروابط
                    </small>
                </div>
            </div>
        </div>
    </div>

<?php if ($success): ?>
<div class="alert alert-success alert-dismissible fade show" data-aos="fade-down">
    <i class="fas fa-check-circle me-2"></i>
    <?php echo htmlspecialchars($success); ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<?php if ($error): ?>
<div class="alert alert-danger alert-dismissible fade show" data-aos="fade-down">
    <i class="fas fa-exclamation-circle me-2"></i>
    <?php echo htmlspecialchars($error); ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

    <!-- أدوات البحث والتصفية -->
    <div class="admin-card mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3 align-items-end">
                <div class="col-md-4">
                    <label class="form-label">البحث</label>
                    <input type="text" name="search" class="form-control"
                           placeholder="ابحث في التصنيفات..."
                           value="<?php echo htmlspecialchars($search); ?>">
                </div>
                <div class="col-md-3">
                    <label class="form-label">الحالة</label>
                    <select name="status" class="form-select">
                        <option value="all" <?php echo $status_filter === 'all' ? 'selected' : ''; ?>>جميع الحالات</option>
                        <option value="active" <?php echo $status_filter === 'active' ? 'selected' : ''; ?>>نشط</option>
                        <option value="inactive" <?php echo $status_filter === 'inactive' ? 'selected' : ''; ?>>غير نشط</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">ترتيب حسب</label>
                    <select name="sort" class="form-select">
                        <option value="sort_order" <?php echo $sort === 'sort_order' ? 'selected' : ''; ?>>ترتيب مخصص</option>
                        <option value="name" <?php echo $sort === 'name' ? 'selected' : ''; ?>>الاسم</option>
                        <option value="created_at" <?php echo $sort === 'created_at' ? 'selected' : ''; ?>>تاريخ الإنشاء</option>
                        <option value="courses_count" <?php echo $sort === 'courses_count' ? 'selected' : ''; ?>>عدد الكورسات</option>
                    </select>
                    <input type="hidden" name="order" value="<?php echo $order; ?>">
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i>
                        </button>
                        <?php if ($search || $status_filter !== 'all'): ?>
                        <a href="manage-categories.php" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i>
                        </a>
                        <?php endif; ?>
                    </div>
                </div>
            </form>

            <div class="row mt-3">
                <div class="col-md-12 text-end">
                    <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
                        <i class="fas fa-plus me-1"></i>
                        إضافة تصنيف جديد
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- جدول التصنيفات -->
    <div class="admin-card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h6 class="mb-0">
                    <i class="fas fa-tags me-2"></i>
                    قائمة التصنيفات (<?php echo count($categories); ?>)
                </h6>
            <div class="d-flex gap-2">
                <div class="dropdown">
                    <button class="btn btn-outline-secondary btn-sm dropdown-toggle" data-bs-toggle="dropdown">
                        <i class="fas fa-sort me-1"></i>
                        ترتيب حسب
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="?sort=name&order=ASC&<?php echo http_build_query($_GET); ?>">الاسم (أ-ي)</a></li>
                        <li><a class="dropdown-item" href="?sort=name&order=DESC&<?php echo http_build_query($_GET); ?>">الاسم (ي-أ)</a></li>
                        <li><a class="dropdown-item" href="?sort=courses_count&order=DESC&<?php echo http_build_query($_GET); ?>">الأكثر استخداماً</a></li>
                        <li><a class="dropdown-item" href="?sort=created_at&order=DESC&<?php echo http_build_query($_GET); ?>">الأحدث</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    <div class="card-body p-0">
        <?php if (empty($categories)): ?>
        <div class="text-center py-5">
            <i class="fas fa-tags fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد تصنيفات</h5>
            <p class="text-muted">ابدأ بإضافة تصنيف جديد لتنظيم الكورسات</p>
            <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
                <i class="fas fa-plus me-1"></i>
                إضافة تصنيف جديد
            </button>
        </div>
        <?php else: ?>
        <form id="bulkActionForm" method="POST">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th width="50">
                                <input type="checkbox" id="selectAll" class="form-check-input">
                            </th>
                            <th>التصنيف</th>
                            <th>الوصف</th>
                            <th>عدد الكورسات</th>
                            <th>تاريخ الإنشاء</th>
                            <th width="150">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($categories as $category): ?>
                        <tr>
                            <td>
                                <input type="checkbox" name="selected_categories[]" value="<?php echo $category['id']; ?>" class="form-check-input item-checkbox">
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="text-white rounded-circle d-flex align-items-center justify-content-center me-3"
                                         style="width: 40px; height: 40px; background-color: <?php echo $category['color'] ?? '#007bff'; ?>;">
                                        <i class="<?php echo $category['icon'] ?? 'fas fa-tag'; ?>"></i>
                                    </div>
                                    <div>
                                        <div class="fw-bold">
                                            <?php echo htmlspecialchars($category['name']); ?>
                                            <?php if (($category['status'] ?? 'active') === 'inactive'): ?>
                                                <span class="badge bg-secondary ms-2">غير نشط</span>
                                            <?php endif; ?>
                                        </div>
                                        <small class="text-muted"><?php echo htmlspecialchars($category['slug']); ?></small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="text-muted">
                                    <?php echo $category['description'] ? htmlspecialchars(substr($category['description'], 0, 100)) . '...' : 'لا يوجد وصف'; ?>
                                </span>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <span class="badge bg-primary me-2"><?php echo $category['courses_count']; ?></span>
                                    <?php if ($category['active_courses_count'] > 0): ?>
                                    <small class="text-success">
                                        <i class="fas fa-check-circle me-1"></i>
                                        <?php echo $category['active_courses_count']; ?> نشط
                                    </small>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td>
                                <small class="text-muted">
                                    <?php echo date('Y-m-d', strtotime($category['created_at'])); ?>
                                </small>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button class="btn btn-sm btn-outline-primary" 
                                            data-bs-toggle="modal" 
                                            data-bs-target="#editCategoryModal<?php echo $category['id']; ?>">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    
                                    <?php if ($category['courses_count'] == 0): ?>
                                    <form method="POST" class="d-inline">
                                        <input type="hidden" name="action" value="delete_category">
                                        <input type="hidden" name="category_id" value="<?php echo $category['id']; ?>">
                                        <button type="submit" class="btn btn-sm btn-outline-danger"
                                                onclick="return confirm('هل أنت متأكد من حذف هذا التصنيف؟')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                    <?php else: ?>
                                    <button class="btn btn-sm btn-outline-secondary" disabled title="لا يمكن حذف تصنيف مرتبط بكورسات">
                                        <i class="fas fa-lock"></i>
                                    </button>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            
            <!-- الإجراءات المجمعة -->
            <div class="card-footer" id="bulkActionBtn" style="display: none;">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <select name="bulk_action" class="form-select d-inline-block w-auto">
                            <option value="">اختر إجراء...</option>
                            <option value="bulk_delete">حذف المحدد</option>
                        </select>
                    </div>
                    <button type="submit" name="action" value="bulk_delete" class="btn btn-danger"
                            onclick="return confirm('هل أنت متأكد من حذف التصنيفات المحددة؟ سيتم حذف التصنيفات غير المرتبطة بكورسات فقط.')">
                        تنفيذ الإجراء
                    </button>
                </div>
            </div>
        </form>
        <?php endif; ?>
    </div>
</div>
</div>

<!-- مودال إضافة تصنيف -->
<div class="modal fade" id="addCategoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus me-2"></i>
                    إضافة تصنيف جديد
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="add_category">

                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label class="form-label">اسم التصنيف *</label>
                                <input type="text" name="name" class="form-control" required
                                       placeholder="مثال: البرمجة">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">اللون</label>
                                <input type="color" name="color" class="form-control form-control-color"
                                       value="#007bff" title="اختر لون التصنيف">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">الأيقونة</label>
                        <select name="icon" class="form-select">
                            <option value="fas fa-tag">🏷️ تصنيف عام</option>
                            <option value="fas fa-code">💻 برمجة</option>
                            <option value="fas fa-paint-brush">🎨 تصميم</option>
                            <option value="fas fa-bullhorn">📢 تسويق</option>
                            <option value="fas fa-briefcase">💼 أعمال</option>
                            <option value="fas fa-language">🌐 لغات</option>
                            <option value="fas fa-flask">🧪 علوم</option>
                            <option value="fas fa-microchip">🔧 تكنولوجيا</option>
                            <option value="fas fa-heartbeat">❤️ صحة</option>
                            <option value="fas fa-book">📚 تعليم</option>
                            <option value="fas fa-music">🎵 فنون</option>
                            <option value="fas fa-camera">📷 تصوير</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">الوصف</label>
                        <textarea name="description" class="form-control" rows="3"
                                  placeholder="وصف مختصر للتصنيف..."></textarea>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        سيتم إنشاء الرابط (slug) تلقائياً من اسم التصنيف
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-plus me-1"></i>
                        إضافة التصنيف
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- مودالات تعديل التصنيفات -->
<?php foreach ($categories as $category): ?>
<div class="modal fade" id="editCategoryModal<?php echo $category['id']; ?>" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-edit me-2"></i>
                    تعديل التصنيف
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="edit_category">
                    <input type="hidden" name="category_id" value="<?php echo $category['id']; ?>">

                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label class="form-label">اسم التصنيف *</label>
                                <input type="text" name="name" class="form-control" required
                                       value="<?php echo htmlspecialchars($category['name']); ?>">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">اللون</label>
                                <input type="color" name="color" class="form-control form-control-color"
                                       value="<?php echo $category['color'] ?? '#007bff'; ?>" title="اختر لون التصنيف">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">الأيقونة</label>
                        <select name="icon" class="form-select">
                            <option value="fas fa-tag" <?php echo ($category['icon'] ?? '') === 'fas fa-tag' ? 'selected' : ''; ?>>🏷️ تصنيف عام</option>
                            <option value="fas fa-code" <?php echo ($category['icon'] ?? '') === 'fas fa-code' ? 'selected' : ''; ?>>💻 برمجة</option>
                            <option value="fas fa-paint-brush" <?php echo ($category['icon'] ?? '') === 'fas fa-paint-brush' ? 'selected' : ''; ?>>🎨 تصميم</option>
                            <option value="fas fa-bullhorn" <?php echo ($category['icon'] ?? '') === 'fas fa-bullhorn' ? 'selected' : ''; ?>>📢 تسويق</option>
                            <option value="fas fa-briefcase" <?php echo ($category['icon'] ?? '') === 'fas fa-briefcase' ? 'selected' : ''; ?>>💼 أعمال</option>
                            <option value="fas fa-language" <?php echo ($category['icon'] ?? '') === 'fas fa-language' ? 'selected' : ''; ?>>🌐 لغات</option>
                            <option value="fas fa-flask" <?php echo ($category['icon'] ?? '') === 'fas fa-flask' ? 'selected' : ''; ?>>🧪 علوم</option>
                            <option value="fas fa-microchip" <?php echo ($category['icon'] ?? '') === 'fas fa-microchip' ? 'selected' : ''; ?>>🔧 تكنولوجيا</option>
                            <option value="fas fa-heartbeat" <?php echo ($category['icon'] ?? '') === 'fas fa-heartbeat' ? 'selected' : ''; ?>>❤️ صحة</option>
                            <option value="fas fa-book" <?php echo ($category['icon'] ?? '') === 'fas fa-book' ? 'selected' : ''; ?>>📚 تعليم</option>
                            <option value="fas fa-music" <?php echo ($category['icon'] ?? '') === 'fas fa-music' ? 'selected' : ''; ?>>🎵 فنون</option>
                            <option value="fas fa-camera" <?php echo ($category['icon'] ?? '') === 'fas fa-camera' ? 'selected' : ''; ?>>📷 تصوير</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">الوصف</label>
                        <textarea name="description" class="form-control" rows="3"><?php echo htmlspecialchars($category['description'] ?? ''); ?></textarea>
                    </div>

                    <?php if ($category['courses_count'] > 0): ?>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        هذا التصنيف مرتبط بـ <?php echo $category['courses_count']; ?> كورس
                    </div>
                    <?php endif; ?>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i>
                        حفظ التغييرات
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php endforeach; ?>

<style>
.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    font-size: 24px;
}
</style>

<script>
// تحديد/إلغاء تحديد جميع العناصر
document.getElementById('selectAll').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.item-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
    toggleBulkActions();
});

// إظهار/إخفاء أزرار الإجراءات المجمعة
function toggleBulkActions() {
    const checkedBoxes = document.querySelectorAll('.item-checkbox:checked');
    const bulkActionBtn = document.getElementById('bulkActionBtn');

    if (checkedBoxes.length > 0) {
        bulkActionBtn.style.display = 'block';
    } else {
        bulkActionBtn.style.display = 'none';
    }
}

// إضافة مستمع للأحداث لجميع checkboxes
document.querySelectorAll('.item-checkbox').forEach(checkbox => {
    checkbox.addEventListener('change', toggleBulkActions);
});
</script>

<?php require_once 'includes/admin-footer.php'; ?>
