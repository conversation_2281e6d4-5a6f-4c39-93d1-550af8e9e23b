<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات المدرب
if (!isLoggedIn() || !isInstructor()) {
    header('Location: ../login.php');
    exit;
}

$pageTitle = 'الإشعارات';
$breadcrumbs = [
    ['title' => 'الإشعارات']
];

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'mark_read') {
        $notification_id = $_POST['notification_id'] ?? 0;
        
        try {
            $stmt = $conn->prepare("
                UPDATE notifications 
                SET is_read = 1, read_at = NOW() 
                WHERE id = ? AND user_id = ?
            ");
            $stmt->execute([$notification_id, $_SESSION['user_id']]);
            
            echo json_encode(['success' => true]);
            exit;
        } catch (PDOException $e) {
            echo json_encode(['success' => false, 'error' => 'حدث خطأ']);
            exit;
        }
    }
    
    if ($action === 'mark_all_read') {
        try {
            $stmt = $conn->prepare("
                UPDATE notifications 
                SET is_read = 1, read_at = NOW() 
                WHERE user_id = ? AND is_read = 0
            ");
            $stmt->execute([$_SESSION['user_id']]);
            
            $success_message = 'تم تحديد جميع الإشعارات كمقروءة';
        } catch (PDOException $e) {
            $error_message = 'حدث خطأ أثناء تحديث الإشعارات';
        }
    }
    
    if ($action === 'delete_notification') {
        $notification_id = $_POST['notification_id'] ?? 0;
        
        try {
            $stmt = $conn->prepare("
                DELETE FROM notifications 
                WHERE id = ? AND user_id = ?
            ");
            $stmt->execute([$notification_id, $_SESSION['user_id']]);
            
            $success_message = 'تم حذف الإشعار بنجاح';
        } catch (PDOException $e) {
            $error_message = 'حدث خطأ أثناء حذف الإشعار';
        }
    }
}

// فلاتر البحث
$filter = $_GET['filter'] ?? 'all';
$type_filter = $_GET['type'] ?? '';

// جلب الإشعارات
try {
    $where_conditions = ["user_id = ?"];
    $params = [$_SESSION['user_id']];
    
    if ($filter === 'unread') {
        $where_conditions[] = "is_read = 0";
    } elseif ($filter === 'read') {
        $where_conditions[] = "is_read = 1";
    }
    
    if (!empty($type_filter)) {
        $where_conditions[] = "type = ?";
        $params[] = $type_filter;
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    $stmt = $conn->prepare("
        SELECT * FROM notifications 
        WHERE $where_clause
        ORDER BY created_at DESC
        LIMIT 50
    ");
    $stmt->execute($params);
    $notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // إحصائيات الإشعارات
    $stmt = $conn->prepare("
        SELECT 
            COUNT(*) as total_notifications,
            COUNT(CASE WHEN is_read = 0 THEN 1 END) as unread_count,
            COUNT(CASE WHEN is_read = 1 THEN 1 END) as read_count,
            COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 END) as today_count
        FROM notifications 
        WHERE user_id = ?
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
} catch (PDOException $e) {
    $error_message = 'حدث خطأ أثناء جلب الإشعارات';
    $notifications = [];
    $stats = ['total_notifications' => 0, 'unread_count' => 0, 'read_count' => 0, 'today_count' => 0];
}

include 'includes/header.php';
?>

<!-- رأس الصفحة -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="h3 mb-2">
            <i class="fas fa-bell text-warning me-2"></i>
            الإشعارات
        </h2>
        <p class="text-muted mb-0">متابعة جميع الإشعارات والتحديثات المهمة</p>
    </div>
    <div class="d-flex gap-2">
        <?php if ($stats['unread_count'] > 0): ?>
        <form method="POST" class="d-inline">
            <input type="hidden" name="action" value="mark_all_read">
            <button type="submit" class="btn btn-outline-primary">
                <i class="fas fa-check-double me-1"></i>تحديد الكل كمقروء
            </button>
        </form>
        <?php endif; ?>
        <button class="btn btn-primary" onclick="refreshNotifications()">
            <i class="fas fa-sync-alt me-1"></i>تحديث
        </button>
    </div>
</div>

<!-- رسائل النجاح والخطأ -->
<?php if (isset($success_message)): ?>
<div class="alert alert-success alert-dismissible fade show" role="alert">
    <i class="fas fa-check-circle me-2"></i>
    <?php echo $success_message; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<?php if (isset($error_message)): ?>
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <i class="fas fa-exclamation-triangle me-2"></i>
    <?php echo $error_message; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<!-- إحصائيات الإشعارات -->
<div class="row g-4 mb-4">
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-primary bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-bell text-primary fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h5 class="mb-1"><?php echo $stats['total_notifications']; ?></h5>
                        <p class="text-muted mb-0">إجمالي الإشعارات</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-warning bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-exclamation-circle text-warning fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h5 class="mb-1"><?php echo $stats['unread_count']; ?></h5>
                        <p class="text-muted mb-0">غير مقروءة</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-success bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-check-circle text-success fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h5 class="mb-1"><?php echo $stats['read_count']; ?></h5>
                        <p class="text-muted mb-0">مقروءة</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-info bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-calendar-day text-info fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h5 class="mb-1"><?php echo $stats['today_count']; ?></h5>
                        <p class="text-muted mb-0">اليوم</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- فلاتر الإشعارات -->
<div class="card border-0 shadow-sm mb-4">
    <div class="card-body">
        <div class="row g-3 align-items-end">
            <div class="col-md-4">
                <label for="filter" class="form-label">حالة القراءة</label>
                <select id="filter" class="form-select" onchange="applyFilters()">
                    <option value="all" <?php echo $filter === 'all' ? 'selected' : ''; ?>>جميع الإشعارات</option>
                    <option value="unread" <?php echo $filter === 'unread' ? 'selected' : ''; ?>>غير مقروءة</option>
                    <option value="read" <?php echo $filter === 'read' ? 'selected' : ''; ?>>مقروءة</option>
                </select>
            </div>
            <div class="col-md-4">
                <label for="type" class="form-label">نوع الإشعار</label>
                <select id="type" class="form-select" onchange="applyFilters()">
                    <option value="">جميع الأنواع</option>
                    <option value="enrollment" <?php echo $type_filter === 'enrollment' ? 'selected' : ''; ?>>طلبات التسجيل</option>
                    <option value="assignment" <?php echo $type_filter === 'assignment' ? 'selected' : ''; ?>>الواجبات</option>
                    <option value="quiz" <?php echo $type_filter === 'quiz' ? 'selected' : ''; ?>>الاختبارات</option>
                    <option value="message" <?php echo $type_filter === 'message' ? 'selected' : ''; ?>>الرسائل</option>
                    <option value="system" <?php echo $type_filter === 'system' ? 'selected' : ''; ?>>النظام</option>
                </select>
            </div>
            <div class="col-md-4">
                <button class="btn btn-outline-secondary w-100" onclick="clearFilters()">
                    <i class="fas fa-undo me-1"></i>إعادة تعيين
                </button>
            </div>
        </div>
    </div>
</div>

<!-- قائمة الإشعارات -->
<div class="card border-0 shadow-sm">
    <div class="card-header bg-white border-bottom">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>
            قائمة الإشعارات
        </h5>
    </div>
    <div class="card-body p-0">
        <?php if (empty($notifications)): ?>
        <div class="text-center py-5">
            <i class="fas fa-bell-slash text-muted" style="font-size: 3rem;"></i>
            <h5 class="mt-3 text-muted">لا توجد إشعارات</h5>
            <p class="text-muted">ستظهر الإشعارات هنا عند وجود تحديثات جديدة</p>
        </div>
        <?php else: ?>
        <div class="notifications-list">
            <?php foreach ($notifications as $notification): ?>
            <div class="notification-item border-bottom p-3 <?php echo !$notification['is_read'] ? 'bg-light' : ''; ?>" 
                 data-notification-id="<?php echo $notification['id']; ?>">
                <div class="d-flex align-items-start">
                    <div class="flex-shrink-0 me-3">
                        <?php
                        $icon_classes = [
                            'enrollment' => 'fas fa-user-plus text-primary',
                            'assignment' => 'fas fa-tasks text-info',
                            'quiz' => 'fas fa-question-circle text-warning',
                            'message' => 'fas fa-envelope text-success',
                            'system' => 'fas fa-cog text-secondary'
                        ];
                        $icon = $icon_classes[$notification['type']] ?? 'fas fa-bell text-muted';
                        ?>
                        <div class="notification-icon bg-white rounded-circle p-2 shadow-sm">
                            <i class="<?php echo $icon; ?>"></i>
                        </div>
                    </div>
                    
                    <div class="flex-grow-1">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <h6 class="mb-0 <?php echo !$notification['is_read'] ? 'fw-bold' : ''; ?>">
                                <?php echo htmlspecialchars($notification['title']); ?>
                            </h6>
                            <div class="d-flex align-items-center gap-2">
                                <?php if (!$notification['is_read']): ?>
                                <span class="badge bg-primary">جديد</span>
                                <?php endif; ?>
                                <small class="text-muted">
                                    <?php echo timeAgo($notification['created_at']); ?>
                                </small>
                            </div>
                        </div>
                        
                        <p class="text-muted mb-2"><?php echo htmlspecialchars($notification['message']); ?></p>
                        
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <?php
                                $type_labels = [
                                    'enrollment' => 'طلب تسجيل',
                                    'assignment' => 'واجب',
                                    'quiz' => 'اختبار',
                                    'message' => 'رسالة',
                                    'system' => 'نظام'
                                ];
                                ?>
                                <span class="badge bg-secondary">
                                    <?php echo $type_labels[$notification['type']] ?? $notification['type']; ?>
                                </span>
                            </div>
                            
                            <div class="btn-group" role="group">
                                <?php if (!$notification['is_read']): ?>
                                <button class="btn btn-sm btn-outline-primary" 
                                        onclick="markAsRead(<?php echo $notification['id']; ?>)" title="تحديد كمقروء">
                                    <i class="fas fa-check"></i>
                                </button>
                                <?php endif; ?>
                                
                                <?php if ($notification['action_url']): ?>
                                <a href="<?php echo htmlspecialchars($notification['action_url']); ?>" 
                                   class="btn btn-sm btn-outline-success" title="عرض">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <?php endif; ?>
                                
                                <button class="btn btn-sm btn-outline-danger" 
                                        onclick="deleteNotification(<?php echo $notification['id']; ?>)" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>
    </div>
</div>

<script>
// تحديد إشعار كمقروء
function markAsRead(notificationId) {
    const formData = new FormData();
    formData.append('action', 'mark_read');
    formData.append('notification_id', notificationId);

    fetch(window.location.href, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const notificationItem = document.querySelector(`[data-notification-id="${notificationId}"]`);
            if (notificationItem) {
                notificationItem.classList.remove('bg-light');
                notificationItem.querySelector('.badge.bg-primary')?.remove();
                notificationItem.querySelector('.fw-bold')?.classList.remove('fw-bold');
                notificationItem.querySelector('.btn-outline-primary')?.remove();
            }

            // تحديث العدادات
            updateNotificationCounts();
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ أثناء تحديث الإشعار');
    });
}

// حذف إشعار
function deleteNotification(notificationId) {
    if (!confirm('هل أنت متأكد من حذف هذا الإشعار؟')) {
        return;
    }

    const formData = new FormData();
    formData.append('action', 'delete_notification');
    formData.append('notification_id', notificationId);

    fetch(window.location.href, {
        method: 'POST',
        body: formData
    })
    .then(response => response.text())
    .then(() => {
        const notificationItem = document.querySelector(`[data-notification-id="${notificationId}"]`);
        if (notificationItem) {
            notificationItem.style.transition = 'opacity 0.3s ease';
            notificationItem.style.opacity = '0';
            setTimeout(() => {
                notificationItem.remove();
                updateNotificationCounts();
            }, 300);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ أثناء حذف الإشعار');
    });
}

// تطبيق الفلاتر
function applyFilters() {
    const filter = document.getElementById('filter').value;
    const type = document.getElementById('type').value;

    const url = new URL(window.location.href);
    url.searchParams.set('filter', filter);
    if (type) {
        url.searchParams.set('type', type);
    } else {
        url.searchParams.delete('type');
    }

    window.location.href = url.toString();
}

// إعادة تعيين الفلاتر
function clearFilters() {
    const url = new URL(window.location.href);
    url.searchParams.delete('filter');
    url.searchParams.delete('type');
    window.location.href = url.toString();
}

// تحديث الإشعارات
function refreshNotifications() {
    window.location.reload();
}

// تحديث عدادات الإشعارات
function updateNotificationCounts() {
    // يمكن تنفيذ هذا عبر AJAX لتحديث العدادات دون إعادة تحميل الصفحة
    setTimeout(() => {
        window.location.reload();
    }, 1000);
}

// تحديث تلقائي للإشعارات كل 30 ثانية
setInterval(function() {
    // يمكن إضافة AJAX لجلب الإشعارات الجديدة
    console.log('تحقق من الإشعارات الجديدة...');
}, 30000);

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // إضافة تأثيرات بصرية للإشعارات
    const notificationItems = document.querySelectorAll('.notification-item');
    notificationItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            if (!this.classList.contains('bg-light')) {
                this.style.backgroundColor = 'rgba(0,123,255,0.05)';
            }
        });

        item.addEventListener('mouseleave', function() {
            if (!this.classList.contains('bg-light')) {
                this.style.backgroundColor = '';
            }
        });

        // تحديد الإشعار كمقروء عند النقر عليه
        item.addEventListener('click', function(e) {
            if (!e.target.closest('.btn-group')) {
                const notificationId = this.dataset.notificationId;
                const isUnread = this.classList.contains('bg-light');

                if (isUnread) {
                    markAsRead(notificationId);
                }
            }
        });
    });

    // تحسين الأزرار
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(button => {
        button.addEventListener('click', function() {
            if (this.type === 'submit') {
                this.disabled = true;
                this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري التحديث...';
            }
        });
    });
});

// دالة مساعدة لحساب الوقت المنقضي
function timeAgo(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);

    if (diffInSeconds < 60) {
        return 'الآن';
    } else if (diffInSeconds < 3600) {
        const minutes = Math.floor(diffInSeconds / 60);
        return `منذ ${minutes} دقيقة`;
    } else if (diffInSeconds < 86400) {
        const hours = Math.floor(diffInSeconds / 3600);
        return `منذ ${hours} ساعة`;
    } else {
        const days = Math.floor(diffInSeconds / 86400);
        return `منذ ${days} يوم`;
    }
}
</script>

<style>
.notification-item {
    transition: all 0.3s ease;
    cursor: pointer;
}

.notification-item:hover {
    transform: translateX(-2px);
}

.notification-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.notifications-list .notification-item:last-child {
    border-bottom: none !important;
}

.badge {
    font-size: 0.75rem;
}

.btn-group .btn {
    border-radius: 0.375rem;
    margin-left: 0.25rem;
}

.card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1) !important;
}

/* تحسين عرض الإشعارات غير المقروءة */
.notification-item.bg-light {
    border-right: 4px solid var(--bs-primary);
}

/* تحسين الأيقونات */
.notification-icon i {
    font-size: 1.2rem;
}

/* تحسين العرض على الأجهزة المحمولة */
@media (max-width: 768px) {
    .notification-item {
        padding: 1rem !important;
    }

    .notification-icon {
        width: 35px;
        height: 35px;
    }

    .notification-icon i {
        font-size: 1rem;
    }

    .btn-group {
        flex-direction: column;
        width: 100%;
    }

    .btn-group .btn {
        margin-left: 0;
        margin-bottom: 0.25rem;
        border-radius: 0.375rem;
    }

    .d-flex.justify-content-between {
        flex-direction: column;
        align-items: flex-start !important;
    }

    .d-flex.justify-content-between > div:last-child {
        margin-top: 0.5rem;
        width: 100%;
    }
}

/* تحسين الألوان */
.text-muted {
    color: #6c757d !important;
}

.bg-light {
    background-color: rgba(13, 110, 253, 0.05) !important;
}

/* تحسين التمرير */
.notifications-list {
    max-height: 600px;
    overflow-y: auto;
}

.notifications-list::-webkit-scrollbar {
    width: 6px;
}

.notifications-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.notifications-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.notifications-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
</style>

<?php
// دالة مساعدة لحساب الوقت المنقضي
function timeAgo($datetime) {
    $time = time() - strtotime($datetime);

    if ($time < 60) {
        return 'الآن';
    } elseif ($time < 3600) {
        return 'منذ ' . floor($time/60) . ' دقيقة';
    } elseif ($time < 86400) {
        return 'منذ ' . floor($time/3600) . ' ساعة';
    } elseif ($time < 2592000) {
        return 'منذ ' . floor($time/86400) . ' يوم';
    } else {
        return date('Y-m-d', strtotime($datetime));
    }
}

include 'includes/footer.php';
?>
