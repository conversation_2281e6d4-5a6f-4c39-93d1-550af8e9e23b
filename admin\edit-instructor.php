<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات المدير
if (!isLoggedIn() || !isAdmin()) {
    header('Location: ../login.php');
    exit;
}

// التحقق من وجود معرف المدرب
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: manage-instructors.php');
    exit;
}

$instructor_id = (int)$_GET['id'];
$success_message = '';
$error_message = '';

// معالجة تحديث بيانات المدرب
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = trim($_POST['name'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $phone = trim($_POST['phone'] ?? '');
    $specialization = trim($_POST['specialization'] ?? '');
    $experience_years = (int)($_POST['experience_years'] ?? 0);
    $bio = trim($_POST['bio'] ?? '');
    $status = $_POST['status'] ?? 'active';
    
    // التحقق من صحة البيانات
    if (empty($name) || empty($email)) {
        $error_message = 'الاسم والبريد الإلكتروني مطلوبان';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error_message = 'البريد الإلكتروني غير صحيح';
    } else {
        try {
            // التحقق من عدم تكرار البريد الإلكتروني
            $stmt = $conn->prepare("SELECT id FROM users WHERE email = ? AND id != ?");
            $stmt->execute([$email, $instructor_id]);
            if ($stmt->fetch()) {
                $error_message = 'البريد الإلكتروني مستخدم من قبل مستخدم آخر';
            } else {
                // معالجة رفع الصورة
                $profile_image = '';
                if (isset($_FILES['profile_image']) && $_FILES['profile_image']['error'] === UPLOAD_ERR_OK) {
                    $upload_dir = '../uploads/profiles/';
                    if (!is_dir($upload_dir)) {
                        mkdir($upload_dir, 0755, true);
                    }
                    
                    $file_extension = strtolower(pathinfo($_FILES['profile_image']['name'], PATHINFO_EXTENSION));
                    $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif'];
                    
                    if (in_array($file_extension, $allowed_extensions)) {
                        $new_filename = 'instructor_' . $instructor_id . '_' . time() . '.' . $file_extension;
                        $profile_image = 'uploads/profiles/' . $new_filename;
                        
                        if (move_uploaded_file($_FILES['profile_image']['tmp_name'], '../' . $profile_image)) {
                            // حذف الصورة القديمة إذا كانت موجودة
                            $old_image = $conn->query("SELECT profile_image FROM users WHERE id = $instructor_id")->fetchColumn();
                            if ($old_image && file_exists('../' . $old_image)) {
                                unlink('../' . $old_image);
                            }
                        } else {
                            $error_message = 'فشل في رفع الصورة';
                        }
                    } else {
                        $error_message = 'نوع الملف غير مدعوم. يرجى رفع صورة بصيغة JPG, PNG أو GIF';
                    }
                }
                
                if (empty($error_message)) {
                    // تحديث بيانات المدرب
                    $sql = "UPDATE users SET name = ?, email = ?, phone = ?, specialization = ?, experience_years = ?, bio = ?, status = ?, updated_at = CURRENT_TIMESTAMP";
                    $params = [$name, $email, $phone, $specialization, $experience_years, $bio, $status];
                    
                    if ($profile_image) {
                        $sql .= ", profile_image = ?";
                        $params[] = $profile_image;
                    }
                    
                    $sql .= " WHERE id = ?";
                    $params[] = $instructor_id;
                    
                    $stmt = $conn->prepare($sql);
                    $stmt->execute($params);
                    
                    // تسجيل النشاط
                    logUserActivity($_SESSION['user_id'], 'تعديل مدرب', "تم تعديل بيانات المدرب: $name");
                    
                    $success_message = 'تم تحديث بيانات المدرب بنجاح';
                }
            }
        } catch (Exception $e) {
            $error_message = 'حدث خطأ أثناء تحديث البيانات: ' . $e->getMessage();
        }
    }
}

// جلب بيانات المدرب
try {
    $stmt = $conn->prepare("
        SELECT u.*, 
        (SELECT COUNT(*) FROM courses WHERE instructor_id = u.id) as courses_count,
        (SELECT COUNT(DISTINCT ce.student_id) FROM course_enrollments ce JOIN courses c ON ce.course_id = c.id WHERE c.instructor_id = u.id) as students_count
        FROM users u
        WHERE u.id = ? AND u.role = 'instructor'
    ");
    $stmt->execute([$instructor_id]);
    $instructor = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$instructor) {
        header('Location: manage-instructors.php');
        exit;
    }
    
} catch (Exception $e) {
    $error_message = 'حدث خطأ أثناء جلب بيانات المدرب';
}

$pageTitle = 'تعديل المدرب: ' . $instructor['name'];
$pageSubtitle = 'تعديل معلومات وإعدادات المدرب';
require_once 'includes/admin-header.php';
?>

<div class="container-fluid py-4">
    <!-- رسائل النجاح والخطأ -->
    <?php if ($success_message): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>
    
    <?php if ($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <div class="col-lg-8">
            <!-- نموذج تعديل المدرب -->
            <div class="admin-card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-user-edit me-2"></i>تعديل معلومات المدرب</h6>
                </div>
                <div class="card-body">
                    <form method="POST" enctype="multipart/form-data" id="editInstructorForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       value="<?php echo htmlspecialchars($instructor['name']); ?>" required>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">البريد الإلكتروني <span class="text-danger">*</span></label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="<?php echo htmlspecialchars($instructor['email']); ?>" required>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" id="phone" name="phone" 
                                       value="<?php echo htmlspecialchars($instructor['phone'] ?? ''); ?>">
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="specialization" class="form-label">التخصص</label>
                                <input type="text" class="form-control" id="specialization" name="specialization" 
                                       value="<?php echo htmlspecialchars($instructor['specialization'] ?? ''); ?>"
                                       placeholder="مثال: تطوير الويب، التسويق الرقمي">
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="experience_years" class="form-label">سنوات الخبرة</label>
                                <input type="number" class="form-control" id="experience_years" name="experience_years" 
                                       value="<?php echo $instructor['experience_years'] ?? 0; ?>" min="0" max="50">
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="status" class="form-label">حالة المدرب</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="active" <?php echo $instructor['status'] === 'active' ? 'selected' : ''; ?>>نشط</option>
                                    <option value="inactive" <?php echo $instructor['status'] === 'inactive' ? 'selected' : ''; ?>>غير نشط</option>
                                    <option value="pending" <?php echo $instructor['status'] === 'pending' ? 'selected' : ''; ?>>قيد المراجعة</option>
                                    <option value="suspended" <?php echo $instructor['status'] === 'suspended' ? 'selected' : ''; ?>>موقوف</option>
                                </select>
                            </div>
                            
                            <div class="col-md-12 mb-3">
                                <label for="bio" class="form-label">نبذة عن المدرب</label>
                                <textarea class="form-control" id="bio" name="bio" rows="4" 
                                          placeholder="اكتب نبذة مختصرة عن المدرب وخبراته..."><?php echo htmlspecialchars($instructor['bio'] ?? ''); ?></textarea>
                            </div>
                            
                            <div class="col-md-12 mb-3">
                                <label for="profile_image" class="form-label">صورة المدرب</label>
                                <input type="file" class="form-control" id="profile_image" name="profile_image" 
                                       accept="image/*" onchange="previewImage(this)">
                                <small class="text-muted">اترك فارغاً للاحتفاظ بالصورة الحالية</small>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>حفظ التغييرات
                            </button>
                            <div>
                                <a href="instructor-details.php?id=<?php echo $instructor_id; ?>" class="btn btn-info me-2">
                                    <i class="fas fa-eye me-2"></i>عرض التفاصيل
                                </a>
                                <a href="manage-instructors.php" class="btn btn-secondary">
                                    <i class="fas fa-arrow-right me-2"></i>العودة للقائمة
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <!-- معاينة الصورة الحالية -->
            <div class="admin-card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-image me-2"></i>صورة المدرب</h6>
                </div>
                <div class="card-body text-center">
                    <div id="imagePreview">
                        <?php if ($instructor['profile_image'] && file_exists('../' . $instructor['profile_image'])): ?>
                            <img src="../<?php echo $instructor['profile_image']; ?>" alt="صورة المدرب" 
                                 class="img-fluid rounded-circle" style="max-height: 200px; max-width: 200px;">
                        <?php else: ?>
                            <div class="bg-light rounded-circle p-4 mx-auto" style="width: 200px; height: 200px; display: flex; align-items: center; justify-content: center;">
                                <i class="fas fa-user fa-4x text-muted"></i>
                            </div>
                            <p class="text-muted mt-2">لا توجد صورة</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <!-- إحصائيات سريعة -->
            <div class="admin-card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-chart-bar me-2"></i>إحصائيات سريعة</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <h4 class="text-primary"><?php echo $instructor['courses_count']; ?></h4>
                            <small class="text-muted">الكورسات</small>
                        </div>
                        <div class="col-6 mb-3">
                            <h4 class="text-success"><?php echo $instructor['students_count']; ?></h4>
                            <small class="text-muted">الطلاب</small>
                        </div>
                        <div class="col-12">
                            <small class="text-muted">
                                <i class="fas fa-calendar me-1"></i>
                                انضم في <?php echo date('Y-m-d', strtotime($instructor['created_at'])); ?>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- إجراءات سريعة -->
            <div class="admin-card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-bolt me-2"></i>إجراءات سريعة</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="instructor-details.php?id=<?php echo $instructor_id; ?>" class="btn btn-outline-info">
                            <i class="fas fa-eye me-2"></i>عرض التفاصيل
                        </a>
                        <button class="btn btn-outline-warning" onclick="resetPassword(<?php echo $instructor_id; ?>)">
                            <i class="fas fa-key me-2"></i>إعادة تعيين كلمة المرور
                        </button>
                        <button class="btn btn-outline-danger" onclick="deleteInstructor(<?php echo $instructor_id; ?>, '<?php echo htmlspecialchars($instructor['name']); ?>')">
                            <i class="fas fa-trash me-2"></i>حذف المدرب
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// معاينة الصورة قبل الرفع
function previewImage(input) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('imagePreview').innerHTML = 
                `<img src="${e.target.result}" alt="معاينة الصورة" class="img-fluid rounded-circle" style="max-height: 200px; max-width: 200px;">`;
        };
        reader.readAsDataURL(input.files[0]);
    }
}

// إعادة تعيين كلمة المرور
function resetPassword(instructorId) {
    Swal.fire({
        title: 'إعادة تعيين كلمة المرور',
        text: 'هل أنت متأكد من إعادة تعيين كلمة المرور؟',
        icon: 'question',
        showCancelButton: true,
        confirmButtonText: 'نعم، أعد التعيين',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            // تنفيذ إعادة تعيين كلمة المرور
            showToast('تم إعادة تعيين كلمة المرور بنجاح', 'success');
        }
    });
}

// حذف المدرب
function deleteInstructor(instructorId, instructorName) {
    Swal.fire({
        title: 'حذف المدرب',
        html: `
            <p>هل أنت متأكد من حذف المدرب <strong>${instructorName}</strong>؟</p>
            <div class="alert alert-warning text-start">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>تحذير:</strong> سيتم حذف جميع البيانات المرتبطة بهذا المدرب
            </div>
        `,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'نعم، احذف المدرب',
        cancelButtonText: 'إلغاء',
        confirmButtonColor: '#dc3545'
    }).then((result) => {
        if (result.isConfirmed) {
            // تنفيذ حذف المدرب
            window.location.href = 'manage-instructors.php';
        }
    });
}

// التحقق من صحة النموذج
document.getElementById('editInstructorForm').addEventListener('submit', function(e) {
    const name = document.getElementById('name').value.trim();
    const email = document.getElementById('email').value.trim();
    
    if (!name || !email) {
        e.preventDefault();
        Swal.fire('خطأ', 'الاسم والبريد الإلكتروني مطلوبان', 'error');
        return false;
    }
    
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
        e.preventDefault();
        Swal.fire('خطأ', 'البريد الإلكتروني غير صحيح', 'error');
        return false;
    }
});
</script>

<?php require_once 'includes/admin-footer.php'; ?>
