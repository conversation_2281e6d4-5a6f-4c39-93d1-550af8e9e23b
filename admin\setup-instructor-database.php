<?php
require_once '../config/database.php';

echo "<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>إعداد قاعدة بيانات المدربين</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css' rel='stylesheet'>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f8f9fa; }
        .container { max-width: 1000px; margin: 30px auto; }
        .log-item { padding: 8px; margin: 3px 0; border-radius: 5px; font-size: 14px; }
        .log-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .log-error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .log-info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .log-warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
    </style>
</head>
<body>
<div class='container'>
    <div class='card'>
        <div class='card-header bg-primary text-white'>
            <h4 class='mb-0'><i class='fas fa-database me-2'></i>إعداد قاعدة بيانات المدربين المحسنة</h4>
        </div>
        <div class='card-body'>";

function logMessage($message, $type = 'info') {
    $icon = $type === 'success' ? 'check-circle' : ($type === 'error' ? 'exclamation-circle' : ($type === 'warning' ? 'exclamation-triangle' : 'info-circle'));
    echo "<div class='log-item log-$type'><i class='fas fa-$icon me-2'></i>$message</div>";
    flush();
    ob_flush();
}

try {
    logMessage('🔧 بدء إعداد قاعدة بيانات المدربين المحسنة...', 'info');
    
    // 1. إضافة أعمدة جديدة لجدول users للمدربين
    logMessage('=== تحديث جدول المستخدمين ===', 'info');
    
    $columns_to_add = [
        'phone' => "ALTER TABLE users ADD COLUMN phone VARCHAR(20) NULL AFTER email",
        'bio' => "ALTER TABLE users ADD COLUMN bio TEXT NULL AFTER phone",
        'specialization' => "ALTER TABLE users ADD COLUMN specialization VARCHAR(255) NULL AFTER bio",
        'experience_years' => "ALTER TABLE users ADD COLUMN experience_years INT NULL AFTER specialization",
        'education_level' => "ALTER TABLE users ADD COLUMN education_level ENUM('bachelor', 'master', 'phd', 'diploma', 'certificate') NULL AFTER experience_years",
        'university' => "ALTER TABLE users ADD COLUMN university VARCHAR(255) NULL AFTER education_level",
        'major' => "ALTER TABLE users ADD COLUMN major VARCHAR(255) NULL AFTER university",
        'certifications' => "ALTER TABLE users ADD COLUMN certifications TEXT NULL AFTER major",
        'skills' => "ALTER TABLE users ADD COLUMN skills TEXT NULL AFTER certifications",
        'languages' => "ALTER TABLE users ADD COLUMN languages TEXT NULL AFTER skills",
        'linkedin_url' => "ALTER TABLE users ADD COLUMN linkedin_url VARCHAR(500) NULL AFTER languages",
        'website_url' => "ALTER TABLE users ADD COLUMN website_url VARCHAR(500) NULL AFTER linkedin_url",
        'profile_image' => "ALTER TABLE users ADD COLUMN profile_image VARCHAR(500) NULL AFTER website_url",
        'hourly_rate' => "ALTER TABLE users ADD COLUMN hourly_rate DECIMAL(10,2) NULL AFTER profile_image",
        'availability' => "ALTER TABLE users ADD COLUMN availability ENUM('full_time', 'part_time', 'weekends', 'flexible') DEFAULT 'flexible' AFTER hourly_rate",
        'teaching_style' => "ALTER TABLE users ADD COLUMN teaching_style TEXT NULL AFTER availability",
        'preferred_subjects' => "ALTER TABLE users ADD COLUMN preferred_subjects TEXT NULL AFTER teaching_style",
        'rating' => "ALTER TABLE users ADD COLUMN rating DECIMAL(3,2) DEFAULT 0.00 AFTER preferred_subjects",
        'total_students' => "ALTER TABLE users ADD COLUMN total_students INT DEFAULT 0 AFTER rating",
        'total_courses' => "ALTER TABLE users ADD COLUMN total_courses INT DEFAULT 0 AFTER total_students",
        'is_verified' => "ALTER TABLE users ADD COLUMN is_verified BOOLEAN DEFAULT FALSE AFTER total_courses",
        'verification_documents' => "ALTER TABLE users ADD COLUMN verification_documents TEXT NULL AFTER is_verified"
    ];
    
    foreach ($columns_to_add as $column => $sql) {
        try {
            // التحقق من وجود العمود
            $check = $conn->query("SHOW COLUMNS FROM users LIKE '$column'");
            if ($check->rowCount() == 0) {
                $conn->exec($sql);
                logMessage("✓ تم إضافة العمود: $column", 'success');
            } else {
                logMessage("⚠ العمود موجود بالفعل: $column", 'warning');
            }
        } catch (Exception $e) {
            logMessage("❌ خطأ في إضافة العمود $column: " . $e->getMessage(), 'error');
        }
    }
    
    // 2. إنشاء جدول تخصصات المدربين
    logMessage('=== إنشاء جدول التخصصات ===', 'info');
    
    $conn->exec("
        CREATE TABLE IF NOT EXISTS instructor_specializations (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL UNIQUE,
            description TEXT,
            icon VARCHAR(100),
            color VARCHAR(7) DEFAULT '#007bff',
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ");
    logMessage("✓ تم إنشاء جدول التخصصات", 'success');
    
    // 3. إدراج التخصصات الافتراضية
    $specializations = [
        ['البرمجة وتطوير المواقع', 'تطوير المواقع والتطبيقات باستخدام لغات البرمجة المختلفة', 'fas fa-code', '#007bff'],
        ['التصميم الجرافيكي', 'تصميم الجرافيك والواجهات والعلامات التجارية', 'fas fa-paint-brush', '#28a745'],
        ['إدارة الأعمال', 'إدارة المشاريع والقيادة والتخطيط الاستراتيجي', 'fas fa-briefcase', '#ffc107'],
        ['التسويق الرقمي', 'التسويق عبر الإنترنت ووسائل التواصل الاجتماعي', 'fas fa-bullhorn', '#dc3545'],
        ['الذكاء الاصطناعي', 'الذكاء الاصطناعي والتعلم الآلي وعلوم البيانات', 'fas fa-robot', '#6f42c1'],
        ['المحاسبة والمالية', 'المحاسبة والتحليل المالي وإدارة الاستثمارات', 'fas fa-calculator', '#20c997'],
        ['اللغات الأجنبية', 'تدريس اللغات الإنجليزية والفرنسية وغيرها', 'fas fa-language', '#fd7e14'],
        ['العلوم والرياضيات', 'تدريس العلوم والرياضيات والفيزياء والكيمياء', 'fas fa-flask', '#e83e8c'],
        ['الطب والصحة', 'العلوم الطبية والتمريض والصحة العامة', 'fas fa-heartbeat', '#17a2b8'],
        ['الهندسة', 'الهندسة المدنية والكهربائية والميكانيكية', 'fas fa-cogs', '#6c757d']
    ];
    
    foreach ($specializations as $spec) {
        try {
            $stmt = $conn->prepare("
                INSERT IGNORE INTO instructor_specializations (name, description, icon, color) 
                VALUES (?, ?, ?, ?)
            ");
            $stmt->execute($spec);
            logMessage("✓ تم إضافة التخصص: {$spec[0]}", 'success');
        } catch (Exception $e) {
            logMessage("⚠ التخصص موجود: {$spec[0]}", 'warning');
        }
    }
    
    // 4. إنشاء جدول مهارات المدربين
    logMessage('=== إنشاء جدول المهارات ===', 'info');
    
    $conn->exec("
        CREATE TABLE IF NOT EXISTS instructor_skills (
            id INT AUTO_INCREMENT PRIMARY KEY,
            instructor_id INT NOT NULL,
            skill_name VARCHAR(255) NOT NULL,
            proficiency_level ENUM('beginner', 'intermediate', 'advanced', 'expert') DEFAULT 'intermediate',
            years_experience INT DEFAULT 0,
            is_certified BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (instructor_id) REFERENCES users(id) ON DELETE CASCADE,
            UNIQUE KEY unique_instructor_skill (instructor_id, skill_name)
        )
    ");
    logMessage("✓ تم إنشاء جدول المهارات", 'success');
    
    // 5. إنشاء جدول شهادات المدربين
    logMessage('=== إنشاء جدول الشهادات ===', 'info');
    
    $conn->exec("
        CREATE TABLE IF NOT EXISTS instructor_certificates (
            id INT AUTO_INCREMENT PRIMARY KEY,
            instructor_id INT NOT NULL,
            certificate_name VARCHAR(255) NOT NULL,
            issuing_organization VARCHAR(255) NOT NULL,
            issue_date DATE,
            expiry_date DATE NULL,
            certificate_url VARCHAR(500) NULL,
            verification_code VARCHAR(100) NULL,
            is_verified BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (instructor_id) REFERENCES users(id) ON DELETE CASCADE
        )
    ");
    logMessage("✓ تم إنشاء جدول الشهادات", 'success');
    
    // 6. إنشاء جدول تقييمات المدربين
    logMessage('=== إنشاء جدول التقييمات ===', 'info');
    
    $conn->exec("
        CREATE TABLE IF NOT EXISTS instructor_reviews (
            id INT AUTO_INCREMENT PRIMARY KEY,
            instructor_id INT NOT NULL,
            student_id INT NOT NULL,
            course_id INT NULL,
            rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
            review_text TEXT,
            is_anonymous BOOLEAN DEFAULT FALSE,
            is_approved BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (instructor_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE SET NULL,
            UNIQUE KEY unique_student_instructor_course (student_id, instructor_id, course_id)
        )
    ");
    logMessage("✓ تم إنشاء جدول التقييمات", 'success');
    
    // 7. إنشاء جدول جدولة المدربين
    logMessage('=== إنشاء جدول الجدولة ===', 'info');
    
    $conn->exec("
        CREATE TABLE IF NOT EXISTS instructor_schedule (
            id INT AUTO_INCREMENT PRIMARY KEY,
            instructor_id INT NOT NULL,
            day_of_week ENUM('monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday') NOT NULL,
            start_time TIME NOT NULL,
            end_time TIME NOT NULL,
            is_available BOOLEAN DEFAULT TRUE,
            timezone VARCHAR(50) DEFAULT 'Asia/Riyadh',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (instructor_id) REFERENCES users(id) ON DELETE CASCADE
        )
    ");
    logMessage("✓ تم إنشاء جدول الجدولة", 'success');
    
    // 8. تحديث المدربين الموجودين ببيانات تجريبية
    logMessage('=== تحديث بيانات المدربين الموجودين ===', 'info');
    
    $existing_instructors = $conn->query("SELECT id, name, email FROM users WHERE role = 'instructor'")->fetchAll(PDO::FETCH_ASSOC);
    
    $sample_data = [
        [
            'specialization' => 'البرمجة وتطوير المواقع',
            'experience_years' => 8,
            'education_level' => 'master',
            'university' => 'جامعة الملك سعود',
            'major' => 'علوم الحاسوب',
            'skills' => 'PHP, JavaScript, Python, MySQL, HTML, CSS',
            'languages' => 'العربية, الإنجليزية',
            'hourly_rate' => 150.00,
            'availability' => 'part_time',
            'bio' => 'مطور ويب محترف مع خبرة واسعة في تطوير التطبيقات والمواقع'
        ],
        [
            'specialization' => 'التصميم الجرافيكي',
            'experience_years' => 6,
            'education_level' => 'bachelor',
            'university' => 'جامعة الملك عبدالعزيز',
            'major' => 'التصميم الجرافيكي',
            'skills' => 'Photoshop, Illustrator, InDesign, Figma',
            'languages' => 'العربية, الإنجليزية, الفرنسية',
            'hourly_rate' => 120.00,
            'availability' => 'flexible',
            'bio' => 'مصممة جرافيك إبداعية متخصصة في تصميم الهوية البصرية'
        ],
        [
            'specialization' => 'إدارة الأعمال',
            'experience_years' => 12,
            'education_level' => 'phd',
            'university' => 'جامعة الملك فهد للبترول والمعادن',
            'major' => 'إدارة الأعمال',
            'skills' => 'إدارة المشاريع, القيادة, التخطيط الاستراتيجي',
            'languages' => 'العربية, الإنجليزية',
            'hourly_rate' => 200.00,
            'availability' => 'weekends',
            'bio' => 'خبير في إدارة الأعمال والمشاريع مع خبرة عملية واسعة'
        ]
    ];
    
    foreach ($existing_instructors as $index => $instructor) {
        if (isset($sample_data[$index])) {
            $data = $sample_data[$index];
            $stmt = $conn->prepare("
                UPDATE users SET 
                    specialization = ?, experience_years = ?, education_level = ?, 
                    university = ?, major = ?, skills = ?, languages = ?, 
                    hourly_rate = ?, availability = ?, bio = ?
                WHERE id = ?
            ");
            $stmt->execute([
                $data['specialization'], $data['experience_years'], $data['education_level'],
                $data['university'], $data['major'], $data['skills'], $data['languages'],
                $data['hourly_rate'], $data['availability'], $data['bio'], $instructor['id']
            ]);
            logMessage("✓ تم تحديث بيانات المدرب: {$instructor['name']}", 'success');
        }
    }
    
    logMessage('🎉 تم إعداد قاعدة بيانات المدربين المحسنة بنجاح!', 'success');
    
} catch (Exception $e) {
    logMessage('❌ خطأ: ' . $e->getMessage(), 'error');
}

echo "
        </div>
        <div class='mt-4 text-center'>
            <div class='alert alert-success'>
                <h5><i class='fas fa-check-circle me-2'></i>تم إعداد قاعدة البيانات بنجاح!</h5>
                <p class='mb-0'>الآن يمكنك استخدام صفحة إضافة المدرب المحسنة</p>
            </div>
            <a href='add-instructor.php' class='btn btn-primary btn-lg me-2'>
                <i class='fas fa-user-plus me-2'></i>إضافة مدرب جديد
            </a>
            <a href='manage-instructors.php' class='btn btn-success me-2'>
                <i class='fas fa-users me-2'></i>إدارة المدربين
            </a>
            <a href='dashboard.php' class='btn btn-info'>
                <i class='fas fa-tachometer-alt me-2'></i>لوحة التحكم
            </a>
        </div>
    </div>
</div>
</body>
</html>";
?>
