<?php
/**
 * إعداد بيانات الصفحة الرئيسية
 * Setup homepage data with categories and sample courses
 */

require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>إعداد بيانات الصفحة الرئيسية</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "</head>";
echo "<body>";
echo "<div class='container mt-4'>";
echo "<h2>🏠 إعداد بيانات الصفحة الرئيسية</h2>";

try {
    // 1. إضافة عمود category للكورسات إذا لم يكن موجود
    echo "<h4>📂 فحص عمود التخصصات</h4>";
    
    $stmt = $conn->query("SHOW COLUMNS FROM courses LIKE 'category'");
    if ($stmt->rowCount() == 0) {
        $conn->exec("ALTER TABLE courses ADD COLUMN category VARCHAR(100) DEFAULT NULL AFTER description");
        echo "<div class='alert alert-success'>✅ تم إضافة عمود التخصصات</div>";
    } else {
        echo "<div class='alert alert-info'>ℹ️ عمود التخصصات موجود</div>";
    }

    // 2. إنشاء جدول مراجعات الكورسات
    echo "<h4>⭐ إنشاء جدول المراجعات</h4>";
    
    $stmt = $conn->query("SHOW TABLES LIKE 'course_reviews'");
    if ($stmt->rowCount() == 0) {
        $conn->exec("
            CREATE TABLE course_reviews (
                id INT AUTO_INCREMENT PRIMARY KEY,
                course_id INT NOT NULL,
                student_id INT NOT NULL,
                rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
                comment TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                
                INDEX idx_course_id (course_id),
                INDEX idx_student_id (student_id),
                INDEX idx_rating (rating),
                
                FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
                FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
                
                UNIQUE KEY unique_course_student (course_id, student_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        echo "<div class='alert alert-success'>✅ تم إنشاء جدول المراجعات</div>";
    } else {
        echo "<div class='alert alert-info'>ℹ️ جدول المراجعات موجود</div>";
    }

    // 3. تحديث الكورسات الموجودة بتخصصات
    echo "<h4>🏷️ تحديث تخصصات الكورسات</h4>";
    
    $categories = [
        'البرمجة وتطوير الويب',
        'التصميم الجرافيكي',
        'التسويق الرقمي',
        'إدارة الأعمال',
        'اللغات',
        'العلوم والرياضيات',
        'الطبخ والحرف',
        'التطوير الشخصي'
    ];
    
    $stmt = $conn->query("SELECT id, title FROM courses WHERE category IS NULL OR category = ''");
    $courses_without_category = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $updated_count = 0;
    foreach ($courses_without_category as $course) {
        $random_category = $categories[array_rand($categories)];
        
        $stmt = $conn->prepare("UPDATE courses SET category = ? WHERE id = ?");
        $stmt->execute([$random_category, $course['id']]);
        $updated_count++;
        
        echo "<div class='alert alert-success'>✅ تم تحديث تخصص الكورس: " . htmlspecialchars($course['title']) . " إلى: $random_category</div>";
    }
    
    if ($updated_count == 0) {
        echo "<div class='alert alert-info'>ℹ️ جميع الكورسات لديها تخصصات</div>";
    }

    // 4. إنشاء كورسات تجريبية إضافية
    echo "<h4>📚 إنشاء كورسات تجريبية</h4>";
    
    $sample_courses = [
        [
            'title' => 'تطوير تطبيقات الويب بـ PHP',
            'description' => 'تعلم تطوير تطبيقات الويب الديناميكية باستخدام PHP وMySQL مع أمثلة عملية ومشاريع تطبيقية',
            'category' => 'البرمجة وتطوير الويب',
            'course_type' => 'paid',
            'price' => 299,
            'currency' => 'ريال'
        ],
        [
            'title' => 'أساسيات التصميم الجرافيكي',
            'description' => 'دورة شاملة في أساسيات التصميم الجرافيكي باستخدام Adobe Photoshop و Illustrator',
            'category' => 'التصميم الجرافيكي',
            'course_type' => 'paid',
            'price' => 199,
            'currency' => 'ريال'
        ],
        [
            'title' => 'التسويق عبر وسائل التواصل الاجتماعي',
            'description' => 'استراتيجيات فعالة للتسويق عبر فيسبوك، إنستغرام، تويتر ولينكد إن',
            'category' => 'التسويق الرقمي',
            'course_type' => 'free',
            'price' => 0,
            'currency' => 'ريال'
        ],
        [
            'title' => 'إدارة المشاريع الاحترافية',
            'description' => 'تعلم أساسيات إدارة المشاريع وأدوات التخطيط والتنفيذ والمتابعة',
            'category' => 'إدارة الأعمال',
            'course_type' => 'paid',
            'price' => 399,
            'currency' => 'ريال'
        ],
        [
            'title' => 'تعلم اللغة الإنجليزية للمبتدئين',
            'description' => 'كورس شامل لتعلم اللغة الإنجليزية من الصفر مع التركيز على المحادثة',
            'category' => 'اللغات',
            'course_type' => 'free',
            'price' => 0,
            'currency' => 'ريال'
        ],
        [
            'title' => 'الرياضيات التطبيقية',
            'description' => 'مفاهيم الرياضيات الأساسية وتطبيقاتها في الحياة العملية والمهنية',
            'category' => 'العلوم والرياضيات',
            'course_type' => 'paid',
            'price' => 149,
            'currency' => 'ريال'
        ]
    ];
    
    // جلب مدرب تجريبي
    $stmt = $conn->query("SELECT id FROM users WHERE role = 'instructor' LIMIT 1");
    $instructor = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$instructor) {
        // إنشاء مدرب تجريبي
        $stmt = $conn->prepare("
            INSERT INTO users (name, email, password, role, status, created_at)
            VALUES (?, ?, ?, 'instructor', 'active', NOW())
        ");
        $stmt->execute([
            'أحمد محمد المدرب',
            '<EMAIL>',
            password_hash('instructor123', PASSWORD_DEFAULT)
        ]);
        $instructor_id = $conn->lastInsertId();
        echo "<div class='alert alert-success'>✅ تم إنشاء مدرب تجريبي</div>";
    } else {
        $instructor_id = $instructor['id'];
    }
    
    $created_courses = 0;
    foreach ($sample_courses as $course_data) {
        // التحقق من عدم وجود كورس بنفس العنوان
        $stmt = $conn->prepare("SELECT id FROM courses WHERE title = ?");
        $stmt->execute([$course_data['title']]);
        
        if (!$stmt->fetch()) {
            $stmt = $conn->prepare("
                INSERT INTO courses (title, description, category, course_type, price, currency, instructor_id, status, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, 'active', NOW())
            ");
            $stmt->execute([
                $course_data['title'],
                $course_data['description'],
                $course_data['category'],
                $course_data['course_type'],
                $course_data['price'],
                $course_data['currency'],
                $instructor_id
            ]);
            
            $created_courses++;
            echo "<div class='alert alert-success'>✅ تم إنشاء كورس: " . htmlspecialchars($course_data['title']) . "</div>";
        }
    }
    
    if ($created_courses == 0) {
        echo "<div class='alert alert-info'>ℹ️ الكورسات التجريبية موجودة مسبقاً</div>";
    }

    // 5. إضافة مراجعات تجريبية
    echo "<h4>💬 إضافة مراجعات تجريبية</h4>";
    
    $stmt = $conn->query("SELECT id FROM courses WHERE status = 'active' LIMIT 5");
    $courses = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $stmt = $conn->query("SELECT id FROM users WHERE role = 'student' LIMIT 3");
    $students = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $sample_reviews = [
        ['rating' => 5, 'comment' => 'كورس ممتاز ومفيد جداً، المدرب محترف ويشرح بطريقة واضحة'],
        ['rating' => 4, 'comment' => 'محتوى جيد ومفيد، أنصح به للمبتدئين'],
        ['rating' => 5, 'comment' => 'استفدت كثيراً من هذا الكورس، شكراً للمدرب'],
        ['rating' => 4, 'comment' => 'كورس شامل ومنظم بشكل ممتاز'],
        ['rating' => 5, 'comment' => 'أفضل كورس أخذته في هذا المجال']
    ];
    
    $reviews_added = 0;
    foreach ($courses as $course_id) {
        foreach ($students as $student_id) {
            // التحقق من عدم وجود مراجعة سابقة
            $stmt = $conn->prepare("SELECT id FROM course_reviews WHERE course_id = ? AND student_id = ?");
            $stmt->execute([$course_id, $student_id]);
            
            if (!$stmt->fetch()) {
                $review = $sample_reviews[array_rand($sample_reviews)];
                
                $stmt = $conn->prepare("
                    INSERT INTO course_reviews (course_id, student_id, rating, comment, created_at)
                    VALUES (?, ?, ?, ?, NOW())
                ");
                $stmt->execute([$course_id, $student_id, $review['rating'], $review['comment']]);
                $reviews_added++;
            }
        }
    }
    
    if ($reviews_added > 0) {
        echo "<div class='alert alert-success'>✅ تم إضافة $reviews_added مراجعة تجريبية</div>";
    } else {
        echo "<div class='alert alert-info'>ℹ️ المراجعات التجريبية موجودة مسبقاً</div>";
    }

    // 6. إحصائيات الموقع المحدثة
    echo "<h4>📊 إحصائيات الموقع</h4>";
    
    $stmt = $conn->query("SELECT COUNT(*) FROM courses WHERE status = 'active'");
    $total_courses = $stmt->fetchColumn();
    
    $stmt = $conn->query("SELECT COUNT(*) FROM users WHERE role = 'instructor'");
    $total_instructors = $stmt->fetchColumn();
    
    $stmt = $conn->query("SELECT COUNT(*) FROM users WHERE role = 'student'");
    $total_students = $stmt->fetchColumn();
    
    $stmt = $conn->query("SELECT COUNT(*) FROM course_enrollments WHERE status = 'active'");
    $total_enrollments = $stmt->fetchColumn();
    
    $stmt = $conn->query("SELECT COUNT(DISTINCT category) FROM courses WHERE category IS NOT NULL");
    $total_categories = $stmt->fetchColumn();
    
    $stmt = $conn->query("SELECT COUNT(*) FROM course_reviews");
    $total_reviews = $stmt->fetchColumn();
    
    echo "<div class='row'>";
    
    echo "<div class='col-md-2'>";
    echo "<div class='card bg-primary text-white'>";
    echo "<div class='card-body text-center'>";
    echo "<h3>$total_courses</h3>";
    echo "<p class='mb-0'>كورسات</p>";
    echo "</div></div></div>";
    
    echo "<div class='col-md-2'>";
    echo "<div class='card bg-success text-white'>";
    echo "<div class='card-body text-center'>";
    echo "<h3>$total_instructors</h3>";
    echo "<p class='mb-0'>مدربين</p>";
    echo "</div></div></div>";
    
    echo "<div class='col-md-2'>";
    echo "<div class='card bg-info text-white'>";
    echo "<div class='card-body text-center'>";
    echo "<h3>$total_students</h3>";
    echo "<p class='mb-0'>طلاب</p>";
    echo "</div></div></div>";
    
    echo "<div class='col-md-2'>";
    echo "<div class='card bg-warning text-white'>";
    echo "<div class='card-body text-center'>";
    echo "<h3>$total_enrollments</h3>";
    echo "<p class='mb-0'>تسجيلات</p>";
    echo "</div></div></div>";
    
    echo "<div class='col-md-2'>";
    echo "<div class='card bg-secondary text-white'>";
    echo "<div class='card-body text-center'>";
    echo "<h3>$total_categories</h3>";
    echo "<p class='mb-0'>تخصصات</p>";
    echo "</div></div></div>";
    
    echo "<div class='col-md-2'>";
    echo "<div class='card bg-danger text-white'>";
    echo "<div class='card-body text-center'>";
    echo "<h3>$total_reviews</h3>";
    echo "<p class='mb-0'>مراجعات</p>";
    echo "</div></div></div>";
    
    echo "</div>";

    // 7. عرض التخصصات المتاحة
    echo "<h4>🏷️ التخصصات المتاحة</h4>";
    
    $stmt = $conn->query("
        SELECT category, COUNT(*) as course_count 
        FROM courses 
        WHERE category IS NOT NULL AND status = 'active'
        GROUP BY category 
        ORDER BY course_count DESC
    ");
    $categories_with_count = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($categories_with_count)) {
        echo "<div class='row'>";
        foreach ($categories_with_count as $cat) {
            echo "<div class='col-md-3 mb-2'>";
            echo "<div class='card'>";
            echo "<div class='card-body text-center'>";
            echo "<h6>" . htmlspecialchars($cat['category']) . "</h6>";
            echo "<span class='badge bg-primary'>{$cat['course_count']} كورس</span>";
            echo "</div></div></div>";
        }
        echo "</div>";
    }

    echo "<div class='alert alert-success mt-4'>";
    echo "<h5>🎉 تم إعداد الصفحة الرئيسية بنجاح!</h5>";
    echo "<ul class='mb-0'>";
    echo "<li>✅ تم إضافة عمود التخصصات</li>";
    echo "<li>✅ تم إنشاء جدول المراجعات</li>";
    echo "<li>✅ تم تحديث تخصصات الكورسات</li>";
    echo "<li>✅ تم إنشاء كورسات تجريبية</li>";
    echo "<li>✅ تم إضافة مراجعات تجريبية</li>";
    echo "<li>✅ الصفحة الرئيسية جاهزة للعرض</li>";
    echo "</ul>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div class='alert alert-danger'>❌ خطأ: " . htmlspecialchars($e->getMessage()) . "</div>";
}

echo "<div class='text-center mt-4'>";
echo "<a href='index.php' class='btn btn-primary btn-lg me-2'>🏠 الصفحة الرئيسية</a>";
echo "<a href='course-details.php?id=18' class='btn btn-success btn-lg me-2'>📚 تفاصيل كورس</a>";
echo "<a href='login.php' class='btn btn-info btn-lg'>🔐 تسجيل الدخول</a>";
echo "</div>";

echo "<div class='mt-4'>";
echo "<div class='alert alert-info'>";
echo "<h6>💡 الميزات الجديدة:</h6>";
echo "<ul>";
echo "<li>🏠 صفحة رئيسية متكاملة مع عرض الكورسات</li>";
echo "<li>🏷️ فلتر الكورسات حسب التخصص</li>";
echo "<li>📊 إحصائيات الموقع المباشرة</li>";
echo "<li>⭐ نظام مراجعات الكورسات</li>";
echo "<li>📱 تصميم متجاوب لجميع الأجهزة</li>";
echo "<li>🎨 واجهة مستخدم جميلة وحديثة</li>";
echo "</ul>";
echo "</div>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
