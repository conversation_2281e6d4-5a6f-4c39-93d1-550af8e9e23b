
/* إصلاحات DataTables للغة العربية */
.dataTables_wrapper {
    direction: rtl;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.dataTables_filter {
    text-align: right;
    margin-bottom: 15px;
}

.dataTables_filter label {
    font-weight: normal;
    white-space: nowrap;
    text-align: right;
}

.dataTables_filter input {
    margin-right: 10px;
    display: inline-block;
    width: auto;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 6px 12px;
}

.dataTables_length {
    text-align: right;
    margin-bottom: 15px;
}

.dataTables_length label {
    font-weight: normal;
    text-align: right;
    white-space: nowrap;
}

.dataTables_length select {
    margin: 0 10px;
    display: inline-block;
    width: auto;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 6px 12px;
}

.dataTables_info {
    text-align: right;
    padding-top: 8px;
    white-space: nowrap;
}

.dataTables_paginate {
    text-align: center;
    margin-top: 15px;
}

.dataTables_paginate .paginate_button {
    box-sizing: border-box;
    display: inline-block;
    min-width: 1.5em;
    padding: 0.5em 1em;
    margin-left: 2px;
    text-align: center;
    text-decoration: none !important;
    cursor: pointer;
    color: #333 !important;
    border: 1px solid transparent;
    border-radius: 2px;
}

.dataTables_paginate .paginate_button.current {
    color: #fff !important;
    background: #007bff;
    border: 1px solid #007bff;
}

.dataTables_paginate .paginate_button:hover {
    color: #fff !important;
    background: #0056b3;
    border: 1px solid #0056b3;
}

.dataTables_paginate .paginate_button.disabled {
    cursor: default;
    color: #999 !important;
    border: 1px solid transparent;
    background: transparent;
}

/* تحسينات للجداول */
.table-responsive {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
}

.table th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
}

.table td {
    vertical-align: middle;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .dataTables_wrapper .dataTables_filter,
    .dataTables_wrapper .dataTables_length {
        text-align: center;
        margin-bottom: 10px;
    }
    
    .dataTables_wrapper .dataTables_info,
    .dataTables_wrapper .dataTables_paginate {
        text-align: center;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
}

/* إخفاء رسائل التحميل المزعجة */
.dataTables_processing {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 200px;
    margin-left: -100px;
    margin-top: -26px;
    text-align: center;
    padding: 1em 0;
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid #ddd;
    border-radius: 4px;
}
