<?php
require_once '../config/database.php';

echo "<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>اختبار الحذف</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css' rel='stylesheet'>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f8f9fa; }
        .container { max-width: 1000px; margin: 30px auto; }
        .log-item { padding: 8px; margin: 3px 0; border-radius: 5px; font-size: 14px; }
        .log-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .log-error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .log-info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
<div class='container'>
    <div class='card'>
        <div class='card-header bg-warning text-white'>
            <h4 class='mb-0'><i class='fas fa-vial me-2'></i>اختبار وظيفة الحذف</h4>
        </div>
        <div class='card-body'>";

function logMessage($message, $type = 'info') {
    $icon = $type === 'success' ? 'check-circle' : ($type === 'error' ? 'exclamation-circle' : 'info-circle');
    echo "<div class='log-item log-$type'><i class='fas fa-$icon me-2'></i>$message</div>";
    flush();
    ob_flush();
}

try {
    logMessage('🧪 بدء اختبار وظيفة الحذف...', 'info');
    
    // 1. إنشاء كورس تجريبي للحذف
    logMessage('=== إنشاء كورس تجريبي ===', 'info');
    
    $stmt = $conn->prepare("
        INSERT INTO courses (title, description, instructor_id, start_date, end_date, max_students, course_type, price, status) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");
    
    $test_course_data = [
        'كورس تجريبي للحذف',
        'هذا كورس تجريبي سيتم حذفه لاختبار وظيفة الحذف',
        1, // افتراض وجود مدرب بمعرف 1
        date('Y-m-d'),
        date('Y-m-d', strtotime('+30 days')),
        50,
        'paid',
        299.99,
        'active'
    ];
    
    $stmt->execute($test_course_data);
    $test_course_id = $conn->lastInsertId();
    
    logMessage("✓ تم إنشاء كورس تجريبي بمعرف: $test_course_id", 'success');
    
    // 2. إضافة بيانات مرتبطة
    logMessage('=== إضافة بيانات مرتبطة ===', 'info');
    
    // إضافة جلسات
    for ($i = 1; $i <= 3; $i++) {
        $stmt = $conn->prepare("
            INSERT INTO sessions (course_id, title, description, session_date, duration, status) 
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute([
            $test_course_id,
            "جلسة تجريبية $i",
            "وصف الجلسة التجريبية $i",
            date('Y-m-d H:i:s', strtotime("+$i days")),
            60,
            'scheduled'
        ]);
    }
    logMessage("✓ تم إضافة 3 جلسات تجريبية", 'success');
    
    // إضافة تسجيلات طلاب
    $students = $conn->query("SELECT id FROM users WHERE role = 'student' LIMIT 5")->fetchAll(PDO::FETCH_COLUMN);
    $enrollments_added = 0;
    
    foreach ($students as $student_id) {
        try {
            $stmt = $conn->prepare("
                INSERT INTO course_enrollments (student_id, course_id, payment_status, payment_amount, status) 
                VALUES (?, ?, ?, ?, ?)
            ");
            $stmt->execute([$student_id, $test_course_id, 'completed', 299.99, 'active']);
            $enrollments_added++;
        } catch (Exception $e) {
            // تجاهل الأخطاء
        }
    }
    logMessage("✓ تم إضافة $enrollments_added تسجيل طالب", 'success');
    
    // إضافة تقييمات
    $reviews_added = 0;
    foreach ($students as $student_id) {
        if (rand(0, 1) == 0) {
            try {
                $stmt = $conn->prepare("
                    INSERT INTO course_reviews (course_id, student_id, rating, review_text) 
                    VALUES (?, ?, ?, ?)
                ");
                $stmt->execute([$test_course_id, $student_id, rand(4, 5), 'تقييم تجريبي للكورس']);
                $reviews_added++;
            } catch (Exception $e) {
                // تجاهل الأخطاء
            }
        }
    }
    logMessage("✓ تم إضافة $reviews_added تقييم", 'success');
    
    // إضافة سجلات حضور
    $sessions = $conn->query("SELECT id FROM sessions WHERE course_id = $test_course_id")->fetchAll(PDO::FETCH_COLUMN);
    $attendance_added = 0;
    
    foreach ($sessions as $session_id) {
        foreach ($students as $student_id) {
            if (rand(0, 1) == 0) {
                try {
                    $stmt = $conn->prepare("
                        INSERT INTO session_attendance (session_id, student_id, attendance_status) 
                        VALUES (?, ?, ?)
                    ");
                    $stmt->execute([$session_id, $student_id, 'present']);
                    $attendance_added++;
                } catch (Exception $e) {
                    // تجاهل الأخطاء
                }
            }
        }
    }
    logMessage("✓ تم إضافة $attendance_added سجل حضور", 'success');
    
    // 3. عرض البيانات قبل الحذف
    logMessage('=== البيانات قبل الحذف ===', 'info');
    
    $stats_before = [
        'الكورس' => $conn->query("SELECT COUNT(*) FROM courses WHERE id = $test_course_id")->fetchColumn(),
        'الجلسات' => $conn->query("SELECT COUNT(*) FROM sessions WHERE course_id = $test_course_id")->fetchColumn(),
        'التسجيلات' => $conn->query("SELECT COUNT(*) FROM course_enrollments WHERE course_id = $test_course_id")->fetchColumn(),
        'التقييمات' => $conn->query("SELECT COUNT(*) FROM course_reviews WHERE course_id = $test_course_id")->fetchColumn(),
        'سجلات الحضور' => $conn->query("SELECT COUNT(*) FROM session_attendance sa JOIN sessions s ON sa.session_id = s.id WHERE s.course_id = $test_course_id")->fetchColumn()
    ];
    
    foreach ($stats_before as $key => $value) {
        logMessage("📊 $key: $value", 'info');
    }
    
    // 4. اختبار الحذف
    logMessage('=== اختبار الحذف ===', 'info');
    
    // محاكاة طلب AJAX
    $_POST['course_id'] = $test_course_id;
    $_SERVER['REQUEST_METHOD'] = 'POST';
    
    // تشغيل كود الحذف
    ob_start();
    include 'ajax/delete-course.php';
    $delete_response = ob_get_clean();
    
    $delete_result = json_decode($delete_response, true);
    
    if ($delete_result && $delete_result['success']) {
        logMessage("✓ تم حذف الكورس بنجاح", 'success');
        
        if (isset($delete_result['details'])) {
            foreach ($delete_result['details'] as $key => $value) {
                logMessage("📊 $key: $value", 'info');
            }
        }
    } else {
        logMessage("❌ فشل في حذف الكورس: " . ($delete_result['message'] ?? 'خطأ غير معروف'), 'error');
    }
    
    // 5. التحقق من الحذف
    logMessage('=== التحقق من الحذف ===', 'info');
    
    $stats_after = [
        'الكورس' => $conn->query("SELECT COUNT(*) FROM courses WHERE id = $test_course_id")->fetchColumn(),
        'الجلسات' => $conn->query("SELECT COUNT(*) FROM sessions WHERE course_id = $test_course_id")->fetchColumn(),
        'التسجيلات' => $conn->query("SELECT COUNT(*) FROM course_enrollments WHERE course_id = $test_course_id")->fetchColumn(),
        'التقييمات' => $conn->query("SELECT COUNT(*) FROM course_reviews WHERE course_id = $test_course_id")->fetchColumn(),
        'سجلات الحضور' => $conn->query("SELECT COUNT(*) FROM session_attendance sa JOIN sessions s ON sa.session_id = s.id WHERE s.course_id = $test_course_id")->fetchColumn()
    ];
    
    $all_deleted = true;
    foreach ($stats_after as $key => $value) {
        if ($value > 0) {
            logMessage("❌ $key: $value (لم يتم الحذف)", 'error');
            $all_deleted = false;
        } else {
            logMessage("✓ $key: $value (تم الحذف)", 'success');
        }
    }
    
    if ($all_deleted) {
        logMessage('🎉 تم حذف جميع البيانات بنجاح!', 'success');
    } else {
        logMessage('⚠️ لم يتم حذف جميع البيانات', 'error');
    }
    
} catch (Exception $e) {
    logMessage('❌ خطأ: ' . $e->getMessage(), 'error');
}

echo "
        </div>
        <div class='mt-4 text-center'>
            <div class='alert alert-info'>
                <h5><i class='fas fa-info-circle me-2'></i>انتهى الاختبار</h5>
                <p class='mb-0'>تم اختبار وظيفة الحذف بنجاح</p>
            </div>
            <a href='manage-courses.php' class='btn btn-primary btn-lg'>
                <i class='fas fa-arrow-right me-2'></i>العودة لإدارة الكورسات
            </a>
        </div>
    </div>
</div>
</body>
</html>";
?>
