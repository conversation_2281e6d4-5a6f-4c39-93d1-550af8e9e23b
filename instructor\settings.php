<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات المدرب
if (!isLoggedIn() || !isInstructor()) {
    header('Location: ../login.php');
    exit;
}

$pageTitle = 'الإعدادات';
$breadcrumbs = [
    ['title' => 'الإعدادات']
];

// معالجة تحديث الملف الشخصي
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'update_profile') {
        $name = trim($_POST['name'] ?? '');
        $email = trim($_POST['email'] ?? '');
        $phone = trim($_POST['phone'] ?? '');
        $bio = trim($_POST['bio'] ?? '');
        $specialization = trim($_POST['specialization'] ?? '');
        $experience_years = $_POST['experience_years'] ?? 0;
        $education = trim($_POST['education'] ?? '');
        $certifications = trim($_POST['certifications'] ?? '');
        $linkedin_url = trim($_POST['linkedin_url'] ?? '');
        $website_url = trim($_POST['website_url'] ?? '');
        
        $errors = [];
        
        if (empty($name)) {
            $errors[] = 'الاسم مطلوب';
        }
        
        if (empty($email)) {
            $errors[] = 'البريد الإلكتروني مطلوب';
        } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $errors[] = 'البريد الإلكتروني غير صحيح';
        }
        
        // التحقق من عدم تكرار البريد الإلكتروني
        if (empty($errors)) {
            try {
                $stmt = $conn->prepare("SELECT id FROM users WHERE email = ? AND id != ?");
                $stmt->execute([$email, $_SESSION['user_id']]);
                if ($stmt->fetch()) {
                    $errors[] = 'البريد الإلكتروني مستخدم من قبل مستخدم آخر';
                }
            } catch (PDOException $e) {
                $errors[] = 'حدث خطأ أثناء التحقق من البريد الإلكتروني';
            }
        }
        
        if (empty($errors)) {
            try {
                $stmt = $conn->prepare("
                    UPDATE users SET 
                        name = ?, email = ?, phone = ?, bio = ?, specialization = ?, 
                        experience_years = ?, education = ?, certifications = ?, 
                        linkedin_url = ?, website_url = ?
                    WHERE id = ?
                ");
                $stmt->execute([
                    $name, $email, $phone, $bio, $specialization, 
                    $experience_years, $education, $certifications, 
                    $linkedin_url, $website_url, $_SESSION['user_id']
                ]);
                
                $_SESSION['user_name'] = $name;
                $success_message = 'تم تحديث الملف الشخصي بنجاح';
            } catch (PDOException $e) {
                $error_message = 'حدث خطأ أثناء تحديث الملف الشخصي';
            }
        }
    }
    
    if ($action === 'change_password') {
        $current_password = $_POST['current_password'] ?? '';
        $new_password = $_POST['new_password'] ?? '';
        $confirm_password = $_POST['confirm_password'] ?? '';
        
        $errors = [];
        
        if (empty($current_password)) {
            $errors[] = 'كلمة المرور الحالية مطلوبة';
        }
        
        if (empty($new_password)) {
            $errors[] = 'كلمة المرور الجديدة مطلوبة';
        } elseif (strlen($new_password) < 6) {
            $errors[] = 'كلمة المرور الجديدة يجب أن تكون 6 أحرف على الأقل';
        }
        
        if ($new_password !== $confirm_password) {
            $errors[] = 'تأكيد كلمة المرور غير متطابق';
        }
        
        if (empty($errors)) {
            try {
                // التحقق من كلمة المرور الحالية
                $stmt = $conn->prepare("SELECT password FROM users WHERE id = ?");
                $stmt->execute([$_SESSION['user_id']]);
                $user = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if (!$user || !password_verify($current_password, $user['password'])) {
                    $errors[] = 'كلمة المرور الحالية غير صحيحة';
                } else {
                    // تحديث كلمة المرور
                    $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                    $stmt = $conn->prepare("UPDATE users SET password = ? WHERE id = ?");
                    $stmt->execute([$hashed_password, $_SESSION['user_id']]);
                    
                    $success_message = 'تم تغيير كلمة المرور بنجاح';
                }
            } catch (PDOException $e) {
                $error_message = 'حدث خطأ أثناء تغيير كلمة المرور';
            }
        }
    }
    
    if ($action === 'update_preferences') {
        $email_notifications = isset($_POST['email_notifications']) ? 1 : 0;
        $sms_notifications = isset($_POST['sms_notifications']) ? 1 : 0;
        $marketing_emails = isset($_POST['marketing_emails']) ? 1 : 0;
        $language = $_POST['language'] ?? 'ar';
        $timezone = $_POST['timezone'] ?? 'Asia/Riyadh';
        
        try {
            // تحديث أو إدراج تفضيلات المستخدم
            $stmt = $conn->prepare("
                INSERT INTO user_preferences (user_id, email_notifications, sms_notifications, marketing_emails, language, timezone)
                VALUES (?, ?, ?, ?, ?, ?)
                ON DUPLICATE KEY UPDATE
                email_notifications = VALUES(email_notifications),
                sms_notifications = VALUES(sms_notifications),
                marketing_emails = VALUES(marketing_emails),
                language = VALUES(language),
                timezone = VALUES(timezone)
            ");
            $stmt->execute([$_SESSION['user_id'], $email_notifications, $sms_notifications, $marketing_emails, $language, $timezone]);
            
            $success_message = 'تم تحديث التفضيلات بنجاح';
        } catch (PDOException $e) {
            $error_message = 'حدث خطأ أثناء تحديث التفضيلات';
        }
    }
}

// جلب بيانات المستخدم
try {
    $stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // جلب التفضيلات
    $stmt = $conn->prepare("SELECT * FROM user_preferences WHERE user_id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $preferences = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$preferences) {
        $preferences = [
            'email_notifications' => 1,
            'sms_notifications' => 0,
            'marketing_emails' => 1,
            'language' => 'ar',
            'timezone' => 'Asia/Riyadh'
        ];
    }
    
} catch (PDOException $e) {
    $error_message = 'حدث خطأ أثناء جلب البيانات';
    $user = [];
    $preferences = [];
}

include 'includes/header.php';
?>

<!-- رأس الصفحة -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="h3 mb-2">
            <i class="fas fa-cog text-primary me-2"></i>
            الإعدادات
        </h2>
        <p class="text-muted mb-0">إدارة الملف الشخصي والتفضيلات</p>
    </div>
    <div class="d-flex gap-2">
        <a href="dashboard.php" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>العودة للوحة التحكم
        </a>
    </div>
</div>

<!-- رسائل النجاح والخطأ -->
<?php if (isset($success_message)): ?>
<div class="alert alert-success alert-dismissible fade show" role="alert">
    <i class="fas fa-check-circle me-2"></i>
    <?php echo $success_message; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<?php if (isset($error_message)): ?>
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <i class="fas fa-exclamation-triangle me-2"></i>
    <?php echo $error_message; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<?php if (!empty($errors)): ?>
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <i class="fas fa-exclamation-triangle me-2"></i>
    <ul class="mb-0">
        <?php foreach ($errors as $error): ?>
        <li><?php echo htmlspecialchars($error); ?></li>
        <?php endforeach; ?>
    </ul>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<!-- تبويبات الإعدادات -->
<div class="row">
    <div class="col-lg-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body p-0">
                <div class="nav flex-column nav-pills" id="settingsTabs" role="tablist">
                    <button class="nav-link active text-start" id="profile-tab" data-bs-toggle="pill" 
                            data-bs-target="#profile" type="button" role="tab">
                        <i class="fas fa-user me-2"></i>الملف الشخصي
                    </button>
                    <button class="nav-link text-start" id="password-tab" data-bs-toggle="pill" 
                            data-bs-target="#password" type="button" role="tab">
                        <i class="fas fa-lock me-2"></i>كلمة المرور
                    </button>
                    <button class="nav-link text-start" id="preferences-tab" data-bs-toggle="pill" 
                            data-bs-target="#preferences" type="button" role="tab">
                        <i class="fas fa-bell me-2"></i>التفضيلات
                    </button>
                    <button class="nav-link text-start" id="privacy-tab" data-bs-toggle="pill" 
                            data-bs-target="#privacy" type="button" role="tab">
                        <i class="fas fa-shield-alt me-2"></i>الخصوصية
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-9">
        <div class="tab-content" id="settingsTabsContent">
            <!-- تبويب الملف الشخصي -->
            <div class="tab-pane fade show active" id="profile" role="tabpanel">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white border-bottom">
                        <h5 class="mb-0">
                            <i class="fas fa-user me-2"></i>
                            الملف الشخصي
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <input type="hidden" name="action" value="update_profile">
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="name" class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                                    <input type="text" name="name" id="name" class="form-control" 
                                           value="<?php echo htmlspecialchars($user['name'] ?? ''); ?>" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label">البريد الإلكتروني <span class="text-danger">*</span></label>
                                    <input type="email" name="email" id="email" class="form-control" 
                                           value="<?php echo htmlspecialchars($user['email'] ?? ''); ?>" required>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="phone" class="form-label">رقم الهاتف</label>
                                    <input type="tel" name="phone" id="phone" class="form-control" 
                                           value="<?php echo htmlspecialchars($user['phone'] ?? ''); ?>">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="specialization" class="form-label">التخصص</label>
                                    <input type="text" name="specialization" id="specialization" class="form-control" 
                                           value="<?php echo htmlspecialchars($user['specialization'] ?? ''); ?>"
                                           placeholder="مثال: علوم الحاسوب">
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="experience_years" class="form-label">سنوات الخبرة</label>
                                    <input type="number" name="experience_years" id="experience_years" class="form-control" 
                                           value="<?php echo $user['experience_years'] ?? 0; ?>" min="0">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="education" class="form-label">المؤهل العلمي</label>
                                    <input type="text" name="education" id="education" class="form-control" 
                                           value="<?php echo htmlspecialchars($user['education'] ?? ''); ?>"
                                           placeholder="مثال: بكالوريوس علوم الحاسوب">
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="bio" class="form-label">نبذة شخصية</label>
                                <textarea name="bio" id="bio" class="form-control" rows="4" 
                                          placeholder="اكتب نبذة مختصرة عن نفسك وخبراتك..."><?php echo htmlspecialchars($user['bio'] ?? ''); ?></textarea>
                            </div>
                            
                            <div class="mb-3">
                                <label for="certifications" class="form-label">الشهادات والدورات</label>
                                <textarea name="certifications" id="certifications" class="form-control" rows="3" 
                                          placeholder="اذكر الشهادات والدورات التي حصلت عليها..."><?php echo htmlspecialchars($user['certifications'] ?? ''); ?></textarea>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="linkedin_url" class="form-label">رابط LinkedIn</label>
                                    <input type="url" name="linkedin_url" id="linkedin_url" class="form-control" 
                                           value="<?php echo htmlspecialchars($user['linkedin_url'] ?? ''); ?>"
                                           placeholder="https://linkedin.com/in/username">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="website_url" class="form-label">الموقع الشخصي</label>
                                    <input type="url" name="website_url" id="website_url" class="form-control" 
                                           value="<?php echo htmlspecialchars($user['website_url'] ?? ''); ?>"
                                           placeholder="https://example.com">
                                </div>
                            </div>
                            
                            <div class="d-flex justify-content-end">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i>حفظ التغييرات
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- تبويب كلمة المرور -->
            <div class="tab-pane fade" id="password" role="tabpanel">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white border-bottom">
                        <h5 class="mb-0">
                            <i class="fas fa-lock me-2"></i>
                            تغيير كلمة المرور
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" id="passwordForm">
                            <input type="hidden" name="action" value="change_password">

                            <div class="mb-3">
                                <label for="current_password" class="form-label">كلمة المرور الحالية <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="password" name="current_password" id="current_password" class="form-control" required>
                                    <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('current_password')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="new_password" class="form-label">كلمة المرور الجديدة <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="password" name="new_password" id="new_password" class="form-control"
                                           minlength="6" required>
                                    <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('new_password')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                <div class="form-text">كلمة المرور يجب أن تكون 6 أحرف على الأقل</div>
                            </div>

                            <div class="mb-3">
                                <label for="confirm_password" class="form-label">تأكيد كلمة المرور الجديدة <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="password" name="confirm_password" id="confirm_password" class="form-control" required>
                                    <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('confirm_password')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                <div id="passwordMatch" class="form-text"></div>
                            </div>

                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>نصائح لكلمة مرور قوية:</strong>
                                <ul class="mb-0 mt-2">
                                    <li>استخدم مزيج من الأحرف الكبيرة والصغيرة</li>
                                    <li>أضف أرقام ورموز خاصة</li>
                                    <li>تجنب استخدام معلومات شخصية</li>
                                    <li>لا تستخدم كلمات مرور سهلة التخمين</li>
                                </ul>
                            </div>

                            <div class="d-flex justify-content-end">
                                <button type="submit" class="btn btn-warning">
                                    <i class="fas fa-key me-1"></i>تغيير كلمة المرور
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- تبويب التفضيلات -->
            <div class="tab-pane fade" id="preferences" role="tabpanel">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white border-bottom">
                        <h5 class="mb-0">
                            <i class="fas fa-bell me-2"></i>
                            التفضيلات والإشعارات
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <input type="hidden" name="action" value="update_preferences">

                            <h6 class="mb-3">الإشعارات</h6>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" name="email_notifications"
                                               id="email_notifications" <?php echo $preferences['email_notifications'] ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="email_notifications">
                                            <i class="fas fa-envelope text-primary me-2"></i>
                                            إشعارات البريد الإلكتروني
                                        </label>
                                    </div>
                                    <small class="text-muted">تلقي إشعارات عن الأنشطة المهمة</small>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" name="sms_notifications"
                                               id="sms_notifications" <?php echo $preferences['sms_notifications'] ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="sms_notifications">
                                            <i class="fas fa-sms text-success me-2"></i>
                                            إشعارات الرسائل النصية
                                        </label>
                                    </div>
                                    <small class="text-muted">تلقي رسائل نصية للأحداث العاجلة</small>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" name="marketing_emails"
                                               id="marketing_emails" <?php echo $preferences['marketing_emails'] ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="marketing_emails">
                                            <i class="fas fa-bullhorn text-info me-2"></i>
                                            رسائل تسويقية
                                        </label>
                                    </div>
                                    <small class="text-muted">تلقي عروض وأخبار المنصة</small>
                                </div>
                            </div>

                            <hr>

                            <h6 class="mb-3">الإعدادات العامة</h6>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="language" class="form-label">اللغة</label>
                                    <select name="language" id="language" class="form-select">
                                        <option value="ar" <?php echo $preferences['language'] === 'ar' ? 'selected' : ''; ?>>العربية</option>
                                        <option value="en" <?php echo $preferences['language'] === 'en' ? 'selected' : ''; ?>>English</option>
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="timezone" class="form-label">المنطقة الزمنية</label>
                                    <select name="timezone" id="timezone" class="form-select">
                                        <option value="Asia/Riyadh" <?php echo $preferences['timezone'] === 'Asia/Riyadh' ? 'selected' : ''; ?>>الرياض (GMT+3)</option>
                                        <option value="Asia/Dubai" <?php echo $preferences['timezone'] === 'Asia/Dubai' ? 'selected' : ''; ?>>دبي (GMT+4)</option>
                                        <option value="Asia/Kuwait" <?php echo $preferences['timezone'] === 'Asia/Kuwait' ? 'selected' : ''; ?>>الكويت (GMT+3)</option>
                                        <option value="Africa/Cairo" <?php echo $preferences['timezone'] === 'Africa/Cairo' ? 'selected' : ''; ?>>القاهرة (GMT+2)</option>
                                    </select>
                                </div>
                            </div>

                            <div class="d-flex justify-content-end">
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-save me-1"></i>حفظ التفضيلات
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- تبويب الخصوصية -->
            <div class="tab-pane fade" id="privacy" role="tabpanel">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white border-bottom">
                        <h5 class="mb-0">
                            <i class="fas fa-shield-alt me-2"></i>
                            الخصوصية والأمان
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="mb-3">إعدادات الملف الشخصي</h6>
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="profile_public" checked>
                                    <label class="form-check-label" for="profile_public">
                                        ملف شخصي عام
                                    </label>
                                    <div class="form-text">السماح للطلاب برؤية ملفك الشخصي</div>
                                </div>

                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="show_email">
                                    <label class="form-check-label" for="show_email">
                                        إظهار البريد الإلكتروني
                                    </label>
                                    <div class="form-text">السماح للطلاب برؤية بريدك الإلكتروني</div>
                                </div>

                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="show_phone">
                                    <label class="form-check-label" for="show_phone">
                                        إظهار رقم الهاتف
                                    </label>
                                    <div class="form-text">السماح للطلاب برؤية رقم هاتفك</div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <h6 class="mb-3">إعدادات الأمان</h6>
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>آخر تسجيل دخول:</strong><br>
                                    <?php echo date('Y-m-d H:i:s'); ?>
                                </div>

                                <div class="d-grid gap-2">
                                    <button class="btn btn-outline-warning" onclick="showLoginHistory()">
                                        <i class="fas fa-history me-1"></i>عرض سجل تسجيل الدخول
                                    </button>
                                    <button class="btn btn-outline-danger" onclick="logoutAllDevices()">
                                        <i class="fas fa-sign-out-alt me-1"></i>تسجيل الخروج من جميع الأجهزة
                                    </button>
                                </div>
                            </div>
                        </div>

                        <hr>

                        <div class="row">
                            <div class="col-12">
                                <h6 class="mb-3 text-danger">منطقة الخطر</h6>
                                <div class="alert alert-danger">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <strong>تحذير:</strong> الإجراءات التالية لا يمكن التراجع عنها
                                </div>

                                <div class="d-flex gap-2">
                                    <button class="btn btn-outline-danger" onclick="exportData()">
                                        <i class="fas fa-download me-1"></i>تصدير بياناتي
                                    </button>
                                    <button class="btn btn-danger" onclick="deleteAccount()">
                                        <i class="fas fa-trash me-1"></i>حذف الحساب نهائياً
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// تبديل إظهار/إخفاء كلمة المرور
function togglePassword(inputId) {
    const input = document.getElementById(inputId);
    const button = input.nextElementSibling;
    const icon = button.querySelector('i');

    if (input.type === 'password') {
        input.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        input.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

// التحقق من تطابق كلمة المرور
document.addEventListener('DOMContentLoaded', function() {
    const newPassword = document.getElementById('new_password');
    const confirmPassword = document.getElementById('confirm_password');
    const passwordMatch = document.getElementById('passwordMatch');

    function checkPasswordMatch() {
        if (confirmPassword.value === '') {
            passwordMatch.textContent = '';
            passwordMatch.className = 'form-text';
            return;
        }

        if (newPassword.value === confirmPassword.value) {
            passwordMatch.textContent = 'كلمات المرور متطابقة ✓';
            passwordMatch.className = 'form-text text-success';
        } else {
            passwordMatch.textContent = 'كلمات المرور غير متطابقة ✗';
            passwordMatch.className = 'form-text text-danger';
        }
    }

    if (newPassword && confirmPassword) {
        newPassword.addEventListener('input', checkPasswordMatch);
        confirmPassword.addEventListener('input', checkPasswordMatch);
    }

    // التحقق من صحة نموذج كلمة المرور
    const passwordForm = document.getElementById('passwordForm');
    if (passwordForm) {
        passwordForm.addEventListener('submit', function(e) {
            if (newPassword.value !== confirmPassword.value) {
                e.preventDefault();
                alert('كلمات المرور غير متطابقة');
                return false;
            }

            if (newPassword.value.length < 6) {
                e.preventDefault();
                alert('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
                return false;
            }
        });
    }
});

// دوال الخصوصية والأمان
function showLoginHistory() {
    alert('سيتم عرض سجل تسجيل الدخول قريباً');
}

function logoutAllDevices() {
    if (confirm('هل أنت متأكد من تسجيل الخروج من جميع الأجهزة؟ ستحتاج لتسجيل الدخول مرة أخرى.')) {
        // يمكن تنفيذ هذا عبر AJAX
        alert('تم تسجيل الخروج من جميع الأجهزة');
        window.location.href = '../logout.php';
    }
}

function exportData() {
    if (confirm('هل تريد تصدير جميع بياناتك؟ سيتم إرسال ملف إلى بريدك الإلكتروني.')) {
        // يمكن تنفيذ هذا عبر AJAX
        alert('سيتم إرسال ملف البيانات إلى بريدك الإلكتروني خلال 24 ساعة');
    }
}

function deleteAccount() {
    const confirmation = prompt('لحذف حسابك نهائياً، اكتب "حذف الحساب" في المربع أدناه:');

    if (confirmation === 'حذف الحساب') {
        if (confirm('هل أنت متأكد تماماً؟ هذا الإجراء لا يمكن التراجع عنه وستفقد جميع بياناتك.')) {
            alert('سيتم حذف حسابك خلال 30 يوماً. يمكنك إلغاء هذا الطلب بتسجيل الدخول خلال هذه الفترة.');
            // يمكن تنفيذ هذا عبر AJAX
        }
    } else if (confirmation !== null) {
        alert('النص المدخل غير صحيح. لم يتم حذف الحساب.');
    }
}

// تحسين تجربة المستخدم
document.addEventListener('DOMContentLoaded', function() {
    // إضافة تأثيرات بصرية للتبويبات
    const tabButtons = document.querySelectorAll('#settingsTabs .nav-link');
    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            // إزالة الفئة النشطة من جميع الأزرار
            tabButtons.forEach(btn => btn.classList.remove('active'));
            // إضافة الفئة النشطة للزر المضغوط
            this.classList.add('active');
        });
    });

    // تحسين النماذج
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function() {
            const submitButton = this.querySelector('button[type="submit"]');
            if (submitButton) {
                submitButton.disabled = true;
                submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري الحفظ...';
            }
        });
    });
});
</script>

<style>
.nav-pills .nav-link {
    border-radius: 0;
    border-bottom: 1px solid #dee2e6;
    color: var(--bs-gray-700);
    padding: 1rem 1.5rem;
}

.nav-pills .nav-link:hover {
    background-color: rgba(0,123,255,0.05);
}

.nav-pills .nav-link.active {
    background-color: var(--bs-primary);
    color: white;
    border-color: var(--bs-primary);
}

.form-check-input:checked {
    background-color: var(--bs-success);
    border-color: var(--bs-success);
}

.form-switch .form-check-input {
    width: 2.5rem;
    height: 1.25rem;
}

.form-switch .form-check-input:checked {
    background-position: right center;
}

.alert {
    border: none;
    border-radius: 0.5rem;
}

.btn {
    border-radius: 0.375rem;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
}

.card {
    border-radius: 0.5rem;
    transition: box-shadow 0.3s ease;
}

.card:hover {
    box-shadow: 0 4px 15px rgba(0,0,0,0.1) !important;
}

@media (max-width: 768px) {
    .nav-pills {
        flex-direction: row;
        overflow-x: auto;
    }

    .nav-pills .nav-link {
        white-space: nowrap;
        min-width: 120px;
        text-align: center;
    }

    .d-flex.gap-2 {
        flex-direction: column;
    }

    .d-flex.gap-2 .btn {
        margin-bottom: 0.5rem;
    }
}

/* تحسين عرض النماذج */
.form-label {
    font-weight: 500;
    color: var(--bs-gray-700);
}

.form-control:focus,
.form-select:focus {
    border-color: var(--bs-primary);
    box-shadow: 0 0 0 0.2rem rgba(var(--bs-primary-rgb), 0.25);
}

.input-group .btn {
    border-color: #ced4da;
}

.input-group .btn:hover {
    background-color: #f8f9fa;
}
</style>

<?php include 'includes/footer.php'; ?>
