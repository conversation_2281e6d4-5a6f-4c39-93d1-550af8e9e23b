<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات الطالب
if (!isLoggedIn() || !isStudent()) {
    header('Location: ../login.php');
    exit;
}

$pageTitle = 'الاختبارات';
$breadcrumbs = [
    ['title' => 'الاختبارات']
];

$student_id = $_SESSION['user_id'];
$success = '';
$error = '';

// معالجة بدء الاختبار
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['start_quiz'])) {
    $quiz_id = (int)$_POST['quiz_id'];
    
    try {
        // التحقق من أن الطالب مسجل في الكورس
        $stmt = $conn->prepare("
            SELECT q.*, c.title as course_title,
                   (SELECT COUNT(*) FROM quiz_attempts WHERE quiz_id = q.id AND student_id = ?) as attempt_count
            FROM quizzes q 
            JOIN courses c ON q.course_id = c.id
            JOIN course_enrollments ce ON c.id = ce.course_id
            WHERE q.id = ? AND ce.student_id = ? AND q.status = 'published'
            AND (q.start_date IS NULL OR q.start_date <= NOW())
            AND (q.end_date IS NULL OR q.end_date >= NOW())
        ");
        $stmt->execute([$student_id, $quiz_id, $student_id]);
        $quiz = $stmt->fetch();
        
        if (!$quiz) {
            throw new Exception('الاختبار غير متاح');
        }
        
        if ($quiz['attempt_count'] >= $quiz['max_attempts']) {
            throw new Exception('لقد استنفدت عدد المحاولات المسموحة');
        }
        
        // إنشاء محاولة جديدة
        $stmt = $conn->prepare("
            INSERT INTO quiz_attempts (quiz_id, student_id, attempt_number, status)
            VALUES (?, ?, ?, 'in_progress')
        ");
        $attempt_number = $quiz['attempt_count'] + 1;
        $stmt->execute([$quiz_id, $student_id, $attempt_number]);
        $attempt_id = $conn->lastInsertId();
        
        // توجيه إلى صفحة الاختبار
        header("Location: take-quiz.php?attempt_id=$attempt_id");
        exit;
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// جلب الاختبارات
try {
    $stmt = $conn->prepare("
        SELECT q.*, c.title as course_title, c.id as course_id,
               u.name as instructor_name,
               (SELECT COUNT(*) FROM quiz_attempts WHERE quiz_id = q.id AND student_id = ?) as attempt_count,
               (SELECT MAX(score) FROM quiz_attempts WHERE quiz_id = q.id AND student_id = ? AND status = 'completed') as best_score,
               (SELECT score FROM quiz_attempts WHERE quiz_id = q.id AND student_id = ? AND status = 'completed' ORDER BY completed_at DESC LIMIT 1) as last_score,
               CASE 
                   WHEN q.end_date IS NOT NULL AND q.end_date < NOW() THEN 'expired'
                   WHEN q.start_date IS NOT NULL AND q.start_date > NOW() THEN 'upcoming'
                   WHEN (SELECT COUNT(*) FROM quiz_attempts WHERE quiz_id = q.id AND student_id = ? AND status = 'in_progress') > 0 THEN 'in_progress'
                   ELSE 'available'
               END as quiz_status
        FROM quizzes q
        JOIN courses c ON q.course_id = c.id
        JOIN course_enrollments ce ON c.id = ce.course_id
        JOIN users u ON c.instructor_id = u.id
        WHERE ce.student_id = ? AND q.status = 'published'
        ORDER BY 
            CASE 
                WHEN q.end_date IS NOT NULL THEN q.end_date
                ELSE '9999-12-31'
            END ASC,
            q.created_at DESC
    ");
    $stmt->execute([$student_id, $student_id, $student_id, $student_id, $student_id]);
    $quizzes = $stmt->fetchAll();
    
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء جلب الاختبارات';
}

include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- العنوان والإحصائيات -->
        <div class="col-12 mb-4">
            <div class="d-flex justify-content-between align-items-center">
                <h2 class="text-primary">
                    <i class="fas fa-question-circle me-2"></i>
                    الاختبارات
                </h2>
                <div class="d-flex gap-3">
                    <?php
                    $available_count = count(array_filter($quizzes, fn($q) => $q['quiz_status'] === 'available'));
                    $completed_count = count(array_filter($quizzes, fn($q) => $q['attempt_count'] > 0));
                    $expired_count = count(array_filter($quizzes, fn($q) => $q['quiz_status'] === 'expired'));
                    ?>
                    <div class="badge bg-primary fs-6">متاحة: <?php echo $available_count; ?></div>
                    <div class="badge bg-success fs-6">مكتملة: <?php echo $completed_count; ?></div>
                    <div class="badge bg-danger fs-6">منتهية: <?php echo $expired_count; ?></div>
                </div>
            </div>
        </div>

        <?php if ($success): ?>
        <div class="col-12 mb-3">
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo htmlspecialchars($success); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </div>
        <?php endif; ?>

        <?php if ($error): ?>
        <div class="col-12 mb-3">
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-circle me-2"></i>
                <?php echo htmlspecialchars($error); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </div>
        <?php endif; ?>

        <!-- قائمة الاختبارات -->
        <div class="col-12">
            <?php if (empty($quizzes)): ?>
            <div class="card-student text-center py-5">
                <div class="card-body">
                    <i class="fas fa-question-circle fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">لا توجد اختبارات متاحة</h4>
                    <p class="text-muted">لم يتم إنشاء أي اختبارات في الكورسات المسجل بها حالياً</p>
                </div>
            </div>
            <?php else: ?>
            <div class="row">
                <?php foreach ($quizzes as $quiz): ?>
                <div class="col-lg-6 col-xl-4 mb-4">
                    <div class="card-student h-100">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h6 class="mb-0 text-primary"><?php echo htmlspecialchars($quiz['course_title']); ?></h6>
                            <?php
                            $status_class = '';
                            $status_text = '';
                            switch ($quiz['quiz_status']) {
                                case 'available':
                                    $status_class = 'bg-success';
                                    $status_text = 'متاح';
                                    break;
                                case 'upcoming':
                                    $status_class = 'bg-warning';
                                    $status_text = 'قريباً';
                                    break;
                                case 'expired':
                                    $status_class = 'bg-danger';
                                    $status_text = 'منتهي';
                                    break;
                                case 'in_progress':
                                    $status_class = 'bg-info';
                                    $status_text = 'جاري';
                                    break;
                            }
                            ?>
                            <span class="badge <?php echo $status_class; ?>"><?php echo $status_text; ?></span>
                        </div>
                        <div class="card-body">
                            <h5 class="card-title"><?php echo htmlspecialchars($quiz['title']); ?></h5>
                            <p class="card-text text-muted">
                                <?php echo htmlspecialchars(substr($quiz['description'], 0, 100)) . '...'; ?>
                            </p>
                            
                            <div class="quiz-details mb-3">
                                <div class="d-flex justify-content-between mb-2">
                                    <span class="text-muted">المدرب:</span>
                                    <span><?php echo htmlspecialchars($quiz['instructor_name']); ?></span>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span class="text-muted">الدرجة الكاملة:</span>
                                    <span><?php echo $quiz['total_marks']; ?> نقطة</span>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span class="text-muted">درجة النجاح:</span>
                                    <span><?php echo $quiz['passing_score']; ?>%</span>
                                </div>
                                <?php if ($quiz['time_limit']): ?>
                                <div class="d-flex justify-content-between mb-2">
                                    <span class="text-muted">المدة المحددة:</span>
                                    <span><?php echo $quiz['time_limit']; ?> دقيقة</span>
                                </div>
                                <?php endif; ?>
                                <div class="d-flex justify-content-between mb-2">
                                    <span class="text-muted">المحاولات:</span>
                                    <span><?php echo $quiz['attempt_count']; ?> / <?php echo $quiz['max_attempts']; ?></span>
                                </div>
                                
                                <?php if ($quiz['start_date']): ?>
                                <div class="d-flex justify-content-between mb-2">
                                    <span class="text-muted">تاريخ البداية:</span>
                                    <span><?php echo date('Y-m-d H:i', strtotime($quiz['start_date'])); ?></span>
                                </div>
                                <?php endif; ?>
                                
                                <?php if ($quiz['end_date']): ?>
                                <div class="d-flex justify-content-between mb-2">
                                    <span class="text-muted">تاريخ النهاية:</span>
                                    <span class="<?php echo $quiz['quiz_status'] === 'expired' ? 'text-danger' : ''; ?>">
                                        <?php echo date('Y-m-d H:i', strtotime($quiz['end_date'])); ?>
                                    </span>
                                </div>
                                <?php endif; ?>
                                
                                <?php if ($quiz['attempt_count'] > 0): ?>
                                <div class="results-info mt-3 p-3 bg-light rounded">
                                    <h6 class="text-success mb-2">
                                        <i class="fas fa-chart-line me-1"></i>
                                        النتائج
                                    </h6>
                                    <?php if ($quiz['best_score'] !== null): ?>
                                    <div class="d-flex justify-content-between mb-1">
                                        <span class="text-muted">أفضل درجة:</span>
                                        <span class="fw-bold text-primary">
                                            <?php echo number_format($quiz['best_score'], 1); ?>%
                                        </span>
                                    </div>
                                    <?php endif; ?>
                                    <?php if ($quiz['last_score'] !== null): ?>
                                    <div class="d-flex justify-content-between mb-1">
                                        <span class="text-muted">آخر درجة:</span>
                                        <span class="fw-bold">
                                            <?php echo number_format($quiz['last_score'], 1); ?>%
                                        </span>
                                    </div>
                                    <?php endif; ?>
                                    <div class="d-flex justify-content-between">
                                        <span class="text-muted">الحالة:</span>
                                        <span class="<?php echo ($quiz['best_score'] ?? 0) >= $quiz['passing_score'] ? 'text-success' : 'text-danger'; ?>">
                                            <?php echo ($quiz['best_score'] ?? 0) >= $quiz['passing_score'] ? 'ناجح' : 'راسب'; ?>
                                        </span>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="card-footer">
                            <?php if ($quiz['quiz_status'] === 'available' && $quiz['attempt_count'] < $quiz['max_attempts']): ?>
                            <form method="POST" class="d-inline w-100">
                                <input type="hidden" name="quiz_id" value="<?php echo $quiz['id']; ?>">
                                <button type="submit" name="start_quiz" class="btn btn-student-primary w-100">
                                    <i class="fas fa-play me-1"></i>
                                    <?php echo $quiz['attempt_count'] > 0 ? 'إعادة المحاولة' : 'بدء الاختبار'; ?>
                                </button>
                            </form>
                            <?php elseif ($quiz['quiz_status'] === 'in_progress'): ?>
                            <a href="take-quiz.php?quiz_id=<?php echo $quiz['id']; ?>" class="btn btn-warning w-100">
                                <i class="fas fa-clock me-1"></i>
                                متابعة الاختبار
                            </a>
                            <?php elseif ($quiz['quiz_status'] === 'upcoming'): ?>
                            <button class="btn btn-outline-warning w-100" disabled>
                                <i class="fas fa-clock me-1"></i>
                                لم يحن الوقت بعد
                            </button>
                            <?php elseif ($quiz['quiz_status'] === 'expired'): ?>
                            <button class="btn btn-outline-danger w-100" disabled>
                                <i class="fas fa-times me-1"></i>
                                انتهت المهلة
                            </button>
                            <?php elseif ($quiz['attempt_count'] >= $quiz['max_attempts']): ?>
                            <button class="btn btn-outline-secondary w-100" disabled>
                                <i class="fas fa-ban me-1"></i>
                                استنفدت المحاولات
                            </button>
                            <?php endif; ?>
                            
                            <button class="btn btn-outline-primary w-100 mt-2" data-bs-toggle="modal" 
                                    data-bs-target="#detailsModal<?php echo $quiz['id']; ?>">
                                <i class="fas fa-eye me-1"></i>
                                عرض التفاصيل
                            </button>
                            
                            <?php if ($quiz['attempt_count'] > 0): ?>
                            <a href="quiz-results.php?quiz_id=<?php echo $quiz['id']; ?>" 
                               class="btn btn-outline-info w-100 mt-2">
                                <i class="fas fa-chart-bar me-1"></i>
                                عرض النتائج
                            </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Modals تفاصيل الاختبارات -->
<?php foreach ($quizzes as $quiz): ?>
<div class="modal fade" id="detailsModal<?php echo $quiz['id']; ?>" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><?php echo htmlspecialchars($quiz['title']); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-8">
                        <div class="mb-3">
                            <h6 class="text-primary">الوصف:</h6>
                            <p><?php echo nl2br(htmlspecialchars($quiz['description'])); ?></p>
                        </div>
                        
                        <?php if ($quiz['instructions']): ?>
                        <div class="mb-3">
                            <h6 class="text-primary">التعليمات:</h6>
                            <p><?php echo nl2br(htmlspecialchars($quiz['instructions'])); ?></p>
                        </div>
                        <?php endif; ?>
                    </div>
                    <div class="col-md-4">
                        <h6 class="text-primary">معلومات الاختبار:</h6>
                        <ul class="list-unstyled">
                            <li><strong>الكورس:</strong> <?php echo htmlspecialchars($quiz['course_title']); ?></li>
                            <li><strong>المدرب:</strong> <?php echo htmlspecialchars($quiz['instructor_name']); ?></li>
                            <li><strong>الدرجة الكاملة:</strong> <?php echo $quiz['total_marks']; ?> نقطة</li>
                            <li><strong>درجة النجاح:</strong> <?php echo $quiz['passing_score']; ?>%</li>
                            <li><strong>عدد المحاولات:</strong> <?php echo $quiz['max_attempts']; ?></li>
                            <?php if ($quiz['time_limit']): ?>
                            <li><strong>المدة المحددة:</strong> <?php echo $quiz['time_limit']; ?> دقيقة</li>
                            <?php endif; ?>
                            <?php if ($quiz['start_date']): ?>
                            <li><strong>تاريخ البداية:</strong> <?php echo date('Y-m-d H:i', strtotime($quiz['start_date'])); ?></li>
                            <?php endif; ?>
                            <?php if ($quiz['end_date']): ?>
                            <li><strong>تاريخ النهاية:</strong> <?php echo date('Y-m-d H:i', strtotime($quiz['end_date'])); ?></li>
                            <?php endif; ?>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <?php if ($quiz['quiz_status'] === 'available' && $quiz['attempt_count'] < $quiz['max_attempts']): ?>
                <form method="POST" class="d-inline">
                    <input type="hidden" name="quiz_id" value="<?php echo $quiz['id']; ?>">
                    <button type="submit" name="start_quiz" class="btn btn-student-primary">
                        <i class="fas fa-play me-1"></i>
                        <?php echo $quiz['attempt_count'] > 0 ? 'إعادة المحاولة' : 'بدء الاختبار'; ?>
                    </button>
                </form>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php endforeach; ?>

<?php include 'includes/footer.php'; ?>
