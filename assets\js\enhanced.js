/**
 * ملف JavaScript المتقدم للموقع
 * Enhanced JavaScript for Learning Platform
 */

class LearningPlatformEnhanced {
    constructor() {
        this.isInitialized = false;
        this.components = {};
        this.animations = {};
        this.utils = {};
        
        this.init();
    }

    init() {
        if (this.isInitialized) return;
        
        document.addEventListener('DOMContentLoaded', () => {
            this.initializeComponents();
            this.initializeEvents();
            this.initializeAnimations();
            this.initializeAccessibility();
            this.isInitialized = true;
            
            console.log('🚀 Enhanced Learning Platform initialized');
        });
    }

    initializeComponents() {
        // تهيئة شريط التنقل المتقدم
        this.initEnhancedNavbar();
        
        // تهيئة مدير الكورسات
        this.initCourseManager();
        
        // تهيئة النماذج المحسنة
        this.initEnhancedForms();
        
        // تهيئة الإشعارات
        this.initNotifications();
    }

    initEnhancedNavbar() {
        const navbar = document.getElementById('mainNavbar');
        if (!navbar) return;

        // تأثير التمرير
        let lastScrollY = 0;
        window.addEventListener('scroll', () => {
            const scrollY = window.scrollY;
            
            if (scrollY > 100) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }

            // إخفاء/إظهار شريط التنقل عند التمرير
            if (scrollY > lastScrollY && scrollY > 200) {
                navbar.style.transform = 'translateY(-100%)';
            } else {
                navbar.style.transform = 'translateY(0)';
            }
            
            lastScrollY = scrollY;
        });

        // تحسين القائمة المحمولة
        const navbarToggler = navbar.querySelector('.navbar-toggler');
        const navbarCollapse = navbar.querySelector('.navbar-collapse');
        
        if (navbarToggler && navbarCollapse) {
            navbarToggler.addEventListener('click', () => {
                navbarCollapse.classList.toggle('show');
                document.body.classList.toggle('nav-open');
            });

            // إغلاق القائمة عند النقر خارجها
            document.addEventListener('click', (e) => {
                if (!navbar.contains(e.target) && navbarCollapse.classList.contains('show')) {
                    navbarCollapse.classList.remove('show');
                    document.body.classList.remove('nav-open');
                }
            });
        }
    }

    initCourseManager() {
        // فلتر الكورسات المحسن
        const categoryButtons = document.querySelectorAll('.category-btn');
        const courseItems = document.querySelectorAll('.course-item');

        categoryButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                
                // تحديث الأزرار
                categoryButtons.forEach(btn => {
                    btn.classList.remove('active', 'btn-primary-enhanced');
                    btn.classList.add('btn-secondary-enhanced');
                });
                
                button.classList.add('active', 'btn-primary-enhanced');
                button.classList.remove('btn-secondary-enhanced');

                const selectedCategory = button.getAttribute('data-category');
                this.filterCourses(selectedCategory, courseItems);
            });
        });

        // البحث المباشر
        this.initLiveSearch();
        
        // تحميل المزيد
        this.initLoadMore();
    }

    filterCourses(category, courseItems) {
        courseItems.forEach((item, index) => {
            const itemCategory = item.getAttribute('data-category');
            const shouldShow = category === 'all' || itemCategory === category;
            
            if (shouldShow) {
                item.style.display = 'block';
                item.style.animation = `fadeInUp 0.6s ease-out ${index * 0.1}s both`;
            } else {
                item.style.animation = 'fadeOut 0.3s ease-out both';
                setTimeout(() => {
                    item.style.display = 'none';
                }, 300);
            }
        });

        // تحديث العداد
        const visibleCount = Array.from(courseItems).filter(item => 
            item.style.display !== 'none'
        ).length;
        
        this.updateCourseCount(visibleCount);
    }

    initLiveSearch() {
        const searchInput = document.querySelector('#courseSearch');
        if (!searchInput) return;

        let searchTimeout;
        searchInput.addEventListener('input', (e) => {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                this.performSearch(e.target.value);
            }, 300);
        });
    }

    performSearch(query) {
        const courseItems = document.querySelectorAll('.course-item');
        const normalizedQuery = query.toLowerCase().trim();

        courseItems.forEach(item => {
            const title = item.querySelector('.course-card-title')?.textContent.toLowerCase() || '';
            const description = item.querySelector('.course-card-description')?.textContent.toLowerCase() || '';
            const instructor = item.querySelector('.course-card-instructor span')?.textContent.toLowerCase() || '';
            
            const matches = title.includes(normalizedQuery) || 
                          description.includes(normalizedQuery) || 
                          instructor.includes(normalizedQuery);
            
            item.style.display = matches ? 'block' : 'none';
        });
    }

    initLoadMore() {
        const loadMoreBtn = document.querySelector('#loadMoreCourses');
        if (!loadMoreBtn) return;

        loadMoreBtn.addEventListener('click', async (e) => {
            e.preventDefault();
            
            const button = e.target;
            const originalText = button.innerHTML;
            
            // إظهار حالة التحميل
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحميل...';
            button.disabled = true;

            try {
                // محاكاة تحميل البيانات
                await this.loadMoreCourses();
                
                // إخفاء الزر إذا لم تعد هناك بيانات
                button.style.display = 'none';
            } catch (error) {
                console.error('Error loading more courses:', error);
                this.showNotification('حدث خطأ أثناء تحميل المزيد من الكورسات', 'error');
            } finally {
                button.innerHTML = originalText;
                button.disabled = false;
            }
        });
    }

    async loadMoreCourses() {
        // محاكاة API call
        return new Promise(resolve => {
            setTimeout(resolve, 1000);
        });
    }

    initEnhancedForms() {
        const forms = document.querySelectorAll('form');
        
        forms.forEach(form => {
            // التحقق المباشر
            this.initRealTimeValidation(form);
            
            // تحسين تجربة الإرسال
            this.enhanceFormSubmission(form);
        });
    }

    initRealTimeValidation(form) {
        const inputs = form.querySelectorAll('input, textarea, select');
        
        inputs.forEach(input => {
            input.addEventListener('blur', () => {
                this.validateField(input);
            });
            
            input.addEventListener('input', () => {
                if (input.classList.contains('is-invalid')) {
                    this.validateField(input);
                }
            });
        });
    }

    validateField(field) {
        const value = field.value.trim();
        const type = field.type;
        const required = field.hasAttribute('required');
        
        let isValid = true;
        let message = '';

        // التحقق من الحقول المطلوبة
        if (required && !value) {
            isValid = false;
            message = 'هذا الحقل مطلوب';
        }
        
        // التحقق من البريد الإلكتروني
        else if (type === 'email' && value) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(value)) {
                isValid = false;
                message = 'يرجى إدخال بريد إلكتروني صحيح';
            }
        }
        
        // التحقق من كلمة المرور
        else if (type === 'password' && value) {
            if (value.length < 8) {
                isValid = false;
                message = 'كلمة المرور يجب أن تكون 8 أحرف على الأقل';
            }
        }

        // تطبيق النتيجة
        if (isValid) {
            field.classList.remove('is-invalid');
            field.classList.add('is-valid');
        } else {
            field.classList.remove('is-valid');
            field.classList.add('is-invalid');
        }

        // إظهار رسالة الخطأ
        this.showFieldMessage(field, message, !isValid);
        
        return isValid;
    }

    showFieldMessage(field, message, isError) {
        let messageElement = field.parentNode.querySelector('.field-message');
        
        if (!messageElement) {
            messageElement = document.createElement('div');
            messageElement.className = 'field-message';
            field.parentNode.appendChild(messageElement);
        }
        
        messageElement.textContent = message;
        messageElement.className = `field-message ${isError ? 'text-danger' : 'text-success'}`;
        messageElement.style.display = message ? 'block' : 'none';
    }

    enhanceFormSubmission(form) {
        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            // التحقق من جميع الحقول
            const inputs = form.querySelectorAll('input, textarea, select');
            let isFormValid = true;
            
            inputs.forEach(input => {
                if (!this.validateField(input)) {
                    isFormValid = false;
                }
            });
            
            if (!isFormValid) {
                this.showNotification('يرجى تصحيح الأخطاء في النموذج', 'error');
                return;
            }
            
            // إرسال النموذج
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الإرسال...';
            submitBtn.disabled = true;
            
            try {
                await this.submitForm(form);
                this.showNotification('تم الإرسال بنجاح', 'success');
            } catch (error) {
                this.showNotification('حدث خطأ أثناء الإرسال', 'error');
            } finally {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }
        });
    }

    async submitForm(form) {
        const formData = new FormData(form);
        const action = form.action || window.location.href;
        
        const response = await fetch(action, {
            method: 'POST',
            body: formData
        });
        
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        
        return response.json();
    }

    initNotifications() {
        // إنشاء حاوية الإشعارات
        if (!document.querySelector('.notifications-container')) {
            const container = document.createElement('div');
            container.className = 'notifications-container';
            container.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 9999;
                max-width: 400px;
            `;
            document.body.appendChild(container);
        }
    }

    showNotification(message, type = 'info', duration = 5000) {
        const container = document.querySelector('.notifications-container');
        if (!container) return;

        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.style.cssText = `
            background: var(--white);
            border: 1px solid var(--gray-200);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-4);
            margin-bottom: var(--spacing-2);
            box-shadow: var(--shadow-lg);
            transform: translateX(100%);
            transition: var(--transition-normal);
            display: flex;
            align-items: center;
            gap: var(--spacing-3);
        `;

        const icon = this.getNotificationIcon(type);
        const color = this.getNotificationColor(type);
        
        notification.innerHTML = `
            <i class="${icon}" style="color: ${color}; font-size: 1.2rem;"></i>
            <span style="flex: 1;">${message}</span>
            <button class="btn-close" style="border: none; background: none; font-size: 1.2rem; cursor: pointer;">&times;</button>
        `;

        container.appendChild(notification);

        // إظهار الإشعار
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);

        // إخفاء الإشعار تلقائياً
        setTimeout(() => {
            this.hideNotification(notification);
        }, duration);

        // إغلاق عند النقر
        notification.querySelector('.btn-close').addEventListener('click', () => {
            this.hideNotification(notification);
        });
    }

    hideNotification(notification) {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            notification.remove();
        }, 300);
    }

    getNotificationIcon(type) {
        const icons = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        };
        return icons[type] || icons.info;
    }

    getNotificationColor(type) {
        const colors = {
            success: 'var(--success-color)',
            error: 'var(--danger-color)',
            warning: 'var(--warning-color)',
            info: 'var(--info-color)'
        };
        return colors[type] || colors.info;
    }

    updateCourseCount(count) {
        const countElement = document.querySelector('#courseCount');
        if (countElement) {
            countElement.textContent = count;
        }
    }

    initializeEvents() {
        // أحداث النقر العامة
        this.initClickEvents();
        
        // أحداث التمرير
        this.initScrollEvents();
        
        // أحداث لوحة المفاتيح
        this.initKeyboardEvents();
    }

    initClickEvents() {
        // تفويض الأحداث للأداء الأفضل
        document.addEventListener('click', (e) => {
            // تأثير الموجة للأزرار
            if (e.target.closest('.btn-enhanced')) {
                this.createRippleEffect(e);
            }
            
            // التمرير السلس للروابط
            if (e.target.closest('a[href^="#"]')) {
                e.preventDefault();
                const target = document.querySelector(e.target.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            }
        });
    }

    createRippleEffect(e) {
        const button = e.target.closest('.btn-enhanced');
        if (!button) return;

        const ripple = document.createElement('span');
        const rect = button.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = e.clientX - rect.left - size / 2;
        const y = e.clientY - rect.top - size / 2;
        
        ripple.style.cssText = `
            position: absolute;
            width: ${size}px;
            height: ${size}px;
            left: ${x}px;
            top: ${y}px;
            background: rgba(255, 255, 255, 0.6);
            border-radius: 50%;
            transform: scale(0);
            animation: ripple 0.6s linear;
            pointer-events: none;
        `;
        
        button.style.position = 'relative';
        button.style.overflow = 'hidden';
        button.appendChild(ripple);
        
        setTimeout(() => {
            ripple.remove();
        }, 600);
    }

    initScrollEvents() {
        // عداد الأرقام المتحرك
        this.initCounterAnimation();
        
        // إظهار العناصر عند التمرير
        this.initScrollReveal();
    }

    initCounterAnimation() {
        const counters = document.querySelectorAll('.counter');
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.animateCounter(entry.target);
                    observer.unobserve(entry.target);
                }
            });
        });

        counters.forEach(counter => observer.observe(counter));
    }

    animateCounter(counter) {
        const target = parseInt(counter.getAttribute('data-target'));
        const duration = 2000;
        const step = target / (duration / 16);
        let current = 0;

        const timer = setInterval(() => {
            current += step;
            if (current >= target) {
                counter.textContent = target;
                clearInterval(timer);
            } else {
                counter.textContent = Math.floor(current);
            }
        }, 16);
    }

    initScrollReveal() {
        const elements = document.querySelectorAll('.animate-on-scroll');
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('in-view');
                }
            });
        });

        elements.forEach(element => observer.observe(element));
    }

    initKeyboardEvents() {
        document.addEventListener('keydown', (e) => {
            // ESC للإغلاق
            if (e.key === 'Escape') {
                this.closeModals();
            }
            
            // Tab للتنقل
            if (e.key === 'Tab') {
                document.body.classList.add('keyboard-navigation');
            }
        });

        document.addEventListener('mousedown', () => {
            document.body.classList.remove('keyboard-navigation');
        });
    }

    closeModals() {
        const modals = document.querySelectorAll('.modal.show');
        modals.forEach(modal => {
            const bsModal = bootstrap.Modal.getInstance(modal);
            if (bsModal) {
                bsModal.hide();
            }
        });
    }

    initializeAnimations() {
        // إضافة CSS للرسوم المتحركة
        this.addAnimationStyles();
    }

    addAnimationStyles() {
        const style = document.createElement('style');
        style.textContent = `
            @keyframes ripple {
                to {
                    transform: scale(4);
                    opacity: 0;
                }
            }
            
            @keyframes fadeInUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
            
            @keyframes fadeOut {
                from {
                    opacity: 1;
                    transform: scale(1);
                }
                to {
                    opacity: 0;
                    transform: scale(0.8);
                }
            }
            
            .keyboard-navigation *:focus {
                outline: 2px solid var(--primary-color) !important;
                outline-offset: 2px !important;
            }
            
            .field-message {
                font-size: 0.875rem;
                margin-top: 0.25rem;
            }
            
            .nav-open {
                overflow: hidden;
            }
            
            .navbar-enhanced {
                transition: transform 0.3s ease-in-out;
            }
        `;
        document.head.appendChild(style);
    }

    initializeAccessibility() {
        // تحسين إمكانية الوصول
        this.enhanceAccessibility();
    }

    enhanceAccessibility() {
        // إضافة ARIA labels للعناصر التفاعلية
        const interactiveElements = document.querySelectorAll('button, a, input, select, textarea');
        interactiveElements.forEach(element => {
            if (!element.getAttribute('aria-label') && !element.getAttribute('aria-labelledby')) {
                const text = element.textContent || element.value || element.placeholder;
                if (text) {
                    element.setAttribute('aria-label', text.trim());
                }
            }
        });

        // تحسين التنقل بلوحة المفاتيح
        const focusableElements = document.querySelectorAll(
            'a, button, input, textarea, select, details, [tabindex]:not([tabindex="-1"])'
        );
        
        focusableElements.forEach((element, index) => {
            element.addEventListener('keydown', (e) => {
                if (e.key === 'ArrowDown' || e.key === 'ArrowRight') {
                    e.preventDefault();
                    const nextIndex = (index + 1) % focusableElements.length;
                    focusableElements[nextIndex].focus();
                } else if (e.key === 'ArrowUp' || e.key === 'ArrowLeft') {
                    e.preventDefault();
                    const prevIndex = (index - 1 + focusableElements.length) % focusableElements.length;
                    focusableElements[prevIndex].focus();
                }
            });
        });
    }
}

// تهيئة النظام
const enhancedPlatform = new LearningPlatformEnhanced();
