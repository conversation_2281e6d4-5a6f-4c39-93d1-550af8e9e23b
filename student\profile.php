<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات الطالب
if (!isLoggedIn() || !isStudent()) {
    header('Location: ../login.php');
    exit;
}

$pageTitle = 'الملف الشخصي';
$breadcrumbs = [
    ['title' => 'الملف الشخصي']
];

$student_id = $_SESSION['user_id'];
$success = '';
$error = '';

// معالجة تحديث الملف الشخصي
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_profile'])) {
    $name = trim($_POST['name']);
    $email = trim($_POST['email']);
    $phone = trim($_POST['phone']);
    $bio = trim($_POST['bio']);
    $date_of_birth = $_POST['date_of_birth'] ?: null;
    $gender = $_POST['gender'] ?: null;
    $country = trim($_POST['country']);
    $city = trim($_POST['city']);
    $timezone = $_POST['timezone'];
    $language = $_POST['language'];
    
    try {
        if (empty($name) || empty($email)) {
            throw new Exception('الاسم والبريد الإلكتروني مطلوبان');
        }
        
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            throw new Exception('البريد الإلكتروني غير صحيح');
        }
        
        // التحقق من عدم تكرار البريد الإلكتروني
        $stmt = $conn->prepare("SELECT id FROM users WHERE email = ? AND id != ?");
        $stmt->execute([$email, $student_id]);
        if ($stmt->fetch()) {
            throw new Exception('البريد الإلكتروني مستخدم من قبل مستخدم آخر');
        }
        
        // معالجة رفع صورة الملف الشخصي
        $profile_image = null;
        if (isset($_FILES['profile_image']) && $_FILES['profile_image']['error'] === UPLOAD_ERR_OK) {
            $upload_dir = '../uploads/users/';
            if (!is_dir($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }
            
            $file_extension = pathinfo($_FILES['profile_image']['name'], PATHINFO_EXTENSION);
            $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif'];
            
            if (!in_array(strtolower($file_extension), $allowed_extensions)) {
                throw new Exception('نوع الصورة غير مسموح');
            }
            
            $file_name = 'profile_' . $student_id . '_' . time() . '.' . $file_extension;
            $profile_image = $upload_dir . $file_name;
            
            if (!move_uploaded_file($_FILES['profile_image']['tmp_name'], $profile_image)) {
                throw new Exception('فشل في رفع الصورة');
            }
        }
        
        // تحديث البيانات
        $update_fields = [
            'name = ?', 'email = ?', 'phone = ?', 'bio = ?', 
            'date_of_birth = ?', 'gender = ?', 'country = ?', 
            'city = ?', 'timezone = ?', 'language = ?', 'updated_at = NOW()'
        ];
        $params = [$name, $email, $phone, $bio, $date_of_birth, $gender, $country, $city, $timezone, $language];
        
        if ($profile_image) {
            $update_fields[] = 'profile_image = ?';
            $params[] = $profile_image;
        }
        
        $params[] = $student_id;
        
        $stmt = $conn->prepare("UPDATE users SET " . implode(', ', $update_fields) . " WHERE id = ?");
        $stmt->execute($params);
        
        // تحديث بيانات الجلسة
        $_SESSION['name'] = $name;
        $_SESSION['email'] = $email;
        
        $success = 'تم تحديث الملف الشخصي بنجاح';
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// معالجة تغيير كلمة المرور
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['change_password'])) {
    $current_password = $_POST['current_password'];
    $new_password = $_POST['new_password'];
    $confirm_password = $_POST['confirm_password'];
    
    try {
        if (empty($current_password) || empty($new_password) || empty($confirm_password)) {
            throw new Exception('جميع حقول كلمة المرور مطلوبة');
        }
        
        if ($new_password !== $confirm_password) {
            throw new Exception('كلمة المرور الجديدة وتأكيدها غير متطابقين');
        }
        
        if (strlen($new_password) < 8) {
            throw new Exception('كلمة المرور يجب أن تكون 8 أحرف على الأقل');
        }
        
        // التحقق من كلمة المرور الحالية
        $stmt = $conn->prepare("SELECT password FROM users WHERE id = ?");
        $stmt->execute([$student_id]);
        $user = $stmt->fetch();
        
        if (!password_verify($current_password, $user['password'])) {
            throw new Exception('كلمة المرور الحالية غير صحيحة');
        }
        
        // تحديث كلمة المرور
        $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
        $stmt = $conn->prepare("UPDATE users SET password = ?, updated_at = NOW() WHERE id = ?");
        $stmt->execute([$hashed_password, $student_id]);
        
        $success = 'تم تغيير كلمة المرور بنجاح';
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

try {
    // جلب بيانات المستخدم
    $stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$student_id]);
    $user = $stmt->fetch();
    
    if (!$user) {
        header('Location: ../logout.php');
        exit;
    }
    
    // جلب إحصائيات الطالب
    $stmt = $conn->prepare("
        SELECT 
            (SELECT COUNT(*) FROM course_enrollments WHERE student_id = ?) as enrolled_courses,
            (SELECT COUNT(*) FROM course_enrollments WHERE student_id = ? AND status = 'completed') as completed_courses,
            (SELECT COUNT(*) FROM certificates WHERE student_id = ?) as certificates_count,
            (SELECT AVG(score) FROM quiz_attempts WHERE student_id = ? AND status = 'completed') as avg_quiz_score
    ");
    $stmt->execute([$student_id, $student_id, $student_id, $student_id]);
    $stats = $stmt->fetch();
    
    // جلب النشاطات الأخيرة
    $stmt = $conn->prepare("
        SELECT 'course_enrollment' as type, c.title as title, ce.enrolled_at as date
        FROM course_enrollments ce
        JOIN courses c ON ce.course_id = c.id
        WHERE ce.student_id = ?
        
        UNION ALL
        
        SELECT 'assignment_submission' as type, a.title as title, asub.submitted_at as date
        FROM assignment_submissions asub
        JOIN assignments a ON asub.assignment_id = a.id
        WHERE asub.student_id = ?
        
        UNION ALL
        
        SELECT 'quiz_completion' as type, q.title as title, qa.completed_at as date
        FROM quiz_attempts qa
        JOIN quizzes q ON qa.quiz_id = q.id
        WHERE qa.student_id = ? AND qa.status = 'completed'
        
        ORDER BY date DESC
        LIMIT 10
    ");
    $stmt->execute([$student_id, $student_id, $student_id]);
    $recent_activities = $stmt->fetchAll();
    
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء جلب البيانات';
}

include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- العنوان -->
        <div class="col-12 mb-4">
            <h2 class="text-primary">
                <i class="fas fa-user me-2"></i>
                الملف الشخصي
            </h2>
            <p class="text-muted">إدارة معلوماتك الشخصية وإعداداتك</p>
        </div>

        <?php if ($success): ?>
        <div class="col-12 mb-3">
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo htmlspecialchars($success); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </div>
        <?php endif; ?>

        <?php if ($error): ?>
        <div class="col-12 mb-3">
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-circle me-2"></i>
                <?php echo htmlspecialchars($error); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </div>
        <?php endif; ?>

        <!-- معلومات الملف الشخصي -->
        <div class="col-lg-4 mb-4">
            <div class="card-student text-center">
                <div class="card-body">
                    <div class="profile-image-container mb-3">
                        <?php if ($user['profile_image'] && file_exists($user['profile_image'])): ?>
                        <img src="<?php echo htmlspecialchars($user['profile_image']); ?>" 
                             class="profile-image" alt="صورة الملف الشخصي">
                        <?php else: ?>
                        <div class="profile-image-placeholder">
                            <i class="fas fa-user fa-3x text-muted"></i>
                        </div>
                        <?php endif; ?>
                    </div>
                    
                    <h4 class="text-primary"><?php echo htmlspecialchars($user['name']); ?></h4>
                    <p class="text-muted"><?php echo htmlspecialchars($user['email']); ?></p>
                    
                    <?php if ($user['bio']): ?>
                    <p class="text-muted"><?php echo htmlspecialchars($user['bio']); ?></p>
                    <?php endif; ?>
                    
                    <div class="profile-stats mt-4">
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="stat-item">
                                    <h5 class="text-primary mb-1"><?php echo $stats['enrolled_courses']; ?></h5>
                                    <small class="text-muted">كورسات مسجلة</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="stat-item">
                                    <h5 class="text-success mb-1"><?php echo $stats['completed_courses']; ?></h5>
                                    <small class="text-muted">كورسات مكتملة</small>
                                </div>
                            </div>
                        </div>
                        <div class="row text-center mt-3">
                            <div class="col-6">
                                <div class="stat-item">
                                    <h5 class="text-warning mb-1"><?php echo $stats['certificates_count']; ?></h5>
                                    <small class="text-muted">شهادات</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="stat-item">
                                    <h5 class="text-info mb-1">
                                        <?php echo $stats['avg_quiz_score'] ? number_format($stats['avg_quiz_score'], 1) . '%' : '0%'; ?>
                                    </h5>
                                    <small class="text-muted">متوسط الدرجات</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="profile-info mt-4">
                        <div class="d-flex justify-content-between mb-2">
                            <span class="text-muted">تاريخ التسجيل:</span>
                            <span><?php echo date('Y-m-d', strtotime($user['created_at'])); ?></span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span class="text-muted">آخر تسجيل دخول:</span>
                            <span>
                                <?php echo $user['last_login'] ? date('Y-m-d H:i', strtotime($user['last_login'])) : 'لم يسجل دخول من قبل'; ?>
                            </span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span class="text-muted">الحالة:</span>
                            <span class="badge bg-success">نشط</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- تبويبات الإعدادات -->
        <div class="col-lg-8">
            <div class="card-student">
                <div class="card-header">
                    <ul class="nav nav-tabs card-header-tabs" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" data-bs-toggle="tab" href="#profile-tab">
                                <i class="fas fa-user me-1"></i>
                                المعلومات الشخصية
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="tab" href="#password-tab">
                                <i class="fas fa-lock me-1"></i>
                                كلمة المرور
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="tab" href="#activity-tab">
                                <i class="fas fa-history me-1"></i>
                                النشاطات الأخيرة
                            </a>
                        </li>
                    </ul>
                </div>
                <div class="card-body">
                    <div class="tab-content">
                        <!-- تبويب المعلومات الشخصية -->
                        <div class="tab-pane fade show active" id="profile-tab">
                            <form method="POST" enctype="multipart/form-data">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الاسم الكامل</label>
                                        <input type="text" name="name" class="form-control" required
                                               value="<?php echo htmlspecialchars($user['name']); ?>">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">البريد الإلكتروني</label>
                                        <input type="email" name="email" class="form-control" required
                                               value="<?php echo htmlspecialchars($user['email']); ?>">
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">رقم الهاتف</label>
                                        <input type="tel" name="phone" class="form-control"
                                               value="<?php echo htmlspecialchars($user['phone']); ?>">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">تاريخ الميلاد</label>
                                        <input type="date" name="date_of_birth" class="form-control"
                                               value="<?php echo $user['date_of_birth']; ?>">
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الجنس</label>
                                        <select name="gender" class="form-select">
                                            <option value="">اختر الجنس</option>
                                            <option value="male" <?php echo $user['gender'] === 'male' ? 'selected' : ''; ?>>ذكر</option>
                                            <option value="female" <?php echo $user['gender'] === 'female' ? 'selected' : ''; ?>>أنثى</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">المنطقة الزمنية</label>
                                        <select name="timezone" class="form-select">
                                            <option value="Asia/Riyadh" <?php echo $user['timezone'] === 'Asia/Riyadh' ? 'selected' : ''; ?>>الرياض</option>
                                            <option value="Asia/Dubai" <?php echo $user['timezone'] === 'Asia/Dubai' ? 'selected' : ''; ?>>دبي</option>
                                            <option value="Asia/Kuwait" <?php echo $user['timezone'] === 'Asia/Kuwait' ? 'selected' : ''; ?>>الكويت</option>
                                            <option value="Asia/Qatar" <?php echo $user['timezone'] === 'Asia/Qatar' ? 'selected' : ''; ?>>قطر</option>
                                            <option value="Asia/Bahrain" <?php echo $user['timezone'] === 'Asia/Bahrain' ? 'selected' : ''; ?>>البحرين</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">البلد</label>
                                        <input type="text" name="country" class="form-control"
                                               value="<?php echo htmlspecialchars($user['country']); ?>">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">المدينة</label>
                                        <input type="text" name="city" class="form-control"
                                               value="<?php echo htmlspecialchars($user['city']); ?>">
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">اللغة</label>
                                        <select name="language" class="form-select">
                                            <option value="ar" <?php echo $user['language'] === 'ar' ? 'selected' : ''; ?>>العربية</option>
                                            <option value="en" <?php echo $user['language'] === 'en' ? 'selected' : ''; ?>>English</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">صورة الملف الشخصي</label>
                                        <input type="file" name="profile_image" class="form-control" 
                                               accept=".jpg,.jpeg,.png,.gif">
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">نبذة شخصية</label>
                                    <textarea name="bio" class="form-control" rows="4" 
                                              placeholder="اكتب نبذة مختصرة عنك..."><?php echo htmlspecialchars($user['bio']); ?></textarea>
                                </div>
                                
                                <div class="text-end">
                                    <button type="submit" name="update_profile" class="btn btn-student-primary">
                                        <i class="fas fa-save me-1"></i>
                                        حفظ التغييرات
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- تبويب كلمة المرور -->
                        <div class="tab-pane fade" id="password-tab">
                            <form method="POST">
                                <div class="mb-3">
                                    <label class="form-label">كلمة المرور الحالية</label>
                                    <input type="password" name="current_password" class="form-control" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">كلمة المرور الجديدة</label>
                                    <input type="password" name="new_password" class="form-control" required
                                           minlength="8">
                                    <div class="form-text">يجب أن تكون 8 أحرف على الأقل</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">تأكيد كلمة المرور الجديدة</label>
                                    <input type="password" name="confirm_password" class="form-control" required
                                           minlength="8">
                                </div>
                                
                                <div class="text-end">
                                    <button type="submit" name="change_password" class="btn btn-student-primary">
                                        <i class="fas fa-key me-1"></i>
                                        تغيير كلمة المرور
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- تبويب النشاطات الأخيرة -->
                        <div class="tab-pane fade" id="activity-tab">
                            <?php if (empty($recent_activities)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-history fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">لا توجد نشاطات</h5>
                                <p class="text-muted">لم تقم بأي نشاطات بعد</p>
                            </div>
                            <?php else: ?>
                            <div class="activity-timeline">
                                <?php foreach ($recent_activities as $activity): ?>
                                <div class="activity-item">
                                    <div class="activity-icon">
                                        <?php
                                        switch ($activity['type']) {
                                            case 'course_enrollment':
                                                echo '<i class="fas fa-graduation-cap text-primary"></i>';
                                                break;
                                            case 'assignment_submission':
                                                echo '<i class="fas fa-tasks text-warning"></i>';
                                                break;
                                            case 'quiz_completion':
                                                echo '<i class="fas fa-question-circle text-info"></i>';
                                                break;
                                        }
                                        ?>
                                    </div>
                                    <div class="activity-content">
                                        <div class="activity-title">
                                            <?php
                                            switch ($activity['type']) {
                                                case 'course_enrollment':
                                                    echo 'تسجيل في كورس: ';
                                                    break;
                                                case 'assignment_submission':
                                                    echo 'تسليم واجب: ';
                                                    break;
                                                case 'quiz_completion':
                                                    echo 'إكمال اختبار: ';
                                                    break;
                                            }
                                            echo htmlspecialchars($activity['title']);
                                            ?>
                                        </div>
                                        <div class="activity-date text-muted">
                                            <?php echo date('Y-m-d H:i', strtotime($activity['date'])); ?>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.profile-image {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid var(--student-primary);
}

.profile-image-placeholder {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background-color: #f8fafc;
    border: 4px solid #e2e8f0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
}

.profile-stats .stat-item {
    padding: 0.5rem;
}

.profile-info {
    text-align: left;
    font-size: 0.9rem;
}

.activity-timeline {
    position: relative;
    padding-left: 2rem;
}

.activity-timeline::before {
    content: '';
    position: absolute;
    left: 1rem;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #e2e8f0;
}

.activity-item {
    position: relative;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: flex-start;
}

.activity-icon {
    position: absolute;
    left: -2rem;
    width: 2rem;
    height: 2rem;
    background: white;
    border: 2px solid #e2e8f0;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
}

.activity-content {
    flex: 1;
    background: #f8fafc;
    padding: 1rem;
    border-radius: 0.5rem;
    margin-left: 1rem;
}

.activity-title {
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.activity-date {
    font-size: 0.8rem;
}
</style>

<?php include 'includes/footer.php'; ?>
