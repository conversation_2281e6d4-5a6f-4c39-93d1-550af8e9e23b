<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من صلاحيات المدرب
if (!isLoggedIn() || !isInstructor()) {
    header('Location: ../login.php');
    exit;
}

$student_id = (int)($_GET['student_id'] ?? 0);
$course_id = (int)($_GET['course_id'] ?? 0);
$error = '';
$success = '';

// التحقق من أن الكورس ينتمي للمدرب
try {
    $stmt = $conn->prepare("SELECT title FROM courses WHERE id = ? AND instructor_id = ?");
    $stmt->execute([$course_id, $_SESSION['user_id']]);
    $course = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$course) {
        header('Location: courses.php');
        exit;
    }
} catch (PDOException $e) {
    $error = 'خطأ في التحقق من الكورس';
}

// جلب بيانات الطالب
try {
    $stmt = $conn->prepare("
        SELECT u.*, e.enrollment_date 
        FROM users u 
        JOIN enrollments e ON u.id = e.student_id 
        WHERE u.id = ? AND e.course_id = ? AND e.status = 'approved'
    ");
    $stmt->execute([$student_id, $course_id]);
    $student = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$student) {
        header('Location: students.php?course_id=' . $course_id);
        exit;
    }
} catch (PDOException $e) {
    $error = 'خطأ في جلب بيانات الطالب';
}

// جلب الدرجات الحالية
try {
    $stmt = $conn->prepare("
        SELECT * FROM grades 
        WHERE student_id = ? AND course_id = ? 
        ORDER BY created_at DESC
    ");
    $stmt->execute([$student_id, $course_id]);
    $existing_grades = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $existing_grades = [];
}

// معالجة إضافة الدرجة
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $assignment_name = trim($_POST['assignment_name'] ?? '');
    $grade = (float)($_POST['grade'] ?? 0);
    $max_grade = (float)($_POST['max_grade'] ?? 100);
    $notes = trim($_POST['notes'] ?? '');
    $grade_type = $_POST['grade_type'] ?? 'assignment';
    
    if (empty($assignment_name)) {
        $error = 'اسم التكليف مطلوب';
    } elseif ($grade < 0 || $grade > $max_grade) {
        $error = 'الدرجة يجب أن تكون بين 0 و ' . $max_grade;
    } else {
        try {
            $stmt = $conn->prepare("
                INSERT INTO grades (student_id, course_id, instructor_id, assignment_name, grade, max_grade, grade_type, notes) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $student_id, 
                $course_id, 
                $_SESSION['user_id'], 
                $assignment_name, 
                $grade, 
                $max_grade, 
                $grade_type, 
                $notes
            ]);
            
            $success = 'تم إضافة الدرجة بنجاح';
            
            // إعادة جلب الدرجات
            $stmt = $conn->prepare("
                SELECT * FROM grades 
                WHERE student_id = ? AND course_id = ? 
                ORDER BY created_at DESC
            ");
            $stmt->execute([$student_id, $course_id]);
            $existing_grades = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (PDOException $e) {
            $error = 'خطأ في إضافة الدرجة: ' . $e->getMessage();
        }
    }
}

$pageTitle = 'إضافة درجة - ' . $student['name'];
$breadcrumbs = [
    ['title' => 'الكورسات', 'url' => 'courses.php'],
    ['title' => $course['title'], 'url' => 'course-details.php?id=' . $course_id],
    ['title' => 'الطلاب', 'url' => 'students.php?course_id=' . $course_id],
    ['title' => 'إضافة درجة']
];

include 'includes/header.php';
?>

<div class="row">
    <div class="col-md-8">
        <!-- نموذج إضافة الدرجة -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-star me-2"></i>
                    إضافة درجة جديدة
                </h5>
            </div>
            <div class="card-body">
                <?php if ($error): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endif; ?>

                <?php if ($success): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        <?php echo htmlspecialchars($success); ?>
                    </div>
                <?php endif; ?>

                <form method="POST" action="">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="assignment_name" class="form-label">اسم التكليف/الاختبار</label>
                                <input type="text" class="form-control" id="assignment_name" name="assignment_name" 
                                       placeholder="مثال: اختبار الوحدة الأولى" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="grade_type" class="form-label">نوع التقييم</label>
                                <select class="form-select" id="grade_type" name="grade_type">
                                    <option value="assignment">واجب</option>
                                    <option value="quiz">اختبار قصير</option>
                                    <option value="exam">امتحان</option>
                                    <option value="project">مشروع</option>
                                    <option value="participation">مشاركة</option>
                                    <option value="attendance">حضور</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="grade" class="form-label">الدرجة المحصلة</label>
                                <input type="number" class="form-control" id="grade" name="grade" 
                                       min="0" step="0.5" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="max_grade" class="form-label">الدرجة الكاملة</label>
                                <input type="number" class="form-control" id="max_grade" name="max_grade" 
                                       value="100" min="1" step="0.5" required>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات (اختياري)</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3" 
                                  placeholder="أي ملاحظات إضافية حول الدرجة..."></textarea>
                    </div>

                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save me-2"></i>
                            حفظ الدرجة
                        </button>
                        <a href="students.php?course_id=<?php echo $course_id; ?>" class="btn btn-secondary">
                            <i class="fas fa-arrow-right me-2"></i>
                            العودة للطلاب
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <!-- معلومات الطالب -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-user me-2"></i>
                    معلومات الطالب
                </h6>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <div class="avatar-lg mx-auto mb-3">
                        <?php echo strtoupper(substr($student['name'], 0, 1)); ?>
                    </div>
                    <h6><?php echo htmlspecialchars($student['name']); ?></h6>
                    <small class="text-muted"><?php echo htmlspecialchars($student['email']); ?></small>
                </div>
                
                <hr>
                
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h6 class="text-primary"><?php echo count($existing_grades); ?></h6>
                            <small class="text-muted">الدرجات</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h6 class="text-success">
                            <?php 
                            if (!empty($existing_grades)) {
                                $total = array_sum(array_column($existing_grades, 'grade'));
                                $max_total = array_sum(array_column($existing_grades, 'max_grade'));
                                echo $max_total > 0 ? round(($total / $max_total) * 100, 1) . '%' : '0%';
                            } else {
                                echo '0%';
                            }
                            ?>
                        </h6>
                        <small class="text-muted">المعدل</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- الدرجات السابقة -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-history me-2"></i>
                    الدرجات السابقة
                </h6>
            </div>
            <div class="card-body">
                <?php if (empty($existing_grades)): ?>
                    <div class="text-center text-muted py-3">
                        <i class="fas fa-inbox fa-2x mb-2"></i>
                        <p class="mb-0">لا توجد درجات مسجلة</p>
                    </div>
                <?php else: ?>
                    <div class="list-group list-group-flush">
                        <?php foreach (array_slice($existing_grades, 0, 5) as $grade): ?>
                            <div class="list-group-item px-0">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="mb-1"><?php echo htmlspecialchars($grade['assignment_name']); ?></h6>
                                        <small class="text-muted">
                                            <?php 
                                            $types = [
                                                'assignment' => 'واجب',
                                                'quiz' => 'اختبار قصير',
                                                'exam' => 'امتحان',
                                                'project' => 'مشروع',
                                                'participation' => 'مشاركة',
                                                'attendance' => 'حضور'
                                            ];
                                            echo $types[$grade['grade_type']] ?? $grade['grade_type'];
                                            ?>
                                        </small>
                                    </div>
                                    <div class="text-end">
                                        <span class="badge bg-primary">
                                            <?php echo $grade['grade']; ?>/<?php echo $grade['max_grade']; ?>
                                        </span>
                                        <br>
                                        <small class="text-muted">
                                            <?php echo round(($grade['grade'] / $grade['max_grade']) * 100, 1); ?>%
                                        </small>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    
                    <?php if (count($existing_grades) > 5): ?>
                        <div class="text-center mt-3">
                            <a href="student-grades.php?student_id=<?php echo $student_id; ?>&course_id=<?php echo $course_id; ?>" 
                               class="btn btn-sm btn-outline-primary">
                                عرض جميع الدرجات
                            </a>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-lg {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--instructor-primary), var(--instructor-secondary));
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: bold;
}

.card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.card-header {
    background: var(--instructor-light);
    border-bottom: 1px solid var(--gray-200);
    border-radius: 12px 12px 0 0 !important;
}

.list-group-item {
    border: none;
    border-bottom: 1px solid var(--gray-100);
}

.list-group-item:last-child {
    border-bottom: none;
}
</style>

<!-- JavaScript -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // تحديث النسبة المئوية عند تغيير الدرجة
    const gradeInput = document.getElementById('grade');
    const maxGradeInput = document.getElementById('max_grade');
    
    function updatePercentage() {
        const grade = parseFloat(gradeInput.value) || 0;
        const maxGrade = parseFloat(maxGradeInput.value) || 100;
        const percentage = maxGrade > 0 ? ((grade / maxGrade) * 100).toFixed(1) : 0;
        
        // إضافة عرض النسبة المئوية
        let percentageDisplay = document.getElementById('percentage-display');
        if (!percentageDisplay) {
            percentageDisplay = document.createElement('small');
            percentageDisplay.id = 'percentage-display';
            percentageDisplay.className = 'text-muted';
            gradeInput.parentNode.appendChild(percentageDisplay);
        }
        percentageDisplay.textContent = `النسبة المئوية: ${percentage}%`;
    }
    
    gradeInput.addEventListener('input', updatePercentage);
    maxGradeInput.addEventListener('input', updatePercentage);
    
    // التحقق من صحة الدرجة
    gradeInput.addEventListener('blur', function() {
        const grade = parseFloat(this.value);
        const maxGrade = parseFloat(maxGradeInput.value) || 100;
        
        if (grade > maxGrade) {
            this.value = maxGrade;
            updatePercentage();
        }
    });
});
</script>

</body>
</html>
