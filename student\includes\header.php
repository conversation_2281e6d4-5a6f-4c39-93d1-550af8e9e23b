<?php
if (!isset($pageTitle)) {
    $pageTitle = 'لوحة تحكم الطالب';
}

// جلب الإشعارات غير المقروءة
try {
    $stmt = $conn->prepare("
        SELECT COUNT(*) as unread_count 
        FROM notifications 
        WHERE user_id = ? AND is_read = 0
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $unread_notifications = $stmt->fetchColumn();
} catch (PDOException $e) {
    $unread_notifications = 0;
}

// جلب الرسائل غير المقروءة
try {
    $stmt = $conn->prepare("
        SELECT COUNT(*) as unread_count 
        FROM messages 
        WHERE receiver_id = ? AND is_read = 0
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $unread_messages = $stmt->fetchColumn();
} catch (PDOException $e) {
    $unread_messages = 0;
}
?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - منصة التعليم الإلكتروني</title>
    
    <!-- CSS Files -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <style>
        :root {
            --student-primary: #3b82f6;
            --student-secondary: #64748b;
            --student-success: #10b981;
            --student-warning: #f59e0b;
            --student-danger: #ef4444;
            --student-info: #06b6d4;
            --student-light: #f8fafc;
            --student-dark: #1e293b;
        }
        
        * {
            font-family: 'Cairo', sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: var(--student-dark);
        }
        
        .navbar-student {
            background: linear-gradient(135deg, var(--student-primary) 0%, #1d4ed8 100%);
            box-shadow: 0 4px 20px rgba(59, 130, 246, 0.3);
            padding: 1rem 0;
        }
        
        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            color: white !important;
        }
        
        .navbar-nav .nav-link {
            color: rgba(255, 255, 255, 0.9) !important;
            font-weight: 500;
            padding: 0.75rem 1rem !important;
            border-radius: 0.5rem;
            transition: all 0.3s ease;
            margin: 0 0.25rem;
        }
        
        .navbar-nav .nav-link:hover,
        .navbar-nav .nav-link.active {
            background-color: rgba(255, 255, 255, 0.15);
            color: white !important;
            transform: translateY(-2px);
        }
        
        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background: var(--student-danger);
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 0.7rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }
        
        .sidebar-student {
            background: white;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            border-radius: 1rem;
            margin: 1rem;
            padding: 1.5rem;
            height: fit-content;
            position: sticky;
            top: 1rem;
        }
        
        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .sidebar-menu li {
            margin-bottom: 0.5rem;
        }
        
        .sidebar-menu a {
            display: flex;
            align-items: center;
            padding: 1rem;
            color: var(--student-secondary);
            text-decoration: none;
            border-radius: 0.75rem;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background: linear-gradient(135deg, var(--student-primary), #1d4ed8);
            color: white;
            transform: translateX(-5px);
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
        }
        
        .sidebar-menu a i {
            width: 20px;
            margin-left: 1rem;
        }
        
        .main-content {
            padding: 1rem;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 1rem;
            margin: 1rem;
            min-height: calc(100vh - 200px);
            backdrop-filter: blur(10px);
        }
        
        .breadcrumb-student {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-radius: 0.75rem;
            padding: 1rem 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        
        .breadcrumb-student .breadcrumb {
            margin: 0;
        }
        
        .breadcrumb-student .breadcrumb-item a {
            color: var(--student-primary);
            text-decoration: none;
            font-weight: 500;
        }
        
        .breadcrumb-student .breadcrumb-item.active {
            color: var(--student-secondary);
            font-weight: 600;
        }
        
        .card-student {
            background: white;
            border: none;
            border-radius: 1rem;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            overflow: hidden;
        }
        
        .card-student:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.15);
        }
        
        .btn-student-primary {
            background: linear-gradient(135deg, var(--student-primary), #1d4ed8);
            border: none;
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-student-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
            color: white;
        }
        
        .progress-student {
            height: 10px;
            border-radius: 5px;
            background: #e2e8f0;
            overflow: hidden;
        }
        
        .progress-bar-student {
            background: linear-gradient(90deg, var(--student-primary), var(--student-info));
            transition: width 1s ease;
        }
        
        @media (max-width: 768px) {
            .sidebar-student {
                display: none;
            }
            
            .main-content {
                margin: 0.5rem;
            }
            
            body {
                padding-bottom: 80px;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-student">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-graduation-cap me-2"></i>
                منصة التعليم - الطالب
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'dashboard.php' ? 'active' : ''; ?>" href="dashboard.php">
                            <i class="fas fa-home me-1"></i>الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'courses.php' ? 'active' : ''; ?>" href="courses.php">
                            <i class="fas fa-book me-1"></i>كورساتي
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'assignments.php' ? 'active' : ''; ?>" href="assignments.php">
                            <i class="fas fa-tasks me-1"></i>الواجبات
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <!-- الإشعارات -->
                    <li class="nav-item dropdown">
                        <a class="nav-link position-relative" href="#" id="notificationsDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-bell"></i>
                            <?php if ($unread_notifications > 0): ?>
                            <span class="notification-badge"><?php echo $unread_notifications; ?></span>
                            <?php endif; ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" style="width: 300px;">
                            <li class="dropdown-header">الإشعارات</li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="notifications.php">عرض جميع الإشعارات</a></li>
                        </ul>
                    </li>
                    
                    <!-- الرسائل -->
                    <li class="nav-item dropdown">
                        <a class="nav-link position-relative" href="#" id="messagesDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-envelope"></i>
                            <?php if ($unread_messages > 0): ?>
                            <span class="notification-badge"><?php echo $unread_messages; ?></span>
                            <?php endif; ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" style="width: 300px;">
                            <li class="dropdown-header">الرسائل</li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="messages.php">عرض جميع الرسائل</a></li>
                        </ul>
                    </li>
                    
                    <!-- ملف المستخدم -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>
                            <?php echo isset($_SESSION['name']) ? $_SESSION['name'] : 'الطالب'; ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="profile.php">
                                <i class="fas fa-user me-2"></i>الملف الشخصي
                            </a></li>
                            <li><a class="dropdown-item" href="certificates.php">
                                <i class="fas fa-certificate me-2"></i>الشهادات
                            </a></li>
                            <li><a class="dropdown-item" href="settings.php">
                                <i class="fas fa-cog me-2"></i>الإعدادات
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-danger" href="../logout.php">
                                <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-lg-2 d-none d-lg-block">
                <div class="sidebar-student">
                    <ul class="sidebar-menu">
                        <li>
                            <a href="dashboard.php" class="<?php echo basename($_SERVER['PHP_SELF']) == 'dashboard.php' ? 'active' : ''; ?>">
                                <i class="fas fa-home"></i>
                                لوحة التحكم
                            </a>
                        </li>
                        <li>
                            <a href="courses.php" class="<?php echo basename($_SERVER['PHP_SELF']) == 'courses.php' ? 'active' : ''; ?>">
                                <i class="fas fa-graduation-cap"></i>
                                كورساتي
                            </a>
                        </li>
                        <li>
                            <a href="browse-courses.php" class="<?php echo basename($_SERVER['PHP_SELF']) == 'browse-courses.php' ? 'active' : ''; ?>">
                                <i class="fas fa-search"></i>
                                تصفح الكورسات
                            </a>
                        </li>
                        <li>
                            <a href="assignments.php" class="<?php echo basename($_SERVER['PHP_SELF']) == 'assignments.php' ? 'active' : ''; ?>">
                                <i class="fas fa-tasks"></i>
                                الواجبات
                            </a>
                        </li>
                        <li>
                            <a href="quizzes.php" class="<?php echo basename($_SERVER['PHP_SELF']) == 'quizzes.php' ? 'active' : ''; ?>">
                                <i class="fas fa-question-circle"></i>
                                الاختبارات
                            </a>
                        </li>
                        <li>
                            <a href="grades.php" class="<?php echo basename($_SERVER['PHP_SELF']) == 'grades.php' ? 'active' : ''; ?>">
                                <i class="fas fa-chart-line"></i>
                                الدرجات
                            </a>
                        </li>
                        <li>
                            <a href="certificates.php" class="<?php echo basename($_SERVER['PHP_SELF']) == 'certificates.php' ? 'active' : ''; ?>">
                                <i class="fas fa-certificate"></i>
                                الشهادات
                            </a>
                        </li>
                        <li>
                            <a href="schedule.php" class="<?php echo basename($_SERVER['PHP_SELF']) == 'schedule.php' ? 'active' : ''; ?>">
                                <i class="fas fa-calendar"></i>
                                الجدول الزمني
                            </a>
                        </li>
                        <li>
                            <a href="library.php" class="<?php echo basename($_SERVER['PHP_SELF']) == 'library.php' ? 'active' : ''; ?>">
                                <i class="fas fa-book-open"></i>
                                المكتبة
                            </a>
                        </li>
                        <li>
                            <a href="forums.php" class="<?php echo basename($_SERVER['PHP_SELF']) == 'forums.php' ? 'active' : ''; ?>">
                                <i class="fas fa-comments"></i>
                                المنتديات
                            </a>
                        </li>
                        <li>
                            <a href="messages.php" class="<?php echo basename($_SERVER['PHP_SELF']) == 'messages.php' ? 'active' : ''; ?>">
                                <i class="fas fa-envelope"></i>
                                الرسائل
                                <?php if ($unread_messages > 0): ?>
                                <span class="badge bg-danger ms-auto"><?php echo $unread_messages; ?></span>
                                <?php endif; ?>
                            </a>
                        </li>
                        <li>
                            <a href="notifications.php" class="<?php echo basename($_SERVER['PHP_SELF']) == 'notifications.php' ? 'active' : ''; ?>">
                                <i class="fas fa-bell"></i>
                                الإشعارات
                                <?php if ($unread_notifications > 0): ?>
                                <span class="badge bg-danger ms-auto"><?php echo $unread_notifications; ?></span>
                                <?php endif; ?>
                            </a>
                        </li>
                        <li>
                            <a href="profile.php" class="<?php echo basename($_SERVER['PHP_SELF']) == 'profile.php' ? 'active' : ''; ?>">
                                <i class="fas fa-user"></i>
                                الملف الشخصي
                            </a>
                        </li>
                        <li>
                            <a href="settings.php" class="<?php echo basename($_SERVER['PHP_SELF']) == 'settings.php' ? 'active' : ''; ?>">
                                <i class="fas fa-cog"></i>
                                الإعدادات
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="col-lg-10">
                <div class="main-content">
                    <!-- Breadcrumb -->
                    <?php if (isset($breadcrumbs) && !empty($breadcrumbs)): ?>
                    <div class="breadcrumb-student">
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item">
                                    <a href="dashboard.php">
                                        <i class="fas fa-home me-1"></i>الرئيسية
                                    </a>
                                </li>
                                <?php foreach ($breadcrumbs as $index => $breadcrumb): ?>
                                    <?php if ($index === count($breadcrumbs) - 1): ?>
                                        <li class="breadcrumb-item active" aria-current="page">
                                            <?php echo htmlspecialchars($breadcrumb['title']); ?>
                                        </li>
                                    <?php else: ?>
                                        <li class="breadcrumb-item">
                                            <a href="<?php echo htmlspecialchars($breadcrumb['url']); ?>">
                                                <?php echo htmlspecialchars($breadcrumb['title']); ?>
                                            </a>
                                        </li>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                        </nav>
                    </div>
                    <?php endif; ?>
